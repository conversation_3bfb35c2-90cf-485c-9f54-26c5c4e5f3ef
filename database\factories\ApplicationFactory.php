<?php

namespace Database\Factories;

use App\Models\Application;
use App\Models\User;
use Illuminate\Database\Eloquent\Factories\Factory;

class ApplicationFactory extends Factory
{
    protected $model = Application::class;

    public function definition(): array
    {
        return [
            'discord_id' => fake()->unique()->numberBetween(100000000000000000, 999999999999999999),
            'name' => fake()->name(),
            'age' => fake()->numberBetween(16, 40),
            'gender' => fake()->randomElement(['male', 'female', 'diverse']),
            'pronouns' => fake()->optional()->randomElement(['he/him', 'she/her', 'they/them']),
            'professions' => fake()->randomElements(['actor', 'builder', 'designer', 'developer'], 2),
            'checkboxQuestions' => [
                'question1' => fake()->boolean(),
                'question2' => fake()->boolean(),
                'question3' => fake()->boolean(),
            ],
            'about_you' => fake()->paragraphs(3, true),
            'strengths_weaknesses' => fake()->paragraphs(2, true),
            'final_words' => fake()->optional()->paragraph(),
            'voice_type' => fake()->optional()->randomElement(['bass', 'tenor', 'alto', 'soprano']),
            'ram' => fake()->numberBetween(4, 32),
            'fps' => (string) fake()->numberBetween(30, 240),
            'desired_role' => fake()->optional()->word(),
            'portfolio' => fake()->optional()->url(),
            'microphone' => fake()->optional()->word(),
            'daw' => fake()->optional()->word(),
            'program' => fake()->optional()->word(),
            'design_style' => fake()->optional()->word(),
            'favorite_design' => fake()->optional()->word(),
            'gpu' => fake()->optional()->word(),
            'languages' => fake()->optional()->word(),
            'ide' => fake()->optional()->word(),
            'status' => 'pending',
            'created_at' => fake()->dateTimeBetween('-1 month', 'now'),
            'updated_at' => fake()->dateTimeBetween('-1 month', 'now'),
        ];
    }

    /**
     * Configure the model for an actor application.
     */
    public function actor(): self
    {
        return $this->state(function (array $attributes) {
            return [
                'professions' => ['actor'],
                'voice_type' => fake()->randomElement(['bass', 'tenor', 'alto', 'soprano']),
                'microphone' => fake()->randomElement(['Blue Yeti', 'Rode NT1', 'Shure SM7B', 'HyperX QuadCast']),
                'ram' => fake()->numberBetween(8, 32),
                'fps' => (string) fake()->numberBetween(60, 240),
            ];
        });
    }

    /**
     * Configure the model for a builder application.
     */
    public function builder(): self
    {
        return $this->state(function (array $attributes) {
            return [
                'professions' => ['builder'],
                'portfolio' => fake()->url(),
                'ram' => fake()->numberBetween(8, 64),
                'fps' => (string) fake()->numberBetween(60, 240),
            ];
        });
    }

    /**
     * Configure the model for a designer application.
     */
    public function designer(): self
    {
        return $this->state(function (array $attributes) {
            return [
                'professions' => ['designer'],
                'design_style' => fake()->randomElement(['Minimalist', 'Modern', 'Retro', 'Pixel Art', 'Realistic']),
                'favorite_design' => fake()->sentence(),
                'program' => fake()->randomElement(['Photoshop', 'Illustrator', 'Figma', 'Sketch', 'GIMP']),
                'portfolio' => fake()->url(),
            ];
        });
    }

    /**
     * Configure the model for a developer application.
     */
    public function developer(): self
    {
        return $this->state(function (array $attributes) {
            return [
                'professions' => ['developer'],
                'languages' => implode(', ', fake()->randomElements(['Java', 'JavaScript', 'PHP', 'Python', 'C#', 'C++'], 3)),
                'ide' => fake()->randomElement(['Visual Studio Code', 'IntelliJ IDEA', 'Eclipse', 'NetBeans', 'PHPStorm']),
                'portfolio' => fake()->url(),
            ];
        });
    }

    /**
     * Configure the model as approved.
     */
    public function approved(): self
    {
        return $this->state(function (array $attributes) {
            $reviewer = User::factory()->admin()->create();

            return [
                'status' => 'approved',
                'reviewer_id' => $reviewer->id,
                'reviewed_at' => now(),
                'team_comment' => fake()->paragraph(),
            ];
        });
    }

    /**
     * Configure the model as rejected.
     */
    public function rejected(): self
    {
        return $this->state(function (array $attributes) {
            $reviewer = User::factory()->admin()->create();

            return [
                'status' => 'rejected',
                'reviewer_id' => $reviewer->id,
                'reviewed_at' => now(),
                'team_comment' => fake()->paragraph(),
            ];
        });
    }

    /**
     * Configure the model as reviewed.
     */
    public function reviewed(): self
    {
        return $this->state(function (array $attributes) {
            $reviewer = User::factory()->admin()->create();

            return [
                'reviewer_id' => $reviewer->id,
                'reviewed_at' => now(),
                'team_comment' => fake()->paragraph(),
            ];
        });
    }
}
