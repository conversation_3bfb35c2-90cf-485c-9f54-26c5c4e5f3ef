@import url('https://fonts.googleapis.com/css2?family=Poppins:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900&display=swap');
@import '@fontsource/geist-sans';
@import '@fontsource/geist-sans/700.css'; /* Für Fettdruck */
@import 'ai-assistant.css'; /* AI Assistant Styles */

@tailwind base;
@tailwind components;
@tailwind utilities;
@plugin "daisyui";

/* Modern gradient backgrounds */
.bg-gradient-modern {
  @apply bg-gradient-to-br from-slate-900 to-slate-800;
}

/* Attachment container styles */
.attachment-container {
    @apply bg-slate-800/70 backdrop-filter backdrop-blur-sm rounded-xl overflow-hidden transition-all duration-200;
}

.attachment-container:hover {
    @apply transform scale-[1.02] shadow-lg;
}

/* Mobile-first approach */
.attachment-container {
    @apply w-full overflow-hidden rounded-xl;
}

/* Larger touch targets on mobile */
@media (max-width: 640px) {
    .btn-mobile {
        @apply p-3; /* Larger padding for better touch targets */
    }

    .attachment-controls {
        @apply space-x-3; /* More space between controls */
    }
}

/* Custom breakpoint for extra small screens */
@media (min-width: 480px) {
    .xs\:grid-cols-2 {
        grid-template-columns: repeat(2, minmax(0, 1fr));
    }
}

/* Base text size adjustment for better readability across devices */
@layer base {
  html {
    -webkit-text-size-adjust: 100%;
    text-size-adjust: 100%;
  }
}

/* Netflix-style UI elements */
.scrollbar-hide {
  -ms-overflow-style: none;  /* IE and Edge */
  scrollbar-width: none;  /* Firefox */
}

.scrollbar-hide::-webkit-scrollbar {
  display: none; /* Chrome, Safari and Opera */
}

/* Netflix-style card hover effects */
.netflix-card {
  transition: all 0.3s ease;
  transform-origin: center bottom;
}

.netflix-card:hover {
  transform: scale(1.05);
  z-index: 10;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.5);
}

.netflix-card:hover .card-info {
  opacity: 1;
  transform: translateY(0);
}

.card-info {
  opacity: 0;
  transform: translateY(10px);
  transition: all 0.3s ease;
}

/* Enhanced theme variables with smooth transitions */
:root {
  --color-primary-rgb: 59, 130, 246; /* RGB equivalent for rgba usage */
  --color-base-content-rgb: 248, 250, 252; /* Default RGB for base-content */
  --radius-selector: 1rem;
  --radius-field: 0.75rem;
  --radius-box: 1.5rem;
  --size-selector: 0.25rem;
  --size-field: 0.25rem;
  --border: 1.5px;
  --depth: 1;
  --noise: 0;

  /* Enhanced transition variables */
  --transition-fast: 150ms cubic-bezier(0.4, 0, 0.2, 1);
  --transition-normal: 300ms cubic-bezier(0.4, 0, 0.2, 1);
  --transition-slow: 500ms cubic-bezier(0.4, 0, 0.2, 1);

  /* Shadow variables */
  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  --shadow-2xl: 0 25px 50px -12px rgba(0, 0, 0, 0.25);

  /* Blur variables */
  --blur-sm: 4px;
  --blur-md: 8px;
  --blur-lg: 12px;
  --blur-xl: 16px;
}

/* Dark theme (minewache) */
[data-theme="minewache"] {
  --color-base-100: #0f172a; /* slate-900 */
  --color-base-100-rgb: 15, 23, 42; /* RGB for base-100 */
  --color-base-200: #1e293b; /* slate-800 */
  --color-base-200-rgb: 30, 41, 59; /* RGB for base-200 */
  --color-base-300: #334155; /* slate-700 */
  --color-base-300-rgb: 51, 65, 85; /* RGB for base-300 */
  --color-base-content: #f8fafc; /* slate-50 */
  --color-base-content-rgb: 248, 250, 252; /* RGB for base-content */
  --color-primary: #3b82f6; /* blue-500 */
  --color-primary-content: #ffffff; /* white */
  --color-primary-rgb: 59, 130, 246; /* RGB for primary */
  --color-secondary: #475569; /* slate-600 */
  --color-secondary-content: #ffffff; /* white */
  --color-accent: #1e293b; /* slate-800 */
  --color-accent-content: #ffffff; /* white */
  --color-neutral: #1e293b; /* slate-800 */
  --color-neutral-content: #ffffff; /* white */
  --color-info: #3b82f6; /* blue-500 */
  --color-info-content: #ffffff; /* white */
  --color-success: #10b981; /* emerald-500 */
  --color-success-content: #ffffff; /* white */
  --color-warning: #f59e0b; /* amber-500 */
  --color-warning-content: #ffffff; /* white */
  --color-error: #ef4444; /* red-500 */
  --color-error-content: #ffffff; /* white */

}

/* Light theme (minewache-light) */
[data-theme="minewache-light"] {
  --color-base-100: #f8fafc; /* slate-50 */
  --color-base-100-rgb: 248, 250, 252; /* RGB for base-100 */
  --color-base-200: #f1f5f9; /* slate-100 */
  --color-base-200-rgb: 241, 245, 249; /* RGB for base-200 */
  --color-base-300: #e2e8f0; /* slate-200 */
  --color-base-300-rgb: 226, 232, 240; /* RGB for base-300 */
  --color-base-content: #0f172a; /* slate-900 */
  --color-base-content-rgb: 15, 23, 42; /* RGB for base-content */
  --color-primary: #3b82f6; /* blue-500 */
  --color-primary-content: #ffffff; /* white */
  --color-primary-rgb: 59, 130, 246; /* RGB for primary */
  --color-secondary: #475569; /* slate-600 */
  --color-secondary-content: #ffffff; /* white */
  --color-accent: #60a5fa; /* blue-400 */
  --color-accent-content: #ffffff; /* white */
  --color-neutral: #e2e8f0; /* slate-200 */
  --color-neutral-content: #1e293b; /* slate-800 */
  --color-info: #3b82f6; /* blue-500 */
  --color-info-content: #ffffff; /* white */
  --color-success: #10b981; /* emerald-500 */
  --color-success-content: #ffffff; /* white */
  --color-warning: #f59e0b; /* amber-500 */
  --color-warning-content: #ffffff; /* white */
  --color-error: #ef4444; /* red-500 */
  --color-error-content: #ffffff; /* white */
}

/* Auto theme (follows system preference) */
[data-theme="minewache-auto"] {
  --color-base-100: #0f172a;
  --color-base-100-rgb: 15, 23, 42;
  --color-base-200: #1e293b;
  --color-base-200-rgb: 30, 41, 59;
  --color-base-300: #334155;
  --color-base-300-rgb: 51, 65, 85;
  --color-base-content: #f8fafc;
  --color-base-content-rgb: 248, 250, 252;
  --color-primary: #3b82f6;
  --color-primary-content: #ffffff;
  --color-primary-rgb: 59, 130, 246;
  --color-secondary: #475569;
  --color-secondary-content: #ffffff;
  --color-accent: #1e293b;
  --color-accent-content: #ffffff;
  --color-neutral: #1e293b;
  --color-neutral-content: #ffffff;
  --color-info: #3b82f6;
  --color-info-content: #ffffff;
  --color-success: #10b981;
  --color-success-content: #ffffff;
  --color-warning: #f59e0b;
  --color-warning-content: #ffffff;
  --color-error: #ef4444;
  --color-error-content: #ffffff;
}

/* High contrast theme for accessibility */
[data-theme="minewache-high-contrast"] {
  --color-base-100: #000000;
  --color-base-100-rgb: 0, 0, 0;
  --color-base-200: #1a1a1a;
  --color-base-200-rgb: 26, 26, 26;
  --color-base-300: #333333;
  --color-base-300-rgb: 51, 51, 51;
  --color-base-content: #ffffff;
  --color-base-content-rgb: 255, 255, 255;
  --color-primary: #0066ff;
  --color-primary-content: #ffffff;
  --color-primary-rgb: 0, 102, 255;
  --color-secondary: #000000;
  --color-secondary-content: #ffffff;
  --color-accent: #ff6600;
  --color-accent-content: #ffffff;
  --color-neutral: #000000;
  --color-neutral-content: #ffffff;
  --color-info: #00ccff;
  --color-info-content: #000000;
  --color-success: #00ff00;
  --color-success-content: #000000;
  --color-warning: #ffff00;
  --color-warning-content: #000000;
  --color-error: #ff0000;
  --color-error-content: #ffffff;
}

/* Colorful theme for vibrant experience */
[data-theme="minewache-colorful"] {
  --color-base-100: #0f172a;
  --color-base-100-rgb: 15, 23, 42;
  --color-base-200: #1e293b;
  --color-base-200-rgb: 30, 41, 59;
  --color-base-300: #334155;
  --color-base-300-rgb: 51, 65, 85;
  --color-base-content: #f8fafc;
  --color-base-content-rgb: 248, 250, 252;
  --color-primary: #8b5cf6;
  --color-primary-content: #ffffff;
  --color-primary-rgb: 139, 92, 246;
  --color-secondary: #06b6d4;
  --color-secondary-content: #ffffff;
  --color-accent: #f59e0b;
  --color-accent-content: #ffffff;
  --color-neutral: #1e293b;
  --color-neutral-content: #ffffff;
  --color-info: #06b6d4;
  --color-info-content: #ffffff;
  --color-success: #10b981;
  --color-success-content: #ffffff;
  --color-warning: #f59e0b;
  --color-warning-content: #ffffff;
  --color-error: #ef4444;
  --color-error-content: #ffffff;
}

.btn-hover:hover {
  background-color: var(--color-primary);
  color: var(--color-primary-content);
}

/* Custom global styles that use theme colors */
@layer base {
  h1 {
    @apply text-primary font-bold;
  }

  h2 {
    @apply text-secondary font-semibold;
  }

  a {
    @apply text-info transition-colors;
  }

  a:hover {
    @apply text-secondary-content;
  }

  button:not([class]) {
    @apply bg-blue-500 hover:bg-blue-600 text-white rounded-full px-4 py-2 shadow-lg transition;
  }

  /* Form inputs with consistent styling */
  input:not([class]), select:not([class]), textarea:not([class]) {
    @apply bg-slate-800 text-slate-100 rounded-2xl px-4 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 w-full;
  }

  /* Light mode form inputs */
  [data-theme="minewache-light"] input:not([class]),
  [data-theme="minewache-light"] select:not([class]),
  [data-theme="minewache-light"] textarea:not([class]) {
    @apply bg-white text-slate-800 border border-slate-200;
  }
}

@keyframes fillWidth {
    from {
        width: 0%;
    }
    to {
        width: 33%;
    }
}
@keyframes fillWidth2-3 {
    from {
        width: 0%;
    }
    to {
        width: 66%;
    }
}

@keyframes fillWidth3-3 {
    from {
        width: 0%;
    }
    to {
        width: 100%;
    }
}

.fill-animation {
    animation: fillWidth 1.5s ease-out forwards;
}
.fill-animation2-3 {
    animation: fillWidth2-3 2.5s ease-out forwards;
}

.fill-animation3-3 {
    animation: fillWidth3-3 3.5s ease-out forwards;
}

/* Accessibility styles */
@layer base {
  /* Improved focus styles for better visibility */
  :focus-visible {
    outline: 3px solid var(--color-primary);
    outline-offset: 2px;
  }

  /* Skip link styling */
  .sr-only:focus {
    z-index: 9999;
  }

  /* High contrast mode support */
  @media (forced-colors: active) {
    .btn, button, a {
      border: 1px solid transparent;
    }
  }

  /* Reduced motion support */
  @media (prefers-reduced-motion: reduce) {
    *, ::before, ::after {
      animation-duration: 0.01ms !important;
      animation-iteration-count: 1 !important;
      transition-duration: 0.01ms !important;
      scroll-behavior: auto !important;
    }
  }
}

/* Glassmorphism effect - Dark Mode (default) */
.glass {
  background: rgba(30, 41, 59, 0.6); /* slate-800 with transparency */
  backdrop-filter: blur(12px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  box-shadow: 0 8px 32px 0 rgba(0, 0, 0, 0.37);
}

/* Glassmorphism effect - Light Mode */
[data-theme="minewache-light"] .glass {
  background: rgba(255, 255, 255, 0.7); /* white with transparency */
  backdrop-filter: blur(12px);
  border: 1px solid rgba(59, 130, 246, 0.1); /* blue-500 with transparency */
  box-shadow: 0 8px 32px 0 rgba(0, 0, 0, 0.1);
}

.glass-light {
  background: rgba(255, 255, 255, 0.15);
  backdrop-filter: blur(8px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

/* Light mode glass-light */
[data-theme="minewache-light"] .glass-light {
  background: rgba(241, 245, 249, 0.5); /* slate-100 with transparency */
  backdrop-filter: blur(8px);
  border: 1px solid rgba(59, 130, 246, 0.1); /* blue-500 with transparency */
}

/* Modern input styling */
.modern-input {
  @apply bg-slate-800 text-slate-100 rounded-2xl px-4 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500;
}

/* Light mode input */
[data-theme="minewache-light"] .modern-input {
  @apply bg-white text-slate-800 border border-slate-200;
}

/* Modern button styling */
.modern-button {
  @apply bg-blue-500 hover:bg-blue-600 text-white rounded-full px-4 py-2 shadow-lg transition;
}

/* Custom component classes */
@layer components {
  .card-themed {
    @apply glass text-base-content p-6 shadow-lg rounded-3xl;
  }

  [data-theme="minewache-light"] .card-themed {
    @apply text-slate-800;
  }

  /* Profession card styling */
  .profession-card {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: hidden;
  }

  .profession-card:hover {
    box-shadow: 0 8px 25px rgba(var(--color-primary-rgb), 0.3);
  }

  .profession-card.selected {
    border-color: rgb(var(--color-primary-rgb));
    box-shadow: 0 8px 25px rgba(var(--color-primary-rgb), 0.3);
  }

  /* Theme-specific profession card styling */
  [data-theme="minewache-light"] .profession-card:hover {
    box-shadow: 0 8px 25px rgba(var(--color-primary-rgb), 0.2);
  }

  [data-theme="minewache-light"] .profession-card.selected {
    box-shadow: 0 8px 25px rgba(var(--color-primary-rgb), 0.2);
  }

  [data-theme="minewache-high-contrast"] .profession-card:hover {
    box-shadow: 0 8px 25px rgba(var(--color-primary-rgb), 0.5);
  }

  [data-theme="minewache-high-contrast"] .profession-card.selected {
    box-shadow: 0 8px 25px rgba(var(--color-primary-rgb), 0.5);
  }

  [data-theme="minewache-colorful"] .profession-card:hover {
    box-shadow: 0 8px 25px rgba(var(--color-primary-rgb), 0.4);
  }

  [data-theme="minewache-colorful"] .profession-card.selected {
    box-shadow: 0 8px 25px rgba(var(--color-primary-rgb), 0.4);
  }

  /* Application Wizard specific animations */
  .animate-fade-in {
    animation: fadeIn 0.6s ease-out;
  }

  .animate-slide-up {
    animation: slideUp 0.6s ease-out;
  }

  @keyframes fadeIn {
    from {
      opacity: 0;
    }
    to {
      opacity: 1;
    }
  }

  @keyframes slideUp {
    from {
      opacity: 0;
      transform: translateY(20px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  /* Modern input styling for application wizard */
  .application-wizard input[type="text"],
  .application-wizard input[type="number"],
  .application-wizard input[type="email"],
  .application-wizard textarea,
  .application-wizard select {
    transition: all 0.3s ease;
    border-radius: 12px;
  }

  .application-wizard input:focus,
  .application-wizard textarea:focus,
  .application-wizard select:focus {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(var(--color-primary-rgb), 0.15);
  }

  .alert-themed {
    @apply p-4 mb-4 rounded-2xl;
  }

  .alert-themed.info {
    @apply bg-info text-info-content;
  }

  .alert-themed.success {
    @apply bg-success text-success-content;
  }

  .alert-themed.warning {
    @apply bg-warning text-warning-content;
  }

  .alert-themed.error {
    @apply bg-error text-error-content;
  }

  /* Breeze component styles */
  .breeze-input {
    @apply modern-input w-full;
  }

  .breeze-input:focus {
    @apply ring-2 ring-blue-500 outline-none;
  }

  .breeze-button {
    @apply modern-button;
  }

  .breeze-link {
    @apply text-blue-400 hover:text-blue-300 transition-colors;
  }

  /* Message bubbles */
  .message-bubble-right {
    @apply bg-blue-500 text-white rounded-2xl rounded-br-none;
  }

  .message-bubble-left {
    @apply bg-slate-700 text-white rounded-2xl rounded-bl-none;
  }

  /* Light mode message bubbles */
  [data-theme="minewache-light"] .message-bubble-right {
    @apply bg-blue-500 text-white;
  }

  [data-theme="minewache-light"] .message-bubble-left {
    @apply bg-slate-200 text-slate-800;
  }
}

/* Navbar animation utilities */
@layer utilities {
  .nav-item-slide-down {
    animation: slideDown 0.5s ease-out forwards;
  }

  .nav-item-slide-up {
    animation: slideUp 0.5s ease-out forwards;
  }

  /* Special animation for burger menu */
  @keyframes spin-grow {
    0% {
      transform: scale(0.8) rotate(0deg);
      opacity: 0;
    }
    100% {
      transform: scale(1) rotate(180deg);
      opacity: 1;
    }
  }

  .nav-burger-appear {
    animation: spin-grow 0.5s ease-out forwards;
  }

  /* Floating background animation for welcome page */
  @keyframes floatingBackground {
    0% { transform: translate(0, 0); }
    50% { transform: translate(-5%, -5%); }
    100% { transform: translate(0, 0); }
  }
}

/* Floating stats animation for welcome page */
/*
 * Floating-Stat-Effekt: noch langsamer (Dauer 7s), alles andere bleibt wie beim klassischen Effekt.
 */
@keyframes float {
  0% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(-16px);
  }
  100% {
    transform: translateY(0);
  }
}

.animate-float {
  animation: float 7s ease-in-out infinite;
  animation-delay: var(--float-delay, 0s);
  will-change: transform;
}

@media (max-width: 640px) {
  @keyframes float {
    0% { transform: translateY(0); }
    50% { transform: translateY(-8px); }
    100% { transform: translateY(0); }
  }
}

@media (prefers-reduced-motion: reduce) {
  .animate-float {
    animation: none !important;
  }
}

/* Fade-in animation utility */
@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}
.animate-fade-in {
  animation: fadeIn 1.2s cubic-bezier(0.4, 0, 0.2, 1) both;
}

/* Slide-up animation utility */
@keyframes slideUp {
  from { transform: translateY(32px); opacity: 0; }
  to { transform: translateY(0); opacity: 1; }
}
.animate-slide-up {
  animation: slideUp 0.8s cubic-bezier(0.4, 0, 0.2, 1) both;
}

/* Slide-down animation utility */
@keyframes slideDown {
  from { transform: translateY(-32px); opacity: 0; }
  to { transform: translateY(0); opacity: 1; }
}
.animate-slide-down {
  animation: slideDown 0.8s cubic-bezier(0.4, 0, 0.2, 1) both;
}

/* Pulse animation utility */
@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.5; }
}
.animate-pulse {
  animation: pulse 1.5s cubic-bezier(0.4, 0, 0.2, 1) infinite;
}
