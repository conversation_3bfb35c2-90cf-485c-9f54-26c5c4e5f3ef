import './bootstrap';
import "@fontsource/geist-sans"; // Standardgewicht (400)
import "@fontsource/geist-sans/400.css"; // Spezifisches Gewicht
import "@fontsource/geist-sans/700.css"; // Fett

// Import cookie consent handler
import './cookie-consent';

// Import ticket view functions
import './ticket-view-functions';

// Theme is now managed by the theme-switcher component
// This is just a fallback in case the component fails to load
// Define Logger if it doesn't exist (fallback for when logger.js hasn't loaded)
if (typeof window.Logger === 'undefined') {
    window.Logger = {
        log: console.log.bind(console),
        debug: console.debug.bind(console),
        warn: console.warn.bind(console),
        error: console.error.bind(console),
        group: console.group.bind(console),
        groupEnd: console.groupEnd.bind(console),
        time: console.time.bind(console),
        timeEnd: console.timeEnd.bind(console),
        forceLog: console.log.bind(console)
    };
}

document.addEventListener('DOMContentLoaded', () => {
    Logger.log('App.js: DOMContentLoaded event fired');

    // Only apply theme if it hasn't been set by the theme-switcher component
    if (!document.documentElement.hasAttribute('data-theme')) {
        Logger.log('App.js: No data-theme attribute found, applying default');

        // Check if user has a saved theme preference
        const savedTheme = localStorage.getItem('theme');
        if (savedTheme) {
            document.documentElement.setAttribute('data-theme', savedTheme);
            Logger.log('App.js: Applied saved theme:', savedTheme);
        } else {
            // Check if user prefers dark mode at system level
            const prefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches;
            const defaultTheme = prefersDark ? 'minewache' : 'minewache-light';
            document.documentElement.setAttribute('data-theme', defaultTheme);
            localStorage.setItem('theme', defaultTheme);
            Logger.log('App.js: Applied default theme based on system preference:', defaultTheme);
        }

        // Add appropriate class to body
        if (document.documentElement.getAttribute('data-theme') === 'minewache-light') {
            document.body.classList.add('light-mode');
        } else {
            document.body.classList.add('dark-mode');
        }
    } else {
        Logger.log('App.js: data-theme already set to:', document.documentElement.getAttribute('data-theme'));
    }
});

// Alpine.js is now loaded by Livewire, so we don't need to load it again
// This prevents the "Detected multiple instances of Alpine running" error
// If window.Alpine is not defined, it means Livewire hasn't loaded Alpine yet
// In that case, we'll load it ourselves
if (!window.Alpine) {
    Logger.log('App.js: Alpine.js not loaded by Livewire, loading it now');
    import('alpinejs').then((module) => {
        window.Alpine = module.default;

        // Log available global functions for debugging
        Logger.debug('Before Alpine start, ticketView available:', typeof window.ticketView);

        window.Alpine.start();

        // Log after initialization
        Logger.debug('After Alpine start, ticketView available:', typeof window.ticketView);
    });
} else {
    Logger.log('App.js: Alpine.js already loaded by Livewire');
    Logger.debug('With existing Alpine, ticketView available:', typeof window.ticketView);
}
