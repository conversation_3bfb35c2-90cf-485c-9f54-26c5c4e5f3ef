<?php

namespace Tests\Unit\Services;

use App\Services\GeminiTagService;
use Illuminate\Support\Facades\Config;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Tests\TestCase; // Or your base test case
use Exception;

class GeminiTagServiceTest extends TestCase
{
    protected function setUp(): void
    {
        parent::setUp();
        // Mock config values
        Config::set('services.gemini.api_key', 'test_api_key');
        Config::set('services.gemini.model_name', 'gemini-1.5-flash-001');
        Log::shouldReceive('debug')->zeroOrMoreTimes(); // Ignore debug logs
        Log::shouldReceive('error')->zeroOrMoreTimes(); // Ignore error logs for controlled tests
    }

    public function test_service_can_be_instantiated()
    {
        $service = new GeminiTagService();
        $this->assertInstanceOf(GeminiTagService::class, $service);
    }

    public function test_generate_tags_for_video_success()
    {
        Http::fake([
            'https://generativelanguage.googleapis.com/v1beta/models/*' => Http::sequence()
                ->push([
                    'candidates' => [
                        [
                            'content' => [
                                'parts' => [
                                    ['text' => json_encode([
                                        'videoSummary' => [
                                            'overallGerman' => 'Test summary.',
                                            'keyVisualElements' => ['visual1'],
                                            'detectedObjects' => ['object1'],
                                            'detectedActions' => ['action1'],
                                            'spokenNamesOrRoles' => ['name1'],
                                            'dialogueThemes' => ['theme1'],
                                        ],
                                        'tiktokHashtags' => ['#test', '#tiktok', '#' . GeminiTagService::MANDATORY_HASHTAG],
                                        'instagramHashtags' => ['#test', '#insta', '#' . GeminiTagService::MANDATORY_HASHTAG],
                                        'categorizedTags' => [
                                            ['category' => 'Test Category', 'tags' => ['tag1', 'tag2']]
                                        ]
                                    ])]
                                ]
                            ]
                        ]
                    ]
                ], 200)
        ]);

        $service = new GeminiTagService();
        $result = $service->generateTagsForVideo('dummybase64data', 'video/mp4');

        $this->assertIsArray($result);
        $this->assertEquals('Test summary.', $result['videoSummary']['overallGerman']);
        $this->assertContains('#' . GeminiTagService::MANDATORY_HASHTAG, $result['tiktokHashtags']);
        $this->assertCount(1, $result['categorizedTags']);
    }

    public function test_generate_tags_handles_api_failure()
    {
        Http::fake([
            'https://generativelanguage.googleapis.com/v1beta/models/*' => Http::response(['error' => ['message' => 'API Error']], 500)
        ]);

        $this->expectException(Exception::class);
        $this->expectExceptionMessage('Gemini API request failed: {"error":{"message":"API Error"}}');

        $service = new GeminiTagService();
        $service->generateTagsForVideo('dummybase64data', 'video/mp4');
    }
    
    public function test_generate_tags_handles_malformed_json_response()
    {
        Http::fake([
            'https://generativelanguage.googleapis.com/v1beta/models/*' => Http::sequence()
                ->push([
                    'candidates' => [
                        [
                            'content' => [
                                'parts' => [
                                    // Malformed JSON - missing closing brace
                                    ['text' => '{"videoSummary": {"overallGerman": "Test summary."']
                                ]
                            ]
                        ]
                    ]
                ], 200)
        ]);

        $this->expectException(Exception::class);
        $this->expectExceptionMessageMatches('/Failed to parse AI response. The response was not valid JSON./');

        $service = new GeminiTagService();
        $service->generateTagsForVideo('dummybase64data', 'video/mp4');
    }

    public function test_constructor_throws_exception_if_api_key_is_missing()
    {
        Config::set('services.gemini.api_key', null);
        $this->expectException(Exception::class);
        $this->expectExceptionMessage('Gemini API key is not configured in services.gemini.api_key');
        new GeminiTagService();
    }
}
