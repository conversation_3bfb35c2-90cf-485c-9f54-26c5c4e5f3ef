@props(['items' => []])

<nav aria-label="Breadcrumb" {{ $attributes->merge(['class' => 'py-2 text-sm']) }}>
    <ol class="flex flex-wrap items-center gap-2">
        <li class="flex items-center">
            <a href="{{ route('home') }}" class="text-base-content/70 hover:text-primary transition-colors flex items-center gap-1">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6" />
                </svg>
                <span class="hidden sm:inline">{{ __('navigation.home') }}</span>
            </a>
        </li>

        @foreach($items as $index => $item)
            <li class="flex items-center">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-3 w-3 text-base-content/50" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
                </svg>
                
                @if(isset($item['route']) && $index < count($items) - 1)
                    <a href="{{ route($item['route'], $item['params'] ?? []) }}" class="ml-1 text-base-content/70 hover:text-primary transition-colors">
                        {{ $item['label'] }}
                    </a>
                @else
                    <span class="ml-1 text-primary font-medium">{{ $item['label'] }}</span>
                @endif
            </li>
        @endforeach
    </ol>
</nav>
