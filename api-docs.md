# Discord Role Monitor Bot

![Bot Status](https://img.shields.io/badge/Status-Production-brightgreen) ![Version](https://img.shields.io/badge/Version-1.0.0-blue)

Ein Discord-Bot, der Rollenänderungen in einem Discord-Server überwacht und diese Informationen an eine Laravel-API sendet, um Benutzerberechtigungen in Echtzeit zu synchronisieren.

## Übersicht

Dieser Bot überwacht Rollenänderungen in einem spezifischen Discord-Server und sendet die Benutzer-ID an eine konfigurierte Laravel-API. Dies ermöglicht eine automatische Berechtigungsverwaltung in Ihrer Anwendung basierend auf Discord-Serverrollen.

## Funktionen

- Überwachung von Rollenänderungen in Echtzeit
- Automatische Benachrichtigung an die API bei Rollenänderungen
- Detaillierte Protokollierung aller Ereignisse
- Automatische API-Verbindungstests beim Start
- Unterstützung für Bearer-Token-Authentifizierung

## Voraussetzungen

- Node.js (v14 oder höher)
- Ein Discord-Bot-Token
- Ein Discord-Server mit Administrator-Berechtigungen
- Eine Laravel-API, die Rollenaktualisierungen verarbeiten kann

## Installation

1. Repository klonen:
   ```bash
   git clone https://github.com/yourusername/discord-role-monitor-bot.git
   cd discord-role-monitor-bot
   ```

2. Abhängigkeiten installieren:
   ```bash
   npm install
   ```

3. Konfigurationsdatei erstellen:
   Kopieren Sie die `.env.example`-Datei zu `.env` und aktualisieren Sie die Werte:
   ```bash
   cp .env.example .env
   ```

4. Konfigurieren Sie die `.env`-Datei mit Ihren Werten:
   ```
   # Discord Bot Configuration
   DISCORD_TOKEN=Ihr_Discord_Bot_Token
   GUILD_ID=Ihre_Discord_Server_ID

   # Laravel API Configuration
   API_ENDPOINT=http://ihre-api-domain.com/api/discord/role-update
   API_KEY=Ihr_API_Schlüssel
   ```

## Verwendung

Starten Sie den Bot mit:

```bash
npm start
```

Für die Entwicklung mit automatischem Neuladen bei Codeänderungen:

```bash
npm run dev
```

## Laravel API-Integration

### API-Endpunkte

#### 1. Rollenaktualisierung

```http
POST /api/discord/role-update
```

Aktualisiert die Berechtigungen eines Benutzers basierend auf seinen Discord-Rollen.

##### Authentifizierung

| Parameter       | Wert                       |
|-----------------|----------------------------|
| Typ             | Bearer Token               |
| Header-Name     | Authorization              |

**Beispiel-Header:**
```http
Authorization: Bearer Ihr_API_Schlüssel
Content-Type: application/json
```

##### Anforderungsstruktur

```json
{
  "user_id": "string (Discord-Benutzer-ID)"
}
```

##### Erfolgreiche Antwort (200 OK)

```json
{
  "success": true,
  "status": "success",
  "message": "Berechtigungen für Benutzer USERNAME aktualisiert",
  "data": {
    "username": "USERNAME",
    "roles_count": 3,
    "permissions_bitmask": 4294967295,
    "updated_at": "2025-03-23T19:40:15+00:00"
  },
  "timestamp": "2025-03-23T19:40:15+00:00"
}
```

##### Fehlerantworten

**Benutzer nicht gefunden (404)**
```json
{
  "success": false,
  "status": "error",
  "error_code": "user_not_found",
  "message": "User mit Discord-ID DISCORD_ID nicht gefunden",
  "timestamp": "2025-03-23T19:40:15+00:00"
}
```

**Discord API-Fehler (502)**
```json
{
  "success": false,
  "status": "error",
  "error_code": "discord_api_error",
  "message": "Discord API Fehler: FEHLERMELDUNG",
  "timestamp": "2025-03-23T19:40:15+00:00"
}
```

#### 2. Gesundheitscheck

```http
GET /api/ping
```

Einfacher Endpunkt zur Überprüfung, ob die API erreichbar ist.

##### Antwort

```json
{
  "status": "ok",
  "timestamp": "2025-03-23T19:40:15+00:00"
}
```

#### 3. Authentifizierungstest

```http
GET /api/auth-test
```

Überprüft, ob der API-Schlüssel gültig ist.

##### Authentifizierung

Bearer Token (wie oben beschrieben)

##### Antwort

```json
{
  "success": true,
  "status": "success",
  "message": "API token is valid",
  "data": {
    "token_type": "Bearer",
    "client_ip": "127.0.0.1",
    "server_time": "2025-03-23T19:40:15+00:00"
  },
  "timestamp": "2025-03-23T19:40:15+00:00"
}
```

#### 4. Rollen-Mapping-Debugging

```http
GET /api/debug/role-mapping
```

Liefert Informationen zur Zuordnung von Discord-Rollen zu Anwendungsrollen.

##### Authentifizierung

Bearer Token (wie oben beschrieben)

##### Antwort

```json
{
  "success": true,
  "status": "success",
  "message": "Role mapping information",
  "data": {
    "role_mappings": {
      "1071024925437067294": "MINEWACHE_TEAM",
      "1183491860304494642": "SCHAUSPIELER",
      "1033491844715257866": "BUILDER",
      "...": "..."
    },
    "guild_id": "1335685796979671111",
    "roles": {
      "MINEWACHE_TEAM": {
        "name": "MINEWACHE_TEAM",
        "label": "Minewache-Team",
        "value": 4294967295,
        "binary": "11111111111111111111111111111111"
      },
      "...": "..."
    }
  },
  "timestamp": "2025-03-23T19:40:15+00:00"
}
```

### Berechtigungssystem

Das Laravel-Backend verwendet ein bitmaskiertes Berechtigungssystem:

1. **Discord-Rollen zu Anwendungsrollen**: Jede Discord-Rolle wird einer Anwendungsrolle zugeordnet (z.B. MINEWACHE_TEAM, SCHAUSPIELER, BUILDER).

2. **Anwendungsrollen zu Bitmasks**: Jede Anwendungsrolle wird als Bit in einer Bitmaske repräsentiert.

3. **Berechtigungsprüfung**: Die Anwendung verwendet Gates und Policies, um Berechtigungen anhand der Bitmasken zu überprüfen.

### Verarbeitungsablauf

```mermaid
sequenceDiagram
    participant Discord Bot
    participant Laravel API
    participant Discord API
    participant Datenbank

    Discord Bot->>Laravel API: POST /api/discord/role-update mit user_id
    Note over Laravel API: Middleware prüft API-Token
    Laravel API->>Datenbank: Suche Benutzer nach Discord-ID
    
    alt Benutzer nicht gefunden
        Laravel API-->>Discord Bot: 404 Benutzer nicht gefunden
    else Benutzer gefunden
        Laravel API->>Discord API: GET /guilds/{guild_id}/members/{user_id}
        Discord API-->>Laravel API: Benutzerrollen
        
        Note over Laravel API: Berechne Berechtigungsbitmask
        
        Laravel API->>Datenbank: Aktualisiere Berechtigungen
        Datenbank-->>Laravel API: Bestätigung
        Laravel API-->>Discord Bot: 200 OK mit aktualisiertem Benutzer
    end
```

### Discord → Bot Kommunikation

### Bot-Ereignisbehandlung

Der Bot überwacht das `GuildMemberUpdate`-Ereignis von Discord, welches ausgelöst wird, wenn sich die Rollen oder andere Eigenschaften eines Servermitglieds ändern:

```javascript
client.on(Events.GuildMemberUpdate, async (oldMember, newMember) => {
  // Nur Ereignisse vom konfigurierten Server verarbeiten
  if (newMember.guild.id !== GUILD_ID) return;
  
  // Prüfen, ob sich die Rollen geändert haben
  if (oldMember.roles.cache.size !== newMember.roles.cache.size ||
      !oldMember.roles.cache.every(role => newMember.roles.cache.has(role.id))) {
      
    // Rollenänderung erkannt - API benachrichtigen
    // ...
  }
});
```

### Verarbeitungsablauf

```mermaid
%%{init: {
  'theme': 'base',
  'themeVariables': {
    'primaryColor': '#6e7fff',
    'primaryTextColor': '#fcfcff',
    'primaryBorderColor': '#6e7fff',
    'secondaryColor': '#c27a00',
    'secondaryTextColor': '#fdfbef',
    'secondaryBorderColor': '#c27a00',
    'tertiaryColor': '#232142',
    'tertiaryTextColor': '#ffffff',
    'noteBackgroundColor': '#232142',
    'noteTextColor': '#ffffff'
  }
}%%
sequenceDiagram
    participant Discord as Discord Server
    participant Bot as Discord Bot
    participant API as Laravel API
    participant DiscordAPI as Discord API
    participant DB as Datenbank
    
    %% Bot Startup Sequence
    Note over Bot: Bot startet
    Bot->>Bot: Konfiguration laden
    Bot->>Discord: Verbinden und authentifizieren
    Discord-->>Bot: Bestätigung & Zugriff gewähren
    Bot->>API: GET /api/ping
    API-->>Bot: 200 OK (Healthcheck)
    Bot->>API: GET /api/auth-test
    Note over API: Token-Validierung
    API-->>Bot: 200 OK (Token gültig)
    Bot->>API: GET /api/debug/role-mapping
    API-->>Bot: 200 OK (Rollenzuordnungen)
    Note over Bot: Bot ist einsatzbereit
    
    %% Role Change Detection
    Discord->>Bot: GuildMemberUpdate Event
    Note over Bot: Prüfen auf Rollenänderungen
    
    Bot->>Bot: Vergleiche alte und neue Rollen
    Note over Bot: Erkenne hinzugefügte/entfernte Rollen
    
    %% API Request for Role Update
    Bot->>API: POST /api/discord/role-update mit user_id
    Note over API: Middleware prüft API-Token
    API->>DB: Suche Benutzer nach Discord-ID
    
    alt Benutzer nicht gefunden
        API-->>Bot: 404 Benutzer nicht gefunden
        Note over Bot: Fehler protokollieren
    else Benutzer gefunden
        API->>DiscordAPI: GET /guilds/{guild_id}/members/{user_id}
        DiscordAPI-->>API: Benutzerrollen
        
        Note over API: Berechne Berechtigungsbitmask
        
        API->>DB: Aktualisiere Berechtigungen
        DB-->>API: Bestätigung
        API-->>Bot: 200 OK mit aktualisiertem Benutzer
        Note over Bot: Erfolgreiche Aktualisierung protokollieren
    end
    
    %% Error Handling
    alt API-Fehler
        API-->>Bot: 4xx/5xx Fehlerantwort
        Note over Bot: Detaillierte Fehlerprotokollierung<br/>mit Fehlercode und Nachricht
    end
```

### Bot-Startsequenz

Der Bot führt beim Start automatisch folgende Aktionen aus:

1. **API-Konnektivitätstests**:
   ```javascript
   async function testApiEndpoints() {
     // Extract the base URL (up to the last slash in the API_ENDPOINT)
     const baseUrl = API_ENDPOINT.substring(0, API_ENDPOINT.lastIndexOf('/'));
     // Get the base API URL by going up one level
     const apiBaseUrl = baseUrl.substring(0, baseUrl.lastIndexOf('/'));
     
     // Test ping endpoint
     try {
       const pingResponse = await axios.get(`${apiBaseUrl}/ping`);
       logWithTimestamp(`Ping endpoint status: ${pingResponse.status}`);
     } catch (error) {
       logWithTimestamp(`Error testing ping endpoint: ${error.message}`, 'error');
     }

     // Test auth and debug endpoints...
   }
   ```

2. **Discord-Ereignisüberwachung initialisieren**:
   ```javascript
   client.once(Events.ClientReady, readyClient => {
     console.log(`Ready! Logged in as ${readyClient.user.tag}`);
     console.log(`Monitoring role changes in guild: ${guild.name}`);
     testApiEndpoints(); // API-Endpunkte testen
   });
   ```

3. **Detaillierte Fehlerprotokollierung**:
   ```javascript
   function logWithTimestamp(message, level = 'info') {
     const timestamp = new Date().toISOString();
     const prefix = level === 'error' ? '❌ ERROR:' : 
                    level === 'debug' ? '🔍 DEBUG:' : '📝 INFO:';
     console.log(`[${timestamp}] ${prefix} ${message}`);
   }
   ```

### API-Anfrage bei Rollenänderungen

Bei einer erkannten Rollenänderung sendet der Bot folgende Anfrage an die Laravel-API:

```javascript
try {
  // Sende die Benutzer-ID an die Laravel API mit Bearer-Token-Authentifizierung
  const response = await axios.post(API_ENDPOINT, {
    user_id: newMember.user.id
  }, {
    headers: {
      'Authorization': `Bearer ${API_KEY}`,
      'Content-Type': 'application/json'
    }
  });
  
  logWithTimestamp(`API request successful. Status code: ${response.status}`);
  logWithTimestamp(`API response: ${JSON.stringify(response.data)}`);
  
} catch (error) {
  logWithTimestamp(`Error sending data to API: ${error.message}`, 'error');
  
  if (error.response) {
    logWithTimestamp(`API error status: ${error.response.status}`, 'error');
    logWithTimestamp(`API response: ${JSON.stringify(error.response.data)}`, 'error');
  }
}
```

### Fehlerbehandlung

Das Laravel-Backend implementiert eine umfassende Fehlerbehandlung:

1. **Standardisierte Fehlerformate**: Alle Fehlerantworten folgen einem konsistenten Format mit `success`, `status`, `error_code` und `message`.

2. **Fehlertypen**:
   - Validierungsfehler (422)
   - Authentifizierungsfehler (401)
   - Nicht gefunden (404)
   - Discord API-Fehler (502)
   - Serverfehler (500)

3. **Logging**: Alle Fehler werden in den Laravel-Logs mit detaillierten Informationen protokolliert.


