@props(['ticket', 'isSupporter'])

<x-modern-card variant="elevated" size="sm">
    <div class="flex flex-col md:flex-row justify-between items-start md:items-center gap-4">
        {{-- Ticket Title and Basic Info --}}
        <div class="flex-1 min-w-0">
            <div class="flex items-center gap-3">
                <img
                    src="{{ $ticket->user->getAvatar(['extension' => 'webp', 'size' => 40]) }}"
                    class="w-10 h-10 rounded-full border-2 border-slate-700"
                    alt="{{ $ticket->user->username }}"
                />
                <div>
                    <h1 class="text-lg sm:text-xl md:text-2xl font-bold text-base-content truncate">
                        {{ $ticket->title }}
                    </h1>
                    <p class="text-xs sm:text-sm text-base-content/60 flex flex-wrap items-center gap-x-2 gap-y-1">
                        <span>#{{ $ticket->id }}</span>
                        <span class="text-base-content/30">•</span>
                        <span>{{ $ticket->user->username }}</span>
                    </p>
                </div>
            </div>
        </div>

        {{-- Placeholder for future action buttons --}}
        @if($isSupporter)
            <div class="flex items-center space-x-2">
                {{-- e.g. Close/Reopen/Assign buttons can go here --}}
            </div>
        @endif
    </div>
</x-modern-card>
