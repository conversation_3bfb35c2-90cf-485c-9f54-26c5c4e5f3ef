@echo off
setlocal enabledelayedexpansion
echo Stoppe Reverb-Server...

REM Finde die PID des Prozesses, der auf Port 6001 hört
for /f "tokens=5" %%a in ('netstat -ano ^| findstr :6001') do (
    set pid=%%a
    if not "!pid!"=="0" (
        echo Gefundener Prozess mit PID: !pid!
        taskkill /F /PID !pid!
        echo Prozess mit PID !pid! beendet.
    )
)

echo Reverb-Server wurde gestoppt.
endlocal
