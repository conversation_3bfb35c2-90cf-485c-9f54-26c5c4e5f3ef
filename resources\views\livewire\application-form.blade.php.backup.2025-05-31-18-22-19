<div class="card bg-base-200 shadow-xl w-full max-w-4xl mx-auto">
    <div class="card-body">
        <!-- Progress Indicator -->
        <div class="w-full mb-8">
            <div class="flex flex-col space-y-2">
                <div class="flex justify-between text-xs text-base-content/70">
                    <span>{{ __('application.step_of', ['current' => 1, 'total' => 3]) }}</span>
                    <span>{{ __('application.completed', ['percent' => 33]) }}</span>
                </div>
                <ul class="steps steps-horizontal w-full">
                    <li class="step step-primary font-medium">{{ __('application.personal_data') }}</li>
                    <li class="step font-medium">{{ __('application.role_specific') }}</li>
                    <li class="step font-medium">{{ __('application.about_you') }}</li>
                </ul>
            </div>
            <div class="mt-6 px-2">
                <p class="text-sm text-base-content/80">
                    {{ __('application.first_step_description') }}
                </p>

                <!-- GDPR Data Notice -->
                <div class="mt-4">
                    <x-gdpr-data-notice type="application" />
                </div>
            </div>
        </div>

        <form action="{{ route('questions') }}" method="get" class="space-y-6">
            @csrf

            <!-- Error Alert -->
            @if ($errors->any())
                <div class="alert alert-error">
                    <x-heroicon-o-exclamation-circle class="h-6 w-6 shrink-0 stroke-current" />
                    <span>{{ __('messages.please_correct_errors') }}</span>
                </div>
            @endif

            <!-- Name Field -->
            <div class="form-control w-full">
                <label class="label">
                    <span class="label-text font-medium">{{ __('application.name') }} <span class="text-error">*</span></span>
                    <span class="label-text-alt opacity-60">{{ __('application.name_help') }}</span>
                </label>
                <div class="input-group">
                    <span class="btn btn-square btn-ghost">
                        <x-heroicon-o-user class="h-5 w-5" />
                    </span>
                    <input type="text" name="name" value="{{ old('name') }}" required
                           placeholder="{{ __('application.name_placeholder') }}"
                           class="input input-bordered w-full @error('name') input-error @enderror focus:border-primary" />
                </div>
                @error('name') <span class="label-text-alt text-error mt-1">{{ $message }}</span> @enderror
                <div class="mt-1.5 text-xs text-base-content/70">
                    {{ __('application.name_description') }}
                </div>
            </div>

            <!-- Professions Field -->
            <style>
                .tooltip-wrapper {
                    position: relative;
                }
                .custom-tooltip {
                    visibility: hidden;
                    position: absolute;
                    bottom: 125%;
                    left: 50%;
                    transform: translateX(-50%);
                    background-color: var(--tw-color-primary-focus, #1d4ed8);
                    color: white;
                    text-align: center;
                    padding: 8px 12px;
                    border-radius: 6px;
                    width: max-content;
                    max-width: 200px;
                    font-size: 0.875rem;
                    z-index: 1;
                    opacity: 0;
                    transition: opacity 0.3s;
                }
                .custom-tooltip::after {
                    content: "";
                    position: absolute;
                    top: 100%;
                    left: 50%;
                    margin-left: -5px;
                    border-width: 5px;
                    border-style: solid;
                    border-color: var(--tw-color-primary-focus, #1d4ed8) transparent transparent transparent;
                }
                .tooltip-trigger:hover + .custom-tooltip {
                    visibility: visible;
                    opacity: 1;
                }
            </style>

            <div class="form-control w-full">
                <label class="label">
                    <span class="label-text font-medium">{{ __('application.professions') }} <span class="text-error">*</span></span>
                    <span class="label-text-alt opacity-60">{{ __('application.professions_help') }}</span>
                </label>
                <div class="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-3">
                    @foreach($professionOptions as $value => $label)
                        <div wire:key="prof-{{ $value }}" class="relative tooltip-wrapper">
                            <input type="checkbox" id="prof-{{ $value }}"
                                   value="{{ $value }}"
                                   name="professions[]"
                                   class="peer hidden">
                            <label for="prof-{{ $value }}"
                                   class="flex flex-col items-center justify-center h-20 sm:h-24 p-2 border-2 rounded-lg cursor-pointer
                              transition-all duration-300 ease-in-out transform
                              hover:scale-105 hover:shadow-md
                              peer-checked:border-primary peer-checked:bg-primary/10
                              peer-checked:scale-[1.02] peer-checked:shadow-lg
                              border-base-300 tooltip-trigger">
                                <!-- Icon based on profession with animation -->
                                <div class="w-8 h-8 flex items-center justify-center transition-transform duration-300 peer-checked:scale-110">
                                    @if($value == 'actor')
                                        <x-heroicon-o-face-smile class="h-6 w-6" />
                                    @elseif($value == 'actor_no_voice')
                                        <x-heroicon-o-speaker-x-mark class="h-6 w-6" />
                                    @elseif($value == 'voice_actor')
                                        <x-heroicon-o-microphone class="h-6 w-6" />
                                    @elseif($value == 'builder')
                                        <x-heroicon-o-wrench-screwdriver class="h-6 w-6" />
                                    @elseif($value == 'designer')
                                        <x-heroicon-o-paint-brush class="h-6 w-6" />
                                    @elseif($value == 'cutter')
                                        <x-heroicon-o-scissors class="h-6 w-6" />
                                    @elseif($value == 'cameraman')
                                        <x-heroicon-o-camera class="h-6 w-6" />
                                    @elseif($value == 'developer')
                                        <x-heroicon-o-code-bracket class="h-6 w-6" />
                                    @elseif($value == 'modeler')
                                        <x-heroicon-o-cube class="h-6 w-6" />
                                    @elseif($value == 'music_producer')
                                        <x-heroicon-o-musical-note class="h-6 w-6" />
                                    @else
                                        <x-heroicon-o-ellipsis-horizontal-circle class="h-6 w-6" />
                                    @endif
                                </div>
                                <span class="mt-2 text-center text-sm transition-all duration-300 peer-checked:font-semibold">{{ $label }}</span>

                                <!-- Improved selection indicator with animation -->
                                <div class="absolute top-1 right-1 w-5 h-5 bg-primary rounded-full flex items-center justify-center
                                opacity-0 scale-0 peer-checked:opacity-100 peer-checked:scale-100
                                transition-all duration-300 ease-in-out">
                                    <x-heroicon-s-check class="h-3 w-3 text-white" />
                                </div>
                            </label>

                            <!-- Simple CSS Tooltip -->
                            <div class="custom-tooltip">
                                {{ __('application.profession_description.' . $value) }}
                            </div>
                        </div>
                    @endforeach
                </div>
                @error('professions') <span class="label-text-alt text-error mt-1">{{ $message }}</span> @enderror
            </div>

            <!-- Other Profession Field - Dynamic display with Livewire -->
            <div class="form-control w-full"
                 x-data="{ showOtherProfession: {{ in_array('other', old('professions', [])) ? 'true' : 'false' }} }"
                 x-show="showOtherProfession"
                 x-transition:enter="transition ease-out duration-300"
                 x-transition:enter-start="opacity-0 transform -translate-y-4"
                 x-transition:enter-end="opacity-100 transform translate-y-0"
                 x-transition:leave="transition ease-in duration-150"
                 x-transition:leave-start="opacity-100 transform translate-y-0"
                 x-transition:leave-end="opacity-0 transform -translate-y-4">
                <label class="label">
                    <span class="label-text font-medium">{{ __('application.other_profession') }}</span>
                    <span class="label-text-alt text-primary opacity-70">{{ __('application.other_profession_help') }}</span>
                </label>
                <div class="input-group">
                    <span class="btn btn-square btn-ghost">
                        <x-heroicon-o-pencil class="h-5 w-5" />
                    </span>
                    <input type="text" name="otherProfession" value="{{ old('otherProfession') }}"
                           placeholder="{{ __('application.other_profession_placeholder') }}" class="input input-bordered w-full @error('otherProfession') input-error @enderror" />
                </div>
                @error('otherProfession') <span class="label-text-alt text-error mt-1">{{ $message }}</span> @enderror
            </div>

            <!-- Add Hidden script to handle Alpine.js Other Profession toggle -->
            <script>
                document.addEventListener('DOMContentLoaded', function() {
                    const otherCheckbox = document.querySelector('#prof-other');
                    if (otherCheckbox) {
                        otherCheckbox.addEventListener('change', function() {
                            const showOtherProfession = this.checked;
                            document.dispatchEvent(new CustomEvent('update-other-profession', {
                                detail: { show: showOtherProfession }
                            }));
                        });
                    }
                });

                document.addEventListener('update-other-profession', function(e) {
                    window.dispatchEvent(new CustomEvent('update-other-profession-state', {
                        detail: { show: e.detail.show }
                    }));
                });
            </script>

            <!-- Personal Info -->
            <div class="mt-8">
                <h3 class="text-lg font-medium mb-4 pb-2 border-b border-base-300">{{ __('application.personal_data') }}</h3>
                <div class="grid md:grid-cols-3 gap-4">
                    <!-- Age Field -->
                    <div class="form-control w-full">
                        <label class="label">
                            <span class="label-text font-medium">{{ __('application.age') }}</span>
                        </label>
                        <div class="input-group">
                            <span class="btn btn-square btn-ghost">
                                <x-heroicon-o-cake class="h-5 w-5" />
                            </span>
                            <input type="number" name="age" value="{{ old('age') }}" min="0" max="120" required
                                   placeholder="{{ __('application.age_placeholder') }}" class="input input-bordered w-full @error('age') input-error @enderror" />
                        </div>
                        @error('age') <span class="label-text-alt text-error mt-1">{{ $message }}</span> @enderror
                        <span class="label-text-alt mt-1 text-base-content/70">{{ __('application.age_help') }}</span>
                    </div>

                    <!-- Gender Field -->
                    <div class="form-control w-full">
                        <label class="label">
                            <span class="label-text font-medium">{{ __('application.gender') }}</span>
                        </label>
                        <div class="input-group">
                            <span class="btn btn-square btn-ghost">
                                <x-heroicon-o-user-group class="h-5 w-5" />
                            </span>
                            <select name="gender" class="select select-bordered w-full @error('gender') select-error @enderror">
                                <option value="" disabled {{ old('gender') ? '' : 'selected' }}>{{ __('application.gender_select') }}</option>
                                <option value="no-info" {{ old('gender') == 'no-info' ? 'selected' : '' }}>{{ __('application.gender_no_info') }}</option>
                                <option value="male" {{ old('gender') == 'male' ? 'selected' : '' }}>{{ __('application.gender_male') }}</option>
                                <option value="female" {{ old('gender') == 'female' ? 'selected' : '' }}>{{ __('application.gender_female') }}</option>
                                <option value="diverse" {{ old('gender') == 'diverse' ? 'selected' : '' }}>{{ __('application.gender_diverse') }}</option>
                                <option value="other" {{ old('gender') == 'other' ? 'selected' : '' }}>{{ __('application.gender_other') }}</option>
                            </select>
                        </div>
                        @error('gender') <span class="label-text-alt text-error mt-1">{{ $message }}</span> @enderror
                    </div>

                    <!-- Pronouns Field -->
                    <div class="form-control w-full">
                        <label class="label">
                            <span class="label-text font-medium">{{ __('application.pronouns') }}</span>
                            <span class="label-text-alt opacity-60">({{ __('messages.optional_field') }})</span>
                        </label>
                        <div class="input-group">
                            <span class="btn btn-square btn-ghost">
                                <x-heroicon-o-chat-bubble-bottom-center-text class="h-5 w-5" />
                            </span>
                            <input type="text" name="pronouns" value="{{ old('pronouns') }}"
                                   placeholder="{{ __('application.pronouns_placeholder') }}" class="input input-bordered w-full @error('pronouns') input-error @enderror" />
                        </div>
                        @error('pronouns') <span class="label-text-alt text-error mt-1">{{ $message }}</span> @enderror
                    </div>
                </div>
            </div>

            <!-- Availability Confirmation -->
            <div class="mt-8 p-4 bg-base-300/50 rounded-lg border border-base-300">
                <div class="form-control">
                    <div class="flex items-start space-x-4">
                        <input type="checkbox" name="confirmation" {{ old('confirmation') ? 'checked' : '' }}
                        class="checkbox checkbox-primary mt-1 @error('confirmation') checkbox-error @enderror" />
                        <div class="space-y-2">
                            <label class="label-text font-medium cursor-pointer">
                                {{ __('application.confirm_availability') }}
                            </label>
                            <div class="flex items-start space-x-2">
                                <x-heroicon-o-clock class="h-5 w-5 shrink-0 mt-0.5 text-primary" />
                                <p class="text-sm text-base-content/80">
                                    {{ __('application.availability_confirmation_text') }}
                                </p>
                            </div>
                        </div>
                    </div>
                    @error('confirmation') <span class="label-text-alt text-error mt-1 ml-8">{{ $message }}</span> @enderror
                </div>
            </div>

            <div class="card-actions flex flex-col md:flex-row md:justify-between items-center gap-4 mt-8">
                <div class="text-sm opacity-75">
                    <div class="flex items-center gap-2">
                        <x-heroicon-o-information-circle class="h-5 w-5 text-primary" />
                        <span>{{ __('application.required_fields_notice') }}</span>
                    </div>
                </div>

                <div class="flex items-center gap-3">
                    <a href="{{ route('home') }}" class="btn btn-ghost">
                        {{ __('messages.cancel') }}
                    </a>
                    <button type="submit" class="btn btn-primary btn-lg gap-2 min-w-[200px] relative group">
                        <span class="hidden group-hover:inline-block absolute -left-2 -translate-x-full">
                            <x-heroicon-s-sparkles class="h-5 w-5 text-primary-content animate-pulse" />
                        </span>
                        {{ __('application.next_step') }}
                        <x-heroicon-o-arrow-right class="h-5 w-5" />
                    </button>
                </div>
            </div>
        </form>
    </div>
</div>
