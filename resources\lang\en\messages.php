<?php

return [
    // General
    'welcome' => 'Welcome to the Minewache website.',
    'welcome_description' => 'Explore the Minecraft police series with exciting chases, criminal cases, and role-playing.',
    'skip_to_content' => 'Skip to content',
    'all_rights_reserved' => 'All rights reserved.',
    'minewache_title' => 'Minewache',
    'minewache_mods' => 'Mods',
    'minewache_heading' => 'The Minewache',
    'join_team_description' => 'Want to join our team? We’re looking for new talents for our Minecraft police series.',
    'apply_now' => 'Apply now',
    'series_title' => 'Minewache - Minecraft Police Series',
    'feature_1_title' => 'Police Action',
    'feature_1_description' => 'Take part in our next episode and support us by joining our Team.',
    'feature_2_title' => 'Youtube',
    'feature_2_description' => 'Watch Our Episodes right here on our website.',
    'feature_3_title' => 'Discord',
    'feature_3_description' => 'Join our Discord server And chat with our communtiy and us, or just leave some feedback.',
    'view_all_episodes' => 'View episodes',

    // Buttons & Actions
    'submit' => 'Submit',
    'save' => 'Save',
    'cancel' => 'Cancel',
    'back' => 'Back',
    'next' => 'Next',
    'continue' => 'Continue',
    'learn_more' => 'Learn more',
    'show_more' => 'Show more',
    'show_less' => 'Show less',

    // Errors & Notifications
    'error' => 'Error',
    'success' => 'Success',
    'warning' => 'Warning',
    'info' => 'Info',
    'please_correct_errors' => 'Fix the errors below.',
    'required_field' => 'This field is required.',
    'optional_field' => 'Optional',
    'please_try_again_later' => 'Something went wrong. Try again later.',

    // Time & Date
    'today' => 'Today',
    'yesterday' => 'Yesterday',
    'days_ago' => ':days days ago',
    'months_ago' => ':months months ago',
    'years_ago' => ':years years ago',

    // Footer
    'footer_description' => 'Minewache is a Minecraft police series with chases, cases, and role-playing.',
    'quick_links' => 'Quick Links',
    'legal' => 'Legal',
    'follow_us' => 'Follow us',
    'contact_us' => 'Contact us',
];
