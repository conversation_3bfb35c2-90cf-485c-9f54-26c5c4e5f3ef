<?php

namespace Database\Factories;

use App\Models\Ticket;
use App\Models\User;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\TicketMessage>
 */
class TicketMessageFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'ticket_id' => Ticket::factory(),
            'user_id' => User::factory(),
            'message' => fake()->paragraph(),
            'is_from_discord' => fake()->boolean(20),
            'discord_message_id' => function (array $attributes) {
                return $attributes['is_from_discord'] ? fake()->numerify('##########') : null;
            },
            'created_at' => fake()->dateTimeBetween('-1 month', 'now'),
            'updated_at' => function (array $attributes) {
                return fake()->dateTimeBetween($attributes['created_at'], 'now');
            },
        ];
    }

    /**
     * Indicate that the message is from Discord.
     */
    public function fromDiscord(): static
    {
        return $this->state(fn (array $attributes) => [
            'is_from_discord' => true,
            'discord_message_id' => fake()->numerify('##########'),
        ]);
    }

    /**
     * Indicate that the message is from the web app.
     */
    public function fromWeb(): static
    {
        return $this->state(fn (array $attributes) => [
            'is_from_discord' => false,
            'discord_message_id' => null,
        ]);
    }
}
