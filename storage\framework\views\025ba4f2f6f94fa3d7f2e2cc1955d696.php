<div>
    <div class="bg-gradient-modern min-h-screen py-8 px-4">
        <?php if (isset($component)) { $__componentOriginalfc34df8c041cfce1b125f68563b94330 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginalfc34df8c041cfce1b125f68563b94330 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.modern-card','data' => ['variant' => 'elevated','size' => 'md']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('modern-card'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['variant' => 'elevated','size' => 'md']); ?>
            <div class="flex flex-col md:flex-row justify-between items-start md:items-center mb-6">
                <h1 class="text-2xl font-bold text-base-content mb-4 md:mb-0">
                    <?php echo e($isSupporter ? __('tickets.all_tickets') : __('tickets.my_tickets')); ?>

                </h1>
                <div class="flex flex-col sm:flex-row gap-3">
                    <?php if (isset($component)) { $__componentOriginal19987cd45c699eefead8b7a8ac222798 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal19987cd45c699eefead8b7a8ac222798 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.modern-button','data' => ['variant' => 'primary','size' => 'md','href' => ''.e(route('tickets.create')).'','icon' => '<svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path></svg>','iconPosition' => 'left']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('modern-button'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['variant' => 'primary','size' => 'md','href' => ''.e(route('tickets.create')).'','icon' => '<svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path></svg>','iconPosition' => 'left']); ?>
                        <?php echo e(__('tickets.new_ticket')); ?>

                     <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal19987cd45c699eefead8b7a8ac222798)): ?>
<?php $attributes = $__attributesOriginal19987cd45c699eefead8b7a8ac222798; ?>
<?php unset($__attributesOriginal19987cd45c699eefead8b7a8ac222798); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal19987cd45c699eefead8b7a8ac222798)): ?>
<?php $component = $__componentOriginal19987cd45c699eefead8b7a8ac222798; ?>
<?php unset($__componentOriginal19987cd45c699eefead8b7a8ac222798); ?>
<?php endif; ?>
                </div>
            </div>

            <!-- Filters -->
            <?php if (isset($component)) { $__componentOriginalfc34df8c041cfce1b125f68563b94330 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginalfc34df8c041cfce1b125f68563b94330 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.modern-card','data' => ['variant' => 'outlined','size' => 'sm','class' => 'mb-6']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('modern-card'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['variant' => 'outlined','size' => 'sm','class' => 'mb-6']); ?>
                <div class="flex flex-col md:flex-row gap-4">
                    <div class="flex-1">
                        <?php if (isset($component)) { $__componentOriginal17d23b099a28c4e2c5f6c448deb256e5 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal17d23b099a28c4e2c5f6c448deb256e5 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.modern-input','data' => ['wire:model.live.debounce.300ms' => 'search','type' => 'text','id' => 'search','label' => ''.e(__('admin.search')).'','placeholder' => ''.e(__('admin.search')).'...','variant' => 'glass','icon' => '<svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path></svg>']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('modern-input'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['wire:model.live.debounce.300ms' => 'search','type' => 'text','id' => 'search','label' => ''.e(__('admin.search')).'','placeholder' => ''.e(__('admin.search')).'...','variant' => 'glass','icon' => '<svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path></svg>']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal17d23b099a28c4e2c5f6c448deb256e5)): ?>
<?php $attributes = $__attributesOriginal17d23b099a28c4e2c5f6c448deb256e5; ?>
<?php unset($__attributesOriginal17d23b099a28c4e2c5f6c448deb256e5); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal17d23b099a28c4e2c5f6c448deb256e5)): ?>
<?php $component = $__componentOriginal17d23b099a28c4e2c5f6c448deb256e5; ?>
<?php unset($__componentOriginal17d23b099a28c4e2c5f6c448deb256e5); ?>
<?php endif; ?>
                    </div>

                    <div class="w-full md:w-48">
                        <label for="status" class="block text-sm font-medium text-base-content/70 mb-1"><?php echo e(__('tickets.status')); ?></label>
                        <select wire:model.live="status" id="status" class="w-full bg-base-100 border border-base-300 rounded-xl px-4 py-2 focus:outline-none focus:ring-2 focus:ring-primary focus:border-primary transition-all duration-300">
                            <option value=""><?php echo e(__('admin.all_statuses')); ?></option>
                            <option value="open"><?php echo e(__('tickets.status_open')); ?></option>
                            <option value="in_progress"><?php echo e(__('tickets.status_in_progress')); ?></option>
                            <option value="closed"><?php echo e(__('tickets.status_closed')); ?></option>
                        </select>
                    </div>

                    <!--[if BLOCK]><![endif]--><?php if($isSupporter): ?>
                        <div class="w-full md:w-48">
                            <label for="assignedTo" class="block text-sm font-medium text-base-content/70 mb-1"><?php echo e(__('tickets.assigned_to')); ?></label>
                            <select wire:model.live="assignedTo" id="assignedTo" class="w-full bg-base-100 border border-base-300 rounded-xl px-4 py-2 focus:outline-none focus:ring-2 focus:ring-primary focus:border-primary transition-all duration-300">
                                <option value=""><?php echo e(__('admin.all')); ?></option>
                                <option value="unassigned"><?php echo e(__('tickets.unassigned')); ?></option>
                                <!--[if BLOCK]><![endif]--><?php $__currentLoopData = $supporters; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $id => $name): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <option value="<?php echo e($id); ?>"><?php echo e($name); ?></option>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?><!--[if ENDBLOCK]><![endif]-->
                            </select>
                        </div>
                    <?php endif; ?><!--[if ENDBLOCK]><![endif]-->

                    <div class="flex items-end">
                        <?php if (isset($component)) { $__componentOriginal19987cd45c699eefead8b7a8ac222798 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal19987cd45c699eefead8b7a8ac222798 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.modern-button','data' => ['wire:click' => 'resetFilters','variant' => 'ghost','size' => 'md','icon' => '<svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path></svg>']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('modern-button'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['wire:click' => 'resetFilters','variant' => 'ghost','size' => 'md','icon' => '<svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path></svg>']); ?>
                            <?php echo e(__('admin.reset_filters')); ?>

                         <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal19987cd45c699eefead8b7a8ac222798)): ?>
<?php $attributes = $__attributesOriginal19987cd45c699eefead8b7a8ac222798; ?>
<?php unset($__attributesOriginal19987cd45c699eefead8b7a8ac222798); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal19987cd45c699eefead8b7a8ac222798)): ?>
<?php $component = $__componentOriginal19987cd45c699eefead8b7a8ac222798; ?>
<?php unset($__componentOriginal19987cd45c699eefead8b7a8ac222798); ?>
<?php endif; ?>
                    </div>
                </div>
             <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginalfc34df8c041cfce1b125f68563b94330)): ?>
<?php $attributes = $__attributesOriginalfc34df8c041cfce1b125f68563b94330; ?>
<?php unset($__attributesOriginalfc34df8c041cfce1b125f68563b94330); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalfc34df8c041cfce1b125f68563b94330)): ?>
<?php $component = $__componentOriginalfc34df8c041cfce1b125f68563b94330; ?>
<?php unset($__componentOriginalfc34df8c041cfce1b125f68563b94330); ?>
<?php endif; ?>

            <!-- Tickets List -->
            <!--[if BLOCK]><![endif]--><?php if($tickets->isEmpty()): ?>
                <?php if (isset($component)) { $__componentOriginalfc34df8c041cfce1b125f68563b94330 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginalfc34df8c041cfce1b125f68563b94330 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.modern-card','data' => ['variant' => 'outlined','size' => 'lg','class' => 'text-center']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('modern-card'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['variant' => 'outlined','size' => 'lg','class' => 'text-center']); ?>
                    <svg class="mx-auto h-12 w-12 text-base-content/40 mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                    </svg>
                    <h3 class="text-lg font-semibold text-base-content mb-2"><?php echo e(__('tickets.no_tickets')); ?></h3>
                    <p class="text-base-content/70 mb-6"><?php echo e(__('tickets.no_tickets_description')); ?></p>
                    <?php if (isset($component)) { $__componentOriginal19987cd45c699eefead8b7a8ac222798 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal19987cd45c699eefead8b7a8ac222798 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.modern-button','data' => ['variant' => 'primary','size' => 'md','href' => ''.e(route('tickets.create')).'','icon' => '<svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path></svg>']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('modern-button'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['variant' => 'primary','size' => 'md','href' => ''.e(route('tickets.create')).'','icon' => '<svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path></svg>']); ?>
                        <?php echo e(__('tickets.create_first_ticket')); ?>

                     <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal19987cd45c699eefead8b7a8ac222798)): ?>
<?php $attributes = $__attributesOriginal19987cd45c699eefead8b7a8ac222798; ?>
<?php unset($__attributesOriginal19987cd45c699eefead8b7a8ac222798); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal19987cd45c699eefead8b7a8ac222798)): ?>
<?php $component = $__componentOriginal19987cd45c699eefead8b7a8ac222798; ?>
<?php unset($__componentOriginal19987cd45c699eefead8b7a8ac222798); ?>
<?php endif; ?>
                 <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginalfc34df8c041cfce1b125f68563b94330)): ?>
<?php $attributes = $__attributesOriginalfc34df8c041cfce1b125f68563b94330; ?>
<?php unset($__attributesOriginalfc34df8c041cfce1b125f68563b94330); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalfc34df8c041cfce1b125f68563b94330)): ?>
<?php $component = $__componentOriginalfc34df8c041cfce1b125f68563b94330; ?>
<?php unset($__componentOriginalfc34df8c041cfce1b125f68563b94330); ?>
<?php endif; ?>
            <?php else: ?>
                <?php if (isset($component)) { $__componentOriginalfc34df8c041cfce1b125f68563b94330 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginalfc34df8c041cfce1b125f68563b94330 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.modern-card','data' => ['variant' => 'elevated','size' => 'sm','class' => 'overflow-hidden']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('modern-card'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['variant' => 'elevated','size' => 'sm','class' => 'overflow-hidden']); ?>
                    <div class="overflow-x-auto">
                        <table class="w-full text-left modern-table">
                            <thead>
                                <tr class="bg-base-200/50 text-base-content/70 text-xs uppercase font-semibold">
                                    <th class="px-4 py-3">ID</th>
                                    <th class="px-4 py-3"><?php echo e(__('tickets.title')); ?></th>
                                    <!--[if BLOCK]><![endif]--><?php if($isSupporter): ?>
                                        <th class="px-4 py-3"><?php echo e(__('tickets.created_by')); ?></th>
                                    <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
                                    <th class="px-4 py-3"><?php echo e(__('tickets.status')); ?></th>
                                    <th class="px-4 py-3"><?php echo e(__('tickets.created_at')); ?></th>
                                    <th class="px-4 py-3"><?php echo e(__('tickets.assigned_to')); ?></th>
                                    <th class="px-4 py-3"><?php echo e(__('tickets.actions')); ?></th>
                                </tr>
                            </thead>
                            <tbody>
                                <!--[if BLOCK]><![endif]--><?php $__currentLoopData = $tickets; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $ticket): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <tr class="border-b border-base-300/20 hover:bg-base-200/30 cursor-pointer transition-colors duration-200" onclick="window.location='<?php echo e(route('tickets.show', $ticket)); ?>'">
                                        <td class="px-4 py-3 font-mono text-sm text-base-content/60">#<?php echo e($ticket->id); ?></td>
                                        <td class="px-4 py-3">
                                            <div class="flex flex-col">
                                                <a href="<?php echo e(route('tickets.show', $ticket)); ?>" class="font-medium text-primary hover:text-primary-focus transition-colors">
                                                    <?php echo e($ticket->title); ?>

                                                </a>
                                                <!--[if BLOCK]><![endif]--><?php if($ticket->messages->isNotEmpty()): ?>
                                                    <span class="text-xs text-base-content/50 mt-1 truncate max-w-xs">
                                                        <?php echo e(Str::limit($ticket->messages->last()->message, 50)); ?>

                                                    </span>
                                                <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
                                            </div>
                                        </td>
                                        <!--[if BLOCK]><![endif]--><?php if($isSupporter): ?>
                                            <td class="px-4 py-3">
                                                <div class="flex items-center gap-2">
                                                    <img class="w-6 h-6 rounded-full modern-avatar" src="<?php echo e($ticket->user->getAvatar(['extension' => 'webp', 'size' => 24])); ?>" alt="<?php echo e($ticket->user->username); ?>">
                                                    <span class="text-sm"><?php echo e($ticket->user->username); ?></span>
                                                </div>
                                            </td>
                                        <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
                                        <td class="px-4 py-3">
                                            <?php if (isset($component)) { $__componentOriginal7c5961ba4242859e8f30202299af2419 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal7c5961ba4242859e8f30202299af2419 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.modern-status-badge','data' => ['status' => $ticket->status,'size' => 'sm']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('modern-status-badge'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['status' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($ticket->status),'size' => 'sm']); ?>
                                                <?php echo e($ticket->statusLabel); ?>

                                             <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal7c5961ba4242859e8f30202299af2419)): ?>
<?php $attributes = $__attributesOriginal7c5961ba4242859e8f30202299af2419; ?>
<?php unset($__attributesOriginal7c5961ba4242859e8f30202299af2419); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal7c5961ba4242859e8f30202299af2419)): ?>
<?php $component = $__componentOriginal7c5961ba4242859e8f30202299af2419; ?>
<?php unset($__componentOriginal7c5961ba4242859e8f30202299af2419); ?>
<?php endif; ?>
                                        </td>
                                        <td class="px-4 py-3">
                                            <div class="flex flex-col">
                                                <span class="text-sm"><?php echo e($ticket->created_at->format('d.m.Y')); ?></span>
                                                <span class="text-xs text-base-content/50"><?php echo e($ticket->created_at->format('H:i')); ?></span>
                                            </div>
                                        </td>
                                        <td class="px-4 py-3">
                                            <!--[if BLOCK]><![endif]--><?php if($ticket->assignedTo): ?>
                                                <div class="flex items-center gap-2">
                                                    <img class="w-6 h-6 rounded-full modern-avatar" src="<?php echo e($ticket->assignedTo->getAvatar(['extension' => 'webp', 'size' => 24])); ?>" alt="<?php echo e($ticket->assignedTo->username); ?>">
                                                    <span class="text-sm"><?php echo e($ticket->assignedTo->username); ?></span>
                                                </div>
                                            <?php else: ?>
                                                <span class="text-base-content/50 italic text-sm"><?php echo e(__('tickets.unassigned')); ?></span>
                                            <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
                                        </td>
                                        <td class="px-4 py-3">
                                            <?php if (isset($component)) { $__componentOriginalaf4239d0ad9d077e0798cecf4e0e309b = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginalaf4239d0ad9d077e0798cecf4e0e309b = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.modern-ticket-actions','data' => ['ticket' => $ticket,'isSupporter' => $isSupporter,'compact' => 'true']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('modern-ticket-actions'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['ticket' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($ticket),'isSupporter' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($isSupporter),'compact' => 'true']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginalaf4239d0ad9d077e0798cecf4e0e309b)): ?>
<?php $attributes = $__attributesOriginalaf4239d0ad9d077e0798cecf4e0e309b; ?>
<?php unset($__attributesOriginalaf4239d0ad9d077e0798cecf4e0e309b); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalaf4239d0ad9d077e0798cecf4e0e309b)): ?>
<?php $component = $__componentOriginalaf4239d0ad9d077e0798cecf4e0e309b; ?>
<?php unset($__componentOriginalaf4239d0ad9d077e0798cecf4e0e309b); ?>
<?php endif; ?>
                                        </td>
                                    </tr>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?><!--[if ENDBLOCK]><![endif]-->
                            </tbody>
                        </table>
                    </div>

                 <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginalfc34df8c041cfce1b125f68563b94330)): ?>
<?php $attributes = $__attributesOriginalfc34df8c041cfce1b125f68563b94330; ?>
<?php unset($__attributesOriginalfc34df8c041cfce1b125f68563b94330); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalfc34df8c041cfce1b125f68563b94330)): ?>
<?php $component = $__componentOriginalfc34df8c041cfce1b125f68563b94330; ?>
<?php unset($__componentOriginalfc34df8c041cfce1b125f68563b94330); ?>
<?php endif; ?>

                <div class="mt-6">
                    <?php echo e($tickets->links()); ?>

                </div>
            <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
         <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginalfc34df8c041cfce1b125f68563b94330)): ?>
<?php $attributes = $__attributesOriginalfc34df8c041cfce1b125f68563b94330; ?>
<?php unset($__attributesOriginalfc34df8c041cfce1b125f68563b94330); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalfc34df8c041cfce1b125f68563b94330)): ?>
<?php $component = $__componentOriginalfc34df8c041cfce1b125f68563b94330; ?>
<?php unset($__componentOriginalfc34df8c041cfce1b125f68563b94330); ?>
<?php endif; ?>
    </div>

    <style>
        /* Modern table styling */
        .modern-table {
            border-collapse: separate;
            border-spacing: 0;
        }

        .modern-table thead th {
            position: sticky;
            top: 0;
            z-index: 10;
            backdrop-filter: blur(8px);
        }

        .modern-table tbody tr:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
        }

        .modern-avatar {
            border: 2px solid rgba(255, 255, 255, 0.2);
            transition: all 0.2s ease;
        }

        .modern-avatar:hover {
            transform: scale(1.1);
            border-color: var(--color-primary);
        }

        /* Theme-specific table adjustments */
        [data-theme="minewache-light"] .modern-table tbody tr:hover {
            background-color: rgba(0, 0, 0, 0.02);
        }

        [data-theme="minewache-dark"] .modern-table tbody tr:hover {
            background-color: rgba(255, 255, 255, 0.05);
        }

        [data-theme="minewache-high-contrast"] .modern-table {
            border: 2px solid currentColor;
        }

        [data-theme="minewache-high-contrast"] .modern-table th,
        [data-theme="minewache-high-contrast"] .modern-table td {
            border: 1px solid currentColor;
        }

        /* Responsive table improvements */
        @media (max-width: 768px) {
            .modern-table {
                font-size: 0.875rem;
            }

            .modern-table th,
            .modern-table td {
                padding: 0.5rem;
            }
        }
    </style>
</div>
<?php /**PATH C:\Users\<USER>\Documents\GitHub\minewache-website\resources\views/livewire/ticket-list.blade.php ENDPATH**/ ?>