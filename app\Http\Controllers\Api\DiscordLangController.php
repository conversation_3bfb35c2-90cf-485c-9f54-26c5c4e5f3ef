<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\File;
use Illuminate\Support\Facades\Lang;
use Illuminate\Validation\Rule;
use Illuminate\Support\Facades\Validator;

class DiscordLangController extends Controller
{
    /**
     * Get the discord language translations.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  string  $lang
     * @return \Illuminate\Http\JsonResponse
     */
    public function getLang(Request $request, $lang = 'en')
    {
        // Validate the language parameter
        $validator = Validator::make(['lang' => $lang], [
            'lang' => ['sometimes', 'string', Rule::in(['en', 'de'])],
        ]);

        if ($validator->fails()) {
            // If validation fails (e.g., lang is not 'en' or 'de'), default to 'en'
            $validatedLang = 'en';
        } else {
            $validatedLang = $validator->validated()['lang'] ?? 'en';
        }

        $filePath = resource_path("lang/{$validatedLang}/discord.php");

        if (!File::exists($filePath)) {
            // If the specific language file doesn't exist, try falling back to English
            if ($validatedLang !== 'en') {
                $fallbackLang = 'en';
                $fallbackFilePath = resource_path("lang/{$fallbackLang}/discord.php");
                if (File::exists($fallbackFilePath)) {
                    $translations = include $fallbackFilePath;
                    return response()->json([
                        'message' => 'Requested language file not found. Falling back to English.',
                        'lang' => $fallbackLang,
                        'translations' => $translations,
                    ]);
                }
            }
            // If English also doesn't exist (highly unlikely for 'en' but good practice) or was the requested lang
            return response()->json(['error' => "Language file for '{$validatedLang}' not found."], 404);
        }

        // Load the translations
        // Using include directly as Lang::get('discord', [], $validatedLang) might depend on full Laravel bootstrap
        // which can be heavy or problematic in some contexts if not fully configured for this specific path.
        // Direct include is safer for a simple file return.
        $translations = include $filePath;

        return response()->json($translations);
    }
}
