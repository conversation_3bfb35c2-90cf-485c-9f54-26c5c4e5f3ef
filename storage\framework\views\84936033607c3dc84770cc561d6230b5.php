<?php $attributes ??= new \Illuminate\View\ComponentAttributeBag;

$__newAttributes = [];
$__propNames = \Illuminate\View\ComponentAttributeBag::extractPropNames(([
    'type' => 'spinner', // spinner, dots, pulse, skeleton
    'size' => 'md', // xs, sm, md, lg, xl
    'color' => 'primary',
    'text' => null,
    'overlay' => false,
    'fullscreen' => false
]));

foreach ($attributes->all() as $__key => $__value) {
    if (in_array($__key, $__propNames)) {
        $$__key = $$__key ?? $__value;
    } else {
        $__newAttributes[$__key] = $__value;
    }
}

$attributes = new \Illuminate\View\ComponentAttributeBag($__newAttributes);

unset($__propNames);
unset($__newAttributes);

foreach (array_filter(([
    'type' => 'spinner', // spinner, dots, pulse, skeleton
    'size' => 'md', // xs, sm, md, lg, xl
    'color' => 'primary',
    'text' => null,
    'overlay' => false,
    'fullscreen' => false
]), 'is_string', ARRAY_FILTER_USE_KEY) as $__key => $__value) {
    $$__key = $$__key ?? $__value;
}

$__defined_vars = get_defined_vars();

foreach ($attributes->all() as $__key => $__value) {
    if (array_key_exists($__key, $__defined_vars)) unset($$__key);
}

unset($__defined_vars); ?>

<?php
$sizeClasses = match($size) {
    'xs' => 'w-4 h-4',
    'sm' => 'w-6 h-6',
    'md' => 'w-8 h-8',
    'lg' => 'w-12 h-12',
    'xl' => 'w-16 h-16',
    default => 'w-8 h-8'
};

$colorClasses = match($color) {
    'primary' => 'text-primary',
    'secondary' => 'text-secondary',
    'accent' => 'text-accent',
    'success' => 'text-success',
    'warning' => 'text-warning',
    'error' => 'text-error',
    'base' => 'text-base-content',
    default => 'text-primary'
};

$containerClasses = 'flex items-center justify-center';
if ($overlay) {
    $containerClasses .= ' absolute inset-0 bg-base-100/80 backdrop-blur-sm z-50';
}
if ($fullscreen) {
    $containerClasses .= ' fixed inset-0 bg-base-100/90 backdrop-blur-md z-[9999]';
}
?>

<div <?php echo e($attributes->merge(['class' => $containerClasses])); ?>>
    <div class="flex flex-col items-center gap-3">
        <!--[if BLOCK]><![endif]--><?php if($type === 'spinner'): ?>
            <div class="<?php echo e($sizeClasses); ?> <?php echo e($colorClasses); ?> animate-spin">
                <svg fill="none" viewBox="0 0 24 24">
                    <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                    <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
            </div>
        <?php elseif($type === 'dots'): ?>
            <div class="flex space-x-1">
                <!--[if BLOCK]><![endif]--><?php for($i = 0; $i < 3; $i++): ?>
                    <div class="w-2 h-2 <?php echo e($colorClasses); ?> bg-current rounded-full animate-pulse" style="animation-delay: <?php echo e($i * 0.2); ?>s"></div>
                <?php endfor; ?><!--[if ENDBLOCK]><![endif]-->
            </div>
        <?php elseif($type === 'pulse'): ?>
            <div class="<?php echo e($sizeClasses); ?> <?php echo e($colorClasses); ?> bg-current rounded-full animate-pulse"></div>
        <?php elseif($type === 'skeleton'): ?>
            <div class="animate-pulse space-y-3">
                <div class="h-4 bg-base-300 rounded w-3/4"></div>
                <div class="h-4 bg-base-300 rounded w-1/2"></div>
                <div class="h-4 bg-base-300 rounded w-5/6"></div>
            </div>
        <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
        
        <!--[if BLOCK]><![endif]--><?php if($text): ?>
            <div class="text-sm <?php echo e($colorClasses); ?> font-medium animate-pulse">
                <?php echo e($text); ?>

            </div>
        <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
        
        <?php echo e($slot); ?>

    </div>
</div>

<style>
    /* Enhanced loading animations */
    @keyframes modern-spin {
        from {
            transform: rotate(0deg);
        }
        to {
            transform: rotate(360deg);
        }
    }
    
    @keyframes modern-pulse {
        0%, 100% {
            opacity: 1;
        }
        50% {
            opacity: 0.5;
        }
    }
    
    @keyframes modern-bounce {
        0%, 80%, 100% {
            transform: scale(0);
        }
        40% {
            transform: scale(1);
        }
    }
    
    .modern-loading .animate-spin {
        animation: modern-spin 1s linear infinite;
    }
    
    .modern-loading .animate-pulse {
        animation: modern-pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
    }
    
    .modern-loading .animate-bounce {
        animation: modern-bounce 1.4s ease-in-out infinite both;
    }
    
    /* Dots animation */
    .modern-loading .dots-animation .dot:nth-child(1) {
        animation-delay: 0s;
    }
    
    .modern-loading .dots-animation .dot:nth-child(2) {
        animation-delay: 0.2s;
    }
    
    .modern-loading .dots-animation .dot:nth-child(3) {
        animation-delay: 0.4s;
    }
    
    /* Theme-specific adjustments */
    [data-theme="minewache-light"] .modern-loading {
        color: rgba(0, 0, 0, 0.7);
    }
    
    [data-theme="minewache-dark"] .modern-loading {
        color: rgba(255, 255, 255, 0.8);
    }
    
    [data-theme="minewache-high-contrast"] .modern-loading {
        font-weight: bold;
        border: 2px solid currentColor;
        border-radius: 0.5rem;
        padding: 1rem;
    }
    
    [data-theme="minewache-colorful"] .modern-loading {
        background: linear-gradient(45deg, var(--color-primary), var(--color-secondary));
        background-clip: text;
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
    }
    
    /* Accessibility improvements */
    @media (prefers-reduced-motion: reduce) {
        .modern-loading .animate-spin,
        .modern-loading .animate-pulse,
        .modern-loading .animate-bounce {
            animation: none;
        }
        
        .modern-loading .animate-pulse {
            opacity: 0.8;
        }
    }
    
    /* Focus management for overlays */
    .modern-loading[data-overlay="true"] {
        pointer-events: auto;
    }
    
    .modern-loading[data-overlay="true"] * {
        pointer-events: none;
    }
</style>
<?php /**PATH C:\Users\<USER>\Documents\GitHub\minewache-website\resources\views/components/modern-loading.blade.php ENDPATH**/ ?>