<?php

require __DIR__.'/vendor/autoload.php';

$app = require_once __DIR__.'/bootstrap/app.php';

$kernel = $app->make(Illuminate\Contracts\Console\Kernel::class);
$kernel->bootstrap();

use App\Models\Ticket;
use App\Models\TicketMessage;
use App\Events\TicketMessageCreated;
use App\Events\TicketMessageCreatedBroadcast;
use Illuminate\Support\Facades\Log;

// Get a ticket to test with
$ticket = Ticket::first();

if (!$ticket) {
    echo "No tickets found. Please create a ticket first.\n";
    exit(1);
}

echo "Using ticket #{$ticket->id}\n";

// Get a valid user ID
$userId = \App\Models\User::first()->id;
echo "Using user #{$userId}\n";

// Create a new message
$message = new TicketMessage([
    'ticket_id' => $ticket->id,
    'user_id' => $userId,
    'message' => 'This is a test message from the script at ' . date('Y-m-d H:i:s'),
]);

$message->save();

echo "Created message #{$message->id}\n";

// Dispatch the regular event (non-broadcasting)
event(new TicketMessageCreated($message));
echo "Dispatched TicketMessageCreated event\n";

// Dispatch the broadcast event
try {
    event(new TicketMessageCreatedBroadcast($message));
    echo "Dispatched TicketMessageCreatedBroadcast event\n";
} catch (Exception $e) {
    echo "Error dispatching TicketMessageCreatedBroadcast event: {$e->getMessage()}\n";
    Log::error('Failed to broadcast TicketMessageCreatedBroadcast event', [
        'message_id' => $message->id,
        'error' => $e->getMessage(),
    ]);
}

// Try a direct HTTP request to the Reverb server
try {
    $client = new \GuzzleHttp\Client();
    $response = $client->post('http://localhost:8080/api/6wnubnahcggubcu5vmds/events', [
        'json' => [
            'channel' => 'private-tickets.' . $ticket->id,
            'name' => 'TicketMessageCreated',
            'data' => json_encode([
                'message' => [
                    'id' => $message->id,
                    'ticket_id' => $message->ticket_id,
                    'user_id' => $message->user_id,
                    'message' => $message->message,
                    'created_at' => $message->created_at->format('Y-m-d H:i:s'),
                ],
            ]),
        ],
        'headers' => [
            'Content-Type' => 'application/json',
            'Accept' => 'application/json',
        ],
    ]);

    echo "Direct HTTP request to Reverb server: " . $response->getStatusCode() . "\n";
} catch (Exception $e) {
    echo "Error making direct HTTP request to Reverb server: {$e->getMessage()}\n";
}

echo "Done!\n";
