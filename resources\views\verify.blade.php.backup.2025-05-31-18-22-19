<x-app-layout>
    <x-slot name="header">
        <h2 class="font-display text-xl md:text-2xl text-gray-900 dark:text-white leading-tight animate-slide-down">
            {{ __('Überprüfe deine Bewerbung') }}
        </h2>
    </x-slot>

    <div class="py-8 md:py-12 bg-base-200">
        <div class="max-w-3xl mx-auto px-4 sm:px-6 lg:px-8">
            <!-- Verbesserte Fortschrittsanzeige -->
            <div class="mb-8 animate-fade-in-up">
                <div class="w-full mb-4">
                    <div class="flex flex-col space-y-2">
                        <div class="flex justify-between text-xs text-base-content/70">
                            <span>Überprüfung vor Absenden</span>
                            <span>100% abgeschlossen</span>
                        </div>
                        <div class="relative pt-1">
                            <div class="h-2 bg-base-300 rounded-full overflow-hidden">
                                <div class="h-full bg-success rounded-full w-full transition-all duration-1000"></div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="flex items-center justify-between mt-4 flex-wrap gap-2">
                    <div class="flex items-center gap-2">
                        <div class="w-8 h-8 rounded-full bg-success flex items-center justify-center">
                            <x-heroicon-o-check class="h-5 w-5 text-success-content" />
                        </div>
                        <span class="text-sm font-medium opacity-70">Persönliche Daten</span>
                    </div>
                    <div class="flex-grow hidden md:block border-t-2 border-dashed border-success/30 mx-2"></div>
                    <div class="flex items-center gap-2">
                        <div class="w-8 h-8 rounded-full bg-success flex items-center justify-center">
                            <x-heroicon-o-check class="h-5 w-5 text-success-content" />
                        </div>
                        <span class="text-sm font-medium opacity-70">Rollenspezifisch</span>
                    </div>
                    <div class="flex-grow hidden md:block border-t-2 border-dashed border-success/30 mx-2"></div>
                    <div class="flex items-center gap-2">
                        <div class="w-8 h-8 rounded-full bg-success flex items-center justify-center">
                            <x-heroicon-o-check class="h-5 w-5 text-success-content" />
                        </div>
                        <span class="text-sm font-medium opacity-70">Über Dich</span>
                    </div>
                </div>

                <div class="mt-6 px-2 bg-success/10 p-4 rounded-lg border border-success/20 flex items-start gap-3">
                    <x-heroicon-o-information-circle class="h-5 w-5 text-success shrink-0 mt-0.5" />
                    <p class="text-sm">
                        Bitte überprüfe deine Angaben noch einmal sorgfältig, bevor du die Bewerbung abschickst. Du kannst jetzt noch Änderungen vornehmen.
                    </p>
                </div>
            </div>

            <!-- Hauptinhaltskarte -->
            <div class="bg-white dark:bg-base-100 rounded-xl shadow-lg overflow-hidden animate-fade-in-up">
                <div class="p-6 md:p-8 text-gray-900 dark:text-gray-100">
                    <div class="flex flex-col sm:flex-row sm:items-start sm:space-x-4 mb-6">
                        <div class="rounded-full bg-success/10 p-3 flex-shrink-0 mb-4 sm:mb-0">
                            <x-heroicon-o-clipboard-document-check class="w-7 h-6 text-success" />
                        </div>
                        <div>
                            <h3 class="font-geometric text-xl md:text-2xl font-medium text-gray-900 dark:text-white mb-2">Fast geschafft!</h3>
                            <p class="text-sm text-base-content/70">Überprüfe deine Angaben und sende deine Bewerbung ab.</p>
                        </div>
                    </div>

                    <div class="card bg-base-200 shadow-xl w-full max-w-4xl mx-auto">
                        <div class="card-body">
                            <form action="{{ route('applications.store') }}" method="POST" class="space-y-8">
                                @csrf
                                <input type="hidden" name="discord_id" value="{{ Illuminate\Support\Facades\Auth::user()->id }}">
                                <input type="hidden" name="user_id" value="{{ Illuminate\Support\Facades\Auth::user()->id }}">

                                <!-- Persönliche Daten Sektion -->
                                <div class="border-b border-base-300 pb-6">
                                    <h4 class="font-medium text-lg mb-4 flex items-center gap-2">
                                        <span class="w-6 h-6 rounded-full bg-primary flex items-center justify-center text-xs text-primary-content">1</span>
                                        Persönliche Daten
                                    </h4>

                                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                        <!-- Name -->
                                        @if(isset($data['name']))
                                        <div class="form-control">
                                            <label class="label">
                                                <span class="label-text font-medium">Name</span>
                                            </label>
                                            <input type="text" name="name" value="{{ $data['name'] }}"
                                                  class="input input-bordered bg-base-100/50">
                                        </div>
                                        @endif

                                        <!-- Age -->
                                        @if(isset($data['age']))
                                        <div class="form-control">
                                            <label class="label">
                                                <span class="label-text font-medium">Alter</span>
                                            </label>
                                            <input type="number" name="age" value="{{ $data['age'] }}" min="0" max="150"
                                                  class="input input-bordered bg-base-100/50">
                                        </div>
                                        @endif

                                        <!-- Gender -->
                                        @if(isset($data['gender']))
                                        <div class="form-control">
                                            <label class="label">
                                                <span class="label-text font-medium">Geschlecht</span>
                                            </label>
                                            <input type="text" name="gender" value="{{ $data['gender'] }}"
                                                  class="input input-bordered bg-base-100/50">
                                        </div>
                                        @endif

                                        <!-- Pronouns -->
                                        @if(isset($data['pronouns']))
                                        <div class="form-control">
                                            <label class="label">
                                                <span class="label-text font-medium">Pronomen</span>
                                            </label>
                                            <input type="text" name="pronouns" value="{{ $data['pronouns'] }}"
                                                  class="input input-bordered bg-base-100/50">
                                        </div>
                                        @endif
                                    </div>
                                </div>

                                <!-- Tätigkeiten Sektion -->
                                @if(isset($data['professions']))
                                <div class="border-b border-base-300 pb-6">
                                    <h4 class="font-medium text-lg mb-4 flex items-center gap-2">
                                        <span class="w-6 h-6 rounded-full bg-primary flex items-center justify-center text-xs text-primary-content">2</span>
                                        Tätigkeiten & Anforderungen
                                    </h4>

                                    <div class="mb-4">
                                        <label class="label">
                                            <span class="label-text font-medium">Gewählte Tätigkeiten</span>
                                        </label>
                                        <div class="flex flex-wrap gap-2">
                                            @foreach($data['professions'] as $profession)
                                                <input type="hidden" name="professions[]" value="{{ $profession }}">
                                                <div class="badge badge-primary badge-lg">
                                                    @if($profession == 'actor')
                                                        Schauspieler
                                                    @elseif($profession == 'actor_no_voice')
                                                        Schauspieler (No Voice)
                                                    @elseif($profession == 'voice_actor')
                                                        Synchronsprecher
                                                    @elseif($profession == 'builder')
                                                        Builder
                                                    @elseif($profession == 'designer')
                                                        Designer
                                                    @elseif($profession == 'cutter')
                                                        Cutter
                                                    @elseif($profession == 'cameraman')
                                                        Kameramann
                                                    @elseif($profession == 'developer')
                                                        Developer
                                                    @elseif($profession == 'modeler')
                                                        Modellierer
                                                    @elseif($profession == 'music_producer')
                                                        Musikproduzent
                                                    @else
                                                        {{ ucfirst($profession) }}
                                                    @endif
                                                </div>
                                            @endforeach
                                        </div>
                                    </div>

                                    <!-- Checkbox Fragen -->
                                    @if(isset($data['checkboxQuestions']) && count($data['checkboxQuestions']) > 0)
                                    <div class="mb-4">
                                        <label class="label">
                                            <span class="label-text font-medium">Bestätigte Anforderungen</span>
                                        </label>
                                        <div class="grid grid-cols-1 sm:grid-cols-2 gap-2">
                                            @foreach($data['checkboxQuestions'] as $checkbox)
                                                <input type="hidden" name="checkboxQuestions[]" value="{{ $checkbox }}">
                                                <div class="flex items-center gap-2">
                                                    <div class="w-5 h-5 rounded-md bg-success/20 flex items-center justify-center">
                                                        <x-heroicon-o-check class="h-4 w-4 text-success" />
                                                    </div>
                                                    <span class="text-sm">
                                                        @if($checkbox == 'pc')
                                                            PC vorhanden
                                                        @elseif($checkbox == 'minecraft')
                                                            Minecraft Java vorhanden
                                                        @elseif($checkbox == 'curseforge')
                                                            CurseForge/Modrinth installiert
                                                        @elseif($checkbox == 'obs')
                                                            OBS installiert
                                                        @elseif($checkbox == 'java_experience')
                                                            Java-Erfahrung
                                                        @elseif($checkbox == 'forge_experience')
                                                            Forge 1.12.2 Erfahrung
                                                        @else
                                                            {{ $checkbox }}
                                                        @endif
                                                    </span>
                                                </div>
                                            @endforeach
                                        </div>
                                    </div>
                                    @endif

                                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mt-4">
                                        <!-- RAM -->
                                        @if(isset($data['ram']))
                                        <div class="form-control">
                                            <label class="label">
                                                <span class="label-text font-medium">RAM (GB)</span>
                                            </label>
                                            <input type="number" name="ram" value="{{ $data['ram'] }}" min="0" max="256"
                                                  class="input input-bordered bg-base-100/50">
                                        </div>
                                        @endif

                                        <!-- FPS -->
                                        @if(isset($data['fps']))
                                        <div class="form-control">
                                            <label class="label">
                                                <span class="label-text font-medium">FPS in Minecraft</span>
                                            </label>
                                            <input type="text" name="fps" value="{{ $data['fps'] }}"
                                                  class="input input-bordered bg-base-100/50">
                                        </div>
                                        @endif

                                        <!-- GPU -->
                                        @if(isset($data['gpu']))
                                        <div class="form-control">
                                            <label class="label">
                                                <span class="label-text font-medium">Grafikkarte</span>
                                            </label>
                                            <input type="text" name="gpu" value="{{ $data['gpu'] }}"
                                                  class="input input-bordered bg-base-100/50">
                                        </div>
                                        @endif

                                        <!-- Voice Type -->
                                        @if(isset($data['voice_type']))
                                        <div class="form-control">
                                            <label class="label">
                                                <span class="label-text font-medium">Stimmhöhe</span>
                                            </label>
                                            <input type="text" name="voice_type" value="{{ $data['voice_type'] }}"
                                                  class="input input-bordered bg-base-100/50">
                                        </div>
                                        @endif

                                        <!-- Microphone -->
                                        @if(isset($data['microphone']))
                                        <div class="form-control">
                                            <label class="label">
                                                <span class="label-text font-medium">Mikrofon</span>
                                            </label>
                                            <input type="text" name="microphone" value="{{ $data['microphone'] }}"
                                                  class="input input-bordered bg-base-100/50">
                                        </div>
                                        @endif

                                        <!-- DAW -->
                                        @if(isset($data['daw']))
                                        <div class="form-control">
                                            <label class="label">
                                                <span class="label-text font-medium">DAW</span>
                                            </label>
                                            <input type="text" name="daw" value="{{ $data['daw'] }}"
                                                  class="input input-bordered bg-base-100/50">
                                        </div>
                                        @endif

                                        <!-- Program -->
                                        @if(isset($data['program']))
                                        <div class="form-control">
                                            <label class="label">
                                                <span class="label-text font-medium">Programm</span>
                                            </label>
                                            <input type="text" name="program" value="{{ $data['program'] }}"
                                                  class="input input-bordered bg-base-100/50">
                                        </div>
                                        @endif

                                        <!-- Design Style -->
                                        @if(isset($data['design_style']))
                                        <div class="form-control">
                                            <label class="label">
                                                <span class="label-text font-medium">Design-Stil</span>
                                            </label>
                                            <input type="text" name="design_style" value="{{ $data['design_style'] }}"
                                                  class="input input-bordered bg-base-100/50">
                                        </div>
                                        @endif

                                        <!-- Favorite Design -->
                                        @if(isset($data['favorite_design']))
                                        <div class="form-control">
                                            <label class="label">
                                                <span class="label-text font-medium">Lieblings-Design</span>
                                            </label>
                                            <input type="text" name="favorite_design" value="{{ $data['favorite_design'] }}"
                                                  class="input input-bordered bg-base-100/50">
                                        </div>
                                        @endif

                                        <!-- Programming Languages -->
                                        @if(isset($data['languages']))
                                        <div class="form-control">
                                            <label class="label">
                                                <span class="label-text font-medium">Programmiersprachen</span>
                                            </label>
                                            <input type="text" name="languages" value="{{ $data['languages'] }}"
                                                  class="input input-bordered bg-base-100/50">
                                        </div>
                                        @endif

                                        <!-- IDE -->
                                        @if(isset($data['ide']))
                                        <div class="form-control">
                                            <label class="label">
                                                <span class="label-text font-medium">IDE</span>
                                            </label>
                                            <input type="text" name="ide" value="{{ $data['ide'] }}"
                                                  class="input input-bordered bg-base-100/50">
                                        </div>
                                        @endif

                                        <!-- Desired Role -->
                                        @if(isset($data['desired_role']))
                                        <div class="form-control">
                                            <label class="label">
                                                <span class="label-text font-medium">Wunschrolle</span>
                                            </label>
                                            <input type="text" name="desired_role" value="{{ $data['desired_role'] }}"
                                                  class="input input-bordered bg-base-100/50">
                                        </div>
                                        @endif
                                    </div>
                                </div>
                                @endif

                                <!-- Portfolio -->
                                @if(isset($data['portfolio']))
                                <div class="form-control">
                                    <label class="label">
                                        <span class="label-text font-medium">Portfolio / Referenzen</span>
                                    </label>
                                    <textarea name="portfolio" rows="3" class="textarea textarea-bordered bg-base-100/50">{{ $data['portfolio'] }}</textarea>
                                </div>
                                @endif

                                <!-- Über Dich Sektion -->
                                <div class="border-t border-base-300 pt-6">
                                    <h4 class="font-medium text-lg mb-4 flex items-center gap-2">
                                        <span class="w-6 h-6 rounded-full bg-primary flex items-center justify-center text-xs text-primary-content">3</span>
                                        Über Dich
                                    </h4>

                                    <!-- Über dich -->
                                    @if(isset($data['about_you']))
                                    <div class="form-control mb-4">
                                        <label class="label">
                                            <span class="label-text font-medium">Über dich</span>
                                        </label>
                                        <textarea name="about_you" rows="4" class="textarea textarea-bordered bg-base-100/50">{{ $data['about_you'] }}</textarea>
                                    </div>
                                    @endif

                                    <!-- Stärken und Schwächen -->
                                    @if(isset($data['strengths_weaknesses']))
                                    <div class="form-control mb-4">
                                        <label class="label">
                                            <span class="label-text font-medium">Stärken und Schwächen</span>
                                        </label>
                                        <textarea name="strengths_weaknesses" rows="3" class="textarea textarea-bordered bg-base-100/50">{{ $data['strengths_weaknesses'] }}</textarea>
                                    </div>
                                    @endif

                                    <!-- Abschlussworte -->
                                    @if(isset($data['final_words']))
                                    <div class="form-control">
                                        <label class="label">
                                            <span class="label-text font-medium">Abschlussworte</span>
                                        </label>
                                        <textarea name="final_words" rows="2" class="textarea textarea-bordered bg-base-100/50">{{ $data['final_words'] }}</textarea>
                                    </div>
                                    @endif
                                </div>

                                <!-- Versteckte Felder für andere Daten -->
                                @foreach($data as $key => $value)
                                    @if(!in_array($key, ['name', 'age', 'gender', 'pronouns', 'professions', 'checkboxQuestions', 'ram', 'fps', 'voice_type', 'microphone', 'daw', 'program', 'design_style', 'favorite_design', 'gpu', 'languages', 'ide', 'desired_role', 'portfolio', 'about_you', 'strengths_weaknesses', 'final_words', '_token']))
                                        @if(is_array($value))
                                            @foreach($value as $subValue)
                                                <input type="hidden" name="{{ $key }}[]" value="{{ $subValue }}">
                                            @endforeach
                                        @else
                                            <input type="hidden" name="{{ $key }}" value="{{ $value }}">
                                        @endif
                                    @endif
                                @endforeach

                                <!-- Bestätigungsbox und Buttons -->
                                <div class="bg-base-300/30 p-4 rounded-lg border border-base-300 mt-8">
                                    <div class="flex items-start space-x-4 mb-6">
                                        <input type="checkbox" id="final-confirmation" required
                                            class="checkbox checkbox-primary mt-1" />
                                        <div>
                                            <label for="final-confirmation" class="font-medium cursor-pointer">
                                                Bestätigung
                                            </label>
                                            <p class="text-sm text-base-content/80 mt-1">
                                                Ich bestätige, dass meine Angaben korrekt sind und ich die Teilnahmebedingungen gelesen habe.
                                            </p>
                                        </div>
                                    </div>

                                    <div class="card-actions flex flex-col md:flex-row md:justify-between items-center gap-4 pt-4">
                                        <a href="javascript:history.back()" class="btn btn-ghost gap-2">
                                            <x-heroicon-o-arrow-left class="h-4 w-4" />
                                            Zurück bearbeiten
                                        </a>

                                        <button type="submit" class="btn btn-success btn-lg gap-2">
                                            Bewerbung jetzt absenden
                                            <x-heroicon-o-paper-airplane class="h-5 w-5" />
                                        </button>
                                    </div>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</x-app-layout>
