<?php

namespace App\Services;

use App\Models\Ticket;
use App\Models\TicketMessage;
use App\Models\User;
use App\Models\TicketAttachment; // Added this line
use App\Jobs\ProcessDiscordAttachmentDownload; // Added this line
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\DB;
use Illuminate\Database\Eloquent\Collection;
use Exception;

class TicketService
{
    public function createTicket(string $discordUserId, string $title, string $description, string $discordChannelId): ?Ticket
    {
        // ... (existing createTicket logic from previous step) ...
        // Find the Laravel user by their Discord ID.
         $user = User::where('id', $discordUserId)->first(); // Assuming 'id' on User model is the Discord ID.

         if (!$user) {
             Log::error("TicketService: Could not find Laravel user with Discord ID: {$discordUserId}");
             return null;
         }

         DB::beginTransaction();
         try {
             $ticket = Ticket::create([
                 'user_id' => $user->id,
                 'title' => $title,
                 'status' => Ticket::STATUS_OPEN,
                 'discord_channel_id' => $discordChannelId,
                 'discord_user_id' => $discordUserId,
             ]);

             TicketMessage::create([
                 'ticket_id' => $ticket->id,
                 'user_id' => $user->id,
                 'discord_user_id' => $discordUserId,
                 'content' => $description,
                 'source' => TicketMessage::SOURCE_DISCORD,
             ]);

             DB::commit();
             Log::info("TicketService: Ticket #{$ticket->id} created successfully for Discord user {$discordUserId}, channel {$discordChannelId}.");
             return $ticket;

         } catch (Exception $e) {
             DB::rollBack();
             Log::error("TicketService: Failed to create ticket.", [
                 'discord_user_id' => $discordUserId,
                 'title' => $title,
                 'exception' => $e
             ]);
             return null;
         }
    }


    /**
     * Gets open tickets for a given Discord user ID.
     *
     * @param string $discordUserId
     * @return Collection|Ticket[]
     */
    public function getUserOpenTickets(string $discordUserId): Collection
    {
        // Assuming Ticket model has 'discord_user_id' and 'status' fields.
        // And User model's 'id' is the discord_user_id
        return Ticket::where('discord_user_id', $discordUserId)
                     ->where('status', '!=', Ticket::STATUS_CLOSED) // Fetch all non-closed tickets
                     ->orderBy('created_at', 'desc')
                     ->get();
    }

    /**
     * Stores a message from Discord into the database for a specific ticket.
     *
     * @param int $ticketDbId The database ID of the ticket.
     * @param string $discordUserId The Discord ID of the message author.
     * @param string $content The content of the message.
     * @param string $discordMessageId The Discord ID of the message itself.
     * @param array $attachmentsData Optional data about attachments.
     * @return TicketMessage|null The created TicketMessage model or null on failure.
     */
    public function storeDiscordMessage(int $ticketDbId, string $discordUserId, string $content, string $discordMessageId, array $attachmentsData = []): ?TicketMessage
    {
        $ticket = Ticket::find($ticketDbId);
        if (!$ticket) {
            Log::error("TicketService: Ticket #{$ticketDbId} not found for storing message from Discord user {$discordUserId}.");
            return null;
        }

        if ($ticket->status === Ticket::STATUS_CLOSED) {
            Log::info("TicketService: Attempt to add message to already closed ticket #{$ticketDbId}. Ignoring.");
            return null;
        }

        $user = User::where('id', $discordUserId)->first();

        DB::beginTransaction();
        try {
            $ticketMessage = TicketMessage::create([
                'ticket_id' => $ticket->id,
                'user_id' => $user ? $user->id : null,
                'discord_user_id' => $discordUserId,
                'discord_message_id' => $discordMessageId,
                'content' => $content,
                'source' => TicketMessage::SOURCE_DISCORD,
            ]);

            if ($ticketMessage && !empty($attachmentsData)) {
                foreach ($attachmentsData as $attachmentItem) {
                    $newAttachment = TicketAttachment::create([
                        'ticket_message_id' => $ticketMessage->id,
                        'discord_attachment_id' => $attachmentItem['id'],
                        'filename' => $attachmentItem['filename'],
                        'url' => $attachmentItem['url'],
                        'mime_type' => $attachmentItem['content_type'],
                        'size' => $attachmentItem['size'],
                    ]);
                    ProcessDiscordAttachmentDownload::dispatch($newAttachment)->onQueue('downloads');
                    Log::info("TicketService: Queued download for attachment {$newAttachment->filename} (ID: {$newAttachment->id}) for ticket message {$ticketMessage->id}.");
                }
            }

            $ticket->touch();
            DB::commit();

            Log::info("TicketService: Message from Discord user {$discordUserId} (Msg ID: {$discordMessageId}) stored for ticket #{$ticketDbId}.");
            return $ticketMessage;

        } catch (Exception $e) {
            DB::rollBack();
            Log::error("TicketService: Failed to store Discord message for ticket #{$ticketDbId}.", [
                'discord_user_id' => $discordUserId,
                'discord_message_id' => $discordMessageId,
                'exception_message' => $e->getMessage(),
                // 'exception_trace' => $e->getTraceAsString() // Avoid logging full trace unless in debug/local
            ]);
            return null;
        }
    }
}
