<x-app-layout>
    <x-slot name="heading">
        {{ __('apply.title') }}
    </x-slot>

    <x-slot name="header">
        <h2 class="font-display text-xl md:text-2xl text-base-content leading-tight animate-slide-down">
            {{ __('apply.your_application') }}
        </h2>
    </x-slot>

    <x-slot name="breadcrumbs">
        <x-breadcrumbs :items="[
            ['label' => __('apply.title')]
        ]" />
    </x-slot>

    <!-- Enhanced Theme Switcher -->
    <x-enhanced-theme-switcher position="bottom-right" type="simple" />

    <div class="py-8 md:py-12 bg-base-200">
        <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">

            <!-- Hero Section -->
            <div class="text-center mb-12">
                <div class="relative inline-block">
                    <div class="absolute -inset-4 bg-gradient-to-r from-primary/20 to-secondary/20 rounded-full blur-xl"></div>
                    <h1 class="relative text-4xl md:text-5xl font-bold text-transparent bg-clip-text bg-gradient-to-r from-primary to-secondary mb-4">
                        {{ __('apply.application_process') }}
                    </h1>
                </div>
                <p class="text-xl text-base-content/80 max-w-2xl mx-auto">
                    {{ __('apply.welcome_text') }}
                </p>
            </div>

            <!-- Application Process Steps -->
            <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-12">
                <x-modern-card variant="elevated" size="lg" interactive="true" class="text-center">
                    <div class="w-16 h-16 bg-primary/20 rounded-full flex items-center justify-center mx-auto mb-4">
                        <span class="text-2xl font-bold text-primary">1</span>
                    </div>
                    <h3 class="text-lg font-semibold mb-2">Bewerbung ausfüllen</h3>
                    <p class="text-base-content/70">Du füllst die Bewerbung mit unserem intelligenten Assistenten aus</p>
                </x-modern-card>

                <x-modern-card variant="elevated" size="lg" interactive="true" class="text-center">
                    <div class="w-16 h-16 bg-secondary/20 rounded-full flex items-center justify-center mx-auto mb-4">
                        <span class="text-2xl font-bold text-secondary">2</span>
                    </div>
                    <h3 class="text-lg font-semibold mb-2">Prüfung</h3>
                    <p class="text-base-content/70">Wir prüfen deine Bewerbung in der Regel innerhalb von einer Woche</p>
                </x-modern-card>

                <x-modern-card variant="elevated" size="lg" interactive="true" class="text-center">
                    <div class="w-16 h-16 bg-accent/20 rounded-full flex items-center justify-center mx-auto mb-4">
                        <span class="text-2xl font-bold text-accent">3</span>
                    </div>
                    <h3 class="text-lg font-semibold mb-2">Benachrichtigung</h3>
                    <p class="text-base-content/70">Du wirst über Discord benachrichtigt, wenn deine Bewerbung angenommen wurde</p>
                </x-modern-card>
            </div>

            <!-- Main Application Card -->
            <x-modern-card variant="gradient" size="xl" class="mb-8">
                <div class="flex items-center gap-4 mb-6">
                    <div class="w-12 h-12 bg-primary/20 rounded-xl flex items-center justify-center">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-7 w-7 text-primary" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                        </svg>
                    </div>
                    <div>
                        <h2 class="text-2xl font-bold text-base-content">Bewerbungsassistent</h2>
                        <p class="text-base-content/70">Intelligenter Schritt-für-Schritt Assistent</p>
                    </div>
                </div>

                <!-- Features Grid -->
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
                    <x-modern-card variant="outlined" size="md">
                        <div class="flex items-start gap-3">
                            <div class="w-10 h-10 bg-success/20 rounded-lg flex items-center justify-center flex-shrink-0">
                                <x-heroicon-o-sparkles class="h-6 w-6 text-success" />
                            </div>
                            <div>
                                <h4 class="font-semibold mb-2">{{ __('apply.application_assistant') }}</h4>
                                <p class="text-sm text-base-content/70 mb-3">{{ __('apply.assistant_description') }}</p>
                                <ul class="space-y-1 text-sm">
                                    <li class="flex items-center gap-2">
                                        <x-heroicon-o-check-circle class="h-4 w-4 text-success" />
                                        <span>{{ __('apply.step_by_step') }}</span>
                                    </li>
                                    <li class="flex items-center gap-2">
                                        <x-heroicon-o-check-circle class="h-4 w-4 text-success" />
                                        <span>{{ __('apply.auto_save') }}</span>
                                    </li>
                                </ul>
                            </div>
                        </div>
                    </x-modern-card>

                    <x-modern-card variant="outlined" size="md">
                        <div class="flex items-start gap-3">
                            <div class="w-10 h-10 bg-info/20 rounded-lg flex items-center justify-center flex-shrink-0">
                                <x-heroicon-o-shield-check class="h-6 w-6 text-info" />
                            </div>
                            <div>
                                <h4 class="font-semibold mb-2">Intelligente Anpassung</h4>
                                <p class="text-sm text-base-content/70 mb-3">Formular passt sich automatisch an deine Auswahl an</p>
                                <ul class="space-y-1 text-sm">
                                    <li class="flex items-center gap-2">
                                        <x-heroicon-o-check-circle class="h-4 w-4 text-success" />
                                        <span>Formularfelder angepasst an deine gewählten Berufe</span>
                                    </li>
                                    <li class="flex items-center gap-2">
                                        <x-heroicon-o-check-circle class="h-4 w-4 text-success" />
                                        <span>Überprüfung aller Daten vor der Einreichung</span>
                                    </li>
                                </ul>
                            </div>
                        </div>
                    </x-modern-card>
                </div>

                <form action="{{ route('application_wizard') }}" method="get" class="space-y-6">
                    @csrf

                    <!-- Privacy Policy Section -->
                    <x-modern-card variant="filled" size="md" class="bg-warning/10 border border-warning/30">
                        <div class="flex items-start gap-3">
                            <div class="w-8 h-8 bg-warning/20 rounded-lg flex items-center justify-center flex-shrink-0 mt-1">
                                <x-heroicon-o-shield-check class="h-5 w-5 text-warning" />
                            </div>
                            <div class="flex-1">
                                <h4 class="font-semibold text-warning mb-2">Datenschutz & Einverständnis</h4>
                                <div x-data="{ checked: false }" class="space-y-3">
                                    <label class="flex items-start gap-3 cursor-pointer">
                                        <input
                                            id="privacy_policy"
                                            name="privacy_policy"
                                            type="checkbox"
                                            required
                                            x-model="checked"
                                            class="w-5 h-5 rounded border-2 border-warning text-warning focus:ring-warning focus:ring-offset-0 mt-0.5 @error('privacy_policy') border-error @enderror"
                                        />
                                        <span class="text-sm text-base-content">
                                            {!! __('apply.privacy_policy', ['privacy_url' => route('datenschutz')]) !!}
                                        </span>
                                    </label>
                                    @error('privacy_policy')
                                        <span class="text-xs text-error">{{ $message }}</span>
                                    @enderror
                                </div>
                            </div>
                        </div>
                    </x-modern-card>

                    <!-- Action Buttons -->
                    <div class="flex flex-col sm:flex-row gap-4 justify-between items-center pt-6">
                        <x-modern-button
                            variant="ghost"
                            href="{{ route('home') }}"
                            icon='<svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18" /></svg>'
                        >
                            {{ __('apply.cancel') }}
                        </x-modern-button>

                        <x-modern-button
                            variant="gradient"
                            size="lg"
                            type="submit"
                            icon='<svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7l5 5m0 0l-5 5m5-5H6" /></svg>'
                            iconPosition="right"
                            class="min-w-[200px]"
                        >
                            {{ __('apply.start_application') }}
                        </x-modern-button>
                    </div>
                </form>
            </x-modern-card>

            <!-- FAQ Section -->
            <x-modern-card variant="elevated" size="lg" class="mt-8">
                <div class="flex items-center gap-3 mb-6">
                    <div class="w-10 h-10 bg-info/20 rounded-lg flex items-center justify-center">
                        <x-heroicon-o-question-mark-circle class="w-6 h-6 text-info" />
                    </div>
                    <h3 class="text-xl font-bold text-base-content">{{ __('apply.faq') }}</h3>
                </div>

                <div class="space-y-4">
                    @foreach([
                        [
                            'question' => __('apply.faq_process_time'),
                            'answer' => __('apply.faq_process_time_answer')
                        ],
                        [
                            'question' => __('apply.faq_requirements'),
                            'answer' => __('apply.faq_requirements_answer')
                        ],
                        [
                            'question' => __('apply.faq_discord'),
                            'answer' => __('apply.faq_discord_answer')
                        ],
                        [
                            'question' => __('apply.faq_age'),
                            'answer' => __('apply.faq_age_answer')
                        ]
                    ] as $index => $faq)
                        <x-modern-card variant="outlined" size="sm" class="overflow-hidden">
                            <details class="group">
                                <summary class="flex justify-between items-center cursor-pointer hover:bg-base-200/50 transition-colors duration-200 p-2 -m-2 rounded-lg">
                                    <span class="font-medium text-base-content">{{ $faq['question'] }}</span>
                                    <x-heroicon-s-chevron-down class="w-5 h-5 text-base-content/60 transition-transform duration-300 group-open:rotate-180 flex-shrink-0 ml-2" />
                                </summary>
                                <div class="mt-3 pt-3 border-t border-base-300">
                                    <p class="text-base-content/80">{{ $faq['answer'] }}</p>
                                </div>
                            </details>
                        </x-modern-card>
                    @endforeach
                </div>
            </x-modern-card>

            <!-- Call to Action -->
            <div class="mt-8 text-center">
                <x-modern-card variant="gradient" size="lg" class="bg-gradient-to-r from-primary/10 to-secondary/10">
                    <div class="text-center">
                        <h3 class="text-2xl font-bold mb-4">Bereit für deine Bewerbung?</h3>
                        <p class="text-base-content/80 mb-6 max-w-2xl mx-auto">
                            Starte jetzt deine Bewerbung und werde Teil unseres Teams. Der Prozess dauert nur wenige Minuten und unser Assistent hilft dir dabei.
                        </p>
                        <x-modern-button
                            variant="primary"
                            size="lg"
                            href="{{ route('application_wizard') }}"
                            icon='<svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7l5 5m0 0l-5 5m5-5H6" /></svg>'
                            iconPosition="right"
                        >
                            Jetzt bewerben
                        </x-modern-button>
                    </div>
                </x-modern-card>
            </div>
        </div>
    </div>
</x-app-layout>
