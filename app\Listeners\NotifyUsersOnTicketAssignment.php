<?php

namespace App\Listeners;

use App\Events\TicketAssignmentUpdated;
use App\Services\DiscordMessagingService;
use App\Models\User; // For type hinting
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\App;
use Illuminate\Support\Facades\Config;

class NotifyUsersOnTicketAssignment implements ShouldQueue
{
    use InteractsWithQueue;

    protected DiscordMessagingService $discordMessagingService;

    /**
     * Create the event listener.
     *
     * @param DiscordMessagingService $discordMessagingService
     * @return void
     */
    public function __construct(DiscordMessagingService $discordMessagingService)
    {
        $this->discordMessagingService = $discordMessagingService;
    }

    /**
     * Handle the event.
     *
     * @param  \App\Events\TicketAssignmentUpdated  $event
     * @return void
     */
    public function handle(TicketAssignmentUpdated $event)
    {
        $ticket = $event->ticket;
        $assignee = $event->assignee; // This is a User model or null
        $assigner = $event->assigner; // This is a User model

        // Determine locale - for now, using fallback. A more robust system might store user/guild locale.
        $locale = Config::get('app.fallback_locale', 'en'); // Default to fallback
        // If the assigner has a preferred locale and it's a supported one, use it.
        // This is a simplification; ideally, the locale context would be passed or determined from the ticket/guild.
        if ($assigner && $assigner->preferred_locale && in_array($assigner->preferred_locale, Config::get('app.supported_locales', ['en']))) {
            $locale = $assigner->preferred_locale;
        }
        App::setLocale($locale);

        Log::info("NotifyUsersOnTicketAssignment: Handling assignment for ticket #{$ticket->id}. Assignee: " . ($assignee ? $assignee->id : 'none') . ". Assigner: {$assigner->id}. Locale: {$locale}");

        if (empty($ticket->discord_channel_id)) {
            Log::warning("NotifyUsersOnTicketAssignment: Ticket #{$ticket->id} has no Discord channel ID. Cannot send assignment notifications to channel.");
            // Still attempt DM if assignee exists
        } else {
            $channelMessage = '';
            if ($assignee && $assignee->discord_id) {
                $assigneeMention = "<@{$assignee->discord_id}>";
                $channelMessage = __('discord.ticket.assigned_to_user_channel', ['assigneeMention' => $assigneeMention, 'assignerName' => $assigner->name]);
            } else {
                $channelMessage = __('discord.ticket.unassigned_channel', ['assignerName' => $assigner->name]);
            }

            $embedData = [
                'title' => __('discord.ticket.assignment_update_title'),
                'description' => $channelMessage,
                'color' => $assignee ? 0x007bff : 0x6c757d, // Blue for assigned, Gray for unassigned
                'timestamp' => now()->toIso8601String(),
            ];
            $embed = $this->discordMessagingService->createEmbed($embedData);

            if ($embed) {
                $this->discordMessagingService->sendMessageToChannel($ticket->discord_channel_id, '', [$embed]);
            } else {
                $this->discordMessagingService->sendMessageToChannel($ticket->discord_channel_id, $channelMessage); // Fallback to text
            }
            Log::info("NotifyUsersOnTicketAssignment: Sent channel notification for ticket #{$ticket->id} to {$ticket->discord_channel_id}.");
        }

        // Send DM to the new assignee
        if ($assignee && $assignee->discord_id) {
            $assigneeLocale = $assignee->preferred_locale ?? $locale; // Use assignee's locale for DM if available
            App::setLocale($assigneeLocale);

            $dmMessage = __('discord.ticket.assigned_dm_notification', [
                'ticketId' => $ticket->id,
                'ticketTitle' => $ticket->title,
                'channelMention' => $ticket->discord_channel_id ? "<#{$ticket->discord_channel_id}>" : '#unknown-channel',
                'assignerName' => $assigner->name,
            ]);
            
            $embedDataDm = [
                 'title' => __('discord.ticket.assigned_dm_title'),
                 'description' => $dmMessage,
                 'color' => 0x007bff, // Blue
                 'url' => $ticket->discord_channel_id ? "https://discord.com/channels/{$ticket->discord_guild_id}/{$ticket->discord_channel_id}" : null,
                 'timestamp' => now()->toIso8601String(),
            ];
            $dmEmbed = $this->discordMessagingService->createEmbed($embedDataDm);

            if ($dmEmbed) {
                $this->discordMessagingService->sendDirectMessage($assignee->discord_id, '', [$dmEmbed]);
            } else {
                $this->discordMessagingService->sendDirectMessage($assignee->discord_id, $dmMessage); // Fallback to text
            }
            Log::info("NotifyUsersOnTicketAssignment: Sent DM to assignee {$assignee->name} (Discord ID: {$assignee->discord_id}) for ticket #{$ticket->id}. Locale: {$assigneeLocale}");
            App::setLocale($locale); // Reset locale back to the original context if needed
        }
    }
}
