<div>
    <!-- Benachrichtigungen -->
    @if(session('error'))
        <div class="alert alert-error mb-6">
            <svg xmlns="http://www.w3.org/2000/svg" class="stroke-current shrink-0 h-6 w-6" fill="none" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 14l2-2m0 0l2-2m-2 2l-2-2m2 2l2 2m7-2a9 9" />
            </svg>
            <span>{{ session('error') }}</span>
        </div>
    @endif

    <!-- Dynamischer Inhalt: Liste oder Detail/Edit Ansicht -->
    @if($viewMode === 'list')
        <!-- Filter und Suchbereich -->
        <div class="bg-white dark:bg-base-100 p-6 rounded-xl shadow-lg mb-6">
            <div class="mb-6">
                <a href="{{ route('admin.dashboard') }}" class="btn btn-outline btn-neutral gap-2">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18" />
                    </svg>
                    Zurück
                </a>
            </div>
            <div class="flex flex-col md:flex-row gap-4">
                <!-- Suchfeld mit Verzögerung für bessere Performance -->
                <div class="form-control md:w-1/3">
                    <div class="input-group">
                        <input type="text" wire:model.live.debounce.500ms="searchTerm" placeholder="Benutzer durchsuchen..."
                               class="input input-bordered w-full" />
                        <button class="btn btn-square btn-primary">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                            </svg>
                        </button>
                    </div>
                </div>

                <!-- Rollenfilter -->
                <div class="form-control md:w-1/4">
                    <select wire:model.live="role" class="select select-bordered w-full">
                        <option value="all">Alle Rollen</option>
                        @foreach($roles as $roleOption)
                            <option value="{{ $roleOption->name }}">{{ $roleOption->label() }}</option>
                        @endforeach
                    </select>
                </div>
            </div>
        </div>

        <!-- Benutzerliste -->
        <div class="bg-white dark:bg-base-100 rounded-xl shadow-lg overflow-hidden">
            <div class="overflow-x-auto">
                <table class="table w-full">
                    <thead>
                        <tr>
                            <th class="bg-base-200 cursor-pointer" wire:click="sortBy('username')">
                                <div class="flex items-center">
                                    Benutzername
                                    @if($sortField === 'username')
                                        <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 ml-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                            @if($sortDirection === 'asc')
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 15l7-7 7 7" />
                                            @else
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
                                            @endif
                                        </svg>
                                    @endif
                                </div>
                            </th>
                            <th class="bg-base-200">Discord Name</th>
                            <th class="bg-base-200">Rollen</th>
                            <th class="bg-base-200 cursor-pointer" wire:click="sortBy('created_at')">
                                <div class="flex items-center">
                                    Registriert am
                                    @if($sortField === 'created_at')
                                        <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 ml-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                            @if($sortDirection === 'asc')
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 15l7-7 7 7" />
                                            @else
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
                                            @endif
                                        </svg>
                                    @endif
                                </div>
                            </th>
                            <th class="bg-base-200">Aktionen</th>
                        </tr>
                    </thead>
                    <tbody>
                        @forelse($users as $user)
                            <tr class="hover">
                                <td class="flex items-center gap-2">
                                    <div class="avatar">
                                        <div class="w-10 rounded-full">
                                            <img src="{{ $user->getAvatar(['extension' => 'webp', 'size' => 40]) }}" alt="{{ $user->username }}" />
                                        </div>
                                    </div>
                                    <span>{{ $user->username }}</span>
                                </td>
                                <td>{{ $user->global_name }}</td>
                                <td>
                                    <div class="flex flex-wrap gap-1">
                                        @php
                                            $userRoles = [];
                                            foreach($roles as $roleOption) {
                                                if($user->hasRole($roleOption)) {
                                                    $userRoles[] = [
                                                        'label' => $roleOption->label(),
                                                        'isAdmin' => $roleOption === \App\Enums\Role::MINEWACHE_TEAM
                                                    ];
                                                }
                                            }
                                        @endphp

                                        @if($user->hasRole(\App\Enums\Role::MINEWACHE_TEAM))
                                            <div class="badge badge-sm badge-warning">
                                                Minewache Team
                                            </div>
                                        @else
                                            @forelse($userRoles as $role)
                                                <div class="badge badge-sm {{ $role['isAdmin'] ? 'badge-warning' : 'badge-primary' }}">
                                                    {{ $role['label'] }}
                                                </div>
                                            @empty
                                                <div class="badge badge-sm badge-ghost">Keine Rollen</div>
                                            @endforelse
                                        @endif
                                    </div>
                                </td>
                                <td>{{ $user->created_at->format('d.m.Y') }}</td>
                                <td>
                                    <div class="flex gap-2">
                                        <button wire:click="viewUser('{{ $user->id }}')" class="btn btn-sm btn-circle btn-ghost tooltip flex items-center justify-center" data-tip="Details anzeigen">
                                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-primary" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                                            </svg>
                                        </button>
                                        <button wire:click="editUser('{{ $user->id }}')" class="btn btn-sm btn-circle btn-ghost tooltip flex items-center justify-center" data-tip="Bearbeiten">
                                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-secondary" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                                            </svg>
                                        </button>
                                        <button wire:click="syncUserRoles('{{ $user->id }}')" class="btn btn-sm btn-circle btn-ghost tooltip tooltip-left flex items-center justify-center" data-tip="Discord-Rollen synchronisieren">
                                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-info" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                                            </svg>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                        @empty
                            <tr>
                                <td colspan="5" class="text-center py-6">
                                    <div class="flex flex-col items-center justify-center">
                                        <svg xmlns="http://www.w3.org/2000/svg" class="h-12 w-12 text-base-300 mb-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197M13 7a4 4 0 11-8 0 4 4 0 018 0z" />
                                        </svg>
                                        <h3 class="text-lg font-medium">Keine Benutzer gefunden</h3>
                                        <p class="text-sm text-base-content/70">Es wurden keine Benutzer gefunden, die deinen Filterkriterien entsprechen.</p>
                                    </div>
                                </td>
                            </tr>
                        @endforelse
                    </tbody>
                </table>
            </div>

            <!-- Pagination -->
            <div class="p-4">
                {{ $users->links() }}
            </div>
        </div>
    @elseif($viewMode === 'detail')
        <!-- Detailansicht eines Benutzers -->
        <div class="bg-white dark:bg-base-100 rounded-xl shadow-lg p-6">
            <div class="flex justify-between items-center mb-6">
                <button wire:click="backToList" class="btn btn-ghost gap-2">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18" />
                    </svg>
                    Zurück zur Übersicht
                </button>

                <div class="flex items-center gap-4">
                    <button wire:click="editUser('{{ $selectedUser->id }}')" class="btn btn-primary gap-2">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                        </svg>
                        Benutzer bearbeiten
                    </button>
                </div>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                <!-- Benutzerinfo -->
                <div class="md:col-span-1">
                    <div class="flex flex-col items-center p-6 bg-base-200 rounded-lg">
                        <div class="avatar mb-4">
                            <div class="w-24 rounded-full ring ring-primary ring-offset-base-100 ring-offset-2">
                                <img src="{{ $selectedUser->getAvatar(['extension' => 'webp', 'size' => 96]) }}" alt="{{ $selectedUser->username }}" />
                            </div>
                        </div>
                        <h3 class="text-xl font-bold">{{ $selectedUser->global_name }}</h3>
                        <p class="text-sm text-base-content/70">{{ $selectedUser->username }}</p>

                        <div class="divider"></div>

                        <div class="w-full">
                            <div class="flex justify-between mb-2">
                                <span class="text-sm text-base-content/70">Discord ID:</span>
                                <span class="text-sm font-medium">{{ $selectedUser->id }}</span>
                            </div>
                            <div class="flex justify-between mb-2">
                                <span class="text-sm text-base-content/70">Registriert am:</span>
                                <span class="text-sm font-medium">{{ $selectedUser->created_at->format('d.m.Y H:i') }}</span>
                            </div>
                            <div class="flex justify-between mb-2">
                                <span class="text-sm text-base-content/70">Letzte Synchronisation:</span>
                                <span class="text-sm font-medium">{{ $selectedUser->last_synced_at ? $selectedUser->last_synced_at->format('d.m.Y H:i') : 'Nie' }}</span>
                            </div>
                        </div>

                        <div class="mt-4 w-full">
                            <button wire:click="syncUserRoles('{{ $selectedUser->id }}')"
                                   wire:loading.class="loading"
                                   wire:loading.attr="disabled"
                                   class="btn btn-info btn-sm w-full gap-2">
                                <x-heroicon-o-arrow-path class="h-5 w-5 wire:loading.class='animate-spin'" />
                                <span wire:loading.remove>Discord-Rollen synchronisieren</span>
                                <span wire:loading>Synchronisiere...</span>
                            </button>
                            <div class="text-xs text-center mt-2 text-base-content/70">
                                Aktualisiert die Berechtigungen basierend auf den Discord-Rollen des Benutzers
                            </div>
                            <div class="mt-2">
                                <div class="text-xs flex items-center justify-center gap-1">
                                    <span class="w-2 h-2 rounded-full {{ $selectedUser->last_synced_at && $selectedUser->last_synced_at->isAfter(now()->subMinutes(30)) ? 'bg-success' : 'bg-warning' }}"></span>
                                    <span>Bot-Status: {{ $selectedUser->last_synced_at && $selectedUser->last_synced_at->isAfter(now()->subMinutes(30)) ? 'Online' : 'Unbekannt' }}</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Benutzerrollen -->
                <div class="md:col-span-2">
                    <div class="p-6 bg-base-200 rounded-lg h-full">
                        <h3 class="text-lg font-bold mb-4">Rollen und Berechtigungen</h3>

                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                            @if($selectedUser->hasRole(\App\Enums\Role::MINEWACHE_TEAM))
                                <div class="flex items-center p-3 bg-base-100 rounded-lg border-l-4 border-primary">
                                    <div class="flex-1">
                                        <h4 class="font-medium">Minewache-Team</h4>
                                        <p class="text-xs text-base-content/70">minewache-team</p>
                                        <p class="text-xs text-base-content/50 mt-1">Wert: 4294967295</p>
                                    </div>
                                    <div class="badge badge-warning">Aktiv</div>
                                </div>
                            @else
                                @foreach($roles as $roleOption)
                                    @if($roleOption->name !== \App\Enums\Role::MINEWACHE_TEAM->name)
                                    <div class="flex items-center p-3 rounded-lg {{ $selectedUser->hasRole($roleOption) ? 'border-l-4 border-primary' : 'bg-gray-500' }}">
                                        <div class="flex-1">
                                            <h4 class="font-medium">{{ $roleOption->label() }}</h4>
                                            <p class="text-xs text-base-content/70">{{ $roleOption->name }}</p>
                                            <p class="text-xs text-base-content/50 mt-1">Wert: {{ $roleOption->value }}</p>
                                        </div>
                                        @if($selectedUser->hasRole($roleOption))
                                            <div class="badge {{ $roleOption === \App\Enums\Role::MINEWACHE_TEAM ? 'badge-warning' : 'badge-primary' }}">Aktiv</div>
                                        @else
                                            <div class="badge badge-ghost">Inaktiv</div>
                                        @endif
                                    </div>
                                    @endif
                                @endforeach
                            @endif
                        </div>

                        <div class="mt-6 p-4 bg-base-100 rounded-lg">
                            <h4 class="font-medium mb-2">Technische Informationen</h4>
                            <div class="flex justify-between mb-2">
                                <span class="text-sm text-base-content/70">Berechtigungs-Bitmask:</span>
                                <span class="text-sm font-mono">{{ $selectedUser->permissions }}</span>
                            </div>
                            <div class="flex justify-between mb-2">
                                <span class="text-sm text-base-content/70">Binär:</span>
                                <span class="text-sm font-mono">{{ decbin($selectedUser->permissions) }}</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    @elseif($viewMode === 'edit')
        <!-- Bearbeitungsansicht eines Benutzers -->
        <div class="bg-white dark:bg-base-100 rounded-xl shadow-lg p-6">
            <div class="flex justify-between items-center mb-6">
                <button wire:click="backToDetail" class="btn btn-ghost gap-2">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18" />
                    </svg>
                    Zurück zur Detailansicht
                </button>

                <h2 class="text-xl font-bold">Benutzer bearbeiten</h2>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                <!-- Benutzerinfo -->
                <div class="md:col-span-1">
                    <div class="flex flex-col items-center p-6 bg-base-200 rounded-lg">
                        <div class="avatar mb-4">
                            <div class="w-24 rounded-full ring ring-primary ring-offset-base-100 ring-offset-2">
                                <img src="{{ $selectedUser->getAvatar(['extension' => 'webp', 'size' => 96]) }}" alt="{{ $selectedUser->username }}" />
                            </div>
                        </div>
                        <h3 class="text-xl font-bold">{{ $selectedUser->global_name }}</h3>
                        <p class="text-sm text-base-content/70">{{ $selectedUser->username }}</p>

                        <div class="divider"></div>

                        <div class="w-full">
                            <div class="flex justify-between mb-2">
                                <span class="text-sm text-base-content/70">Discord ID:</span>
                                <span class="text-sm font-medium">{{ $selectedUser->discord_id }}</span>
                            </div>
                            <div class="flex justify-between mb-2">
                                <span class="text-sm text-base-content/70">Registriert am:</span>
                                <span class="text-sm font-medium">{{ $selectedUser->created_at->format('d.m.Y H:i') }}</span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Benutzerrollen bearbeiten -->
                <div class="md:col-span-2">
                    <div class="p-6 bg-base-200 rounded-lg h-full">
                        <h3 class="text-lg font-bold mb-4">Rollen und Berechtigungen bearbeiten</h3>

                        <div class="alert alert-info mb-4">
                            <x-heroicon-o-information-circle class="h-6 w-6 shrink-0" />
                            <span>Manuelle Änderungen werden nicht mit Discord synchronisiert. Verwende die Synchronisierungsfunktion, um die Rollen vom Discord-Server zu übernehmen.</span>
                        </div>

                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                            @foreach($roles as $roleOption)
                                <div class="flex items-center p-3 bg-base-100 rounded-lg {{ array_key_exists($roleOption->name, $userRoles) && $userRoles[$roleOption->name] ? 'border-l-4 border-primary' : '' }}">
                                    <div class="flex-1">
                                        <h4 class="font-medium">{{ $roleOption->label() }}</h4>
                                        <p class="text-xs text-base-content/70">{{ $roleOption->name }}</p>
                                        <p class="text-xs text-base-content/50 mt-1">Wert: {{ $roleOption->value }}</p>
                                    </div>
                                    <label class="cursor-pointer">
                                        <input type="checkbox" wire:model="userRoles.{{ $roleOption->name }}" class="toggle {{ $roleOption === \App\Enums\Role::MINEWACHE_TEAM ? 'toggle-warning' : 'toggle-primary' }}" />
                                    </label>
                                </div>
                            @endforeach
                        </div>

                        <!-- Status-Info für Benutzer -->
                        <div class="mt-4 p-4 bg-base-100 rounded-lg">
                            <div class="flex items-center justify-between">
                                <h4 class="font-medium">Synchronisierungsstatus</h4>
                                <span class="flex items-center gap-1">
                                    <span class="w-2 h-2 rounded-full {{ $selectedUser->last_synced_at && $selectedUser->last_synced_at->isAfter(now()->subHours(1)) ? 'bg-success' : 'bg-warning' }}"></span>
                                    <span class="text-sm">{{ $selectedUser->last_synced_at ? $selectedUser->last_synced_at->diffForHumans() : 'Nie synchronisiert' }}</span>
                                </span>
                            </div>
                        </div>

                        <!-- Aktions-Buttons mit verbessertem Feedback -->
                        <div class="flex flex-col md:flex-row justify-between gap-4 mt-6">
                            <button wire:click="syncUserRoles('{{ $selectedUser->id }}')"
                                   wire:loading.class="loading"
                                   wire:loading.attr="disabled"
                                   wire:target="syncUserRoles"
                                   class="btn btn-info gap-2">
                                <x-heroicon-o-arrow-path class="h-5 w-5" wire:loading.class="animate-spin" wire:target="syncUserRoles" />
                                <span wire:loading.remove wire:target="syncUserRoles">Mit Discord synchronisieren</span>
                                <span wire:loading wire:target="syncUserRoles">Synchronisiere...</span>
                            </button>

                            <button wire:click="updateUser"
                                   wire:loading.class="loading"
                                   wire:loading.attr="disabled"
                                   wire:target="updateUser"
                                   class="btn btn-primary gap-2">
                                <x-heroicon-o-check class="h-5 w-5" wire:loading.class="hidden" wire:target="updateUser" />
                                <x-heroicon-o-arrow-path class="h-5 w-5 hidden" wire:loading.class.remove="hidden" wire:loading.class="animate-spin" wire:target="updateUser" />
                                <span wire:loading.remove wire:target="updateUser">Änderungen speichern</span>
                                <span wire:loading wire:target="updateUser">Speichere...</span>
                            </button>
                        </div>

                        <div id="update-status" class="mt-4 hidden">
                            <!-- Wird durch JS-Events gefüllt -->
                        </div>
                    </div>
                </div>
            </div>
        </div>
    @endif

    <!-- JavaScript zur Verarbeitung von Events und UI-Updates -->
    @push('scripts')
        <script>
            document.addEventListener('livewire:initialized', () => {
                // Status-Updates für Benutzeraktualisierung
                const updateStatus = document.getElementById('update-status');

                if (updateStatus) {
                    Livewire.on('update-started', () => {
                        updateStatus.innerHTML = `
                            <div class="alert alert-info shadow-sm animate-fade-in">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 animate-spin" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                                </svg>
                                <span>Benutzerberechtigungen werden aktualisiert...</span>
                            </div>
                        `;
                        updateStatus.classList.remove('hidden');
                    });

                    Livewire.on('update-completed', (event) => {
                        if (event.success) {
                            updateStatus.innerHTML = `
                                <div class="alert alert-success shadow-sm animate-fade-in">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
                                    </svg>
                                    <span>Berechtigungen wurden erfolgreich aktualisiert!</span>
                                </div>
                            `;
                        } else {
                            updateStatus.innerHTML = `
                                <div class="alert alert-error shadow-sm animate-fade-in">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 14l2-2m0 0l2-2m-2 2l-2-2m2 2l2 2m7-2a9 9 0 11-18 0 9 9 0 0118 0z" />
                                    </svg>
                                    <span>Fehler: ${event.error || 'Unbekannter Fehler bei der Aktualisierung'}</span>
                                </div>
                            `;
                        }
                        updateStatus.classList.remove('hidden');

                        // Nach einiger Zeit ausblenden
                        setTimeout(() => {
                            const alert = updateStatus.querySelector('.alert');
                            if (alert) {
                                alert.classList.add('animate-fade-out');
                                setTimeout(() => {
                                    updateStatus.classList.add('hidden');
                                }, 500);
                            }
                        }, 5000);
                    });
                }

                // Statusänderungen markieren
                Livewire.on('user-roles-updated', (event) => {
                    const toggles = document.querySelectorAll('.toggle');

                    // Kurzen Puls-Effekt auf alle Toggles anwenden
                    toggles.forEach(toggle => {
                        toggle.classList.add('animate-pulse');
                        setTimeout(() => {
                            toggle.classList.remove('animate-pulse');
                        }, 1000);
                    });
                });

                // Behandlung von Benachrichtigungen über Event-Bus
                Livewire.on('notify', (event) => {
                    // Diese Funktion könnte in Zukunft genutzt werden,
                    // um Toast-Nachrichten anzuzeigen
                    console.log('Notification:', event.message, 'Type:', event.type);
                });
            });
        </script>
    @endpush
</div>
