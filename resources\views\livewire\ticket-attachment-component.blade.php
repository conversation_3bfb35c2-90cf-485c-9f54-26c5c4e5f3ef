<div class="attachment-container" data-attachment-id="{{ $attachment->id }}">
    @if($attachment->is_processing)
        {{-- Processing Placeholder --}}
        <div class="flex items-center justify-center gap-2 px-3 py-4 text-xs font-medium bg-slate-800/70 text-slate-400 rounded-xl aspect-video attachment-processing">
            <span class="loading loading-spinner loading-xs"></span>
            <span class="truncate max-w-[100px]">{{ Str::limit($attachment->original_filename, 15) }}</span>
        </div>
    @elseif($attachment->has_processing_failed)
        {{-- Failed State --}}
        <div class="flex flex-col items-center justify-center gap-1 px-3 py-4 text-xs font-medium bg-error/20 text-error-content rounded-xl aspect-video attachment-preview">
            <svg class="w-5 h-5 mb-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
            </svg>
            <span class="text-center text-error font-semibold">{{ __('tickets.processing_failed') }}</span>
            <span class="truncate max-w-[120px] text-error/80">{{ Str::limit($attachment->original_filename, 20) }}</span>
            <x-modern-button variant="primary" href="{{ route('tickets.attachments.download', $attachment) }}" title="{{ __('tickets.download') }}">{{ __('tickets.download') }}</x-modern-button>
        </div>
    @elseif($attachment->is_processed)
        {{-- Render based on type --}}
        @if($attachment->is_image)
            <div class="relative group rounded-xl overflow-hidden aspect-video bg-slate-900/50">
                <img src="{{ $attachment->media_url }}"
                     alt="{{ $attachment->original_filename }}"
                     class="w-full h-full object-contain hover:opacity-90 transition-opacity cursor-zoom-in"
                     data-gallery-id="{{ $attachment->ticket_message_id }}"
                     data-gallery-index="{{ $galleryIndex }}"
                     wire:click="openImageGallery('{{ $attachment->ticket_message_id }}', {{ $galleryIndex }})" />

                <div class="absolute bottom-0 left-0 right-0 p-2 bg-gradient-to-t from-black/70 to-transparent opacity-0 group-hover:opacity-100 transition-opacity">
                    <div class="flex justify-between items-center">
                        <span class="text-xs text-white truncate max-w-[80%]">{{ $attachment->original_filename }}</span>
                        <div class="flex gap-1">
                            <button type="button" wire:click="openImageGallery('{{ $attachment->ticket_message_id }}', {{ $galleryIndex }})" class="btn btn-xs btn-circle bg-black/50 hover:bg-black/70 border-0 text-white" title="Zoom">
                                <svg class="w-3.5 h-3.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0zM10 7v3m0 0v3m0-3h3m-3 0H7"></path>
                                </svg>
                            </button>
                            <x-modern-button variant="primary" href="{{ route('tickets.attachments.download', $attachment) }}" title="{{ __('tickets.download') }}"><svg class="w-3.5 h-3.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4"></path>
                                </svg></x-modern-button>
                        </div>
                    </div>
                </div>
            </div>
        @elseif($attachment->is_video)
            <div class="rounded-xl overflow-hidden bg-slate-800/70">
                <div class="relative aspect-video">
                    <video
                        class="w-full h-full object-contain"
                        preload="metadata"
                        poster="{{ $attachment->thumbnail_url }}"
                        data-player-id="video-{{ $attachment->id }}">
                        <source src="{{ $attachment->media_url }}" type="{{ $attachment->mime_type ?? 'video/mp4' }}">
                        {{ __('tickets.video_not_supported') }}
                    </video>
                    <div class="absolute inset-0 flex items-center justify-center bg-black/30 play-overlay cursor-pointer">
                        <div class="w-12 h-12 sm:w-16 sm:h-16 rounded-full bg-blue-500/80 flex items-center justify-center">
                            <svg class="w-6 h-6 sm:w-8 sm:h-8 text-white" fill="currentColor" viewBox="0 0 24 24"><path d="M8 5v14l11-7z"></path></svg>
                        </div>
                    </div>
                </div>
                <div class="p-2 bg-slate-700/50">
                    <div class="flex justify-between items-center">
                        <span class="truncate max-w-[150px] sm:max-w-[200px] text-sm">{{ $attachment->original_filename }}</span>
                        <div class="flex gap-1">
                            <button type="button" wire:click="openVideoModal('{{ $attachment->media_url }}', '{{ $attachment->original_filename }}')"
                                    class="btn btn-xs btn-ghost btn-circle" title="{{ __('tickets.fullscreen') }}">
                                <svg class="w-3.5 h-3.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 8V6a2 2 0 012-2h2M4 16v2a2 2 0 002 2h2m8-18h2a2 2 0 012 2v2m0 10v2a2 2 0 01-2 2h-2"></path>
                                </svg>
                            </button>
                            <x-modern-button variant="ghost" href="{{ route('tickets.attachments.download', $attachment) }}" title="{{ __('tickets.download') }}"><svg class="w-3.5 h-3.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4"></path>
                                </svg></x-modern-button>
                        </div>
                    </div>
                </div>
            </div>
        @elseif($attachment->is_audio)
            <div class="rounded-xl overflow-hidden bg-slate-700/50 p-3">
                <div class="flex items-center gap-3 mb-2">
                    <svg class="w-5 h-5 text-primary" fill="currentColor" viewBox="0 0 20 20"><path d="M18 3a1 1 0 00-1.196-.98l-10 2A1 1 0 006 5v9.114A4.369 4.369 0 005 14c-1.657 0-3 .895-3 2s1.343 2 3 2 3-.895 3-2V7.82l8-1.6v5.894A4.37 4.37 0 0015 12c-1.657 0-3 .895-3 2s1.343 2 3 2 3-.895 3-2V3z"></path></svg>
                    <span class="font-medium text-sm truncate">{{ $attachment->original_filename }}</span>
                </div>
                <audio controls class="w-full h-10" preload="metadata">
                    <source src="{{ $attachment->media_url }}" type="{{ $attachment->mime_type ?? 'audio/mpeg' }}">
                    {{ __('tickets.audio_not_supported') }}
                </audio>
                <div class="flex justify-between items-center mt-2 text-xs text-slate-400">
                    <span>{{ human_filesize($attachment->file_size) }}</span>
                    <x-modern-button variant="ghost" href="{{ route('tickets.attachments.download', $attachment) }}" title="{{ __('tickets.download') }}"><svg class="w-3.5 h-3.5 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4"></path></svg>
                        {{ __('tickets.download') }}</x-modern-button>
                </div>
            </div>
        @elseif($attachment->is_pdf)
            <div class="flex items-center gap-3 p-3 bg-slate-800/70 hover:bg-slate-700/80 rounded-xl transition-all duration-150">
                <div class="w-10 h-10 flex-shrink-0 rounded-lg text-red-400 bg-slate-700/50 flex items-center justify-center">
                    <svg class="w-6 h-6" fill="currentColor" viewBox="0 0 24 24">
                        <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8l-6-6zm-1 11h-2v2h2v-2zm-2-4h2v3h-2V9z"/>
                        <path d="M14 2v6h6"/>
                    </svg>
                </div>
                <div class="flex-1 min-w-0">
                    <p class="text-sm font-medium truncate">{{ $attachment->original_filename }}</p>
                    <p class="text-xs text-slate-400">
                        PDF Document {{ $attachment->file_size ? '• ' . human_filesize($attachment->file_size) : '' }}
                    </p>
                </div>
                <div class="flex-shrink-0">
                    <button type="button"
                            class="btn btn-xs btn-ghost"
                            wire:click="openPdfModal('{{ $attachment->media_url }}', '{{ $attachment->original_filename }}')">
                        <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                        </svg>
                        {{ __('tickets.preview') }}
                    </button>
                    <x-modern-button variant="ghost" href="{{ route('tickets.attachments.download', $attachment) }}" ><svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4"></path>
                        </svg>
                        {{ __('tickets.download') }}</x-modern-button>
                </div>
            </div>
        @else
            {{-- Generic File --}}
            @php
                $fileExtension = pathinfo($attachment->original_filename, PATHINFO_EXTENSION);
                $iconColor = match(strtolower($fileExtension)) {
                    'pdf' => 'text-red-400',
                    'doc', 'docx' => 'text-blue-400',
                    'xls', 'xlsx' => 'text-green-400',
                    'ppt', 'pptx' => 'text-orange-400',
                    'zip', 'rar', '7z' => 'text-yellow-400',
                    'txt', 'log' => 'text-gray-400',
                    default => 'text-purple-400'
                };
                $mediaType = $attachment->media_type ? ucfirst($attachment->media_type) : 'File';
            @endphp
            <div class="flex items-center gap-3 p-3 bg-slate-800/70 hover:bg-slate-700/80 rounded-xl transition-all duration-150">
                <div class="w-10 h-10 flex-shrink-0 rounded-lg {{ $iconColor }} bg-slate-700/50 flex items-center justify-center">
                    <span class="uppercase font-bold text-xs">{{ $fileExtension ?: '?' }}</span>
                </div>
                <div class="flex-1 min-w-0">
                    <p class="text-sm font-medium truncate">{{ $attachment->original_filename }}</p>
                    <p class="text-xs text-slate-400">
                        {{ $mediaType }} {{ $attachment->file_size ? '• ' . human_filesize($attachment->file_size) : '' }}
                    </p>
                </div>
                <div class="flex-shrink-0">
                    <x-modern-button variant="ghost" href="{{ route('tickets.attachments.download', $attachment) }}" ><svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4"></path>
                        </svg>
                        {{ __('tickets.download') }}</x-modern-button>
                </div>
            </div>
        @endif
    @endif
</div>
