<x-app-layout>
    <x-slot name="heading">
        Glassmorphism Beispiele
    </x-slot>
    
    <x-slot name="breadcrumbs">
        <x-breadcrumbs :items="[
            ['label' => 'Beispiele', 'route' => 'examples'],
            ['label' => 'Glassmorphism']
        ]" />
    </x-slot>

    <div class="container mx-auto px-4 py-12">
        <!-- Hero Section Example -->
        <section class="relative py-20 mb-20 rounded-3xl overflow-hidden bg-gradient-to-br from-primary/10 via-base-200 to-secondary/10">
            <!-- Background decorative elements -->
            <div class="absolute top-0 left-0 w-full h-full overflow-hidden pointer-events-none" aria-hidden="true">
                <div class="absolute -top-24 -left-24 w-64 h-64 bg-primary/10 rounded-full blur-3xl"></div>
                <div class="absolute top-1/2 -right-32 w-80 h-80 bg-secondary/10 rounded-full blur-3xl"></div>
                <div class="absolute -bottom-16 left-1/4 w-72 h-72 bg-primary/10 rounded-full blur-3xl"></div>
            </div>
            
            <div class="relative z-10 max-w-5xl mx-auto px-4 flex flex-col md:flex-row items-center gap-8 md:gap-12">
                <div class="md:w-1/2">
                    <x-glassmorphism-card level="light" border="primary-all" padding="p-8 md:p-10" class="text-center md:text-left">
                        <span class="inline-block bg-primary/15 px-4 py-2 rounded-full mb-4 text-primary font-medium text-sm">Glassmorphism Hero</span>
                        <h1 class="text-3xl md:text-4xl lg:text-5xl font-bold mb-6">Moderne UI mit <span class="text-primary">Glassmorphismus</span></h1>
                        <p class="text-lg mb-8 text-base-content/90 leading-relaxed">
                            Entdecke, wie Glassmorphismus deine Benutzeroberfläche auf ein neues Level hebt. Mit subtilen Transparenzen, Unschärfeeffekten und eleganten Rahmen.
                        </p>
                        <div class="flex flex-wrap gap-4 justify-center md:justify-start">
                            <a href="#" class="btn btn-primary">Mehr erfahren</a>
                            <a href="#" class="btn btn-outline">Beispiele ansehen</a>
                        </div>
                    </x-glassmorphism-card>
                </div>
                
                <div class="md:w-1/2 flex justify-center">
                    <x-glassmorphism-card level="dark" border="secondary-all" padding="p-6" class="w-full max-w-md">
                        <div class="text-center">
                            <div class="w-20 h-20 bg-secondary/20 rounded-full flex items-center justify-center mx-auto mb-6">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-10 w-10 text-secondary" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" />
                                </svg>
                            </div>
                            <h2 class="text-2xl font-bold mb-4">Sicher und modern</h2>
                            <p class="mb-6 text-base-content/90">
                                Glassmorphismus bietet nicht nur ein modernes Erscheinungsbild, sondern verbessert auch die Benutzererfahrung durch klare visuelle Hierarchie.
                            </p>
                            <a href="#" class="btn btn-secondary btn-sm">Mehr erfahren</a>
                        </div>
                    </x-glassmorphism-card>
                </div>
            </div>
        </section>
        
        <!-- Features Section Example -->
        <section class="mb-20">
            <div class="text-center mb-12">
                <h2 class="text-3xl font-bold mb-4">Vielseitige Einsatzmöglichkeiten</h2>
                <p class="text-lg text-base-content/80 max-w-2xl mx-auto">
                    Der Glassmorphismus-Effekt kann in verschiedenen Kontexten eingesetzt werden, um Inhalte hervorzuheben und die Benutzeroberfläche zu verbessern.
                </p>
            </div>
            
            <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                <!-- Feature 1 -->
                <x-glassmorphism-card hover="true" border="primary" level="light">
                    <div class="p-3 bg-primary/20 rounded-full w-14 h-14 flex items-center justify-center mb-4">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-8 w-8 text-primary" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6V4m0 2a2 2 0 100 4m0-4a2 2 0 110 4m-6 8a2 2 0 100-4m0 4a2 2 0 110-4m0 4v2m0-6V4m6 6v10m6-2a2 2 0 100-4m0 4a2 2 0 110-4m0 4v2m0-6V4" />
                        </svg>
                    </div>
                    <h3 class="text-xl font-bold mb-2">Anpassbar</h3>
                    <p class="text-base-content/80 mb-4">
                        Passe Transparenz, Rahmen und Schatten an, um den perfekten Look für deine Anwendung zu erzielen.
                    </p>
                    <a href="#" class="text-primary font-medium hover:underline inline-flex items-center">
                        Mehr erfahren
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 ml-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
                        </svg>
                    </a>
                </x-glassmorphism-card>
                
                <!-- Feature 2 -->
                <x-glassmorphism-card hover="true" border="secondary" level="light">
                    <div class="p-3 bg-secondary/20 rounded-full w-14 h-14 flex items-center justify-center mb-4">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-8 w-8 text-secondary" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 21a4 4 0 01-4-4V5a2 2 0 012-2h4a2 2 0 012 2v12a4 4 0 01-4 4zm0 0h12a2 2 0 002-2v-4a2 2 0 00-2-2h-2.343M11 7.343l1.657-1.657a2 2 0 012.828 0l2.829 2.829a2 2 0 010 2.828l-8.486 8.485M7 17h.01" />
                        </svg>
                    </div>
                    <h3 class="text-xl font-bold mb-2">Modern</h3>
                    <p class="text-base-content/80 mb-4">
                        Verleihe deiner Anwendung ein zeitgemäßes Erscheinungsbild mit diesem trendigen Designelement.
                    </p>
                    <a href="#" class="text-secondary font-medium hover:underline inline-flex items-center">
                        Mehr erfahren
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 ml-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
                        </svg>
                    </a>
                </x-glassmorphism-card>
                
                <!-- Feature 3 -->
                <x-glassmorphism-card hover="true" level="light">
                    <div class="p-3 bg-accent/20 rounded-full w-14 h-14 flex items-center justify-center mb-4">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-8 w-8 text-accent" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z" />
                        </svg>
                    </div>
                    <h3 class="text-xl font-bold mb-2">Performant</h3>
                    <p class="text-base-content/80 mb-4">
                        Optimiert für moderne Browser mit Fallbacks für ältere Geräte, um eine gute Performance zu gewährleisten.
                    </p>
                    <a href="#" class="text-accent font-medium hover:underline inline-flex items-center">
                        Mehr erfahren
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 ml-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
                        </svg>
                    </a>
                </x-glassmorphism-card>
            </div>
        </section>
        
        <!-- Testimonials Section Example -->
        <section class="mb-20 relative py-16 rounded-3xl overflow-hidden bg-gradient-to-br from-base-200 via-base-200 to-primary/5">
            <!-- Background decorative elements -->
            <div class="absolute top-0 left-0 w-full h-full overflow-hidden pointer-events-none" aria-hidden="true">
                <div class="absolute top-1/4 left-1/4 w-64 h-64 bg-primary/5 rounded-full blur-3xl"></div>
                <div class="absolute bottom-1/4 right-1/4 w-80 h-80 bg-secondary/5 rounded-full blur-3xl"></div>
            </div>
            
            <div class="relative z-10 max-w-5xl mx-auto px-4">
                <div class="text-center mb-12">
                    <h2 class="text-3xl font-bold mb-4">Was unsere Nutzer sagen</h2>
                    <p class="text-lg text-base-content/80 max-w-2xl mx-auto">
                        Erfahre, wie der Glassmorphismus-Effekt die Benutzererfahrung unserer Kunden verbessert hat.
                    </p>
                </div>
                
                <div class="grid grid-cols-1 md:grid-cols-2 gap-8">
                    <!-- Testimonial 1 -->
                    <x-glassmorphism-card level="light" border="primary-all">
                        <div class="flex flex-col h-full">
                            <div class="mb-4">
                                <svg class="h-8 w-8 text-primary" fill="currentColor" viewBox="0 0 24 24">
                                    <path d="M14.017 21v-7.391c0-5.704 3.731-9.57 8.983-10.609l.995 2.151c-2.432.917-3.995 3.638-3.995 5.849h4v10h-9.983zm-14.017 0v-7.391c0-5.704 3.748-9.57 9-10.609l.996 2.151c-2.433.917-3.996 3.638-3.996 5.849h3.983v10h-9.983z" />
                                </svg>
                            </div>
                            <p class="text-base-content/90 mb-6 flex-grow">
                                "Der Glassmorphismus-Effekt hat unsere Benutzeroberfläche komplett transformiert. Die Benutzer finden sich jetzt viel besser zurecht und die wichtigen Elemente stechen deutlich hervor."
                            </p>
                            <div class="flex items-center">
                                <div class="w-12 h-12 rounded-full bg-primary/20 flex items-center justify-center mr-4">
                                    <span class="text-primary font-bold">MK</span>
                                </div>
                                <div>
                                    <h4 class="font-bold">Max Mustermann</h4>
                                    <p class="text-sm text-base-content/70">Produktmanager</p>
                                </div>
                            </div>
                        </div>
                    </x-glassmorphism-card>
                    
                    <!-- Testimonial 2 -->
                    <x-glassmorphism-card level="light" border="secondary-all">
                        <div class="flex flex-col h-full">
                            <div class="mb-4">
                                <svg class="h-8 w-8 text-secondary" fill="currentColor" viewBox="0 0 24 24">
                                    <path d="M14.017 21v-7.391c0-5.704 3.731-9.57 8.983-10.609l.995 2.151c-2.432.917-3.995 3.638-3.995 5.849h4v10h-9.983zm-14.017 0v-7.391c0-5.704 3.748-9.57 9-10.609l.996 2.151c-2.433.917-3.996 3.638-3.996 5.849h3.983v10h-9.983z" />
                                </svg>
                            </div>
                            <p class="text-base-content/90 mb-6 flex-grow">
                                "Die Kombination aus Transparenz und Unschärfe schafft eine angenehme Tiefe in der Benutzeroberfläche. Unsere Nutzer verbringen jetzt mehr Zeit auf der Seite und die Conversion-Rate ist gestiegen."
                            </p>
                            <div class="flex items-center">
                                <div class="w-12 h-12 rounded-full bg-secondary/20 flex items-center justify-center mr-4">
                                    <span class="text-secondary font-bold">LM</span>
                                </div>
                                <div>
                                    <h4 class="font-bold">Laura Musterfrau</h4>
                                    <p class="text-sm text-base-content/70">UX Designer</p>
                                </div>
                            </div>
                        </div>
                    </x-glassmorphism-card>
                </div>
            </div>
        </section>
        
        <!-- Call to Action Section Example -->
        <section class="mb-12">
            <x-glassmorphism-card level="dark" border="primary-all" padding="p-10 md:p-12" class="text-center">
                <h2 class="text-3xl font-bold mb-6">Bereit, Glassmorphismus in deinem Projekt einzusetzen?</h2>
                <p class="text-lg text-base-content/90 max-w-2xl mx-auto mb-8">
                    Nutze unsere Glassmorphismus-Komponente, um deiner Anwendung ein modernes und elegantes Erscheinungsbild zu verleihen.
                </p>
                <div class="flex flex-wrap justify-center gap-4">
                    <a href="{{ route('design.glassmorphism') }}" class="btn btn-primary btn-lg">
                        Design-Dokumentation ansehen
                    </a>
                    <a href="#" class="btn btn-outline btn-lg">
                        Beispiele erkunden
                    </a>
                </div>
            </x-glassmorphism-card>
        </section>
    </div>
</x-app-layout>
