<?php if (isset($component)) { $__componentOriginal9ac128a9029c0e4701924bd2d73d7f54 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54 = $attributes; } ?>
<?php $component = App\View\Components\AppLayout::resolve([] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('app-layout'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\App\View\Components\AppLayout::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
     <?php $__env->slot('heading', null, []); ?> 
        <?php echo e(__('messages.minewache_mods')); ?>

     <?php $__env->endSlot(); ?>

    <main class="flex flex-col min-h-dvh bg-base-200"
          x-data="{
              search: '<?php echo e($search); ?>',
              category: '<?php echo e($category); ?>',
              sortBy: '<?php echo e($sortBy); ?>',
              requiredOnly: <?php echo e($requiredOnly ? 'true' : 'false'); ?>,
              sizeFilter: '<?php echo e($sizeFilter); ?>',
              showInstallGuide: false,

              // Function to update URL with all filters
              updateFilters() {
                  window.location.href = '<?php echo e(route('mods')); ?>?search=' + this.search +
                                         '&category=' + this.category +
                                         '&sort=' + this.sortBy +
                                         '&required=' + (this.requiredOnly ? '1' : '0') +
                                         '&size=' + this.sizeFilter;
              }
          }">

        <!-- Hero Section -->
        <section class="py-12 bg-base-100">
            <div class="container mx-auto px-4">
                <div class="text-center">
                    <h1 class="text-5xl font-bold text-primary mb-6 animate-fade-in">Minewache Modpack</h1>
                    <p class="text-xl text-base-content/80 mb-8 max-w-3xl mx-auto animate-delayed-fade">
                        Entdecke alle Mods, die wir für das ultimative Minewache-Erlebnis verwenden.
                    </p>

                    <!-- Suchfeld -->
                    <form class="relative max-w-2xl mx-auto mb-8" @submit.prevent="updateFilters()">
                        <?php if (isset($component)) { $__componentOriginal17d23b099a28c4e2c5f6c448deb256e5 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal17d23b099a28c4e2c5f6c448deb256e5 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.modern-input','data' => ['type' => 'text','name' => 'search','placeholder' => 'Suche nach Mods...','variant' => 'outlined','xModel' => 'search','@input.debounce.500ms' => 'updateFilters()']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('modern-input'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['type' => 'text','name' => 'search','placeholder' => 'Suche nach Mods...','variant' => 'outlined','x-model' => 'search','@input.debounce.500ms' => 'updateFilters()']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal17d23b099a28c4e2c5f6c448deb256e5)): ?>
<?php $attributes = $__attributesOriginal17d23b099a28c4e2c5f6c448deb256e5; ?>
<?php unset($__attributesOriginal17d23b099a28c4e2c5f6c448deb256e5); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal17d23b099a28c4e2c5f6c448deb256e5)): ?>
<?php $component = $__componentOriginal17d23b099a28c4e2c5f6c448deb256e5; ?>
<?php unset($__componentOriginal17d23b099a28c4e2c5f6c448deb256e5); ?>
<?php endif; ?>
                        <svg class="absolute left-4 top-1/2 transform -translate-y-1/2 w-5 h-5 text-base-content/50"
                             xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                  d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                        </svg>
                    </form>

                    <!-- Statistiken -->
                    <div class="grid grid-cols-2 md:grid-cols-4 gap-4 max-w-4xl mx-auto">
                        <div class="stat bg-base-200 rounded-xl p-4">
                            <div class="stat-title">Mods</div>
                            <div class="stat-value text-primary"><?php echo e($modCount); ?></div>
                        </div>
                        <div class="stat bg-base-200 rounded-xl p-4">
                            <div class="stat-title">Version</div>
                            <div class="stat-value text-primary">1.12.2</div>
                        </div>
                        <div class="stat bg-base-200 rounded-xl p-4">
                            <div class="stat-title">Forge</div>
                            <div class="stat-value text-primary">14.23.5</div>
                        </div>
                        <div class="stat bg-base-200 rounded-xl p-4">
                            <div class="stat-title">Größe</div>
                            <div class="stat-value text-primary"><?php echo e(number_format($totalSize, 1)); ?>MB</div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Mods Grid -->
        <section class="py-12 bg-base-200">
            <div class="container mx-auto px-4">
                <!-- Filter & Sort -->
                <div class="flex flex-col md:flex-row gap-4 justify-between items-center mb-8">
                    <!-- Sortierung -->
                    <select x-model="sortBy"
                            @change="updateFilters()"
                            class="select select-bordered">
                        <option value="name">Name</option>
                        <option value="downloads">Downloads</option>
                        <option value="size">Größe</option>
                    </select>
                </div>

                <!-- Main Content with Left Filter and Mods Grid -->
                <div class="flex flex-col md:flex-row gap-4">
                    <!-- Left Filter Section -->
                    <div class="md:w-32 bg-base-100 p-2 rounded-lg shadow-md self-start sticky top-20">
                        <h3 class="text-base font-bold mb-3">Filter</h3>

                        <!-- Category Filter -->
                        <div class="mb-4">
                            <h4 class="text-sm font-medium mb-1">Kategorien</h4>
                            <div class="space-y-1">
                                <div class="form-control">
                                    <label class="label py-1 cursor-pointer justify-start gap-2">
                                        <input type="radio" name="category-radio" class="radio radio-sm radio-primary"
                                               :checked="category === ''"
                                               @click="category = ''; updateFilters()">
                                        <span class="label-text text-xs">Alle</span>
                                    </label>
                                </div>
                                <?php $__currentLoopData = ['Performance', 'Gameplay', 'Visuals', 'Utility', 'Library', 'Technology', 'Transportation', 'Roleplay', 'Customization']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $cat): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <div class="form-control">
                                    <label class="label py-1 cursor-pointer justify-start gap-2">
                                        <input type="radio" name="category-radio" class="radio radio-sm radio-primary"
                                               :checked="category === '<?php echo e($cat); ?>'"
                                               @click="category = '<?php echo e($cat); ?>'; updateFilters()">
                                        <span class="label-text text-xs"><?php echo e($cat); ?></span>
                                    </label>
                                </div>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </div>
                        </div>

                        <!-- Size Filter -->
                        <div>
                            <h4 class="text-sm font-medium mb-1">Größe</h4>
                            <div class="form-control">
                                <label class="label py-1 cursor-pointer justify-start gap-2">
                                    <input type="radio" name="size-radio" class="radio radio-sm radio-primary"
                                           :checked="sizeFilter === 'small'"
                                           @click="sizeFilter = 'small'; updateFilters()">
                                    <span class="label-text text-xs">Unter 1MB</span>
                                </label>
                            </div>
                            <div class="form-control">
                                <label class="label py-1 cursor-pointer justify-start gap-2">
                                    <input type="radio" name="size-radio" class="radio radio-sm radio-primary"
                                           :checked="sizeFilter === 'medium'"
                                           @click="sizeFilter = 'medium'; updateFilters()">
                                    <span class="label-text text-xs">1MB - 5MB</span>
                                </label>
                            </div>
                            <div class="form-control">
                                <label class="label py-1 cursor-pointer justify-start gap-2">
                                    <input type="radio" name="size-radio" class="radio radio-sm radio-primary"
                                           :checked="sizeFilter === 'large'"
                                           @click="sizeFilter = 'large'; updateFilters()">
                                    <span class="label-text text-xs">Über 5MB</span>
                                </label>
                            </div>
                            <div class="form-control">
                                <label class="label py-1 cursor-pointer justify-start gap-2">
                                    <input type="radio" name="size-radio" class="radio radio-sm radio-primary"
                                           :checked="sizeFilter === ''"
                                           @click="sizeFilter = ''; updateFilters()">
                                    <span class="label-text text-xs">Alle Größen</span>
                                </label>
                            </div>
                        </div>
                    </div>

                    <!-- Mods Grid -->
                    <div class="flex-1 grid grid-cols-2 gap-3">
                    <?php $__currentLoopData = $mods; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $mod): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <div class="card bg-base-100 hover:shadow-xl transition-all duration-300 group h-40">
                            <div class="card-body p-3">
                                <div class="flex items-start gap-3">
                                    <div class="w-12 h-12 rounded-lg bg-base-200 overflow-hidden">
                                        <img src="<?php echo e($mod['icon_url']); ?>"
                                             alt="<?php echo e($mod['name']); ?> icon"
                                             class="w-full h-full object-cover transform group-hover:scale-110 transition-transform duration-300">
                                    </div>
                                    <div class="flex-1 min-w-0">
                                        <h3 class="card-title text-base font-bold group-hover:text-primary transition-colors truncate">
                                            <?php echo e($mod['name']); ?>

                                        </h3>
                                        <p class="text-xs text-base-content/70">by <?php echo e($mod['author']); ?></p>
                                        <p class="text-xs mt-1 line-clamp-2"><?php echo e($mod['description']); ?></p>
                                        <div class="flex flex-wrap items-center gap-2 mt-2">
                                            <span class="badge badge-sm badge-outline"><?php echo e($mod['category']); ?></span>
                                            <span class="text-xs text-base-content/60">
                                                <i class="fas fa-download mr-1"></i> <?php echo e(number_format($mod['downloads'])); ?>

                                            </span>
                                            <span class="text-xs text-base-content/60">
                                                <i class="fas fa-weight-hanging mr-1"></i> <?php echo e($mod['size_mb']); ?>MB
                                            </span>
                                            <?php if($mod['required']): ?>
                                            <span class="badge badge-sm badge-primary">Required</span>
                                            <?php endif; ?>
                                        </div>
                                    </div>
                                </div>
                                <div class="card-actions justify-end mt-3">
                                    <?php if (isset($component)) { $__componentOriginal19987cd45c699eefead8b7a8ac222798 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal19987cd45c699eefead8b7a8ac222798 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.modern-button','data' => ['variant' => 'ghost','href' => ''.e($mod['curseforge_url']).'','target' => '_blank']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('modern-button'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['variant' => 'ghost','href' => ''.e($mod['curseforge_url']).'','target' => '_blank']); ?>CurseForge
                                        <svg xmlns="http://www.w3.org/2000/svg" class="h-3 w-3" fill="none"
                                             viewBox="0 0 24 24" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                                  d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14" />
                                        </svg> <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal19987cd45c699eefead8b7a8ac222798)): ?>
<?php $attributes = $__attributesOriginal19987cd45c699eefead8b7a8ac222798; ?>
<?php unset($__attributesOriginal19987cd45c699eefead8b7a8ac222798); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal19987cd45c699eefead8b7a8ac222798)): ?>
<?php $component = $__componentOriginal19987cd45c699eefead8b7a8ac222798; ?>
<?php unset($__componentOriginal19987cd45c699eefead8b7a8ac222798); ?>
<?php endif; ?>
                                </div>
                            </div>
                        </div>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </div>
                </div>

                <!-- Download Section -->
                <div class="text-center mt-20 pt-8 space-y-4">
                    <?php if (isset($component)) { $__componentOriginal19987cd45c699eefead8b7a8ac222798 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal19987cd45c699eefead8b7a8ac222798 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.modern-button','data' => ['variant' => 'primary','size' => 'lg','@click' => 'showInstallGuide = true']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('modern-button'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['variant' => 'primary','size' => 'lg','@click' => 'showInstallGuide = true']); ?><svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none"
                             viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                  d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4" />
                        </svg>
                        Modpack Installieren <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal19987cd45c699eefead8b7a8ac222798)): ?>
<?php $attributes = $__attributesOriginal19987cd45c699eefead8b7a8ac222798; ?>
<?php unset($__attributesOriginal19987cd45c699eefead8b7a8ac222798); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal19987cd45c699eefead8b7a8ac222798)): ?>
<?php $component = $__componentOriginal19987cd45c699eefead8b7a8ac222798; ?>
<?php unset($__componentOriginal19987cd45c699eefead8b7a8ac222798); ?>
<?php endif; ?>
                </div>
            </div>
        </section>

        <!-- Installation Guide Modal -->
        <div x-show="showInstallGuide"
             class="fixed inset-0  backdrop-blur-sm z-50 flex items-center justify-center p-4">
            <div class="bg-base-100 rounded-lg max-w-2xl w-full p-6" @click.away="showInstallGuide = false">
                <h2 class="text-2xl font-bold mb-4">Installation Guide</h2>
                <div class="prose">
                    <ol class="list-decimal list-inside">
                        <li class="mb-4">Installiere den CurseForge Client
                            <a href="https://www.curseforge.com/download/app"
                               target="_blank"
                               class="link link-primary">hier</a>
                        </li>
                        <li class="mb-4">Öffne den CurseForge Client und wähle Minecraft aus</li>
                        <li class="mb-4">Suche Nach Die_Minewache</li>
                        <li class="mb-4">Installiere das Modpack</li>
                    </ol>
                </div>
                <div class="mt-6 flex justify-end">
                    <?php if (isset($component)) { $__componentOriginal19987cd45c699eefead8b7a8ac222798 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal19987cd45c699eefead8b7a8ac222798 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.modern-button','data' => ['variant' => 'primary','size' => 'md','@click' => 'showInstallGuide = false']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('modern-button'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['variant' => 'primary','size' => 'md','@click' => 'showInstallGuide = false']); ?>Verstanden <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal19987cd45c699eefead8b7a8ac222798)): ?>
<?php $attributes = $__attributesOriginal19987cd45c699eefead8b7a8ac222798; ?>
<?php unset($__attributesOriginal19987cd45c699eefead8b7a8ac222798); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal19987cd45c699eefead8b7a8ac222798)): ?>
<?php $component = $__componentOriginal19987cd45c699eefead8b7a8ac222798; ?>
<?php unset($__componentOriginal19987cd45c699eefead8b7a8ac222798); ?>
<?php endif; ?>
                </div>
            </div>
        </div>
    </main>
 <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54)): ?>
<?php $attributes = $__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54; ?>
<?php unset($__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal9ac128a9029c0e4701924bd2d73d7f54)): ?>
<?php $component = $__componentOriginal9ac128a9029c0e4701924bd2d73d7f54; ?>
<?php unset($__componentOriginal9ac128a9029c0e4701924bd2d73d7f54); ?>
<?php endif; ?>
<?php /**PATH C:\Users\<USER>\Documents\GitHub\minewache-website\resources\views/mods.blade.php ENDPATH**/ ?>