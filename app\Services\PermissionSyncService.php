<?php

namespace App\Services;

use App\Models\User;
use App\Enums\Role; // Assuming App\Enums\Role exists as per PermissionController
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\DB; // For potential transaction usage

class PermissionSyncService
{
    /**
     * Synchronizes a user's permissions in the Laravel application based on their Discord roles.
     *
     * @param string $discordUserId The user's Discord ID.
     * @param array $discordRoleIds An array of Discord role IDs the user currently has.
     * @return bool True if successful, false otherwise.
     */
    public function syncUserPermissionsFromDiscordRoles(string $discordUserId, array $discordRoleIds): bool
    {
        Log::info("PermissionSyncService: Starting permission sync for Discord user {$discordUserId}.");

        // The User model's primary key is typically 'id', but it stores the Discord ID.
        // Based on PermissionController, it seems $user->id IS the Discord ID.
        // If there's a separate 'discord_id' column, this query should be User::where('discord_id', $discordUserId)->first();
        $user = User::where('id', $discordUserId)->first(); // Assuming 'id' column stores Discord ID as per PermissionController context

        if (!$user) {
            Log::warning("PermissionSyncService: User with Discord ID {$discordUserId} not found in Laravel.");
            return false;
        }

        Log::debug("PermissionSyncService: Found user {$user->name} (Laravel ID: {$user->getAuthIdentifier()}) for Discord ID {$discordUserId}.");

        $bitmask = $this->calculatePermissionsBitmask($discordRoleIds);

        Log::debug("PermissionSyncService: Calculated permissions bitmask for Discord user {$discordUserId} is {$bitmask} (" . decbin($bitmask) . ").");

        try {
            // Using a transaction can be good if there are multiple related updates
            DB::transaction(function () use ($user, $bitmask) {
                $user->permissions = $bitmask;
                $user->last_synced_at = now();
                $user->save();
            });

            Log::info("PermissionSyncService: Successfully updated permissions for user {$user->name} (Discord ID: {$discordUserId}). New bitmask: {$bitmask}.");
            return true;

        } catch (\Exception $e) {
            Log::error("PermissionSyncService: Failed to update user permissions for Discord ID {$discordUserId}.", [
                'exception' => $e,
                'user_id' => $user->getAuthIdentifier(), // Laravel user ID
                'discord_id' => $discordUserId
            ]);
            return false;
        }
    }

    /**
     * Calculates the permission bitmask from an array of Discord role IDs.
     *
     * @param array $discordRoleIds
     * @return int
     */
    public function calculatePermissionsBitmask(array $discordRoleIds): int
    {
        $roleMapping = $this->getRoleMapping();
        $bitmask = 0;
        $matchedRolesForLog = [];

        foreach ($discordRoleIds as $roleId) {
            if (isset($roleMapping[$roleId])) {
                $roleEnumName = $roleMapping[$roleId]['name'];
                $roleEnumValue = $roleMapping[$roleId]['value'];
                $bitmask |= $roleEnumValue;
                $matchedRolesForLog[$roleId] = [
                    'name' => $roleEnumName,
                    'value' => $roleEnumValue,
                ];
            }
        }

        Log::debug('PermissionSyncService: Role mapping and bitmask calculation details', [
            'input_discord_roles' => $discordRoleIds,
            'matched_laravel_roles' => $matchedRolesForLog,
            'resulting_bitmask' => $bitmask,
            'resulting_binary' => decbin($bitmask)
        ]);

        return $bitmask;
    }

    /**
     * Gets the mapping of Discord Role IDs to Laravel Role Enum names and values.
     * This is based on PermissionController::getRoleMapping()
     *
     * @return array Format: ['discord_role_id' => ['name' => 'LARAVEL_ROLE_ENUM_NAME', 'value' => Role::LARAVEL_ROLE_ENUM_NAME->value]]
     */
    public function getRoleMapping(): array
    {
        $mapping = [];
        // This structure is slightly different from PermissionController to include the Enum value directly
        // for easier use in calculatePermissionsBitmask.
        $rawMapping = [
            // Discord role ID => Role enum mapping (name only)
            '1071024925437067294' => 'MINEWACHE_TEAM',
            '1183491860304494642' => 'SCHAUSPIELER',
            '1033491844715257866' => 'BUILDER',
            '1033523833514242068' => 'DESIGNER',
            '1033474411963109466' => 'SYNCHRONSPRECHER',
            '1041839025696292885' => 'MODELLIERER',
            '1033491763144433724' => 'DEVELOPER',
            '1033491696144621658' => 'KAMERAMANN',
            '1033491854995505213' => 'CUTTER',
            '1031205205934604439' => 'ANGENOMMEN',
            '1113859130961166468' => 'ANGENOMMENPLUS',
            // New roles for the other guild (from PermissionController)
            '1335685797080465434' => 'MINEWACHE_TEAM',
            '1335685797055041541' => 'SCHAUSPIELER',
            '1335685797055041544' => 'BUILDER',
            '1335685797067755581' => 'DESIGNER',
            '1335685797055041539' => 'SYNCHRONSPRECHER',
            '1335685797067755582' => 'MODELLIERER',
            '1335685797067755586' => 'DEVELOPER',
            '1335685797067755584' => 'KAMERAMANN',
            '1335685797067755585' => 'CUTTER',
            '1335685797080465428' => 'ANGENOMMEN',
            '1335685797080465429' => 'ANGENOMMENPLUS',
        ];

        foreach ($rawMapping as $discordRoleId => $roleEnumName) {
            if (defined(Role::class . '::' . $roleEnumName)) {
                $mapping[$discordRoleId] = [
                    'name' => $roleEnumName,
                    'value' => constant(Role::class . '::' . $roleEnumName)->value,
                ];
            } else {
                Log::warning("PermissionSyncService: Role Enum '{$roleEnumName}' not found in App\Enums\Role for Discord Role ID {$discordRoleId}.");
            }
        }
        return $mapping;
    }
}
?>
