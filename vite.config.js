import { defineConfig } from 'vite';
import laravel from 'laravel-vite-plugin';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

export default defineConfig({
    plugins: [
        laravel({
            input: [
                'resources/css/app.css',
                'resources/css/breeze-components.css',
                'resources/css/modern-ui.css',
                'resources/css/ticket-view-custom.css',
                'resources/js/app.js',
                'resources/js/ticket-view-functions.js',
                'resources/js/ticket-view-custom.js'
            ],
            refresh: true,
        }),
    ],
    define: {
        // Pass app environment to the frontend
        'import.meta.env.VITE_APP_ENV': JSON.stringify(process.env.APP_ENV || 'local'),
        'import.meta.env.VITE_REVERB_APP_KEY': JSON.stringify(process.env.REVERB_APP_KEY || 'minewache_key'),
        'import.meta.env.VITE_REVERB_HOST': JSON.stringify(process.env.REVERB_HOST || 'localhost'),
        'import.meta.env.VITE_REVERB_PORT': JSON.stringify(process.env.REVERB_PORT || '6001'),
        'import.meta.env.VITE_REVERB_SCHEME': JSON.stringify(process.env.REVERB_SCHEME || 'http'),
    },
});
