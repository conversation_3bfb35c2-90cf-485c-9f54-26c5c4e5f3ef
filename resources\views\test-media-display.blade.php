<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Media Display Test</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/alpinejs@3.x.x/dist/cdn.min.js" defer></script>
</head>
<body class="bg-gray-100 dark:bg-gray-900 text-gray-900 dark:text-white">
    <div class="container mx-auto px-4 py-8">
        <h1 class="text-2xl font-bold mb-6">Media Display Test</h1>

        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            @foreach($attachments as $attachment)
                <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-4">
                    <h2 class="text-lg font-semibold mb-2">{{ $attachment->original_filename }}</h2>
                    <p class="text-sm text-gray-600 dark:text-gray-400 mb-2">
                        Type: {{ $attachment->media_type ?? 'Unknown' }} |
                        Status: {{ $attachment->processing_status }}
                    </p>

                    @if($attachment->is_image && $attachment->is_processed)
                        <div class="relative group">
                            <img src="{{ $attachment->thumbnail_url ?? $attachment->media_url }}"
                                 alt="{{ $attachment->original_filename }}"
                                 class="max-w-full max-h-[200px] rounded-lg cursor-pointer object-contain"
                                 onclick="openImageModal('{{ $attachment->media_url }}', '{{ $attachment->original_filename }}')" />
                        </div>
                    @elseif($attachment->is_video && $attachment->is_processed)
                        <div class="relative group w-full">
                            <video controls class="w-full rounded-lg">
                                <source src="{{ $attachment->media_url }}" type="video/mp4">
                                Video not supported
                            </video>
                            <div class="mt-2">
                                <button onclick="openVideoModal('{{ $attachment->media_url }}', '{{ $attachment->original_filename }}')" class="text-blue-600 dark:text-blue-400 hover:underline text-sm">
                                    View Fullscreen
                                </button>
                            </div>
                        </div>
                    @elseif($attachment->is_audio && $attachment->is_processed)
                        <div class="w-full">
                            <audio controls class="w-full">
                                @php
                                    $audioFormat = $attachment->media_metadata['format'] ?? pathinfo($attachment->processed_file_path ?? $attachment->file_path, PATHINFO_EXTENSION);
                                    $mimeType = match($audioFormat) {
                                        'mp3' => 'audio/mpeg',
                                        'wav' => 'audio/wav',
                                        'ogg' => 'audio/ogg',
                                        'opus' => 'audio/opus',
                                        'aac' => 'audio/aac',
                                        'm4a' => 'audio/mp4',
                                        default => 'audio/mpeg'
                                    };
                                @endphp
                                <source src="{{ $attachment->media_url }}" type="{{ $mimeType }}">
                                <source src="{{ $attachment->download_url }}" type="{{ $attachment->mime_type }}">
                                Audio not supported
                            </audio>
                        </div>
                    @elseif($attachment->is_pdf && $attachment->is_processed)
                        <div class="relative group w-full">
                            <div class="relative bg-gray-100 dark:bg-gray-700 rounded-lg p-2 cursor-pointer" onclick="openPdfModal('{{ $attachment->media_url }}', '{{ $attachment->original_filename }}')">
                                @if($attachment->thumbnail_path)
                                    <img src="{{ $attachment->thumbnail_url }}"
                                         alt="{{ $attachment->original_filename }}"
                                         class="max-w-full max-h-[150px] mx-auto object-contain" />
                                @else
                                    <div class="flex items-center justify-center h-[150px]">
                                        <svg class="w-16 h-16 text-gray-400" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                                            <path fill-rule="evenodd" d="M4 4a2 2 0 012-2h4.586A2 2 0 0112 2.586L15.414 6A2 2 0 0116 7.414V16a2 2 0 01-2 2H6a2 2 0 01-2-2V4zm2 6a1 1 0 011-1h6a1 1 0 110 2H7a1 1 0 01-1-1zm1 3a1 1 0 100 2h6a1 1 0 100-2H7z" clip-rule="evenodd"></path>
                                        </svg>
                                    </div>
                                @endif
                            </div>
                            <div class="mt-2">
                                <button onclick="openPdfModal('{{ $attachment->media_url }}', '{{ $attachment->original_filename }}')" class="text-blue-600 dark:text-blue-400 hover:underline text-sm">
                                    View PDF
                                </button>
                            </div>
                        </div>
                    @elseif($attachment->is_processing)
                        <div class="flex items-center gap-1 px-2 py-1 text-xs font-medium bg-yellow-100 text-yellow-800 dark:bg-yellow-800 dark:text-yellow-100 rounded-lg">
                            <svg class="w-3 h-3 animate-spin" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                                <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                            </svg>
                            Processing...
                        </div>
                    @elseif($attachment->has_processing_failed)
                        <div class="flex items-center gap-1 px-2 py-1 text-xs font-medium bg-red-100 text-red-800 dark:bg-red-800 dark:text-red-100 rounded-lg">
                            <svg class="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                            Processing failed: {{ $attachment->processing_error }}
                        </div>
                    @else
                        <div class="flex items-center gap-1 px-2 py-1 text-xs font-medium bg-gray-200 hover:bg-gray-300 text-gray-900 dark:bg-gray-600 dark:hover:bg-gray-500 dark:text-white rounded-lg">
                            <svg class="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15.172 7l-6.586 6.586a2 2 0 102.828 2.828l6.414-6.586a4 4 0 00-5.656-5.656l-6.415 6.585a6 6 0 108.486 8.486L20.5 13"></path>
                            </svg>
                            {{ $attachment->original_filename }}
                        </div>
                    @endif

                    <div class="mt-4">
                        <a href="{{ route('tickets.attachments.download', $attachment) }}" class="text-blue-600 dark:text-blue-400 hover:underline text-sm">
                            Download
                        </a>
                    </div>

                    @if($attachment->is_processed && $attachment->media_metadata)
                        <div class="mt-2 text-xs text-gray-600 dark:text-gray-400">
                            <details>
                                <summary class="cursor-pointer">Metadata</summary>
                                <pre class="mt-1 p-2 bg-gray-100 dark:bg-gray-700 rounded overflow-auto">{{ json_encode($attachment->media_metadata, JSON_PRETTY_PRINT) }}</pre>
                            </details>
                        </div>
                    @endif
                </div>
            @endforeach
        </div>
    </div>

    <!-- Video Modal -->
    <div id="videoModal" class="fixed inset-0 z-50 hidden overflow-y-auto">
        <div class="flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
            <div class="fixed inset-0 transition-opacity" aria-hidden="true">
                <div class="absolute inset-0 bg-gray-900 opacity-75"></div>
            </div>
            <span class="hidden sm:inline-block sm:align-middle sm:h-screen" aria-hidden="true">&#8203;</span>
            <div class="inline-block align-bottom bg-white dark:bg-gray-800 rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-4xl sm:w-full">
                <div class="absolute top-0 right-0 pt-2 pr-2 z-10">
                    <button type="button" onclick="closeVideoModal()" class="text-gray-400 hover:text-gray-500 focus:outline-none">
                        <span class="sr-only">Close</span>
                        <svg class="h-6 w-6" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                        </svg>
                    </button>
                </div>
                <div class="p-4">
                    <h3 id="videoModalTitle" class="text-lg font-medium text-gray-900 dark:text-white mb-2"></h3>
                    <div class="aspect-video bg-black rounded-lg overflow-hidden">
                        <video id="videoPlayer" class="w-full h-full" controls autoplay>
                            <source id="videoSource" src="" type="video/mp4">
                            Video not supported
                        </video>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Image Modal -->
    <div id="imageModal" class="fixed inset-0 z-50 hidden overflow-y-auto">
        <div class="flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
            <div class="fixed inset-0 transition-opacity" aria-hidden="true">
                <div class="absolute inset-0 bg-gray-900 opacity-75"></div>
            </div>
            <span class="hidden sm:inline-block sm:align-middle sm:h-screen" aria-hidden="true">&#8203;</span>
            <div class="inline-block align-bottom bg-white dark:bg-gray-800 rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-5xl sm:w-full">
                <div class="absolute top-0 right-0 pt-2 pr-2 z-10">
                    <button type="button" onclick="closeImageModal()" class="text-gray-400 hover:text-gray-500 focus:outline-none">
                        <span class="sr-only">Close</span>
                        <svg class="h-6 w-6" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                        </svg>
                    </button>
                </div>
                <div class="p-4">
                    <h3 id="imageModalTitle" class="text-lg font-medium text-gray-900 dark:text-white mb-2"></h3>
                    <div class="flex justify-center bg-gray-100 dark:bg-gray-900 rounded-lg overflow-hidden">
                        <img id="imageModalContent" class="max-h-[80vh] max-w-full object-contain" src="" alt="">
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- PDF Modal -->
    <div id="pdfModal" class="fixed inset-0 z-50 hidden overflow-y-auto">
        <div class="flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
            <div class="fixed inset-0 transition-opacity" aria-hidden="true">
                <div class="absolute inset-0 bg-gray-900 opacity-75"></div>
            </div>
            <span class="hidden sm:inline-block sm:align-middle sm:h-screen" aria-hidden="true">&#8203;</span>
            <div class="inline-block align-bottom bg-white dark:bg-gray-800 rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-5xl sm:w-full">
                <div class="absolute top-0 right-0 pt-2 pr-2 z-10">
                    <button type="button" onclick="closePdfModal()" class="text-gray-400 hover:text-gray-500 focus:outline-none">
                        <span class="sr-only">Close</span>
                        <svg class="h-6 w-6" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                        </svg>
                    </button>
                </div>
                <div class="p-4">
                    <h3 id="pdfModalTitle" class="text-lg font-medium text-gray-900 dark:text-white mb-2"></h3>
                    <div class="bg-gray-100 dark:bg-gray-900 rounded-lg overflow-hidden">
                        <iframe id="pdfViewer" class="w-full h-[80vh]" src="" frameborder="0"></iframe>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Video modal functions
        function openVideoModal(videoUrl, title) {
            const modal = document.getElementById('videoModal');
            const videoSource = document.getElementById('videoSource');
            const videoPlayer = document.getElementById('videoPlayer');
            const videoTitle = document.getElementById('videoModalTitle');

            videoSource.src = videoUrl;
            videoTitle.textContent = title;
            videoPlayer.load();

            modal.classList.remove('hidden');
            document.body.classList.add('overflow-hidden');
        }

        function closeVideoModal() {
            const modal = document.getElementById('videoModal');
            const videoPlayer = document.getElementById('videoPlayer');

            videoPlayer.pause();
            modal.classList.add('hidden');
            document.body.classList.remove('overflow-hidden');
        }

        // Image modal functions
        function openImageModal(imageUrl, title) {
            const modal = document.getElementById('imageModal');
            const imageElement = document.getElementById('imageModalContent');
            const imageTitle = document.getElementById('imageModalTitle');

            imageElement.src = imageUrl;
            imageElement.alt = title;
            imageTitle.textContent = title;

            modal.classList.remove('hidden');
            document.body.classList.add('overflow-hidden');
        }

        function closeImageModal() {
            const modal = document.getElementById('imageModal');
            modal.classList.add('hidden');
            document.body.classList.remove('overflow-hidden');
        }

        // PDF modal functions
        function openPdfModal(pdfUrl, title) {
            const modal = document.getElementById('pdfModal');
            const pdfViewer = document.getElementById('pdfViewer');
            const pdfTitle = document.getElementById('pdfModalTitle');

            // Try to use the browser's built-in PDF viewer first
            // If that fails, fall back to Google Docs viewer
            try {
                // For modern browsers that support PDF viewing
                pdfViewer.src = pdfUrl;
                pdfTitle.textContent = title;

                // Set up a fallback in case the PDF doesn't load properly
                pdfViewer.onerror = function() {
                    pdfViewer.src = 'https://docs.google.com/viewer?url=' + encodeURIComponent(pdfUrl) + '&embedded=true';
                };

                // Also set a timeout to check if the PDF loaded correctly
                setTimeout(function() {
                    // If the PDF viewer is empty or has an error, use Google Docs viewer
                    if (pdfViewer.contentDocument &&
                        (pdfViewer.contentDocument.body.innerHTML === '' ||
                         pdfViewer.contentDocument.body.querySelector('error'))) {
                        pdfViewer.src = 'https://docs.google.com/viewer?url=' + encodeURIComponent(pdfUrl) + '&embedded=true';
                    }
                }, 1000);
            } catch (e) {
                // Fallback to Google Docs viewer
                pdfViewer.src = 'https://docs.google.com/viewer?url=' + encodeURIComponent(pdfUrl) + '&embedded=true';
                pdfTitle.textContent = title;
            }

            modal.classList.remove('hidden');
            document.body.classList.add('overflow-hidden');
        }

        function closePdfModal() {
            const modal = document.getElementById('pdfModal');
            const pdfViewer = document.getElementById('pdfViewer');

            // Clear the iframe source to stop loading
            pdfViewer.src = '';
            modal.classList.add('hidden');
            document.body.classList.remove('overflow-hidden');
        }
    </script>
</body>
</html>
