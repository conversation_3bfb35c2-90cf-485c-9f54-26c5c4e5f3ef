
import React, { useState, useEffect } from 'react';
import { GlassCard } from './common/GlassCard';
import { KeyIcon, CheckCircleIcon, ExclamationCircleIcon } from './common/Icons';
import { API_KEY_REGEX } from '../constants';

interface ApiKeyInputProps {
  currentKey: string; 
  onSaveKey: (key: string) => void;
  error: string | null; // Error from parent (e.g. API validation error)
  clearApiError: () => void; // Function to clear parent's API error
}

export const ApiKeyInput: React.FC<ApiKeyInputProps> = ({ currentKey, onSaveKey, error, clearApiError }) => {
  const [inputKey, setInputKey] = useState('');
  const [showSuccess, setShowSuccess] = useState(false);
  const [formatError, setFormatError] = useState<string | null>(null);

  // If parent passes an error (e.g. API call failed due to bad key), reflect it
  useEffect(() => {
    if (error) {
      setFormatError(error); // Show parent's error
      // Potentially clear inputKey if parent error means the submitted key was bad
      // setInputKey(''); // Clears field if parent had an issue with the key
    }
  }, [error]);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newKey = e.target.value;
    setInputKey(newKey);
    if (formatError || error) { // Clear previous errors on new input
        setFormatError(null);
        clearApiError(); 
    }
    if (newKey.trim() && !API_KEY_REGEX.test(newKey.trim())) {
      setFormatError("Invalid API key format. Key should start with 'AIza' and be 39 characters long.");
    } else {
      setFormatError(null);
    }
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    const trimmedKey = inputKey.trim();
    if (trimmedKey && API_KEY_REGEX.test(trimmedKey)) {
      onSaveKey(trimmedKey);
      // Success message is now primarily handled by parent (API key set message)
      // setInputKey(''); // Parent will re-render, this component might unmount or receive new props
      setShowSuccess(true); // Brief local success indication
      setTimeout(() => setShowSuccess(false), 2000); 
      setFormatError(null); // Clear format error on successful save attempt
    } else if (trimmedKey && !API_KEY_REGEX.test(trimmedKey)) {
        setFormatError("Invalid API key format. Cannot save.");
    } else if (!trimmedKey) {
        setFormatError("API key cannot be empty.");
    }
  };

  const isKeyValidFormat = inputKey.trim() ? API_KEY_REGEX.test(inputKey.trim()) : false;
  const canSubmit = inputKey.trim() && isKeyValidFormat;

  return (
    <GlassCard className="p-6 sm:p-8 mb-8 animate-fadeIn">
      <form onSubmit={handleSubmit} className="space-y-6">
        <div className="text-center">
          <KeyIcon className="w-12 h-12 mx-auto mb-3 text-orange-500" />
          <h2 className="text-2xl font-semibold text-slate-100">Enter Your Gemini API Key</h2>
          <p className="text-sm text-slate-400 mt-1">
            Your API key is required to analyze videos. It's stored locally in your browser.
          </p>
        </div>

        <div>
          <label htmlFor="apiKey" className="block text-sm font-medium text-slate-300 mb-1">
            Gemini API Key
          </label>
          <input
            type="password"
            id="apiKey"
            value={inputKey}
            onChange={handleInputChange}
            className={`w-full px-4 py-3 bg-slate-700/50 border rounded-lg text-slate-100 focus:ring-2 outline-none transition-colors ${
              formatError ? 'border-red-500 focus:ring-red-500 focus:border-red-500' : 'border-slate-600 focus:ring-orange-500 focus:border-orange-500'
            }`}
            placeholder="Enter your API key here (e.g., AIza...)"
            required
            aria-describedby="apiKeyHelp apiKeyError"
            autoComplete="off"
          />
          <p id="apiKeyHelp" className="mt-2 text-xs text-slate-500">
            Obtain a key from Google AI Studio. Example format: AIzaSy***********************************
          </p>
          {formatError && (
            <p id="apiKeyError" className="mt-2 text-xs text-red-400 flex items-center">
              <ExclamationCircleIcon className="w-4 h-4 mr-1 flex-shrink-0" />
              {formatError}
            </p>
          )}
        </div>
        
        {showSuccess && !formatError && !error && ( // Only show local success if no other errors
            <div className="flex items-center text-green-400 p-3 bg-green-800/30 rounded-lg my-2">
                <CheckCircleIcon className="w-5 h-5 mr-2 flex-shrink-0"/>
                <span>API Key format accepted. Saving...</span>
            </div>
        )}

        <button
          type="submit"
          className="btn-primary w-full flex items-center justify-center group disabled:opacity-50 disabled:cursor-not-allowed"
          disabled={!canSubmit || !!error} // Also disable if parent has an error
        >
          <KeyIcon className="w-5 h-5 mr-2 transition-transform duration-300 group-hover:rotate-[-15deg]" />
          Save API Key
        </button>
      </form>
    </GlassCard>
  );
};