<?php $attributes ??= new \Illuminate\View\ComponentAttributeBag;

$__newAttributes = [];
$__propNames = \Illuminate\View\ComponentAttributeBag::extractPropNames(([
    'ticket',
    'showAiConsentPrompt',
    'aiResponsePending',
    'quotedMessage',
    'form',
    'uploadError',
    'isSupporter',
]));

foreach ($attributes->all() as $__key => $__value) {
    if (in_array($__key, $__propNames)) {
        $$__key = $$__key ?? $__value;
    } else {
        $__newAttributes[$__key] = $__value;
    }
}

$attributes = new \Illuminate\View\ComponentAttributeBag($__newAttributes);

unset($__propNames);
unset($__newAttributes);

foreach (array_filter(([
    'ticket',
    'showAiConsentPrompt',
    'aiResponsePending',
    'quotedMessage',
    'form',
    'uploadError',
    'isSupporter',
]), 'is_string', ARRAY_FILTER_USE_KEY) as $__key => $__value) {
    $$__key = $$__key ?? $__value;
}

$__defined_vars = get_defined_vars();

foreach ($attributes->all() as $__key => $__value) {
    if (array_key_exists($__key, $__defined_vars)) unset($$__key);
}

unset($__defined_vars); ?>

<!-- Ticket Messages & Reply -->
<?php if (isset($component)) { $__componentOriginalfc34df8c041cfce1b125f68563b94330 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginalfc34df8c041cfce1b125f68563b94330 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.modern-card','data' => ['variant' => 'elevated','size' => 'md']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('modern-card'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['variant' => 'elevated','size' => 'md']); ?>
    <div class="flex items-center justify-between mb-4">
        <h2 class="text-lg font-semibold flex items-center gap-2">
            <svg class="w-5 h-5 opacity-70" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 10h.01M12 10h.01M16 10h.01M9 16H5a2 2 0 01-2-2V6a2 2 0 012-2h14a2 2 0 012 2v8a2 2 0 01-2 2h-5l-5 5v-5z"></path>
            </svg>
            <?php echo e(__('tickets.conversation')); ?>

        </h2>
        <?php if (isset($component)) { $__componentOriginal7c5961ba4242859e8f30202299af2419 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal7c5961ba4242859e8f30202299af2419 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.modern-status-badge','data' => ['status' => $ticket->status,'size' => 'sm']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('modern-status-badge'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['status' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($ticket->status),'size' => 'sm']); ?>
            <?php echo e($ticket->statusLabel); ?>

         <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal7c5961ba4242859e8f30202299af2419)): ?>
<?php $attributes = $__attributesOriginal7c5961ba4242859e8f30202299af2419; ?>
<?php unset($__attributesOriginal7c5961ba4242859e8f30202299af2419); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal7c5961ba4242859e8f30202299af2419)): ?>
<?php $component = $__componentOriginal7c5961ba4242859e8f30202299af2419; ?>
<?php unset($__componentOriginal7c5961ba4242859e8f30202299af2419); ?>
<?php endif; ?>
    </div>

    <!-- AI Consent Prompt -->
    <!--[if BLOCK]><![endif]--><?php if($showAiConsentPrompt): ?>
    <div class="mb-4 p-4 bg-blue-900/30 border border-blue-500/30 rounded-xl shadow-lg animate-fadeIn ai-consent-prompt">
        <div class="flex items-start gap-3">
            <div class="shrink-0">
                <div class="w-10 h-10 bg-blue-500/20 rounded-full flex items-center justify-center">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-blue-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z" />
                    </svg>
                </div>
            </div>
            <div class="flex-1">
                <h3 class="text-base font-semibold text-white mb-1"><?php echo e(__('tickets.gemini_consent_title')); ?></h3>
                <p class="text-sm text-blue-100 mb-3">
                    <?php echo e(__('tickets.gemini_consent_description')); ?>

                </p>
                <div class="flex flex-wrap gap-2">
                    <?php if (isset($component)) { $__componentOriginal19987cd45c699eefead8b7a8ac222798 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal19987cd45c699eefead8b7a8ac222798 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.modern-button','data' => ['wire:click' => 'setGeminiConsent(false)','variant' => 'ghost','size' => 'sm']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('modern-button'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['wire:click' => 'setGeminiConsent(false)','variant' => 'ghost','size' => 'sm']); ?>
                        <?php echo e(__('tickets.gemini_consent_decline')); ?>

                     <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal19987cd45c699eefead8b7a8ac222798)): ?>
<?php $attributes = $__attributesOriginal19987cd45c699eefead8b7a8ac222798; ?>
<?php unset($__attributesOriginal19987cd45c699eefead8b7a8ac222798); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal19987cd45c699eefead8b7a8ac222798)): ?>
<?php $component = $__componentOriginal19987cd45c699eefead8b7a8ac222798; ?>
<?php unset($__componentOriginal19987cd45c699eefead8b7a8ac222798); ?>
<?php endif; ?>
                    <?php if (isset($component)) { $__componentOriginal19987cd45c699eefead8b7a8ac222798 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal19987cd45c699eefead8b7a8ac222798 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.modern-button','data' => ['wire:click' => 'setGeminiConsent(true)','variant' => 'primary','size' => 'sm']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('modern-button'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['wire:click' => 'setGeminiConsent(true)','variant' => 'primary','size' => 'sm']); ?>
                        <?php echo e(__('tickets.gemini_consent_accept')); ?>

                     <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal19987cd45c699eefead8b7a8ac222798)): ?>
<?php $attributes = $__attributesOriginal19987cd45c699eefead8b7a8ac222798; ?>
<?php unset($__attributesOriginal19987cd45c699eefead8b7a8ac222798); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal19987cd45c699eefead8b7a8ac222798)): ?>
<?php $component = $__componentOriginal19987cd45c699eefead8b7a8ac222798; ?>
<?php unset($__componentOriginal19987cd45c699eefead8b7a8ac222798); ?>
<?php endif; ?>
                </div>
            </div>
        </div>
    </div>
    <?php endif; ?><!--[if ENDBLOCK]><![endif]-->

    <!-- AI Response Pending Indicator -->
    <!--[if BLOCK]><![endif]--><?php if($aiResponsePending): ?>
    <div class="mb-4 p-4 bg-blue-900/30 border border-blue-500/30 rounded-xl shadow-lg animate-pulse ai-response-pending">
        <div class="flex items-center gap-3">
            <div class="shrink-0">
                <div class="w-10 h-10 bg-blue-500/20 rounded-full flex items-center justify-center">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-blue-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z" />
                    </svg>
                </div>
            </div>
            <div>
                <h3 class="text-base font-semibold text-white"><?php echo e(__('tickets.gemini_response_pending')); ?></h3>
                <div class="flex items-center mt-1.5">
                    <div class="typing-dot"></div>
                    <div class="typing-dot"></div>
                    <div class="typing-dot"></div>
                </div>
            </div>
        </div>
    </div>
    <?php endif; ?><!--[if ENDBLOCK]><![endif]-->

    
    <div class="relative flex-1 flex flex-col mb-2 px-2">
        
        <div class="fixed bottom-24 right-6 z-30 hidden" id="scroll-to-bottom">
            <button type="button"
                onclick="document.getElementById('messages-container').scrollTo({ top: document.getElementById('messages-container').scrollHeight, behavior: 'smooth' })"
                class="bg-slate-700/80 hover:bg-slate-600/80 text-white rounded-full p-3 shadow-lg transition-all duration-200 hover:scale-110 animate-bounce">
                <svg class="w-4 h-4" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 14 8">
                    <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="m1 1 6 6 6-6"/>
                </svg>
                <span class="sr-only"><?php echo e(__('tickets.scroll_to_bottom')); ?></span>
            </button>
        </div>

        
        <div class="fixed bottom-16 left-1/2 transform -translate-x-1/2 z-30 hidden cursor-pointer"
             id="new-message-indicator"
             onclick="document.getElementById('messages-container').scrollTo({ top: document.getElementById('messages-container').scrollHeight, behavior: 'smooth' }); this.classList.add('hidden');">
            <div class="bg-blue-600 text-white text-xs font-medium px-4 py-2 rounded-full shadow-lg flex items-center gap-2 new-message-indicator transition-all duration-200 hover:bg-blue-500">
                <span><?php echo e(__('tickets.new_message_received')); ?></span>
                <svg class="w-3 h-3 animate-bounce" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 14 8">
                    <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="m1 1 6 6 6-6"/>
                </svg>
            </div>
        </div>

        <!--[if BLOCK]><![endif]--><?php if($ticket->messages->isEmpty()): ?>
            <div class="text-center py-8 bg-slate-700 rounded-2xl shadow-sm">
                <svg class="w-12 h-12 mx-auto text-white/60 mb-3" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"></path>
                </svg>
                <p class="text-white/80"><?php echo e(__('tickets.no_messages')); ?></p>
            </div>
        <?php else: ?>
            
            <div class="flex-1 space-y-4 overflow-y-auto styled-scrollbar pr-2 pb-4 max-h-[calc(100vh-200px)]" id="messages-container">
                
                <div id="typing-indicator" class="flex items-start gap-2.5 hidden animate-fadeIn" data-typing-users="{}">
                    <div class="skeleton w-8 h-8 rounded-full shrink-0"></div>
                    <div class="flex flex-col gap-1">
                        <div class="flex flex-col w-full max-w-[320px] leading-1.5 p-3 bg-slate-700 rounded-2xl rounded-bl-none">
                            <div class="flex items-center space-x-2 rtl:space-x-reverse">
                                <span class="text-sm font-semibold text-base-content typing-user-name"></span>
                            </div>
                            <div class="flex items-center mt-1.5">
                                <div class="typing-dot"></div>
                                <div class="typing-dot"></div>
                                <div class="typing-dot"></div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!--[if BLOCK]><![endif]--><?php $__currentLoopData = $ticket->messages; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $message): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <div class="flex items-end gap-2 <?php echo e($message->user_id === auth()->id() ? 'justify-end' : ''); ?> animate-fadeIn group" data-message-id="<?php echo e($message->id); ?>">
                        
                        <!--[if BLOCK]><![endif]--><?php if($message->user_id !== auth()->id()): ?>
                            <span class="text-xs text-slate-400 self-end"><?php echo e($message->created_at->format('H:i')); ?></span>
                        <?php endif; ?><!--[if ENDBLOCK]><![endif]-->

                        
                        <div class="flex flex-col max-w-xs sm:max-w-sm md:max-w-md">
                            
                            <div class="p-3 <?php echo e($message->message_source === 'ai' ? 'message-bubble-ai text-slate-100' : ($message->user_id === auth()->id() ? 'message-bubble-right text-white' : 'message-bubble-left text-slate-100')); ?> shadow-md relative">
                                
                                <!--[if BLOCK]><![endif]--><?php if($message->message_source === 'ai'): ?>
                                    <div class="absolute -top-3 left-3 bg-blue-600 text-white text-xs px-2 py-0.5 rounded-full shadow-md flex items-center gap-1 ai-badge">
                                        <svg xmlns="http://www.w3.org/2000/svg" class="h-3 w-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z" />
                                        </svg>
                                        <span><?php echo e(__('tickets.gemini_badge_text')); ?></span>
                                    </div>
                                <?php endif; ?><!--[if ENDBLOCK]><![endif]-->

                                
                                <div class="absolute top-2 <?php echo e($message->user_id === auth()->id() ? 'left-2' : 'right-2'); ?> z-10 opacity-0 group-hover:opacity-100 transition-opacity duration-200">
                                    <div class="dropdown <?php echo e($message->user_id === auth()->id() ? 'dropdown-end' : 'dropdown-start'); ?>">
                                        <label tabindex="0" class="btn btn-xs btn-ghost btn-circle text-white/70 hover:bg-white/10 hover:text-white">
                                            <svg class="w-4 h-4" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="currentColor" viewBox="0 0 4 15">
                                                <path d="M3.5 1.5a1.5 1.5 0 1 1-3 0 1.5 1.5 0 0 1 3 0Zm0 6.041a1.5 1.5 0 1 1-3 0 1.5 1.5 0 0 1 3 0Zm0 5.959a1.5 1.5 0 1 1-3 0 1.5 1.5 0 0 1 3 0Z"/>
                                            </svg>
                                        </label>
                                        <ul tabindex="0" class="dropdown-content z-[20] menu p-2 shadow glass rounded-xl w-44 mt-1">
                                            <li>
                                                <button wire:click="quoteMessage(<?php echo e($message->id); ?>)" class="text-sm flex items-center gap-2 text-white hover:bg-slate-700/50 rounded-lg py-2 px-3 transition-colors duration-200">
                                                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 10h.01M12 10h.01M16 10h.01M9 16H5a2 2 0 01-2-2V6a2 2 0 012-2h14a2 2 0 012 2v8a2 2 0 01-2 2h-5l-5 5v-5z"></path></svg>
                                                    <?php echo e(__('tickets.quote')); ?>

                                                </button>
                                            </li>
                                            <li>
                                                <button onclick="copyToClipboard('<?php echo e(addslashes($message->message)); ?>')" class="text-sm flex items-center gap-2 text-white hover:bg-slate-700/50 rounded-lg py-2 px-3 transition-colors duration-200">
                                                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 16H6a2 2 0 01-2-2V6a2 2 0 0 012-2h8a2 2 0 0 012 2v2m-6 12h8a2 2 0 0 002-2v-8a2 2 0 0 00-2-2h-8a2 2 0 0 00-2 2v8a2 2 0 0 002 2z"></path></svg>
                                                    <?php echo e(__('tickets.copy')); ?>

                                                </button>
                                            </li>
                                            <!--[if BLOCK]><![endif]--><?php if($isSupporter): ?>
                                                <li class="mt-1 pt-1 border-t border-white/10">
                                                    <button wire:click="deleteMessage(<?php echo e($message->id); ?>)" wire:confirm="<?php echo e(__('tickets.confirm_delete_message')); ?>" class="text-sm flex items-center gap-2 text-red-300 hover:bg-red-500/20 rounded-lg py-2 px-3 transition-colors duration-200">
                                                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path></svg>
                                                        <?php echo e(__('tickets.delete')); ?>

                                                    </button>
                                                </li>
                                            <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
                                        </ul>
                                    </div>
                                </div>
                                
                                <div class="prose prose-sm text-white max-w-none whitespace-pre-wrap break-words"><?php echo e($message->message); ?></div>

                                
                                <!--[if BLOCK]><![endif]--><?php if($message->attachments->isNotEmpty()): ?>
                                    <div class="mt-3 pt-3 border-t <?php echo e($message->user_id === auth()->id() ? 'border-primary-focus/30' : 'border-base-300/50 dark:border-gray-600/50'); ?>">
                                        <div class="grid grid-cols-1 xs:grid-cols-2 sm:grid-cols-3 gap-2 sm:gap-3">
                                            <!--[if BLOCK]><![endif]--><?php $__currentLoopData = $message->attachments; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $attachment): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                <?php
$__split = function ($name, $params = []) {
    return [$name, $params];
};
[$__name, $__params] = $__split('ticket-attachment-component', ['attachment' => $attachment,'galleryIndex' => $loop->index]);

$__html = app('livewire')->mount($__name, $__params, 'attachment-'.$attachment->id, $__slots ?? [], get_defined_vars());

echo $__html;

unset($__html);
unset($__name);
unset($__params);
unset($__split);
if (isset($__slots)) unset($__slots);
?>
                                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?><!--[if ENDBLOCK]><![endif]-->
                                        </div>
                                    </div>
                                <?php endif; ?><!--[if ENDBLOCK]><![endif]-->

                            </div>
                            
                            <span class="text-xs font-normal text-base-content/50 px-1 mt-0.5"><?php echo e($message->created_at->format('d.m.Y H:i')); ?></span>
                        </div>

                        
                        <!--[if BLOCK]><![endif]--><?php if($message->user_id === auth()->id()): ?>
                            <span class="text-xs text-slate-400 self-end"><?php echo e($message->created_at->format('H:i')); ?></span>
                            <img class="w-8 h-8 rounded-full object-cover shrink-0"
                                 src="<?php echo e($message->user->getAvatar(['extension' => 'webp', 'size' => 32])); ?>"
                                 alt="<?php echo e($message->user->username); ?>">
                        <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
                    </div>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?><!--[if ENDBLOCK]><![endif]-->
            </div>
        <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
    </div>

    
    <!--[if BLOCK]><![endif]--><?php if($ticket->status !== 'closed'): ?>
        <form wire:submit="addReply" class="mt-2">
            
            <!--[if BLOCK]><![endif]--><?php if($quotedMessage): ?>
                <div class="mb-2 p-2 border-l-4 border-slate-500 bg-slate-700 rounded-r-lg text-sm relative">
                    <button type="button" wire:click="cancelQuote" class="absolute top-1 right-1 text-base-content/50 hover:text-base-content">
                        <svg class="w-3 h-3" fill="currentColor" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd"></path></svg>
                    </button>
                    <p class="font-semibold text-xs mb-1"><?php echo e(__('tickets.replying_to', ['user' => $quotedMessage->user->username])); ?></p>
                    <p class="text-base-content/70 line-clamp-2"><?php echo e($quotedMessage->message); ?></p>
                </div>
            <?php endif; ?><!--[if ENDBLOCK]><![endif]-->

            
            <div class="flex items-center gap-3">
                <div class="flex-1">
                    <?php if (isset($component)) { $__componentOriginal17d23b099a28c4e2c5f6c448deb256e5 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal17d23b099a28c4e2c5f6c448deb256e5 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.modern-input','data' => ['wire:model.live' => 'form.message','type' => 'text','placeholder' => ''.e(__('tickets.write_reply_placeholder')).'','variant' => 'glass','required' => true,'wire:keydown.ctrl.enter' => 'addReply','wire:input.debounce.500ms' => 'notifyTyping','class' => 'py-3']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('modern-input'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['wire:model.live' => 'form.message','type' => 'text','placeholder' => ''.e(__('tickets.write_reply_placeholder')).'','variant' => 'glass','required' => true,'wire:keydown.ctrl.enter' => 'addReply','wire:input.debounce.500ms' => 'notifyTyping','class' => 'py-3']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal17d23b099a28c4e2c5f6c448deb256e5)): ?>
<?php $attributes = $__attributesOriginal17d23b099a28c4e2c5f6c448deb256e5; ?>
<?php unset($__attributesOriginal17d23b099a28c4e2c5f6c448deb256e5); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal17d23b099a28c4e2c5f6c448deb256e5)): ?>
<?php $component = $__componentOriginal17d23b099a28c4e2c5f6c448deb256e5; ?>
<?php unset($__componentOriginal17d23b099a28c4e2c5f6c448deb256e5); ?>
<?php endif; ?>
                </div>

                
                <?php if (isset($component)) { $__componentOriginal19987cd45c699eefead8b7a8ac222798 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal19987cd45c699eefead8b7a8ac222798 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.modern-button','data' => ['type' => 'button','variant' => 'ghost','size' => 'md','onclick' => 'document.getElementById(\'attachments\').click()','title' => ''.e(__('tickets.attachments')).'','icon' => '<svg class="w-5 h-5" fill="none" viewBox="0 0 12 20"><path stroke="currentColor" stroke-linejoin="round" stroke-width="2" d="M1 6v8a5 5 0 1 0 10 0V4.5a3.5 3.5 0 1 0-7 0V13a2 2 0 0 0 4 0V6"/></svg>']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('modern-button'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['type' => 'button','variant' => 'ghost','size' => 'md','onclick' => 'document.getElementById(\'attachments\').click()','title' => ''.e(__('tickets.attachments')).'','icon' => '<svg class="w-5 h-5" fill="none" viewBox="0 0 12 20"><path stroke="currentColor" stroke-linejoin="round" stroke-width="2" d="M1 6v8a5 5 0 1 0 10 0V4.5a3.5 3.5 0 1 0-7 0V13a2 2 0 0 0 4 0V6"/></svg>']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal19987cd45c699eefead8b7a8ac222798)): ?>
<?php $attributes = $__attributesOriginal19987cd45c699eefead8b7a8ac222798; ?>
<?php unset($__attributesOriginal19987cd45c699eefead8b7a8ac222798); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal19987cd45c699eefead8b7a8ac222798)): ?>
<?php $component = $__componentOriginal19987cd45c699eefead8b7a8ac222798; ?>
<?php unset($__componentOriginal19987cd45c699eefead8b7a8ac222798); ?>
<?php endif; ?>

                
                <?php if (isset($component)) { $__componentOriginal19987cd45c699eefead8b7a8ac222798 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal19987cd45c699eefead8b7a8ac222798 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.modern-button','data' => ['type' => 'submit','variant' => 'primary','size' => 'md','loading' => false,'wire:loading.class' => 'opacity-50','wire:target' => 'addReply, form.attachments','icon' => '<svg class="w-5 h-5 transform rotate-90" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" d="M6 12L3.269 3.126A59.768 59.768 0 0121.485 12 59.77 59.77 0 013.27 20.876L5.999 12zm0 0h7.5" /></svg>']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('modern-button'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['type' => 'submit','variant' => 'primary','size' => 'md','loading' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute(false),'wire:loading.class' => 'opacity-50','wire:target' => 'addReply, form.attachments','icon' => '<svg class="w-5 h-5 transform rotate-90" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" d="M6 12L3.269 3.126A59.768 59.768 0 0121.485 12 59.77 59.77 0 013.27 20.876L5.999 12zm0 0h7.5" /></svg>']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal19987cd45c699eefead8b7a8ac222798)): ?>
<?php $attributes = $__attributesOriginal19987cd45c699eefead8b7a8ac222798; ?>
<?php unset($__attributesOriginal19987cd45c699eefead8b7a8ac222798); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal19987cd45c699eefead8b7a8ac222798)): ?>
<?php $component = $__componentOriginal19987cd45c699eefead8b7a8ac222798; ?>
<?php unset($__componentOriginal19987cd45c699eefead8b7a8ac222798); ?>
<?php endif; ?>
            </div>
            <div class="text-xs text-base-content/50 mt-2 text-center">
                <?php echo e(__('tickets.press_ctrl_enter')); ?>

            </div>
            
            <div class="mt-2">
                <!--[if BLOCK]><![endif]--><?php $__errorArgs = ['form.message'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                    <div class="text-red-300 text-sm flex items-center gap-1">
                        <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                            <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clip-rule="evenodd"></path>
                        </svg>
                        <span><?php echo e($message); ?></span>
                    </div>
                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?><!--[if ENDBLOCK]><![endif]-->
            </div>

            <!-- Hidden file input -->
            <div class="hidden">
                <input wire:model="form.attachments" type="file" id="attachments" multiple wire:loading.attr="disabled" accept="image/*,video/*,audio/*,.pdf,.txt,.log,.zip,.rar" />
            </div>

            <!-- Upload Progress -->
            <div wire:loading wire:target="form.attachments">
                <div class="mt-3 p-4 bg-slate-700 rounded-xl shadow-lg">
                    <div class="flex items-center justify-between mb-3">
                        <div class="flex items-center gap-2 text-sm">
                            <span class="loading loading-spinner loading-sm text-blue-300"></span>
                            <span class="font-medium text-white"><?php echo e(__('tickets.uploading_attachments')); ?></span>
                        </div>
                        <div class="flex items-center gap-2">
                            <span class="text-xs font-semibold text-blue-300" x-text="$wire.uploadProgress + '%'">0%</span>
                            <button type="button" onclick="handleCancelUpload()" class="btn btn-xs btn-ghost btn-circle text-red-300 hover:bg-red-500/20">
                                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                                </svg>
                            </button>
                        </div>
                    </div>
                    <div class="w-full bg-slate-800/50 rounded-full h-3 overflow-hidden">
                        <div class="bg-blue-500 h-3 rounded-full transition-all duration-300 ease-in-out"
                             x-bind:style="'width: ' + $wire.uploadProgress + '%'"></div>
                    </div>
                    <div class="flex justify-between items-center mt-2 text-xs text-slate-300">
                        <p><?php echo e(__('tickets.please_wait_upload')); ?></p>
                        <p x-show="$wire.uploadProgress < 100"><?php echo e(__('tickets.uploading')); ?>...</p>
                        <p x-show="$wire.uploadProgress >= 100"><?php echo e(__('tickets.processing')); ?>...</p>
                    </div>
                </div>
            </div>

            <!-- Upload Error -->
            <!--[if BLOCK]><![endif]--><?php if($uploadError): ?>
            <div wire:key="upload-error" class="mt-3 p-4 text-sm rounded-xl bg-slate-700 border border-red-500/30 shadow-lg animate-fadeIn" role="alert">
                <div class="flex items-start gap-3">
                    <svg class="w-6 h-6 shrink-0 mt-0.5 text-red-400" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="currentColor" viewBox="0 0 20 20">
                        <path d="M10 .5a9.5 9.5 0 1 0 9.5 9.5A9.51 9.51 0 0 0 10 .5ZM10 15a1 1 0 1 1 0-2 1 1 0 0 1 0 2Zm1-4a1 1 0 0 1-2 0V6a1 1 0 0 1 2 0v5Z"/>
                    </svg>
                    <div class="flex-1">
                        <p class="font-medium text-red-300 text-base"><?php echo e(__('tickets.upload_error')); ?></p>
                        <p class="mt-2 text-white"><?php echo e($uploadError); ?></p>
                        <div class="mt-3 flex gap-2">
                            <button type="button" wire:click="dismissUploadError" class="px-3 py-1.5 bg-red-500/20 hover:bg-red-500/30 text-red-300 rounded-lg text-xs font-medium transition-colors">
                                <?php echo e(__('tickets.dismiss')); ?>

                            </button>
                            <button type="button" onclick="document.getElementById('attachments').click()" class="px-3 py-1.5 bg-blue-500/20 hover:bg-blue-500/30 text-blue-300 rounded-lg text-xs font-medium transition-colors">
                                <?php echo e(__('tickets.try_again')); ?>

                            </button>
                        </div>
                    </div>
                </div>
            </div>
            <?php endif; ?><!--[if ENDBLOCK]><![endif]-->

            <!-- Attachment previews -->
            <!--[if BLOCK]><![endif]--><?php if(count($form->attachments) > 0 && !$uploadError): ?>
                <div class="mt-4 bg-slate-700/70 p-4 rounded-xl shadow-md">
                    <div class="flex justify-between items-center mb-3">
                        <p class="text-sm font-medium text-white"><?php echo e(__('tickets.selected_attachments')); ?> (<?php echo e(count($form->attachments)); ?>)</p>
                        <button type="button" wire:click="clearAttachments" class="text-xs text-slate-300 hover:text-red-300 transition-colors">
                            <?php echo e(__('tickets.clear_all')); ?>

                        </button>
                    </div>
                    <div class="grid grid-cols-1 sm:grid-cols-2 gap-3">
                        <!--[if BLOCK]><![endif]--><?php $__currentLoopData = $form->attachments; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $index => $attachment): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <div wire:key="attachment-preview-<?php echo e($index); ?>" class="flex items-center gap-3 p-3 bg-slate-800/70 hover:bg-slate-700 text-white rounded-xl transition-all duration-150 group">
                                <?php
                                    $fileType = strtolower($attachment->getClientOriginalExtension());
                                    $isImage = in_array($fileType, ['jpg', 'jpeg', 'png', 'gif', 'webp', 'svg']);
                                    $isVideo = in_array($fileType, ['mp4', 'webm', 'ogg', 'mov', 'avi']);
                                    $isAudio = in_array($fileType, ['mp3', 'wav', 'ogg', 'opus', 'aac', 'm4a']);
                                    $isPdf = $fileType === 'pdf';
                                    $fileSize = round($attachment->getSize() / 1024, 1);
                                    $fileSizeStr = $fileSize > 1024 ? round($fileSize / 1024, 1) . ' MB' : $fileSize . ' KB';
                                ?>

                                <!--[if BLOCK]><![endif]--><?php if($isImage): ?>
                                    <div class="w-10 h-10 bg-blue-500/20 rounded-lg flex items-center justify-center shrink-0">
                                        <svg class="w-5 h-5 text-blue-300" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 0 0-2-2H6a2 2 0 0 0-2 2v12a2 2 0 0 0 2 2z"></path></svg>
                                    </div>
                                <?php elseif($isVideo): ?>
                                    <div class="w-10 h-10 bg-purple-500/20 rounded-lg flex items-center justify-center shrink-0">
                                        <svg class="w-5 h-5 text-purple-300" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 10l4.553-2.276A1 1 0 0 1 21 8.618v6.764a1 1 0 0 1-1.447.894L15 14M5 18h8a2 2 0 0 0 2-2V8a2 2 0 0 0-2-2H5a2 2 0 0 0-2 2v8a2 2 0 0 0 2 2z"></path></svg>
                                    </div>
                                <?php elseif($isAudio): ?>
                                    <div class="w-10 h-10 bg-green-500/20 rounded-lg flex items-center justify-center shrink-0">
                                        <svg class="w-5 h-5 text-green-300" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15.536 8.464a5 5 0 010 7.072m2.828-9.9a9 9 0 010 12.728M5.858 15.858a3 3 0 0 1-4.243 0V8.142a3 3 0 0 1 4.243 0L7 9.284l1.142-1.142a3 3 0 0 1 4.243 0v7.716a3 3 0 0 1-4.243 0L7 14.716 5.858 15.858z"></path></svg>
                                    </div>
                                <?php elseif($isPdf): ?>
                                    <div class="w-10 h-10 bg-red-500/20 rounded-lg flex items-center justify-center shrink-0">
                                        <svg class="w-5 h-5 text-red-300" fill="currentColor" viewBox="0 0 24 24"><path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8l-6-6zm-1 11h-2v2h2v-2zm-2-4h2v3h-2V9z"/><path d="M14 2v6h6"/></svg>
                                    </div>
                                <?php else: ?>
                                    <div class="w-10 h-10 bg-slate-500/20 rounded-lg flex items-center justify-center shrink-0">
                                        <svg class="w-5 h-5 text-slate-300" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15.172 7l-6.586 6.586a2 0 0 1-2.828 0l6.414-6.586a4 4 0 0 0-5.656-5.656l-6.415 6.585a6 6 0 0 0 8.486 8.486L20.5 13"></path></svg>
                                    </div>
                                <?php endif; ?><!--[if ENDBLOCK]><![endif]-->

                                <div class="flex-1 min-w-0">
                                    <p class="text-sm font-medium truncate"><?php echo e($attachment->getClientOriginalName()); ?></p>
                                    <p class="text-xs text-slate-400"><?php echo e($fileSizeStr); ?></p>
                                </div>

                                <button type="button" wire:click="removeAttachment(<?php echo e($index); ?>)" class="btn btn-xs btn-ghost btn-circle text-white/70 hover:bg-red-500/20 hover:text-red-300 opacity-70 group-hover:opacity-100 transition-opacity">
                                    <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20"> <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 0 0 1 1.414 1.414L11.414 10l4.293 4.293a1 0 0 1-1.414 1.414L10 11.414l-4.293 4.293a1 0 0 1-1.414-1.414L8.586 10 4.293 5.707a1 0 0 1 0-1.414z" clip-rule="evenodd"></path></svg>
                                </button>
                            </div>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?><!--[if ENDBLOCK]><![endif]-->
                    </div>
                </div>
            <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
        </form>
    <?php else: ?>
        <?php if (isset($component)) { $__componentOriginalfc34df8c041cfce1b125f68563b94330 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginalfc34df8c041cfce1b125f68563b94330 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.modern-card','data' => ['variant' => 'outlined','size' => 'md','class' => 'text-center']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('modern-card'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['variant' => 'outlined','size' => 'md','class' => 'text-center']); ?>
            <div class="flex flex-col items-center gap-4">
                <svg class="w-8 h-8 text-base-content/40" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 0 0 0 2-2v-6a2 0 0 0-2-2H6a2 0 0 0-2 2v6a2 0 0 0 2 2zm10-10V7a4 0 0 0-8 0v4h8z"></path>
                </svg>
                <div>
                    <h3 class="font-semibold text-base-content mb-2"><?php echo e(__('tickets.ticket_closed')); ?></h3>
                    <p class="text-base-content/70 mb-4"><?php echo e(__('tickets.ticket_closed_reply')); ?></p>
                    <!--[if BLOCK]><![endif]--><?php if($isSupporter): ?>
                        <?php if (isset($component)) { $__componentOriginal19987cd45c699eefead8b7a8ac222798 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal19987cd45c699eefead8b7a8ac222798 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.modern-button','data' => ['wire:click' => 'setStatusAndUpdate(\'open\')','variant' => 'primary','size' => 'sm','icon' => '<svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 11V7a4 0 0 0 8 0m-4 8v2m-6 4h12a2 0 0 0 2-2v-6a2 0 0 0-2-2H6a2 0 0 0-2 2v6a2 0 0 0 2 2z"></path></svg>']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('modern-button'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['wire:click' => 'setStatusAndUpdate(\'open\')','variant' => 'primary','size' => 'sm','icon' => '<svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 11V7a4 0 0 0 8 0m-4 8v2m-6 4h12a2 0 0 0 2-2v-6a2 0 0 0-2-2H6a2 0 0 0-2 2v6a2 0 0 0 2 2z"></path></svg>']); ?>
                            <?php echo e(__('tickets.reopen')); ?>

                         <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal19987cd45c699eefead8b7a8ac222798)): ?>
<?php $attributes = $__attributesOriginal19987cd45c699eefead8b7a8ac222798; ?>
<?php unset($__attributesOriginal19987cd45c699eefead8b7a8ac222798); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal19987cd45c699eefead8b7a8ac222798)): ?>
<?php $component = $__componentOriginal19987cd45c699eefead8b7a8ac222798; ?>
<?php unset($__componentOriginal19987cd45c699eefead8b7a8ac222798); ?>
<?php endif; ?>
                    <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
                </div>
            </div>
         <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginalfc34df8c041cfce1b125f68563b94330)): ?>
<?php $attributes = $__attributesOriginalfc34df8c041cfce1b125f68563b94330; ?>
<?php unset($__attributesOriginalfc34df8c041cfce1b125f68563b94330); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalfc34df8c041cfce1b125f68563b94330)): ?>
<?php $component = $__componentOriginalfc34df8c041cfce1b125f68563b94330; ?>
<?php unset($__componentOriginalfc34df8c041cfce1b125f68563b94330); ?>
<?php endif; ?>
    <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
 <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginalfc34df8c041cfce1b125f68563b94330)): ?>
<?php $attributes = $__attributesOriginalfc34df8c041cfce1b125f68563b94330; ?>
<?php unset($__attributesOriginalfc34df8c041cfce1b125f68563b94330); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalfc34df8c041cfce1b125f68563b94330)): ?>
<?php $component = $__componentOriginalfc34df8c041cfce1b125f68563b94330; ?>
<?php unset($__componentOriginalfc34df8c041cfce1b125f68563b94330); ?>
<?php endif; ?>
<?php /**PATH C:\Users\<USER>\Documents\GitHub\minewache-website\resources\views/livewire/ticket/partials/conversation.blade.php ENDPATH**/ ?>