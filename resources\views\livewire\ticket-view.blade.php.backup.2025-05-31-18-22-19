<div>
<!-- Load Alpine component script directly -->
<!-- Load direct Reverb configuration as fallback only in development -->
@if(config('debugging.frontend.websocket_debug', false))
<script src="{{ asset('js/direct-reverb.js') }}"></script>
@endif

<script src="{{ asset('js/ticket-view-alpine.js') }}"></script>
@if(config('debugging.frontend.websocket_debug', false))
<!-- Include WebSocket debugging script in development environment -->
<script src="{{ asset('js/websocket-debug.js') }}"></script>
@endif

<!-- Initialize Echo channel subscription for this ticket -->
<script>
document.addEventListener('DOMContentLoaded', function() {
    if (window.Echo) {
        console.log('Initializing Echo channel subscription for ticket {{ $ticket->id }}');
        try {
            // Subscribe to the private channel for this ticket
            window.Echo.private('tickets.{{ $ticket->id }}')
                .listen('.TicketMessageCreated', (event) => {
                    console.log('Received TicketMessageCreated event:', event);
                    // The Livewire component will handle this via the #[On] attribute
                })
                .listen('.typing', (event) => {
                    console.log('Received typing event:', event);
                    // The Livewire component will handle this via the #[On] attribute
                })
                .listen('.TicketMessageAttachmentsReady', (event) => {
                    console.log('Received TicketMessageAttachmentsReady event:', event);
                    // The Livewire component will handle this via the #[On] attribute
                })
                .listen('.TicketStatusUpdated', (event) => {
                    console.log('Received TicketStatusUpdated event:', event);
                    // The Livewire component will handle this via the #[On] attribute
                });
            console.log('Echo channel subscription initialized successfully');
        } catch (error) {
            console.error('Error initializing Echo channel subscription:', error);
        }
    } else {
        console.error('Echo is not available. WebSocket functionality will not work.');
    }
});
</script>

<div
    {{-- wire:poll.{{ $enablePolling ? $pollingInterval : 'none' }} --}} {{-- Temporarily disable polling directive --}}
    x-data="ticketView()"
    x-init="init()"
    @message-added.window="handleMessageAdded($event.detail.message)"
    @play-notification.window="playNotificationSound()"
    @attachments-ready.window="handleAttachmentsReady($event.detail.messageId, $event.detail.attachments)"
    @typing-update.window="handleTypingUpdate($event.detail.users)"
    id="ticket-view"
    class="bg-gradient-modern min-h-screen py-8 px-4 flex flex-col items-center"
>
    <!-- Notification sound -->
    <audio id="notification-sound" preload="auto" controls style="display: none;">
        <source src="{{ asset('audio/notification.mp3') }}" type="audio/mpeg">
        <source src="{{ asset('sounds/notification.opus') }}" type="audio/opus">
        Your browser does not support the audio element.
    </audio>

    <!-- Breadcrumbs - Simplified -->
    <nav class="text-sm breadcrumbs px-1 w-full max-w-5xl mb-4" aria-label="Breadcrumb">
        <ul>
            <li>
                <a href="{{ route('home') }}" class="inline-flex items-center gap-1 hover:text-primary transition-colors">
                    <svg class="w-4 h-4" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="currentColor" viewBox="0 0 20 20">
                        <path d="m19.707 9.293-2-2-7-7a1 1 0 0 0-1.414 0l-7 7-2 2a1 1 0 0 0 1.414 1.414L2 10.414V18a2 2 0 0 0 2 2h3a1 1 0 0 0 1-1v-4a1 1 0 0 1 1-1h2a1 1 0 0 1 1 1v4a1 1 0 0 0 1 1h3a2 2 0 0 0 2-2v-7.586l.293.293a1 1 0 0 0 1.414-1.414Z"/>
                    </svg>
                    {{ __('nav.home') }}
                </a>
            </li>
            <li>
                <a href="{{ route('tickets.index') }}" class="inline-flex items-center gap-1 hover:text-primary transition-colors">
                    {{ __('tickets.tickets') }}
                </a>
            </li>
            <li>
                <span class="inline-flex items-center gap-1 text-base-content/70">
                    {{ __('tickets.ticket') }} #{{ $ticket->id }}
                </span>
            </li>
        </ul>
    </nav>

    <!-- Ticket Header & Actions -->
    <div class="glass rounded-3xl shadow-2xl p-4 md:p-6 w-full max-w-5xl">
        <div class="flex flex-col md:flex-row justify-between items-start md:items-center gap-4">
            {{-- Ticket Title and Basic Info --}}
            <div class="flex-1 min-w-0">
                <div class="flex items-center gap-3 mb-2">
                    <img src="{{ $ticket->user->getAvatar(['extension' => 'webp', 'size' => 40]) }}" class="w-10 h-10 rounded-full border-2 border-slate-700" alt="{{ $ticket->user->username }}" />
                    <div>
                        <h1 class="text-lg sm:text-xl md:text-2xl font-bold text-base-content truncate">
                            {{ $ticket->title }}
                        </h1>
                        <p class="text-xs sm:text-sm text-base-content/60 flex flex-wrap items-center gap-x-2 gap-y-1">
                            <span>#{{ $ticket->id }}</span>
                            <span class="text-base-content/30">•</span>
                            <span>{{ $ticket->user->username }}</span>
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </div>


    <!-- Ticket Information -->
    <div class="w-full max-w-5xl glass rounded-3xl shadow-2xl p-3 sm:p-4 md:p-6 grid grid-cols-1 md:grid-cols-3 gap-4 md:gap-6">
        {{-- Description --}}
        <div class="md:col-span-2">
            <h2 class="text-lg font-semibold mb-3 flex items-center gap-2">
                <svg class="w-5 h-5 opacity-70" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 8h10M7 12h10M7 16h10"></path>
                </svg>
                {{ __('tickets.description') }}
            </h2>
            <div class="prose prose-sm md:prose-base dark:prose-invert max-w-none whitespace-pre-wrap">{{ $ticket->description }}</div>
        </div>

        {{-- Details --}}
        <div class="border-t md:border-t-0 md:border-l border-base-300/30 pt-4 md:pt-0 md:pl-6">
            <h2 class="text-lg font-semibold mb-3 flex items-center gap-2">
                <svg class="w-5 h-5 opacity-70" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                </svg>
                {{ __('tickets.details') }}
            </h2>
            <ul class="space-y-2 text-sm md:text-base">
                <li class="flex justify-between items-center">
                    <span class="text-base-content/60">{{ __('tickets.status') }}:</span>
                    <span class="badge {{ $ticket->statusColor }} badge-sm">{{ $ticket->statusLabel }}</span>
                </li>
                <li class="flex justify-between items-center">
                    <span class="text-base-content/60">{{ __('tickets.assigned_to') }}:</span>
                    @if($ticket->assignedTo)
                        <span class="font-medium flex items-center gap-1">
                             <img class="w-4 h-4 rounded-full" src="{{ $ticket->assignedTo->getAvatar(['extension' => 'webp', 'size' => 16]) }}" alt="{{ $ticket->assignedTo->username }}">
                            {{ $ticket->assignedTo->username }}
                        </span>
                    @else
                        <span class="text-base-content/50 italic">{{ __('tickets.unassigned') }}</span>
                    @endif
                </li>
                <li class="flex justify-between items-center">
                    <span class="text-base-content/60">{{ __('tickets.created_at') }}:</span>
                    <span>{{ $ticket->created_at->format('d.m.Y H:i') }}</span>
                </li>
                <li class="flex justify-between items-center">
                    <span class="text-base-content/60">{{ __('tickets.updated_at') }}:</span>
                    <span>{{ $ticket->updated_at->format('d.m.Y H:i') }}</span>
                </li>
                <li class="flex justify-between items-center">
                    <span class="text-base-content/60">{{ __('tickets.message_count') }}:</span>
                    <span class="font-medium">{{ $ticket->messages->count() }}</span>
                </li>
            </ul>
        </div>
    </div>

    {{-- Ticket Activity Timeline (Optional - Consider making collapsible) --}}
    {{-- <div class="mb-8"> ... </div> --}}



    <!-- Ticket Messages & Reply -->
    <div class="glass w-full max-w-5xl rounded-3xl shadow-2xl p-3 sm:p-6 flex flex-col h-[500px] sm:h-[700px]">
        <div class="flex items-center justify-between mb-4">
            <h2 class="text-lg font-semibold flex items-center gap-2">
                <svg class="w-5 h-5 opacity-70" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 10h.01M12 10h.01M16 10h.01M9 16H5a2 2 0 01-2-2V6a2 2 0 012-2h14a2 2 0 012 2v8a2 2 0 01-2 2h-5l-5 5v-5z"></path>
                </svg>
                {{ __('tickets.conversation') }}
            </h2>
            <span class="badge {{ $ticket->statusColor }} badge-sm">{{ $ticket->statusLabel }}</span>
        </div>

        <!-- AI Consent Prompt -->
        @if($showAiConsentPrompt)
        <div class="mb-4 p-4 bg-blue-900/30 border border-blue-500/30 rounded-xl shadow-lg animate-fadeIn ai-consent-prompt">
            <div class="flex items-start gap-3">
                <div class="shrink-0">
                    <div class="w-10 h-10 bg-blue-500/20 rounded-full flex items-center justify-center">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-blue-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z" />
                        </svg>
                    </div>
                </div>
                <div class="flex-1">
                    <h3 class="text-base font-semibold text-white mb-1">{{ __('tickets.gemini_consent_title') }}</h3>
                    <p class="text-sm text-blue-100 mb-3">
                        {{ __('tickets.gemini_consent_description') }}
                    </p>
                    <div class="flex flex-wrap gap-2">
                        <button wire:click="setGeminiConsent(false)" class="px-3 py-1.5 bg-slate-700 hover:bg-slate-600 text-white rounded-lg text-sm transition-colors">
                            {{ __('tickets.gemini_consent_decline') }}
                        </button>
                        <button wire:click="setGeminiConsent(true)" class="px-3 py-1.5 bg-blue-600 hover:bg-blue-500 text-white rounded-lg text-sm transition-colors">
                            {{ __('tickets.gemini_consent_accept') }}
                        </button>
                    </div>
                </div>
            </div>
        </div>
        @endif

        <!-- AI Response Pending Indicator -->
        @if($aiResponsePending)
        <div class="mb-4 p-4 bg-blue-900/30 border border-blue-500/30 rounded-xl shadow-lg animate-pulse ai-response-pending">
            <div class="flex items-center gap-3">
                <div class="shrink-0">
                    <div class="w-10 h-10 bg-blue-500/20 rounded-full flex items-center justify-center">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-blue-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z" />
                        </svg>
                    </div>
                </div>
                <div>
                    <h3 class="text-base font-semibold text-white">{{ __('tickets.gemini_response_pending') }}</h3>
                    <div class="flex items-center mt-1.5">
                        <div class="typing-dot"></div>
                        <div class="typing-dot"></div>
                        <div class="typing-dot"></div>
                    </div>
                </div>
            </div>
        </div>
        @endif

            {{-- Messages Container --}}
            <div class="relative flex-1 overflow-y-auto mb-4 px-2">
                {{-- Scroll to bottom button --}}
                <div class="fixed bottom-24 right-6 z-30 hidden" id="scroll-to-bottom">
                    <button type="button" onclick="document.getElementById('messages-container').scrollTo({ top: document.getElementById('messages-container').scrollHeight, behavior: 'smooth' })" class="bg-slate-700/80 hover:bg-slate-600/80 text-white rounded-full p-3 shadow-lg transition-all duration-200 hover:scale-110 animate-bounce">
                        <svg class="w-4 h-4" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 14 8">
                            <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="m1 1 6 6 6-6"/>
                        </svg>
                        <span class="sr-only">{{ __('tickets.scroll_to_bottom') }}</span>
                    </button>
                </div>

                {{-- New message indicator --}}
                <div class="fixed bottom-16 left-1/2 transform -translate-x-1/2 z-30 hidden cursor-pointer" id="new-message-indicator" onclick="document.getElementById('messages-container').scrollTo({ top: document.getElementById('messages-container').scrollHeight, behavior: 'smooth' }); this.classList.add('hidden');">
                    <div class="bg-blue-600 text-white text-xs font-medium px-4 py-2 rounded-full shadow-lg flex items-center gap-2 new-message-indicator transition-all duration-200 hover:bg-blue-500">
                        <span>{{ __('tickets.new_message_received') }}</span>
                        <svg class="w-3 h-3 animate-bounce" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 14 8">
                            <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="m1 1 6 6 6-6"/>
                        </svg>
                    </div>
                </div>

                @if($ticket->messages->isEmpty())
                    <div class="text-center py-8 bg-slate-700 rounded-2xl shadow-sm">
                        <svg class="w-12 h-12 mx-auto text-white/60 mb-3" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"></path>
                        </svg>
                        <p class="text-white/80">{{ __('tickets.no_messages') }}</p>
                    </div>
                @else
                    {{-- Messages List --}}
                    <div class="space-y-4 overflow-y-auto styled-scrollbar pr-2 pb-4" id="messages-container">
                        {{-- Typing indicator --}}
                        <div id="typing-indicator" class="flex items-start gap-2.5 hidden animate-fadeIn" data-typing-users="{}">
                            <div class="skeleton w-8 h-8 rounded-full shrink-0"></div>
                            <div class="flex flex-col gap-1">
                                <div class="flex flex-col w-full max-w-[320px] leading-1.5 p-3 bg-slate-700 rounded-2xl rounded-bl-none">
                                    <div class="flex items-center space-x-2 rtl:space-x-reverse">
                                        <span class="text-sm font-semibold text-base-content typing-user-name"></span>
                                    </div>
                                    <div class="flex items-center mt-1.5">
                                        <div class="typing-dot"></div>
                                        <div class="typing-dot"></div>
                                        <div class="typing-dot"></div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        {{-- Actual Messages --}}
                        @foreach($ticket->messages as $message)
                            <div class="flex items-end gap-2 {{ $message->user_id === auth()->id() ? 'justify-end' : '' }} animate-fadeIn group" data-message-id="{{ $message->id }}">
                                {{-- Timestamp for incoming messages --}}
                                @if($message->user_id !== auth()->id())
                                    <span class="text-xs text-slate-400 self-end">{{ $message->created_at->format('H:i') }}</span>
                                @endif

                                {{-- Message Bubble --}}
                                <div class="flex flex-col max-w-xs sm:max-w-sm md:max-w-md">
                                    {{-- Message Content --}}
                                    <div class="p-3 {{ $message->message_source === 'ai' ? 'message-bubble-ai text-slate-100' : ($message->user_id === auth()->id() ? 'message-bubble-right text-white' : 'message-bubble-left text-slate-100') }} shadow-md relative">
                                        {{-- AI Badge for AI messages --}}
                                        @if($message->message_source === 'ai')
                                            <div class="absolute -top-3 left-3 bg-blue-600 text-white text-xs px-2 py-0.5 rounded-full shadow-md flex items-center gap-1 ai-badge">
                                                <svg xmlns="http://www.w3.org/2000/svg" class="h-3 w-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z" />
                                                </svg>
                                                <span>{{ __('tickets.gemini_badge_text') }}</span>
                                            </div>
                                        @endif

                                        {{-- Message Actions (Improved Kebab Menu) --}}
                                        <div class="absolute top-2 {{ $message->user_id === auth()->id() ? 'left-2' : 'right-2' }} z-10 opacity-0 group-hover:opacity-100 transition-opacity duration-200">
                                            <div class="dropdown {{ $message->user_id === auth()->id() ? 'dropdown-end' : 'dropdown-start' }}">
                                                <label tabindex="0" class="btn btn-xs btn-ghost btn-circle text-white/70 hover:bg-white/10 hover:text-white">
                                                    <svg class="w-4 h-4" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="currentColor" viewBox="0 0 4 15">
                                                        <path d="M3.5 1.5a1.5 1.5 0 1 1-3 0 1.5 1.5 0 0 1 3 0Zm0 6.041a1.5 1.5 0 1 1-3 0 1.5 1.5 0 0 1 3 0Zm0 5.959a1.5 1.5 0 1 1-3 0 1.5 1.5 0 0 1 3 0Z"/>
                                                </label>
                                                <ul tabindex="0" class="dropdown-content z-[20] menu p-2 shadow glass rounded-xl w-44 mt-1">
                                                    <li>
                                                        <button wire:click="quoteMessage({{ $message->id }})" class="text-sm flex items-center gap-2 text-white hover:bg-slate-700/50 rounded-lg py-2 px-3 transition-colors duration-200">
                                                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 10h.01M12 10h.01M16 10h.01M9 16H5a2 2 0 01-2-2V6a2 2 0 012-2h14a2 2 0 012 2v8a2 2 0 01-2 2h-5l-5 5v-5z"></path></svg>
                                                            {{ __('tickets.quote') }}
                                                        </button>
                                                    </li>
                                                    <li>
                                                        <button onclick="copyToClipboard('{{ addslashes($message->message) }}')" class="text-sm flex items-center gap-2 text-white hover:bg-slate-700/50 rounded-lg py-2 px-3 transition-colors duration-200">
                                                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z"></path></svg>
                                                            {{ __('tickets.copy') }}
                                                        </button>
                                                    </li>
                                                    @if($isSupporter)
                                                        <li class="mt-1 pt-1 border-t border-white/10">
                                                            <button wire:click="deleteMessage({{ $message->id }})" wire:confirm="{{ __('tickets.confirm_delete_message') }}" class="text-sm flex items-center gap-2 text-red-300 hover:bg-red-500/20 rounded-lg py-2 px-3 transition-colors duration-200">
                                                                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path></svg>
                                                                {{ __('tickets.delete') }}
                                                            </button>
                                                        </li>
                                                    @endif
                                                </ul>
                                            </div>
                                        </div>
                                        {{-- Message Content --}}
                                        <div class="prose prose-sm text-white max-w-none whitespace-pre-wrap break-words">{{ $message->message }}</div>

                                        {{-- Attachments (Grid Layout) --}}
                                        @if($message->attachments->isNotEmpty())
                                            <div class="mt-3 pt-3 border-t {{ $message->user_id === auth()->id() ? 'border-primary-focus/30' : 'border-base-300/50 dark:border-gray-600/50' }}">
                                                {{-- Responsive Grid --}}
                                                <div class="grid grid-cols-1 xs:grid-cols-2 sm:grid-cols-3 gap-2 sm:gap-3">
                                                    @foreach($message->attachments as $attachment)
                                                        {{-- Use the Livewire TicketAttachmentComponent --}}
                                                        <livewire:ticket-attachment-component :attachment="$attachment" :galleryIndex="$loop->index" :key="'attachment-'.$attachment->id" />
                                                    @endforeach
                                                </div>
                                            </div>
                                        @endif


                                    </div>
                                    {{-- Message Timestamp --}}
                                    <span class="text-xs font-normal text-base-content/50 px-1 mt-0.5">{{ $message->created_at->format('d.m.Y H:i') }}</span>
                                </div>

                                {{-- Timestamp and Avatar for outgoing messages --}}
                                @if($message->user_id === auth()->id())
                                    <span class="text-xs text-slate-400 self-end">{{ $message->created_at->format('H:i') }}</span>
                                    <img class="w-8 h-8 rounded-full object-cover shrink-0"
                                         src="{{ $message->user->getAvatar(['extension' => 'webp', 'size' => 32]) }}"
                                         alt="{{ $message->user->username }}">
                                @endif
                            </div>
                        @endforeach
                    </div>
                @endif
            </div>

            {{-- Reply Form --}}
            @if($ticket->status !== 'closed')
                <form wire:submit="addReply" class="mt-4">
                    {{-- Quoted Message Preview --}}
                     @if($quotedMessage)
                        <div class="mb-2 p-2 border-l-4 border-slate-500 bg-slate-700 rounded-r-lg text-sm relative">
                            <button type="button" wire:click="cancelQuote" class="absolute top-1 right-1 text-base-content/50 hover:text-base-content">
                                <svg class="w-3 h-3" fill="currentColor" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd"></path></svg>
                            </button>
                            <p class="font-semibold text-xs mb-1">{{ __('tickets.replying_to', ['user' => $quotedMessage->user->username]) }}</p>
                            <p class="text-base-content/70 line-clamp-2">{{ $quotedMessage->message }}</p>
                        </div>
                    @endif

                    {{-- Modern Input Area --}}
                    <div class="flex items-center gap-2">
                        <input type="text"
                               wire:model.live="form.message"
                               class="flex-1 bg-slate-800 text-slate-100 rounded-2xl px-4 py-3 focus:outline-none focus:ring-2 focus:ring-blue-500"
                               placeholder="{{ __('tickets.write_reply_placeholder') }}"
                               required
                               wire:keydown.ctrl.enter="addReply"
                               wire:input.debounce.500ms="notifyTyping" />

                        {{-- Attachment Button --}}
                        <button type="button"
                                class="bg-slate-700 hover:bg-slate-600 text-white rounded-full p-3 shadow-lg transition-all duration-200 hover:scale-110"
                                onclick="document.getElementById('attachments').click()"
                                title="{{ __('tickets.attachments') }}">
                            <svg class="w-5 h-5" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 12 20">
                                <path stroke="currentColor" stroke-linejoin="round" stroke-width="2" d="M1 6v8a5 5 0 1 0 10 0V4.5a3.5 3.5 0 1 0-7 0V13a2 2 0 0 0 4 0V6"/>
                            </svg>
                        </button>

                        {{-- Send Button --}}
                        <button type="submit" class="bg-blue-500 hover:bg-blue-600 text-white rounded-full p-3 shadow-lg transition-all duration-200 hover:scale-110" wire:loading.class="opacity-50" wire:target="addReply, form.attachments">
                            <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="w-5 h-5 transform rotate-90">
                                <path stroke-linecap="round" stroke-linejoin="round" d="M6 12L3.269 3.126A59.768 59.768 0 0121.485 12 59.77 59.77 0 013.27 20.876L5.999 12zm0 0h7.5" />
                            </svg>
                        </button>
                    </div>
                    <div class="text-xs text-base-content/50 mt-2 text-center">
                        {{ __('tickets.press_ctrl_enter') }}
                    </div>
                    {{-- Error Message --}}
                    <div class="mt-2">
                        @error('form.message')
                            <div class="text-red-300 text-sm flex items-center gap-1">
                                <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                                    <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clip-rule="evenodd"></path>
                                </svg>
                                <span>{{ $message }}</span>
                            </div>
                        @enderror
                    </div>

                    <!-- Hidden file input -->
                    <div class="hidden">
                        <input wire:model="form.attachments" type="file" id="attachments" multiple wire:loading.attr="disabled" accept="image/*,video/*,audio/*,.pdf,.txt,.log,.zip,.rar" />
                    </div>

                    <!-- Upload Progress -->
                    <div wire:loading wire:target="form.attachments">
                        <div class="mt-3 p-4 bg-slate-700 rounded-xl shadow-lg">
                            <div class="flex items-center justify-between mb-3">
                                <div class="flex items-center gap-2 text-sm">
                                    <span class="loading loading-spinner loading-sm text-blue-300"></span>
                                    <span class="font-medium text-white">{{ __('tickets.uploading_attachments') }}</span>
                                </div>
                                <div class="flex items-center gap-2">
                                    <span class="text-xs font-semibold text-blue-300" x-text="$wire.uploadProgress + '%'">0%</span>
                                    <button type="button" onclick="handleCancelUpload()" class="btn btn-xs btn-ghost btn-circle text-red-300 hover:bg-red-500/20">
                                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                                        </svg>
                                    </button>
                                </div>
                            </div>
                            <div class="w-full bg-slate-800/50 rounded-full h-3 overflow-hidden">
                                <div class="bg-blue-500 h-3 rounded-full transition-all duration-300 ease-in-out"
                                     x-bind:style="'width: ' + $wire.uploadProgress + '%'"></div>
                            </div>
                            <div class="flex justify-between items-center mt-2 text-xs text-slate-300">
                                <p>{{ __('tickets.please_wait_upload') }}</p>
                                <p x-show="$wire.uploadProgress < 100">{{ __('tickets.uploading') }}...</p>
                                <p x-show="$wire.uploadProgress >= 100">{{ __('tickets.processing') }}...</p>
                            </div>
                        </div>
                    </div>

                    <!-- Upload Error -->
                    @if($uploadError)
                    <div wire:key="upload-error" class="mt-3 p-4 text-sm rounded-xl bg-slate-700 border border-red-500/30 shadow-lg animate-fadeIn" role="alert">
                        <div class="flex items-start gap-3">
                            <svg class="w-6 h-6 shrink-0 mt-0.5 text-red-400" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="currentColor" viewBox="0 0 20 20">
                                <path d="M10 .5a9.5 9.5 0 1 0 9.5 9.5A9.51 9.51 0 0 0 10 .5ZM10 15a1 1 0 1 1 0-2 1 1 0 0 1 0 2Zm1-4a1 1 0 0 1-2 0V6a1 1 0 0 1 2 0v5Z"/>
                            </svg>
                            <div class="flex-1">
                                <p class="font-medium text-red-300 text-base">{{ __('tickets.upload_error') }}</p>
                                <p class="mt-2 text-white">{{ $uploadError }}</p>
                                <div class="mt-3 flex gap-2">
                                    <button type="button" wire:click="dismissUploadError" class="px-3 py-1.5 bg-red-500/20 hover:bg-red-500/30 text-red-300 rounded-lg text-xs font-medium transition-colors">
                                        {{ __('tickets.dismiss') }}
                                    </button>
                                    <button type="button" onclick="document.getElementById('attachments').click()" class="px-3 py-1.5 bg-blue-500/20 hover:bg-blue-500/30 text-blue-300 rounded-lg text-xs font-medium transition-colors">
                                        {{ __('tickets.try_again') }}
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                    @endif

                    <!-- Attachment previews -->
                    @if(count($form->attachments) > 0 && !$uploadError)
                        <div class="mt-4 bg-slate-700/70 p-4 rounded-xl shadow-md">
                            <div class="flex justify-between items-center mb-3">
                                <p class="text-sm font-medium text-white">{{ __('tickets.selected_attachments') }} ({{ count($form->attachments) }})</p>
                                <button type="button" wire:click="clearAttachments" class="text-xs text-slate-300 hover:text-red-300 transition-colors">
                                    {{ __('tickets.clear_all') }}
                                </button>
                            </div>
                            <div class="grid grid-cols-1 sm:grid-cols-2 gap-3">
                                @foreach($form->attachments as $index => $attachment)
                                    <div wire:key="attachment-preview-{{ $index }}" class="flex items-center gap-3 p-3 bg-slate-800/70 hover:bg-slate-700 text-white rounded-xl transition-all duration-150 group">
                                        {{-- Icon based on type --}}
                                        @php
                                            $fileType = strtolower($attachment->getClientOriginalExtension());
                                            $isImage = in_array($fileType, ['jpg', 'jpeg', 'png', 'gif', 'webp', 'svg']);
                                            $isVideo = in_array($fileType, ['mp4', 'webm', 'ogg', 'mov', 'avi']);
                                            $isAudio = in_array($fileType, ['mp3', 'wav', 'ogg', 'opus', 'aac', 'm4a']);
                                            $isPdf = $fileType === 'pdf';
                                            $fileSize = round($attachment->getSize() / 1024, 1); // Size in KB
                                            $fileSizeStr = $fileSize > 1024 ? round($fileSize / 1024, 1) . ' MB' : $fileSize . ' KB';
                                        @endphp

                                        @if($isImage)
                                            <div class="w-10 h-10 bg-blue-500/20 rounded-lg flex items-center justify-center shrink-0">
                                                <svg class="w-5 h-5 text-blue-300" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"></path></svg>
                                            </div>
                                        @elseif($isVideo)
                                            <div class="w-10 h-10 bg-purple-500/20 rounded-lg flex items-center justify-center shrink-0">
                                                <svg class="w-5 h-5 text-purple-300" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z"></path></svg>
                                            </div>
                                        @elseif($isAudio)
                                            <div class="w-10 h-10 bg-green-500/20 rounded-lg flex items-center justify-center shrink-0">
                                                <svg class="w-5 h-5 text-green-300" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15.536 8.464a5 5 0 010 7.072m2.828-9.9a9 9 0 010 12.728M5.858 15.858a3 3 0 01-4.243 0V8.142a3 3 0 014.243 0L7 9.284l1.142-1.142a3 3 0 014.243 0v7.716a3 3 0 01-4.243 0L7 14.716 5.858 15.858z"></path></svg>
                                            </div>
                                        @elseif($isPdf)
                                            <div class="w-10 h-10 bg-red-500/20 rounded-lg flex items-center justify-center shrink-0">
                                                <svg class="w-5 h-5 text-red-300" fill="currentColor" viewBox="0 0 24 24"><path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8l-6-6zm-1 11h-2v2h2v-2zm-2-4h2v3h-2V9z"/><path d="M14 2v6h6"/></svg>
                                            </div>
                                        @else
                                            <div class="w-10 h-10 bg-slate-500/20 rounded-lg flex items-center justify-center shrink-0">
                                                <svg class="w-5 h-5 text-slate-300" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15.172 7l-6.586 6.586a2 2 0 102.828 2.828l6.414-6.586a4 4 0 00-5.656-5.656l-6.415 6.585a6 6 0 108.486 8.486L20.5 13"></path></svg>
                                            </div>
                                        @endif

                                        <div class="flex-1 min-w-0">
                                            <p class="text-sm font-medium truncate">{{ $attachment->getClientOriginalName() }}</p>
                                            <p class="text-xs text-slate-400">{{ $fileSizeStr }}</p>
                                        </div>

                                        <button type="button" wire:click="removeAttachment({{ $index }})" class="btn btn-xs btn-ghost btn-circle text-white/70 hover:bg-red-500/20 hover:text-red-300 opacity-70 group-hover:opacity-100 transition-opacity">
                                            <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20"> <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd"></path></svg>
                                        </button>
                                    </div>
                                @endforeach
                            </div>
                        </div>
                    @endif
                </form>
            @else
                <div class="mt-4 p-4 bg-slate-700/50 rounded-xl text-center text-base-content/70">
                    {{ __('tickets.ticket_closed_reply') }}
                    @if($isSupporter)
                        <button wire:click="setStatusAndUpdate('open')" class="btn btn-sm btn-link text-primary">{{ __('tickets.reopen') }}</button>
                    @endif
                </div>
            @endif
        </div>

    {{-- Modals (Image, Video, PDF) - Using Livewire Components --}}
    <livewire:image-gallery />
    <livewire:video-modal />
    <livewire:pdf-modal />

    <!-- Debug component - Will only show in development environment -->
    @if(config('debugging.frontend.debug_panels', false))
    <div id="debug-panel" class="fixed bottom-0 right-0 bg-slate-900/90 text-white p-3 m-4 rounded-lg shadow-lg z-50 text-xs" style="max-width: 400px; max-height: 300px; overflow: auto;">
        <h3 class="font-bold text-sm mb-2 flex justify-between items-center">
            <span>Debug Panel</span>
            <button onclick="this.parentElement.parentElement.classList.toggle('hidden')"
                    class="text-white hover:text-red-400 px-2">×</button>
        </h3>
        <div>
            <div><strong>WebSocket Host:</strong> <span id="debug-ws-host">Checking...</span></div>
            <div><strong>WebSocket Port:</strong> <span id="debug-ws-port">Checking...</span></div>
            <div><strong>WebSocket Status:</strong> <span id="debug-ws-status">Unknown</span></div>
            <div><strong>ticketView function:</strong> <span id="debug-ticket-view">Checking...</span></div>
            <div><strong>Alpine Status:</strong> <span id="debug-alpine">Checking...</span></div>
        </div>
        <div class="mt-2 pt-2 border-t border-white/20">
            <button onclick="testWebSocket()" class="bg-blue-600 hover:bg-blue-700 text-white px-2 py-1 rounded text-xs">
                Test WebSocket
            </button>
            <button onclick="testAlpine()" class="bg-green-600 hover:bg-green-700 text-white px-2 py-1 rounded text-xs ml-1">
                Test Alpine
            </button>
            <button onclick="testTicketView()" class="bg-purple-600 hover:bg-purple-700 text-white px-2 py-1 rounded text-xs ml-1">
                Test ticketView
            </button>
        </div>
    </div>

    <script>
        // Update debug panel when page loads
        document.addEventListener('DOMContentLoaded', function() {
            setTimeout(updateDebugPanel, 1000);
        });

        function updateDebugPanel() {
            // Check WebSocket configuration
            if (window.Echo && window.Echo.connector && window.Echo.connector.options) {
                document.getElementById('debug-ws-host').textContent = window.Echo.connector.options.wsHost || 'Not set';
                document.getElementById('debug-ws-port').textContent = window.Echo.connector.options.wsPort || 'Not set';

                if (window.Echo.connector.pusher && window.Echo.connector.pusher.connection) {
                    document.getElementById('debug-ws-status').textContent = window.Echo.connector.pusher.connection.state;
                } else {
                    document.getElementById('debug-ws-status').textContent = 'Connection not initialized';
                }
            } else {
                document.getElementById('debug-ws-host').textContent = 'Echo not initialized';
                document.getElementById('debug-ws-port').textContent = 'Echo not initialized';
                document.getElementById('debug-ws-status').textContent = 'Echo not initialized';
            }

            // Check ticketView function
            document.getElementById('debug-ticket-view').textContent =
                typeof window.ticketView === 'function' ? 'Available' : 'Not available';

            // Check Alpine status
            document.getElementById('debug-alpine').textContent =
                window.Alpine ? 'Available' : 'Not available';
        }

        function testWebSocket() {
            if (!window.Echo) {
                alert('Echo not initialized');
                return;
            }

            try {
                // Try to connect to Echo
                if (window.Echo.connector && window.Echo.connector.pusher) {
                    if (window.Echo.connector.pusher.connection.state === 'connected') {
                        alert('WebSocket already connected!');
                    } else {
                        window.Echo.connector.pusher.connect();
                        alert('Attempting to connect to WebSocket...');
                    }
                } else {
                    alert('Echo connector not properly initialized');
                }
            } catch(e) {
                alert('WebSocket test error: ' + e.message);
            }

            // Update debug panel
            setTimeout(updateDebugPanel, 1000);
        }

        function testAlpine() {
            if (!window.Alpine) {
                alert('Alpine is not available!');
                return;
            }

            try {
                alert('Alpine is available. Version: ' + (window.Alpine.version || 'unknown'));
            } catch(e) {
                alert('Alpine test error: ' + e.message);
            }
        }

        function testTicketView() {
            if (typeof window.ticketView !== 'function') {
                alert('ticketView function is not available!');
                return;
            }

            try {
                const component = window.ticketView();
                if (component && typeof component === 'object') {
                    alert('ticketView is working and returns: ' + JSON.stringify(Object.keys(component)));
                } else {
                    alert('ticketView returned an unexpected value: ' + component);
                }
            } catch(e) {
                alert('ticketView test error: ' + e.message);
            }
        }

        // Update debug panel every 5 seconds
        setInterval(updateDebugPanel, 5000);
    </script>
    @endif

    <!-- CSS for UI improvements -->
    <style>
        /* Glass Morphism Effect */
        .glass {
            background: rgba(30, 41, 59, 0.6); /* Leicht transparenter Hintergrund */
            backdrop-filter: blur(12px); /* Hintergrundunschärfe */
            border: 1px solid rgba(255, 255, 255, 0.1); /* Dezenter Rand */
            box-shadow: 0 8px 32px 0 rgba(0, 0, 0, 0.37);
        }

        .glass-light {
            background: rgba(255, 255, 255, 0.15);
            backdrop-filter: blur(8px);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .message-bubble-right {
            background: rgba(59, 130, 246, 0.9);
            border-radius: 1rem 1rem 0.25rem 1rem;
        }

        .message-bubble-left {
            background: rgba(51, 65, 85, 0.9);
            border-radius: 1rem 1rem 1rem 0.25rem;
        }

        .message-bubble-ai {
            background: rgba(37, 99, 235, 0.7);
            border-radius: 1rem 1rem 1rem 0.25rem;
            border-left: 3px solid rgba(59, 130, 246, 0.9);
        }

        .typing-dot {
            display: inline-block;
            width: 8px;
            height: 8px;
            border-radius: 50%;
            margin-right: 4px;
            background: white;
            animation: typing 1.5s infinite ease-in-out;
        }

        .typing-dot:nth-child(1) {
            animation-delay: 0s;
        }

        .typing-dot:nth-child(2) {
            animation-delay: 0.3s;
        }

        .typing-dot:nth-child(3) {
            animation-delay: 0.6s;
        }

        @keyframes typing {
            0%, 100% {
                transform: translateY(0);
                opacity: 0.5;
            }
            50% {
                transform: translateY(-5px);
                opacity: 1;
            }
        }

        .modern-input {
            background: rgba(30, 41, 59, 0.4);
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: 1rem;
            color: white;
        }

        .modern-input:focus {
            outline: none;
            border-color: rgba(59, 130, 246, 0.5);
            box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.25);
        }

        .modern-button {
            background: rgb(59, 130, 246);
            border-radius: 9999px;
            transition: all 0.2s ease;
        }

        .modern-button:hover {
            background: rgb(37, 99, 235);
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(37, 99, 235, 0.4);
        }

        /* Typing indicator */
        .typing-dot {
            display: inline-block;
            width: 6px; /* Smaller dots */
            height: 6px;
            border-radius: 50%;
            background-color: currentColor; /* Use text color */
            margin: 0 1.5px;
            animation: typing-dot 1.4s infinite ease-in-out both;
        }
        .typing-dot:nth-child(1) { animation-delay: -0.32s; }
        .typing-dot:nth-child(2) { animation-delay: -0.16s; }
        .typing-dot:nth-child(3) { animation-delay: 0s; }

        @keyframes typing-dot {
            0%, 80%, 100% { transform: scale(0); opacity: 0.5; }
            40% { transform: scale(1.0); opacity: 1; }
        }

        /* New message indicator pulse */
        .new-message-indicator { animation: pulse 2s infinite; }
        @keyframes pulse {
            0% { box-shadow: 0 0 0 0 rgba(59, 130, 246, 0.7); }
            70% { box-shadow: 0 0 0 8px rgba(59, 130, 246, 0); }
            100% { box-shadow: 0 0 0 0 rgba(59, 130, 246, 0); }
        }

        /* Fade-in animation for new messages and typing indicator */
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(5px); }
            to { opacity: 1; transform: translateY(0); }
        }
        .animate-fadeIn { animation: fadeIn 0.3s ease-out forwards; }

        /* Improved scrollbar styling */
        .styled-scrollbar { scrollbar-width: thin; scrollbar-color: rgba(255, 255, 255, 0.3) transparent; } /* Firefox */

        .styled-scrollbar::-webkit-scrollbar { width: 4px; height: 4px; }
        .styled-scrollbar::-webkit-scrollbar-track { background: transparent; border-radius: 3px; }
        .styled-scrollbar::-webkit-scrollbar-thumb { background-color: rgba(255, 255, 255, 0.3); border-radius: 3px; }
        .styled-scrollbar::-webkit-scrollbar-thumb:hover { background-color: rgba(255, 255, 255, 0.5); }

        /* Attachment Preview Enhancements */
        .attachment-preview {
            transition: transform 0.2s ease-in-out, box-shadow 0.2s ease-in-out;
            border-radius: 0.75rem;
            overflow: hidden;
        }
        .attachment-preview:hover {
            transform: scale(1.03);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
        }

        /* Ensure prose styles apply correctly */
        .prose {
            --tw-prose-body: theme(colors.gray.100);
            --tw-prose-headings: theme(colors.white);
            color: white;
        }
        .prose-invert {
             --tw-prose-body: theme(colors.white);
             --tw-prose-headings: theme(colors.white);
             --tw-prose-links: theme(colors.white);
             --tw-prose-bold: theme(colors.white);
             color: white;
        }

        /* Modern chat background */
        .modern-chat-bg {
            background-image: linear-gradient(to bottom right, #0f172a, #1e293b);
        }

        /* Improve touch targets on mobile for file previews */
        @media (max-width: 640px) {
            .btn-mobile {
                @apply p-2; /* Slightly larger touch area */
            }
        }
        .touch-manipulation {
            touch-action: manipulation; /* Improve touch responsiveness */
        }

    </style>

    <!-- JavaScript for copy to clipboard, modals, etc. -->
    @push('scripts')
    <script>
        // Debug function to test if ticketView is accessible
        console.log('Checking if ticketView is accessible:', typeof window.ticketView);

        // Function to cancel file uploads
        function handleCancelUpload() {
            // Call the Livewire method to update the component state
            @this.cancelUpload();

            // Reset the file input to cancel any in-progress uploads
            document.getElementById('attachments').value = '';

            // Show a toast notification
            const toast = document.createElement('div');
            toast.className = 'fixed bottom-4 right-4 bg-blue-600 text-white px-4 py-2 rounded-lg shadow-lg z-[100] text-sm animate-fadeIn';
            toast.textContent = '{{ __('tickets.upload_cancelled') }}';
            document.body.appendChild(toast);
            setTimeout(() => {
                toast.style.transition = 'opacity 0.5s ease-out';
                toast.style.opacity = '0';
                setTimeout(() => toast.remove(), 500);
            }, 2000);
        }

        // Copy to Clipboard
        function copyToClipboard(text) {
            if (!navigator.clipboard) {
                // Fallback for older browsers
                console.warn('Clipboard API not available');
                return;
            }
            navigator.clipboard.writeText(text).then(function() {
                // Show a toast notification (using a simple implementation)
                const toast = document.createElement('div');
                toast.className = 'fixed bottom-4 right-4 bg-green-600 text-white px-3 py-1.5 rounded-lg shadow-lg z-[100] text-sm animate-fadeIn';
                toast.textContent = '{{ __('tickets.copied_to_clipboard') }}';
                document.body.appendChild(toast);
                setTimeout(() => {
                    toast.style.transition = 'opacity 0.5s ease-out';
                    toast.style.opacity = '0';
                    setTimeout(() => toast.remove(), 500);
                }, 2000);
            }).catch(function(err) {
                console.error('Could not copy text: ', err);
            });
        }

        // Initialize custom video players
        function initCustomVideoPlayers() {
            document.querySelectorAll('video[data-player-id]').forEach(video => {
                const playerId = video.getAttribute('data-player-id');
                const container = video.closest('.rounded-xl');
                if (!container) return;

                const overlay = container.querySelector('.play-overlay');
                if (!overlay) return;

                // Toggle play/pause on overlay click
                overlay.addEventListener('click', () => {
                    if (video.paused) {
                        video.play();
                        overlay.classList.add('hidden');
                    } else {
                        video.pause();
                        overlay.classList.remove('hidden');
                    }
                });

                // Show overlay when video is paused
                video.addEventListener('pause', () => {
                    overlay.classList.remove('hidden');
                });

                // Hide overlay when video is playing
                video.addEventListener('play', () => {
                    overlay.classList.add('hidden');
                });
            });
        }

        // Main initialization for Livewire 3
        document.addEventListener('livewire:init', () => {
            console.log('Livewire initialized');

            // Hook for actions after component updates (replaces message.processed hook)
            Livewire.hook('commit', ({ component, succeed }) => {
                succeed(({ snapshot, effects }) => {
                    // This code runs after a component update is successfully processed
                    if (component.name === 'ticket-view') {
                        console.log('Commit hook for ticket-view, re-initializing video players');
                        initCustomVideoPlayers();

                        // Check if buildGalleryData function exists before calling
                        if (typeof buildGalleryData === 'function') {
                            buildGalleryData();
                        }
                    }
                });
            });

            // Initial setup that runs once when Livewire is initialized
            if (typeof initFlowbite !== 'undefined') {
                initFlowbite();
            } else {
                console.warn('Flowbite not loaded, dropdowns/tooltips might not work.');
            }

            // Initialize video players on startup
            initCustomVideoPlayers();

            // Sound toggle listener
            const soundToggle = document.getElementById('sound-toggle');
            if (soundToggle) {
                soundToggle.addEventListener('change', function() {
                    // The actual sound playing logic is handled in ticketView() Alpine component
                    console.log('Sound notifications toggled:', this.checked);
                });
            }
        });

        // For SPA navigation
        document.addEventListener('livewire:navigated', () => {
            console.log('Livewire navigated, re-initializing Flowbite and video players');
            if (typeof initFlowbite !== 'undefined') {
                initFlowbite();
            }
            initCustomVideoPlayers();
        });

        // Global event listeners
        window.addEventListener('attachments-ready', function(event) {
            console.log('Received attachments-ready event:', event.detail);
            if (typeof handleAttachmentUpdates === 'function') {
                handleAttachmentUpdates(event.detail.messageId, event.detail.attachments);
            } else {
                console.warn('handleAttachmentUpdates function not found');
            }
        });

        window.addEventListener('message-added', function(event) {
            console.log('Received message-added event:', event.detail);
            const messagesContainer = document.getElementById('messages-container');
            if (messagesContainer) {
                setTimeout(() => {
                    messagesContainer.scrollTop = messagesContainer.scrollHeight;
                }, 100); // Small delay to allow DOM update
            }
        });
    </script>
    @endpush

</div>
</div>
