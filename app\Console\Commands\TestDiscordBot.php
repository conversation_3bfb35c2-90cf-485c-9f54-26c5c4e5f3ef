<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Discord\Discord;
use Discord\Parts\User\Activity;

class TestDiscordBot extends Command
{
    protected $signature = 'test:discord-bot';
    protected $description = 'Test Discord bot configuration and connection';

    public function handle()
    {
        $this->info('Testing Discord bot configuration...');
        
        $botToken = config('minewache_discord_bot.token');
        $intents = config('minewache_discord_bot.intents');
        
        if (empty($botToken)) {
            $this->error('Discord Bot Token is not configured. Please check your .env file.');
            return Command::FAILURE;
        }
        
        $this->info('Bot token: ' . substr($botToken, 0, 10) . '...');
        $this->info('Intents configured: ' . count($intents));
        
        try {
            $this->info('Attempting to connect to Discord...');
            
            $discord = new Discord([
                'token' => $botToken,
                'loadAllMembers' => false,
                'intents' => $intents,
            ]);
            
            $discord->on('ready', function (Discord $discord) {
                $this->info('✅ Discord Bot Connected Successfully!');
                $this->line('Bot User: ' . $discord->user->username . '#' . $discord->user->discriminator);
                $this->line('Bot ID: ' . $discord->user->id);
                
                // Test setting activity
                $activity = new Activity($discord, [
                    'name' => 'Testing Connection',
                    'type' => Activity::TYPE_PLAYING
                ]);
                $discord->updatePresence($activity);
                $this->info('✅ Bot presence updated successfully');
                
                // Close connection after successful test
                $discord->close();
            });
            
            $discord->on('error', function ($error) {
                $this->error('❌ Discord Bot Error: ' . $error->getMessage());
            });
            
            // Run for a short time to test connection
            $discord->run();
            
            return Command::SUCCESS;
            
        } catch (\Exception $e) {
            $this->error('❌ Failed to connect to Discord: ' . $e->getMessage());
            return Command::FAILURE;
        }
    }
}
