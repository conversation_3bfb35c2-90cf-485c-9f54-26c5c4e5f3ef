# FFmpeg Integration for Media Processing

This document explains how the FFmpeg integration works in the Minewache ticket system and how to set it up. The integration processes media files for both the web interface and Discord bot.

## Overview

The ticket system now includes media processing capabilities using FFmpeg. This allows for:

- Converting videos to web-compatible formats (MP4 with H.264/AAC)
- Generating video thumbnails
- Converting audio files to MP3
- Optimizing images for web display

## Requirements

- FFmpeg installed on the server
- PHP 8.2 or higher
- Laravel 11
- Queue worker running

## Installation

### 1. Install FFmpeg

#### On Ubuntu/Debian:

```bash
sudo apt update
sudo apt install ffmpeg
```

#### On macOS (using Homebrew):

```bash
brew install ffmpeg
```

#### On Windows:

1. Download FFmpeg from [https://ffmpeg.org/download.html](https://ffmpeg.org/download.html)
2. Extract the files to a location on your computer (e.g., `C:\ffmpeg`)
3. Add the `bin` directory to your system PATH

### 2. Verify FFmpeg Installation

Run the following command to verify that FFmpeg is installed correctly:

```bash
ffmpeg -version
```

### 3. Configure Laravel

1. Make sure the FFmpeg paths are set correctly in your `.env` file:

```
FFMPEG_BINARIES=ffmpeg
FFPROBE_BINARIES=ffprobe
FFMPEG_TEMPORARY_FILES_ROOT=/tmp
```

For Windows, you might need to specify the full path:

```
FFMPEG_BINARIES=C:\ffmpeg\bin\ffmpeg.exe
FFPROBE_BINARIES=C:\ffmpeg\bin\ffprobe.exe
FFMPEG_TEMPORARY_FILES_ROOT=C:\temp
```

2. Ensure your queue worker is running:

```bash
# For development
php artisan queue:work --queue=media

# For production (using Supervisor)
sudo supervisorctl status minewache-media-worker
```

The media processing jobs use a dedicated `media` queue to ensure they don't block other jobs. For production, it's recommended to use a process manager like Supervisor to keep the queue worker running.

3. Check the media queue status:

```bash
# Run the queue check script
php scripts/check-media-queue.php

# Or check the queue directly
php artisan queue:monitor redis media
```

## How It Works

### Upload Process

1. When a user uploads a file in a ticket message, the file is stored in the `storage/app/ticket-attachments/{ticket_id}` directory.
2. A record is created in the `ticket_attachments` table with the file information.
3. A job (`ProcessTicketMediaJob`) is dispatched to process the file asynchronously.
4. The queue worker processes the job and updates the database with the processed file information.
5. Both the web interface and Discord bot will use the processed files when available.

### Media Processing

The `ProcessTicketMediaJob` handles different types of media:

#### Videos

- Converts to MP4 format with H.264 video codec and AAC audio codec
- Generates a thumbnail image from the video
- Resizes videos larger than 720p to maintain reasonable file sizes
- Stores metadata like duration, dimensions, etc.

#### Audio

- Converts to MP3 format for universal compatibility
- Stores metadata like duration

#### Images

- Creates thumbnails for large images
- Preserves original image for download

### Display in UI

The ticket view has been enhanced to display different types of media:

- **Images**: Displayed as thumbnails that can be clicked to view full size
- **Videos**: Displayed as thumbnails with a play button that opens a video player modal
- **Audio**: Displayed with an audio player
- **Other files**: Displayed as download links

## Troubleshooting

### Common Issues

1. **FFmpeg not found**: Make sure FFmpeg is installed and the path is correctly set in your `.env` file.

2. **Processing fails**: Check the Laravel logs for detailed error messages. Common issues include:
   - Insufficient permissions to read/write files
   - Unsupported media formats
   - Insufficient server resources (memory, CPU)

3. **Queue not processing**: Ensure your queue worker is running. The queue worker should start automatically with `npm run dev` during development. For production, use a process manager like Supervisor.

4. **Discord bot not receiving processed files**: Check that the Discord bot is running and that the API token is correctly set in your `.env` file. The bot should automatically receive the processed files when they are available.

### Debugging

To debug media processing issues, you can:

1. Check the `processing_error` field in the `ticket_attachments` table for failed jobs.
2. Review the Laravel logs in `storage/logs/laravel.log`.
3. Test FFmpeg commands manually on the server to ensure they work as expected.

## Extending

The media processing system is designed to be extensible. To add support for additional formats or processing options:

1. Modify the `ProcessTicketMediaJob` class to handle new media types or processing options.
2. Update the view templates to display the new media types appropriately.

## Security Considerations

- All uploaded files are stored in a private storage location and are not directly accessible via URL.
- Processed media files are served through Laravel routes that can include authentication checks.
- File size limits are enforced to prevent abuse (currently 10MB per file).
- The Discord bot uses API token authentication to access the files, ensuring secure transmission.

## Discord Bot Integration

The FFmpeg integration works seamlessly with the Discord bot:

1. When a file is uploaded through the web interface, it's processed and then sent to Discord with the correct format.
2. When a file is uploaded through Discord, it's downloaded, processed, and then displayed correctly in the web interface.
3. The bot uses the processed versions of files when available, ensuring optimal file sizes and compatibility.
4. File extensions and MIME types are automatically corrected based on the processed file format.
