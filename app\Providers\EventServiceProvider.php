<?php

namespace App\Providers;

use App\Events\AttachmentProcessingFinished;
use App\Events\Discord\UserRolesChanged;
use App\Events\TicketMessageCreated;
use App\Events\TicketRepliedByUser; // Added this line
use App\Listeners\CheckForAIResponse;
use App\Listeners\Discord\SyncUserPermissions;
use App\Listeners\HandleAttachmentProcessingFinished;
use App\Listeners\SendTicketReplyToDiscord; // Added this line
use Illuminate\Auth\Events\Registered;
use Illuminate\Auth\Listeners\SendEmailVerificationNotification;
use Illuminate\Foundation\Support\Providers\EventServiceProvider as ServiceProvider;
use Illuminate\Support\Facades\Event;

class EventServiceProvider extends ServiceProvider
{
    /**
     * The event to listener mappings for the application.
     *
     * @var array<class-string, array<int, class-string>>
     */
    protected $listen = [
        Registered::class => [
            SendEmailVerificationNotification::class,
        ],
        AttachmentProcessingFinished::class => [
            HandleAttachmentProcessingFinished::class,
        ],
        TicketMessageCreated::class => [
            CheckForAIResponse::class,
        ],
        UserRolesChanged::class => [
            SyncUserPermissions::class,
        ],
        TicketRepliedByUser::class => [ // Added this block
            SendTicketReplyToDiscord::class,
        ],
    ];

    /**
     * Register any events for your application.
     */
    public function boot(): void
    {
        //
    }

    /**
     * Determine if events and listeners should be automatically discovered.
     */
    public function shouldDiscoverEvents(): bool
    {
        return false;
    }
}
