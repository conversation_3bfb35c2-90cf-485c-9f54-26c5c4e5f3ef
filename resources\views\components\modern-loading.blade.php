@props([
    'type' => 'spinner', // spinner, dots, pulse, skeleton
    'size' => 'md', // xs, sm, md, lg, xl
    'color' => 'primary',
    'text' => null,
    'overlay' => false,
    'fullscreen' => false
])

@php
$sizeClasses = match($size) {
    'xs' => 'w-4 h-4',
    'sm' => 'w-6 h-6',
    'md' => 'w-8 h-8',
    'lg' => 'w-12 h-12',
    'xl' => 'w-16 h-16',
    default => 'w-8 h-8'
};

$colorClasses = match($color) {
    'primary' => 'text-primary',
    'secondary' => 'text-secondary',
    'accent' => 'text-accent',
    'success' => 'text-success',
    'warning' => 'text-warning',
    'error' => 'text-error',
    'base' => 'text-base-content',
    default => 'text-primary'
};

$containerClasses = 'flex items-center justify-center';
if ($overlay) {
    $containerClasses .= ' absolute inset-0 bg-base-100/80 backdrop-blur-sm z-50';
}
if ($fullscreen) {
    $containerClasses .= ' fixed inset-0 bg-base-100/90 backdrop-blur-md z-[9999]';
}
@endphp

<div {{ $attributes->merge(['class' => $containerClasses]) }}>
    <div class="flex flex-col items-center gap-3">
        @if($type === 'spinner')
            <div class="{{ $sizeClasses }} {{ $colorClasses }} animate-spin">
                <svg fill="none" viewBox="0 0 24 24">
                    <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                    <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
            </div>
        @elseif($type === 'dots')
            <div class="flex space-x-1">
                @for($i = 0; $i < 3; $i++)
                    <div class="w-2 h-2 {{ $colorClasses }} bg-current rounded-full animate-pulse" style="animation-delay: {{ $i * 0.2 }}s"></div>
                @endfor
            </div>
        @elseif($type === 'pulse')
            <div class="{{ $sizeClasses }} {{ $colorClasses }} bg-current rounded-full animate-pulse"></div>
        @elseif($type === 'skeleton')
            <div class="animate-pulse space-y-3">
                <div class="h-4 bg-base-300 rounded w-3/4"></div>
                <div class="h-4 bg-base-300 rounded w-1/2"></div>
                <div class="h-4 bg-base-300 rounded w-5/6"></div>
            </div>
        @endif
        
        @if($text)
            <div class="text-sm {{ $colorClasses }} font-medium animate-pulse">
                {{ $text }}
            </div>
        @endif
        
        {{ $slot }}
    </div>
</div>

<style>
    /* Enhanced loading animations */
    @keyframes modern-spin {
        from {
            transform: rotate(0deg);
        }
        to {
            transform: rotate(360deg);
        }
    }
    
    @keyframes modern-pulse {
        0%, 100% {
            opacity: 1;
        }
        50% {
            opacity: 0.5;
        }
    }
    
    @keyframes modern-bounce {
        0%, 80%, 100% {
            transform: scale(0);
        }
        40% {
            transform: scale(1);
        }
    }
    
    .modern-loading .animate-spin {
        animation: modern-spin 1s linear infinite;
    }
    
    .modern-loading .animate-pulse {
        animation: modern-pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
    }
    
    .modern-loading .animate-bounce {
        animation: modern-bounce 1.4s ease-in-out infinite both;
    }
    
    /* Dots animation */
    .modern-loading .dots-animation .dot:nth-child(1) {
        animation-delay: 0s;
    }
    
    .modern-loading .dots-animation .dot:nth-child(2) {
        animation-delay: 0.2s;
    }
    
    .modern-loading .dots-animation .dot:nth-child(3) {
        animation-delay: 0.4s;
    }
    
    /* Theme-specific adjustments */
    [data-theme="minewache-light"] .modern-loading {
        color: rgba(0, 0, 0, 0.7);
    }
    
    [data-theme="minewache-dark"] .modern-loading {
        color: rgba(255, 255, 255, 0.8);
    }
    
    [data-theme="minewache-high-contrast"] .modern-loading {
        font-weight: bold;
        border: 2px solid currentColor;
        border-radius: 0.5rem;
        padding: 1rem;
    }
    
    [data-theme="minewache-colorful"] .modern-loading {
        background: linear-gradient(45deg, var(--color-primary), var(--color-secondary));
        background-clip: text;
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
    }
    
    /* Accessibility improvements */
    @media (prefers-reduced-motion: reduce) {
        .modern-loading .animate-spin,
        .modern-loading .animate-pulse,
        .modern-loading .animate-bounce {
            animation: none;
        }
        
        .modern-loading .animate-pulse {
            opacity: 0.8;
        }
    }
    
    /* Focus management for overlays */
    .modern-loading[data-overlay="true"] {
        pointer-events: auto;
    }
    
    .modern-loading[data-overlay="true"] * {
        pointer-events: none;
    }
</style>
