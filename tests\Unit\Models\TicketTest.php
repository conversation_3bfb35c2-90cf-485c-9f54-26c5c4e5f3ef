<?php

namespace Tests\Unit\Models;

use App\Models\Ticket;
use App\Models\TicketMessage;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class TicketTest extends TestCase
{
    use RefreshDatabase;

    /** @test */
    public function it_has_fillable_attributes()
    {
        $fillable = [
            'user_id',
            'title',
            'description',
            'status',
            'discord_channel_id',
            'assigned_to',
            'is_reconstructed',
            'gemini_consent',
            'gemini_consent_at',
        ];

        $ticket = new Ticket();
        $this->assertEquals($fillable, $ticket->getFillable());
    }

    /** @test */
    public function it_casts_attributes_correctly()
    {
        $ticket = new Ticket();
        $casts = $ticket->getCasts();

        $this->assertArrayHasKey('is_reconstructed', $casts);
        $this->assertEquals('boolean', $casts['is_reconstructed']);

        $this->assertArrayHasKey('gemini_consent', $casts);
        $this->assertEquals('boolean', $casts['gemini_consent']);

        $this->assertArrayHasKey('gemini_consent_at', $casts);
        $this->assertEquals('datetime', $casts['gemini_consent_at']);

        $this->assertArrayHasKey('created_at', $casts);
        $this->assertEquals('datetime', $casts['created_at']);

        $this->assertArrayHasKey('updated_at', $casts);
        $this->assertEquals('datetime', $casts['updated_at']);
    }

    /** @test */
    public function it_belongs_to_a_user()
    {
        $user = User::factory()->create();
        $ticket = Ticket::factory()->create(['user_id' => $user->id]);

        $this->assertInstanceOf(User::class, $ticket->user);
        $this->assertEquals($user->id, $ticket->user->id);
    }

    /** @test */
    public function it_can_be_assigned_to_a_user()
    {
        $user = User::factory()->create();
        $supporter = User::factory()->create();
        $ticket = Ticket::factory()->create([
            'user_id' => $user->id,
            'assigned_to' => $supporter->id
        ]);

        $this->assertInstanceOf(User::class, $ticket->assignedTo);
        $this->assertEquals($supporter->id, $ticket->assignedTo->id);
    }

    /** @test */
    public function it_has_many_messages()
    {
        $ticket = Ticket::factory()->create();
        $message = TicketMessage::factory()->create(['ticket_id' => $ticket->id]);

        $this->assertInstanceOf(TicketMessage::class, $ticket->messages->first());
        $this->assertEquals($message->id, $ticket->messages->first()->id);
    }

    /** @test */
    public function it_has_status_label_attribute()
    {
        $openTicket = Ticket::factory()->create(['status' => 'open']);
        $inProgressTicket = Ticket::factory()->create(['status' => 'in_progress']);
        $closedTicket = Ticket::factory()->create(['status' => 'closed']);

        $this->assertEquals(__('tickets.status_open'), $openTicket->statusLabel);
        $this->assertEquals(__('tickets.status_in_progress'), $inProgressTicket->statusLabel);
        $this->assertEquals(__('tickets.status_closed'), $closedTicket->statusLabel);
    }

    /** @test */
    public function it_has_status_color_attribute()
    {
        $openTicket = Ticket::factory()->create(['status' => 'open']);
        $inProgressTicket = Ticket::factory()->create(['status' => 'in_progress']);
        $closedTicket = Ticket::factory()->create(['status' => 'closed']);

        $this->assertEquals('badge-warning', $openTicket->statusColor);
        $this->assertEquals('badge-info', $inProgressTicket->statusColor);
        $this->assertEquals('badge-success', $closedTicket->statusColor);
    }
}
