<x-app-layout>
    <x-slot name="header">
        <h2 class="font-display text-xl md:text-2xl text-gray-900 dark:text-white leading-tight">
            {{ __('Admin Dashboard') }}
        </h2>
    </x-slot>

    <div class="py-12">
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
            <!-- Dashboard Overview -->
            <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
                <!-- Stats Cards -->
                <div class="bg-white dark:bg-base-100 overflow-hidden shadow-lg rounded-lg p-6 border-l-4 border-primary">
                    <div class="flex items-center">
                        <div class="p-3 rounded-full bg-primary/10 mr-4">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-8 w-8 text-primary" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197M13 7a4 4 0 11-8 0 4 4 0 018 0z" />
                            </svg>
                        </div>
                        <div>
                            <p class="text-gray-500 dark:text-gray-400 text-sm font-medium">Teammitglieder</p>
                            <p class="text-3xl font-bold text-gray-900 dark:text-white">{{ \App\Models\User::whereRaw('permissions & ?', [\App\Enums\Role::MINEWACHE_TEAM->value])->count() }}</p>
                        </div>
                    </div>
                </div>

                <div class="bg-white dark:bg-base-100 overflow-hidden shadow-lg rounded-lg p-6 border-l-4 border-warning">
                    <div class="flex items-center">
                        <div class="p-3 rounded-full bg-warning/10 mr-4">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-8 w-8 text-warning" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                            </svg>
                        </div>
                        <div>
                            <p class="text-gray-500 dark:text-gray-400 text-sm font-medium">Offene Bewerbungen</p>
                            <p class="text-3xl font-bold text-gray-900 dark:text-white">{{ \App\Models\Application::where('status', 'pending')->count() }}</p>
                        </div>
                    </div>
                </div>

                <div class="bg-white dark:bg-base-100 overflow-hidden shadow-lg rounded-lg p-6 border-l-4 border-success">
                    <div class="flex items-center">
                        <div class="p-3 rounded-full bg-success/10 mr-4">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-8 w-8 text-success" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                            </svg>
                        </div>
                        <div>
                            <p class="text-gray-500 dark:text-gray-400 text-sm font-medium">Angenommene Bewerber</p>
                            <p class="text-3xl font-bold text-gray-900 dark:text-white">{{ \App\Models\User::whereRaw('permissions & ?', [\App\Enums\Role::ANGENOMMEN->value])->count() }}</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Admin Modules -->
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                <!-- Bewerbungsverwaltung -->
                <a href="{{ route('admin.applications') }}" class="bg-white dark:bg-base-100 overflow-hidden shadow-lg rounded-lg p-6 transition-all duration-300 hover:shadow-xl hover:-translate-y-1 border border-base-300 hover:border-primary">
                    <div class="flex items-center mb-4">
                        <div class="p-3 rounded-full bg-primary/10 mr-4">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-8 w-8 text-primary" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
                            </svg>
                        </div>
                        <h3 class="text-xl font-bold text-gray-900 dark:text-white">Bewerbungsverwaltung</h3>
                    </div>
                    <p class="text-gray-600 dark:text-gray-300">Bewerbungen einsehen, bearbeiten und beantworten.</p>
                </a>

                <!-- Benutzerverwaltung -->
                <a href="{{ route('admin.users') }}" class="bg-white dark:bg-base-100 overflow-hidden shadow-lg rounded-lg p-6 transition-all duration-300 hover:shadow-xl hover:-translate-y-1 border border-base-300 hover:border-primary">
                    <div class="flex items-center mb-4">
                        <div class="p-3 rounded-full bg-primary/10 mr-4">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-8 w-8 text-primary" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
                            </svg>
                        </div>
                        <h3 class="text-xl font-bold text-gray-900 dark:text-white">Benutzerverwaltung</h3>
                    </div>
                    <p class="text-gray-600 dark:text-gray-300">Benutzer verwalten, Rollen zuweisen und Berechtigungen bearbeiten.</p>
                </a>

                <!-- Discord Integration -->
                <a href="{{ route('admin.discord-bot-status') }}" class="bg-white dark:bg-base-100 overflow-hidden shadow-lg rounded-lg p-6 transition-all duration-300 hover:shadow-xl hover:-translate-y-1 border border-base-300 hover:border-primary">
                    <div class="flex items-center mb-4">
                        <div class="p-3 rounded-full bg-primary/10 mr-4">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-8 w-8 text-primary" viewBox="0 0 24 24" fill="currentColor">
                                <path d="M20.317 4.37a19.791 19.791 0 0 0-4.885-1.515a.074.074 0 0 0-.079.037c-.21.375-.444.864-.608 1.25a18.27 18.27 0 0 0-5.487 0a12.64 12.64 0 0 0-.617-1.25a.077.077 0 0 0-.079-.037A19.736 19.736 0 0 0 3.677 4.37a.07.07 0 0 0-.032.027C.533 9.046-.32 13.58.099 18.057a.082.082 0 0 0 .031.057a19.9 19.9 0 0 0 5.993 3.03a.078.078 0 0 0 .084-.028a14.09 14.09 0 0 0 1.226-1.994a.076.076 0 0 0-.041-.106a13.107 13.107 0 0 1-1.872-.892a.077.077 0 0 1-.008-.128a10.2 10.2 0 0 0 .372-.292a.074.074 0 0 1 .077-.01c3.928 1.793 8.18 1.793 12.062 0a.074.074 0 0 1 .078.01c.12.098.246.198.373.292a.077.077 0 0 1-.006.127a12.299 12.299 0 0 1-1.873.892a.077.077 0 0 0-.041.107c.36.698.772 1.362 1.225 1.993a.076.076 0 0 0 .084.028a19.839 19.839 0 0 0 6.002-3.03a.077.077 0 0 0 .032-.054c.5-5.177-.838-9.674-3.549-13.66a.061.061 0 0 0-.031-.03zM8.02 15.33c-1.183 0-2.157-1.085-2.157-2.419c0-1.333.956-2.419 2.157-2.419c1.21 0 2.176 1.096 2.157 2.42c0 1.333-.956 2.418-2.157 2.418zm7.975 0c-1.183 0-2.157-1.085-2.157-2.419c0-1.333.955-2.419 2.157-2.419c1.21 0 2.176 1.096 2.157 2.42c0 1.333-.946 2.418-2.157 2.418z" />
                            </svg>
                        </div>
                        <h3 class="text-xl font-bold text-gray-900 dark:text-white">Discord Integration</h3>
                    </div>
                    <p class="text-gray-600 dark:text-gray-300">Discord-Bot verwalten, Status überwachen und Rollen synchronisieren.</p>

                    @php
                        $discordBotService = new \App\Services\DiscordBotService();
                        $botStatus = $discordBotService->getStatus(true); // Use cached status
                        $isOnline = $botStatus && ($botStatus['online'] ?? false);
                    @endphp

                    <div class="mt-2">
                        <span class="badge {{ $isOnline ? 'badge-success' : 'badge-error' }} gap-1">
                            <div class="w-2 h-2 rounded-full {{ $isOnline ? 'bg-success-content' : 'bg-error-content' }} animate-pulse"></div>
                            Bot {{ $isOnline ? 'Online' : 'Offline' }}
                        </span>
                    </div>
                </a>

                <!-- API-Dokumentation -->
                <a href="{{ route('admin.api-docs') }}" class="bg-white dark:bg-base-100 overflow-hidden shadow-lg rounded-lg p-6 transition-all duration-300 hover:shadow-xl hover:-translate-y-1 border border-base-300 hover:border-primary">
                    <div class="flex items-center mb-4">
                        <div class="p-3 rounded-full bg-primary/10 mr-4">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-8 w-8 text-primary" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                            </svg>
                        </div>
                        <h3 class="text-xl font-bold text-gray-900 dark:text-white">API-Dokumentation</h3>
                    </div>
                    <p class="text-gray-600 dark:text-gray-300">API-Endpunkte und Authentifizierung verwalten.</p>
                </a>

                <!-- System Settings -->
                <a href="{{ route('admin.system') }}" class="bg-white dark:bg-base-100 overflow-hidden shadow-lg rounded-lg p-6 transition-all duration-300 hover:shadow-xl hover:-translate-y-1 border border-base-300 hover:border-primary">
                    <div class="flex items-center mb-4">
                        <div class="p-3 rounded-full bg-primary/10 mr-4">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-8 w-8 text-primary" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" />
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                            </svg>
                        </div>
                        <h3 class="text-xl font-bold text-gray-900 dark:text-white">Systemeinstellungen</h3>
                    </div>
                    <p class="text-gray-600 dark:text-gray-300">Website-Konfiguration, API-Tokens und Systemeinstellungen verwalten.</p>
                </a>

                <!-- Support Tickets -->
                <a href="{{ route('tickets.index') }}" class="bg-white dark:bg-base-100 overflow-hidden shadow-lg rounded-lg p-6 transition-all duration-300 hover:shadow-xl hover:-translate-y-1 border border-base-300 hover:border-primary">
                    <div class="flex items-center mb-4">
                        <div class="p-3 rounded-full bg-primary/10 mr-4">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-8 w-8 text-primary" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 8h10M7 12h4m1 8l-4-4H5a2 2 0 01-2-2V6a2 2 0 012-2h14a2 2 0 012 2v8a2 2 0 01-2 2h-3l-4 4z" />
                            </svg>
                        </div>
                        <h3 class="text-xl font-semibold text-gray-900 dark:text-white">{{ __('tickets.support_tickets') }}</h3>
                    </div>
                    <p class="text-gray-600 dark:text-gray-300">{{ __('tickets.manage_support_tickets') }}</p>
                </a>

                <!-- Logs & Monitoring -->
                <a href="{{ route('admin.logs') }}" class="bg-white dark:bg-base-100 overflow-hidden shadow-lg rounded-lg p-6 transition-all duration-300 hover:shadow-xl hover:-translate-y-1 border border-base-300 hover:border-primary">
                    <div class="flex items-center mb-4">
                        <div class="p-3 rounded-full bg-primary/10 mr-4">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-8 w-8 text-primary" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                            </svg>
                        </div>
                        <h3 class="text-xl font-bold text-gray-900 dark:text-white">Logs & Monitoring</h3>
                    </div>
                    <p class="text-gray-600 dark:text-gray-300">System-Logs einsehen und Website-Performance überwachen.</p>
                </a>

                <!-- Gemini AI Monitoring -->
                <a href="{{ route('admin.gemini.monitoring') }}" class="bg-white dark:bg-base-100 overflow-hidden shadow-lg rounded-lg p-6 transition-all duration-300 hover:shadow-xl hover:-translate-y-1 border border-base-300 hover:border-primary">
                    <div class="flex items-center mb-4">
                        <div class="p-3 rounded-full bg-primary/10 mr-4">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-8 w-8 text-primary" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z" />
                            </svg>
                        </div>
                        <h3 class="text-xl font-bold text-gray-900 dark:text-white">Gemini AI Monitoring</h3>
                    </div>
                    <p class="text-gray-600 dark:text-gray-300">KI-Nutzung überwachen, Rate Limits verwalten und Statistiken einsehen.</p>

                    @php
                        $rateLimiter = new \App\Services\GeminiRateLimiter();
                        $usageStats = $rateLimiter->getUsageStats();
                        $aiMessagesToday = \App\Models\TicketMessage::where('message_source', 'ai')
                            ->whereDate('created_at', today())
                            ->count();
                    @endphp

                    <div class="mt-2">
                        <span class="badge badge-primary gap-1">
                            {{ $aiMessagesToday }} KI-Antworten heute
                        </span>
                    </div>
                </a>

                <!-- AI Video Tag Generator -->
                <a href="{{ route('admin.tag.generator') }}" class="bg-white dark:bg-base-100 overflow-hidden shadow-lg rounded-lg p-6 transition-all duration-300 hover:shadow-xl hover:-translate-y-1 border border-base-300 hover:border-primary">
                    <div class="flex items-center mb-4">
                        <div class="p-3 rounded-full bg-primary/10 mr-4">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-8 w-8 text-primary" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 4V2a1 1 0 011-1h4a1 1 0 011 1v2m0 0V1a1 1 0 011-1h2a1 1 0 011 1v3M7 4H5a2 2 0 00-2 2v11a2 2 0 002 2h10a2 2 0 002-2V6a2 2 0 00-2-2h-2M7 4h6m-6 0v3m6-3v3m-6 0h6m-6 0H5m8 0h2M9 11h6m-6 4h6" />
                            </svg>
                        </div>
                        <h3 class="text-xl font-bold text-gray-900 dark:text-white">AI Video Tag Generator</h3>
                    </div>
                    <p class="text-gray-600 dark:text-gray-300">Videos hochladen und SEO-optimierte Tags und Zusammenfassungen mit KI generieren.</p>

                    <div class="mt-2">
                        <span class="badge badge-secondary gap-1">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z" />
                            </svg>
                            Video-KI
                        </span>
                    </div>
                </a>
            </div>

            <!-- Neueste Benutzer -->
            <div class="mt-8 bg-white dark:bg-base-100 overflow-hidden shadow-lg rounded-lg p-6">
                <h3 class="text-xl font-bold text-gray-900 dark:text-white mb-4">Neueste Benutzer</h3>

                <div class="overflow-x-auto">
                    <table class="table w-full">
                        <thead>
                            <tr>
                                <th class="bg-base-200">Benutzer</th>
                                <th class="bg-base-200">Discord Name</th>
                                <th class="bg-base-200">Registriert am</th>
                                <th class="bg-base-200">Aktionen</th>
                            </tr>
                        </thead>
                        <tbody>
                            @php
                                $recentUsers = \App\Models\User::orderBy('created_at', 'desc')->take(5)->get();
                            @endphp

                            @forelse($recentUsers as $user)
                                <tr class="hover">
                                    <td class="flex items-center gap-2">
                                        <div class="avatar">
                                            <div class="w-8 rounded-full">
                                                <img src="{{ $user->getAvatar(['extension' => 'webp', 'size' => 32]) }}" alt="{{ $user->username }}" />
                                            </div>
                                        </div>
                                        <span>{{ $user->username }}</span>
                                    </td>
                                    <td>{{ $user->global_name }}</td>
                                    <td>{{ $user->created_at->format('d.m.Y H:i') }}</td>
                                    <td>
                                        <a href="{{ route('admin.users') }}?search={{ $user->username }}" class="btn btn-sm btn-ghost">
                                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                                            </svg>
                                        </a>
                                    </td>
                                </tr>
                            @empty
                                <tr>
                                    <td colspan="4" class="text-center py-4">Keine Benutzer gefunden</td>
                                </tr>
                            @endforelse
                        </tbody>
                    </table>
                </div>

                <div class="mt-4 text-center">
                    <x-modern-button variant="primary" href="{{ route('admin.users') }}" >Alle Benutzer anzeigen</x-modern-button>
                </div>
            </div>
        </div>
    </div>
</x-app-layout>
