<?php if (isset($component)) { $__componentOriginal9ac128a9029c0e4701924bd2d73d7f54 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54 = $attributes; } ?>
<?php $component = App\View\Components\AppLayout::resolve([] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('app-layout'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\App\View\Components\AppLayout::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
     <?php $__env->slot('heading', null, []); ?> 
        <?php echo e(__('tickets.create_ticket')); ?>

     <?php $__env->endSlot(); ?>

    <!-- Enhanced Theme Switcher -->
    <?php if (isset($component)) { $__componentOriginal5ae8d5ca1c67746ce4565d9cc8953813 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal5ae8d5ca1c67746ce4565d9cc8953813 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.enhanced-theme-switcher','data' => ['position' => 'bottom-left','type' => 'simple']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('enhanced-theme-switcher'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['position' => 'bottom-left','type' => 'simple']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal5ae8d5ca1c67746ce4565d9cc8953813)): ?>
<?php $attributes = $__attributesOriginal5ae8d5ca1c67746ce4565d9cc8953813; ?>
<?php unset($__attributesOriginal5ae8d5ca1c67746ce4565d9cc8953813); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal5ae8d5ca1c67746ce4565d9cc8953813)): ?>
<?php $component = $__componentOriginal5ae8d5ca1c67746ce4565d9cc8953813; ?>
<?php unset($__componentOriginal5ae8d5ca1c67746ce4565d9cc8953813); ?>
<?php endif; ?>

    <div class="py-8">
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
            <?php
$__split = function ($name, $params = []) {
    return [$name, $params];
};
[$__name, $__params] = $__split('ticket-create', []);

$__html = app('livewire')->mount($__name, $__params, 'lw-1672298166-0', $__slots ?? [], get_defined_vars());

echo $__html;

unset($__html);
unset($__name);
unset($__params);
unset($__split);
if (isset($__slots)) unset($__slots);
?>
        </div>
    </div>
 <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54)): ?>
<?php $attributes = $__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54; ?>
<?php unset($__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal9ac128a9029c0e4701924bd2d73d7f54)): ?>
<?php $component = $__componentOriginal9ac128a9029c0e4701924bd2d73d7f54; ?>
<?php unset($__componentOriginal9ac128a9029c0e4701924bd2d73d7f54); ?>
<?php endif; ?>
<?php /**PATH C:\Users\<USER>\Documents\GitHub\minewache-website\resources\views/tickets/create.blade.php ENDPATH**/ ?>