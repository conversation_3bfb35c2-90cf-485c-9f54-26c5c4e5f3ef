<div class="w-full bg-base-100 text-white min-h-screen pb-12 rounded-lg">
    <!-- Livewire loading indicator -->
    <div wire:loading class="fixed top-0 left-0 right-0 z-50 bg-primary text-white text-center py-1 text-sm">
        <?php echo e(__('youtube.loading')); ?>

    </div>
    <!-- Hero section with featured video (Enhanced) -->
    <!--[if BLOCK]><![endif]--><?php if($featuredVideo): ?>
    <div class="relative w-full rounded-xl overflow-hidden" style="height: 70vh; min-height: 450px; max-height: 85vh;">
        <!-- Enhanced gradient overlays for better text readability -->
        <div class="absolute inset-0 bg-gradient-to-b from-base-100 via-transparent to-base-100 z-10"></div>
        <div class="absolute inset-0 bg-gradient-to-r from-base-100 via-base-100 to-transparent z-10"></div>
        <div class="absolute inset-0 bg-base-100 z-5"></div> <!-- Overall dimming -->

        <!-- Background thumbnail with subtle zoom animation -->
        <div class="w-full h-full overflow-hidden">
            <!--[if BLOCK]><![endif]--><?php if($featuredVideo->thumbnail_url): ?>
                <img src="<?php echo e($featuredVideo->thumbnail_url); ?>" alt="<?php echo e($featuredVideo->title); ?>" class="w-full h-full object-cover animate-slow-zoom">
            <?php else: ?>
                <img src="https://img.youtube.com/vi/<?php echo e($featuredVideo->link); ?>/maxresdefault.jpg" alt="<?php echo e($featuredVideo->title); ?>" class="w-full h-full object-cover animate-slow-zoom" onerror="this.src='https://img.youtube.com/vi/<?php echo e($featuredVideo->link); ?>/0.jpg';">
            <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
        </div>

        <!-- Enhanced content positioning with better spacing -->
        <div class="absolute bottom-0 left-0 p-6 sm:p-10 md:p-14 z-20 w-full lg:w-2/3 pb-14 sm:pb-20 md:pb-28">
            <div class="animate-slide-up opacity-0">
                <!-- Enhanced typography with better hierarchy -->
                <div class="flex items-center gap-3 mb-3">
                    <h1 class="text-3xl sm:text-4xl md:text-5xl font-bold text-base-content">Minewache</h1>
                    <div class="h-8 w-0.5 bg-primary/70 hidden sm:block"></div>
                    <span class="bg-primary text-white px-3 py-1 text-xs sm:text-sm font-bold rounded-md shadow-md hidden sm:block"><?php echo e(__('youtube.new')); ?></span>
                </div>

                <div class="mb-4">
                    <div class="flex items-center gap-2 mb-1">
                        <span class="bg-primary text-white px-2 py-0.5 text-xs font-bold rounded-md shadow-md sm:hidden"><?php echo e(__('youtube.new')); ?></span>
                        <span class="text-primary font-medium text-sm">S<?php echo e($featuredVideo->season); ?> E<?php echo e($featuredVideo->episode); ?></span>
                    </div>
                    <h2 class="text-xl sm:text-2xl md:text-3xl font-semibold text-base-content leading-tight"><?php echo e($featuredVideo->title ?: __('youtube.season_episode_format', ['season' => $featuredVideo->season, 'episode' => $featuredVideo->episode])); ?></h2>
                </div>

                <!-- Enhanced video metadata with icons -->
                <div class="flex flex-wrap items-center gap-4 text-sm text-gray-500 mb-4 sm:mb-5">
                    <!--[if BLOCK]><![endif]--><?php if($featuredVideo->published_at): ?>
                        <span class="flex items-center gap-1.5">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
                            </svg>
                            <span><?php echo e($featuredVideo->published_time_ago); ?></span>
                        </span>
                    <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
                    <!--[if BLOCK]><![endif]--><?php if($featuredVideo->duration_seconds): ?>
                        <span class="flex items-center gap-1.5">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                            </svg>
                            <span><?php echo e($featuredVideo->formatted_duration); ?></span>
                        </span>
                    <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
                    <!--[if BLOCK]><![endif]--><?php if($featuredVideo->view_count): ?>
                        <span class="flex items-center gap-1.5">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                            </svg>
                            <span><?php echo e($featuredVideo->formatted_view_count); ?></span>
                        </span>
                    <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
                </div>

                <!-- Enhanced description with better readability -->
                <div x-data="{ expanded: false }" class="relative mb-6 sm:mb-8">
                    <p class="text-sm sm:text-base md:text-lg text-base-content max-w-2xl transition-all duration-300"
                       :class="{ 'line-clamp-3': !expanded }">
                        <?php echo e($featuredVideo->description ?: __('youtube.watch_latest_videos')); ?>

                    </p>
                    <!--[if BLOCK]><![endif]--><?php if(strlen($featuredVideo->description) > 150): ?>
                        <button @click="expanded = !expanded" class="text-xs text-primary hover:text-primary-focus mt-1 transition-colors">
                            <span x-show="!expanded"><?php echo e(__('youtube.show_more')); ?></span>
                            <span x-show="expanded"><?php echo e(__('youtube.show_less')); ?></span>
                        </button>
                    <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
                </div>

                <!-- Enhanced action buttons -->
                <div class="flex flex-wrap gap-3 sm:gap-4">
                    <button wire:click="playVideo('<?php echo e($featuredVideo->link); ?>')" class="btn btn-primary gap-2 px-6 sm:px-8 py-3 text-sm sm:text-base font-medium shadow-lg hover:shadow-primary/20 transition-all duration-300">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 sm:h-6 sm:w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M14.752 11.168l-3.197-2.132A1 1 0 0010 9.87v4.263a1 1 0 001.555.832l3.197-2.132a1 1 0 000-1.664z" />
                        </svg>
                        <?php echo e(__('youtube.play')); ?>

                    </button>
                    <a href="#season<?php echo e($featuredVideo->season); ?>" wire:click="switchSeason(<?php echo e($featuredVideo->season); ?>)" class="btn btn-outline btn-secondary gap-2 px-6 sm:px-8 py-3 text-sm sm:text-base font-medium hover:bg-secondary/20 transition-all duration-300">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 sm:h-6 sm:w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h7" />
                        </svg>
                        <?php echo e(__('youtube.browse_season')); ?>

                    </a>
                </div>
            </div>
        </div>
    </div>
    <?php endif; ?><!--[if ENDBLOCK]><![endif]-->

    <!-- Enhanced Breadcrumb navigation -->
    <nav aria-label="Breadcrumb" class="bg-base-100 py-3 border-b border-gray-800">
        <div class="container mx-auto px-4">
            <ol class="flex flex-wrap items-center gap-2 text-sm">
                <li class="flex items-center">
                    <a href="/" class="text-base-content hover:text-primary transition-colors flex items-center gap-1">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6" />
                        </svg>
                        <span class="hidden sm:inline"><?php echo e(__('navigation.home')); ?></span>
                    </a>
                </li>

                <li class="flex items-center">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-3 w-3 text-gray-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
                    </svg>
                    <a href="<?php echo e(route('youtube')); ?>" class="ml-1 text-base-content hover:text-primary transition-colors">
                        <?php echo e(__('navigation.youtube')); ?>

                    </a>
                </li>

                <li class="flex items-center">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-3 w-3 text-gray-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
                    </svg>
                    <span class="ml-1 text-gray-500 font-medium"><?php echo e(__('youtube.season_with_number', ['number' => $activeSeason])); ?></span>
                </li>

                <!--[if BLOCK]><![endif]--><?php if($searchTerm): ?>
                    <li class="flex items-center">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-3 w-3 text-gray-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
                        </svg>
                        <span class="ml-1 text-primary font-medium flex items-center gap-1">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-3.5 w-3.5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                            </svg>
                            "<?php echo e($searchTerm); ?>"
                        </span>
                    </li>
                <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
            </ol>
        </div>
    </nav>

    <!-- Season tabs navigation (Improved) -->
    <div class="sticky top-0 bg-base-100 backdrop-blur-sm z-30 py-2 sm:py-3 border-b border-gray-800 mb-2 sm:mb-4 shadow-md">
        <div class="container mx-auto px-4">
            <div class="flex justify-between items-center gap-4">
                <div class="tabs tabs-boxed bg-gray-900/70 p-1.5 overflow-x-auto scrollbar-hide relative rounded-lg shadow-inner">
                    <!-- Loading indicator above the tabs -->
                    <div wire:loading wire:target="switchSeason" class="absolute top-0 left-0 right-0 h-1 bg-primary/30 z-10 rounded-t-lg overflow-hidden">
                        <div class="h-full bg-primary animate-progress-bar"></div>
                    </div>
                    <!--[if BLOCK]><![endif]--><?php $__currentLoopData = $seasonGroups; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $season => $links): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <button wire:click="switchSeason(<?php echo e($season); ?>)"
                               wire:key="tab-<?php echo e($season); ?>"
                               class="tab tab-sm sm:tab-md md:tab-lg <?php echo e($activeSeason == $season ? 'tab-active' : ''); ?> whitespace-nowrap transition-all duration-300 mx-0.5"
                               <?php if($activeSeason == $season): ?> aria-selected="true" <?php endif; ?>
                               aria-controls="season<?php echo e($season); ?>">
                            <span class="text-xs sm:text-sm md:text-gray-400 font-medium">S<?php echo e($season); ?></span>
                            <span class="ml-1 text-xs opacity-80 hidden sm:inline">
                                <!--[if BLOCK]><![endif]--><?php if($activeSeason == $season && $searchTerm): ?>
                                    (<?php echo e($links->count()); ?>/<?php echo e($seasonGroups[$season]->count()); ?>)
                                <?php else: ?>
                                    (<?php echo e($links->count()); ?>)
                                <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
                            </span>
                        </button>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?><!--[if ENDBLOCK]><![endif]-->
                </div>
                <div class="relative flex-shrink-0">
                    <div class="relative flex items-center bg-gray-900/70 rounded-lg overflow-hidden focus-within:ring-2 focus-within:ring-primary transition-all duration-300 shadow-inner">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 text-gray-400 absolute left-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                        </svg>
                        <input type="text"
                               wire:model.live.debounce.300ms="searchTerm"
                               placeholder="<?php echo e(__('youtube.search')); ?>"
                               class="pl-8 pr-3 py-2 w-28 sm:w-40 md:w-48 bg-transparent border-none text-white text-sm focus:outline-none"
                               aria-label="<?php echo e(__('youtube.search')); ?>">
                        <!--[if BLOCK]><![endif]--><?php if($searchTerm): ?>
                            <button wire:click="$set('searchTerm', '')" class="absolute right-2 text-gray-400 hover:text-white transition-colors" aria-label="<?php echo e(__('youtube.clear_search')); ?>">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                                </svg>
                            </button>
                        <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Season content (Only one visible at a time) -->
    <div class="container bg-base-100 mx-auto px-2 sm:px-4 py-2 sm:py-4 -mt-12 sm:-mt-16 relative z-20 pt-16 sm:pt-20 rounded-lg">
        <!--[if BLOCK]><![endif]--><?php $__currentLoopData = $seasonGroups; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $season => $links): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
            <div id="season<?php echo e($season); ?>" class="season-content mb-12 bg-base-100" wire:key="season-<?php echo e($season); ?>" <?php if($activeSeason != $season): ?> style="display: none;" <?php endif; ?>>
                <!-- Season header with breadcrumb indicator -->
                <div class="flex items-center justify-between mb-4 sm:mb-6 border-b border-gray-800 pb-2">
                    <h2 class="text-xl sm:text-2xl font-bold text-base-content flex items-center gap-2">
                        <span><?php echo e(__('youtube.season_with_number', ['number' => $season])); ?></span>
                        <span class="text-xs sm:text-sm text-gray-400">
                            <!--[if BLOCK]><![endif]--><?php if($searchTerm): ?>
                                (<?php echo e($links->count()); ?>/<?php echo e($seasonGroups[$season]->count()); ?>)
                            <?php else: ?>
                                (<?php echo e($links->count()); ?>)
                            <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
                        </span>
                    </h2>
                    <!--[if BLOCK]><![endif]--><?php if($searchTerm): ?>
                        <div class="flex items-center gap-2 text-sm text-primary">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                            </svg>
                            <span>"<?php echo e($searchTerm); ?>"</span>
                            <button wire:click="$set('searchTerm', '')" class="text-gray-400 hover:text-white transition-colors" aria-label="<?php echo e(__('youtube.clear_search')); ?>">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                                </svg>
                            </button>
                        </div>
                    <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
                </div>

                <div class="relative">
                    <!-- Loading skeleton for videos (shown during loading) -->
                    <div wire:loading wire:target="switchSeason, searchTerm" class="w-full">
                        <div class="flex justify-center items-center py-8">
                            <div class="w-12 h-12 rounded-full border-4 border-gray-800 border-t-primary animate-spin"></div>
                        </div>
                    </div>

                    <!-- Responsive grid layout -->
                    <div wire:loading.remove class="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-3 sm:gap-4 md:gap-6">
                        <!-- No results message -->
                        <!--[if BLOCK]><![endif]--><?php if($activeSeason == $season && $searchTerm && $links->isEmpty()): ?>
                            <div class="col-span-full text-center py-16 text-white">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-16 w-16 mx-auto text-gray-500 mb-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                                </svg>
                                <h3 class="text-xl font-bold mb-2"><?php echo e(__('youtube.no_videos_found')); ?></h3>
                                <p class="text-gray-400"><?php echo e(__('youtube.try_different_search')); ?></p>
                            </div>
                        <?php endif; ?><!--[if ENDBLOCK]><![endif]-->

                        <!--[if BLOCK]><![endif]--><?php $__currentLoopData = $links->sortBy('episode'); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $link): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <div class="netflix-card video-item transform transition-all hover:scale-105 hover:z-10"
                                 wire:key="video-<?php echo e($link->id); ?>"
                                 x-data="{ showDescription: false }">
                                <div class="relative group rounded-lg overflow-hidden shadow-lg bg-base-100 h-full border border-gray-800 hover:border-primary hover:shadow-xl transition-all duration-300">
                                    <!-- Thumbnail with play button overlay -->
                                    <div class="relative aspect-video bg-gray-900 overflow-hidden">
                                        <!--[if BLOCK]><![endif]--><?php if($link->thumbnail_url): ?>
                                            <img
                                                src="<?php echo e($link->thumbnail_url); ?>"
                                                alt="<?php echo e($link->title ?: __('youtube.season_episode_format', ['season' => $season, 'episode' => $link->episode])); ?>"
                                                class="w-full h-full object-cover transition-all group-hover:opacity-70 group-hover:scale-105"
                                                loading="lazy"
                                            >
                                        <?php else: ?>
                                            <img
                                                src="https://img.youtube.com/vi/<?php echo e($link->link); ?>/maxresdefault.jpg"
                                                alt="<?php echo e($link->title ?: __('youtube.season_episode_format', ['season' => $season, 'episode' => $link->episode])); ?>"
                                                class="w-full h-full object-cover transition-all group-hover:opacity-70 group-hover:scale-105"
                                                onerror="this.src='https://img.youtube.com/vi/<?php echo e($link->link); ?>/0.jpg';"
                                                loading="lazy"
                                            >
                                        <?php endif; ?><!--[if ENDBLOCK]><![endif]-->

                                        <!-- Season & Episode badge -->
                                        <div class="absolute top-2 left-2 bg-primary/90 text-white text-xs px-2 py-1 rounded-md font-bold shadow-md">
                                            S<?php echo e($season); ?> <?php echo e(__('youtube.episode_tag')); ?><?php echo e($link->episode); ?>

                                        </div>

                                        <!-- Duration badge -->
                                        <!--[if BLOCK]><![endif]--><?php if($link->duration_seconds): ?>
                                            <div class="absolute bottom-2 right-2 bg-warning-content/80 text-white text-xs px-2 py-1 rounded-md shadow-md flex items-center gap-1">
                                                <svg xmlns="http://www.w3.org/2000/svg" class="h-3 w-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                                                </svg>
                                                <?php echo e($link->formatted_duration); ?>

                                            </div>
                                        <?php endif; ?><!--[if ENDBLOCK]><![endif]-->

                                        <!-- Play button overlay with gradient -->
                                        <div class="absolute inset-0 bg-gradient-to-t from-black/80 via-black/20 to-transparent opacity-0 group-hover:opacity-100 transition-opacity flex items-center justify-center">
                                            <button wire:click="playVideo('<?php echo e($link->link); ?>')" class="bg-primary text-white rounded-full p-3 transform transition-transform hover:scale-110 shadow-lg">
                                                <svg xmlns="http://www.w3.org/2000/svg" class="h-7 w-7" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M14.752 11.168l-3.197-2.132A1 1 0 0010 9.87v4.263a1 1 0 001.555.832l3.197-2.132a1 1 0 000-1.664z" />
                                                </svg>
                                            </button>
                                        </div>
                                    </div>

                                    <!-- Video info (Always visible) -->
                                    <div class="p-3 bg-base-100 text-neutral card-info">
                                        <!-- Title with tooltip for full title -->
                                        <div class="mb-2">
                                            <h3 class="text-sm sm:text-base text-base-content font-bold line-clamp-2 group-hover:text-primary transition-colors duration-300"
                                                title="<?php echo e($link->title ?: __('youtube.season_episode_format', ['season' => $season, 'episode' => $link->episode])); ?>">
                                                <?php echo e($link->title ?: __('youtube.episode_with_number', ['number' => $link->episode])); ?>

                                            </h3>
                                        </div>

                                        <!-- Short description if available -->
                                        <!--[if BLOCK]><![endif]--><?php if($link->description): ?>
                                            <p class="text-xs text-gray-400 line-clamp-2 mb-2 hover:text-gray-300 transition-colors duration-300"
                                               @mouseenter="showDescription = true"
                                               @mouseleave="showDescription = false">
                                                <?php echo e(Str::limit($link->description, 100)); ?>

                                            </p>
                                            <!-- Expanded description tooltip -->
                                            <div x-show="showDescription"
                                                 x-transition:enter="transition ease-out duration-200"
                                                 x-transition:enter-start="opacity-0 scale-95"
                                                 x-transition:enter-end="opacity-100 scale-100"
                                                 x-transition:leave="transition ease-in duration-100"
                                                 x-transition:leave-start="opacity-100 scale-100"
                                                 x-transition:leave-end="opacity-0 scale-95"
                                                 class="absolute z-50 bg-gray-900 text-white p-3 rounded-lg shadow-xl max-w-xs text-xs"
                                                 style="display: none;">
                                                <?php echo e(Str::limit($link->description, 300)); ?>

                                            </div>
                                        <?php endif; ?><!--[if ENDBLOCK]><![endif]-->

                                        <!-- Metadata row with improved icons -->
                                        <div class="flex flex-wrap items-center text-xs text-gray-400 gap-3 mb-3">
                                            <!--[if BLOCK]><![endif]--><?php if($link->published_at): ?>
                                                <span class="inline-flex items-center gap-1.5">
                                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-3.5 w-3.5 text-gray-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
                                                    </svg>
                                                    <span><?php echo e($link->published_at->format('d.m.Y')); ?></span>
                                                </span>
                                            <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
                                            <!--[if BLOCK]><![endif]--><?php if($link->view_count): ?>
                                                <span class="inline-flex items-center gap-1.5">
                                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-3.5 w-3.5 text-gray-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                                                    </svg>
                                                    <span><?php echo e($link->formatted_view_count); ?></span>
                                                </span>
                                            <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
                                        </div>

                                        <!-- Watch button with improved styling -->
                                        <div class="flex gap-2">
                                            <button wire:click="playVideo('<?php echo e($link->link); ?>')" class="btn btn-sm btn-primary flex-grow gap-2 font-medium">
                                                <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M14.752 11.168l-3.197-2.132A1 1 0 0010 9.87v4.263a1 1 0 001.555.832l3.197-2.132a1 1 0 000-1.664z" />
                                                </svg>
                                                <?php echo e(__('youtube.play')); ?>

                                            </button>
                                            <button wire:click="playVideo('<?php echo e($link->link); ?>')" class="btn btn-sm btn-outline btn-secondary aspect-square p-0">
                                                <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                                                </svg>
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?><!--[if ENDBLOCK]><![endif]-->
                    </div>
                </div>
            </div>
        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?><!--[if ENDBLOCK]><![endif]-->
    </div>

    <!-- Enhanced video player modal with Livewire (Mobile-optimized) -->
    <!--[if BLOCK]><![endif]--><?php if($showModal): ?>
    <div x-data="{ descriptionExpanded: false }"
         class="fixed inset-0 z-50 flex items-center justify-center backdrop-blur-md animate-fade-in overflow-y-auto py-4 sm:py-6"
         wire:key="modal-container"
         x-on:click.self="$wire.closeModal()">
        <div class="relative w-full max-w-6xl mx-auto p-2 sm:p-4 md:p-5 bg-base-100 rounded-xl overflow-hidden shadow-2xl max-h-full flex flex-col">
            <!-- Close button with improved styling (more accessible on mobile) -->
            <button wire:click="closeModal"
                    class="absolute top-2 right-2 sm:top-3 sm:right-3 text-white bg-black/70 hover:bg-primary rounded-full w-8 h-8 sm:w-10 sm:h-10 flex items-center justify-center transition-all duration-300 z-50 shadow-lg group">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 sm:h-6 sm:w-6 group-hover:rotate-90 transition-transform duration-300" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                </svg>
            </button>

            <!-- Video player with loading state (responsive aspect ratio) -->
            <div class="w-full rounded-xl overflow-hidden shadow-2xl bg-gray-900 relative" style="aspect-ratio: 16/9;">
                <!-- Loading skeleton (shown while iframe loads) -->
                <div wire:loading class="absolute inset-0 flex items-center justify-center bg-gray-900/90 z-10">
                    <div class="flex flex-col items-center justify-center p-2 w-auto">
                        <!-- Simple loading spinner -->
                        <div class="w-12 h-12 rounded-full border-4 border-gray-800 border-t-primary animate-spin mb-2"></div>
                        <!-- Loading text -->
                        <div class="text-white/70 text-sm"><?php echo e(__('youtube.video_loading')); ?></div>
                    </div>
                </div>

                <!--[if BLOCK]><![endif]--><?php if($currentVideo['videoId']): ?>
                    <iframe id="videoPlayer"
                            class="w-full h-full"
                            src="https://www.youtube.com/embed/<?php echo e($currentVideo['videoId']); ?>?autoplay=1&controls=1&rel=0&origin=<?php echo e(url('/')); ?>"
                            frameborder="0"
                            allow="autoplay; fullscreen; encrypted-media; picture-in-picture"
                            allowfullscreen
                            loading="lazy"></iframe>
                <?php else: ?>
                    <div class="w-full h-full bg-gray-900 flex items-center justify-center">
                        <div class="flex flex-col items-center justify-center p-2 w-auto">
                            <!-- Simple loading spinner -->
                            <div class="w-12 h-12 rounded-full border-4 border-gray-800 border-t-primary animate-spin mb-2"></div>
                            <!-- Loading text -->
                            <div class="text-white/70 text-sm"><?php echo e(__('youtube.video_loading')); ?></div>
                        </div>
                    </div>
                <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
            </div>

            <!-- Video info section with improved layout and styling (scrollable on small screens) -->
            <div class="p-3 sm:p-4 md:p-5 text-white bg-gray-900/90 backdrop-blur-md rounded-xl mt-2 sm:mt-3 shadow-xl overflow-y-auto">
                <!-- Title and season/episode with better hierarchy -->
                <div class="flex items-start justify-between gap-2 sm:gap-4 mb-2 sm:mb-3">
                    <div class="flex-grow">
                        <div class="flex items-center gap-2 mb-1">
                            <!--[if BLOCK]><![endif]--><?php if($currentVideo['season'] && $currentVideo['episode']): ?>
                                <span class="bg-primary/90 text-white px-2 py-0.5 text-xs font-bold rounded-md shadow-sm">
                                    S<?php echo e($currentVideo['season']); ?> E<?php echo e($currentVideo['episode']); ?>

                                </span>
                            <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
                        </div>
                        <h3 class="text-base sm:text-lg md:text-xl font-bold"><?php echo e($currentVideo['title']); ?></h3>
                    </div>
                </div>

                <!-- Metadata with improved styling and icons (more compact on mobile) -->
                <div class="flex flex-wrap items-center gap-2 sm:gap-4 text-xs sm:text-sm text-gray-300 mb-3 sm:mb-4 border-b border-gray-800 pb-3 sm:pb-4">
                    <!--[if BLOCK]><![endif]--><?php if($currentVideo['published']): ?>
                        <span class="flex items-center gap-1 sm:gap-1.5">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-3.5 w-3.5 sm:h-4 sm:w-4 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
                            </svg>
                            <span><?php echo e($currentVideo['published']); ?></span>
                        </span>
                    <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
                    <!--[if BLOCK]><![endif]--><?php if($currentVideo['duration']): ?>
                        <span class="flex items-center gap-1 sm:gap-1.5">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-3.5 w-3.5 sm:h-4 sm:w-4 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                            </svg>
                            <span><?php echo e($currentVideo['duration']); ?></span>
                        </span>
                    <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
                    <!--[if BLOCK]><![endif]--><?php if($currentVideo['views']): ?>
                        <span class="flex items-center gap-1 sm:gap-1.5">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-3.5 w-3.5 sm:h-4 sm:w-4 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                            </svg>
                            <span><?php echo e($currentVideo['views']); ?></span>
                        </span>
                    <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
                </div>

                <!-- Description with expandable functionality (height-limited on mobile) -->
                <div class="relative">
                    <h4 class="text-xs sm:text-sm font-medium text-gray-200 mb-1 sm:mb-2"><?php echo e(__('youtube.description')); ?></h4>
                    <div class="bg-gray-800/50 rounded-lg p-2 sm:p-3 relative">
                        <p class="text-xs sm:text-sm text-gray-300 transition-all duration-300"
                           :class="{ 'line-clamp-3': !descriptionExpanded, 'max-h-32 sm:max-h-48 md:max-h-60 overflow-y-auto pr-2': descriptionExpanded }">
                            <?php echo e($currentVideo['description'] ?: __('youtube.no_description')); ?>

                        </p>
                        <!--[if BLOCK]><![endif]--><?php if(strlen($currentVideo['description']) > 150): ?>
                            <button @click="descriptionExpanded = !descriptionExpanded"
                                    class="text-xs text-primary hover:text-primary-focus mt-1 sm:mt-2 transition-colors flex items-center gap-1">
                                <span x-show="!descriptionExpanded"><?php echo e(__('youtube.show_more')); ?></span>
                                <span x-show="descriptionExpanded"><?php echo e(__('youtube.show_less')); ?></span>
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-3 w-3 transition-transform duration-300"
                                     :class="{ 'rotate-180': descriptionExpanded }" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
                                </svg>
                            </button>
                        <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
                    </div>
                </div>
            </div>
        </div>
    </div>
    <?php endif; ?><!--[if ENDBLOCK]><![endif]-->

    <style>
        /* Font fallbacks */
        @font-face {
            font-family: 'GeistSans';
            src: local('Arial'), local('Helvetica'), local('sans-serif');
            font-display: swap;
        }

        @font-face {
            font-family: 'Anona';
            src: local('Georgia'), local('Times New Roman'), local('serif');
            font-display: swap;
        }

        /* Enhanced animations */
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(10px); }
            to { opacity: 1; transform: translateY(0); }
        }

        .animate-fade-in {
            animation: fadeIn 0.5s ease-out forwards;
        }

        .animate-slide-up {
            animation: slideUp 0.8s ease-out forwards;
            animation-delay: 0.3s;
        }

        @keyframes slideUp {
            from { opacity: 0; transform: translateY(30px); }
            to { opacity: 1; transform: translateY(0); }
        }

        /* Slow zoom animation for hero image */
        @keyframes slowZoom {
            from { transform: scale(1); }
            to { transform: scale(1.05); }
        }

        .animate-slow-zoom {
            animation: slowZoom 15s ease-in-out infinite alternate;
        }

        /* Pulse animation for buttons */
        @keyframes subtlePulse {
            0% { box-shadow: 0 0 0 0 rgba(var(--color-primary-rgb), 0.4); }
            70% { box-shadow: 0 0 0 10px rgba(var(--color-primary-rgb), 0); }
            100% { box-shadow: 0 0 0 0 rgba(var(--color-primary-rgb), 0); }
        }

        /* Spin animation for loading spinner */
        @keyframes spin {
            from { transform: rotate(0deg); }
            to { transform: rotate(360deg); }
        }

        .animate-spin {
            animation: spin 1s linear infinite;
        }

        /* Progress bar animation */
        @keyframes progressBar {
            0% { width: 0; left: 0; }
            50% { width: 40%; left: 30%; }
            100% { width: 0; left: 100%; }
        }

        .animate-progress-bar {
            position: absolute;
            animation: progressBar 1.5s ease-in-out infinite;
        }

        .btn-primary:hover {
            animation: subtlePulse 2s infinite;
        }

        /* Hide scrollbar but keep functionality */
        .scrollbar-hide {
            -ms-overflow-style: none;  /* IE and Edge */
            scrollbar-width: none;  /* Firefox */
        }
        .scrollbar-hide::-webkit-scrollbar {
            display: none; /* Chrome, Safari, Opera */
        }

        /* Enhanced tab styling */
        .tabs-boxed .tab {
            margin: 0 2px;
            border-radius: 0.5rem;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .tabs-boxed .tab-active {
            background-color: theme('colors.primary');
            color: white;
            font-weight: bold;
            box-shadow: 0 4px 10px -2px rgba(var(--color-primary-rgb), 0.5);
        }

        .tabs-boxed .tab-active::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 0;
            width: 100%;
            height: 2px;
            background-color: white;
            transform: scaleX(0.7);
            transition: transform 0.3s ease;
        }

        .tabs-boxed .tab-active:hover::after {
            transform: scaleX(1);
        }

        .tabs-boxed .tab:hover:not(.tab-active) {
            background-color: rgba(255, 255, 255, 0.15);
            transform: translateY(-1px);
        }

        /* Season content transition */
        .season-content {
            transition: all 0.5s ease;
        }

        /* Video card hover effects */
        .netflix-card {
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }

        .netflix-card:hover {
            z-index: 10;
            transform: scale(1.05) translateY(-5px) !important;
        }

        /* Card info styling */
        .card-info {
            opacity: 1 !important;
            visibility: visible !important;
            transform: translateY(0) !important;
            transition: all 0.3s ease;
        }

        /* Improved focus states for accessibility */
        button:focus, a:focus, input:focus {
            outline: 2px solid theme('colors.primary');
            outline-offset: 2px;
        }

        /* Improved button hover states */
        .btn {
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        }

        .btn:active {
            transform: translateY(0);
        }

        /* Alpine.js cloak to prevent flash of unstyled content */
        [x-cloak] { display: none !important; }

        /* Mobile-specific modal styles */
        @media (max-width: 640px) {
            /* Ensure modal content is properly sized on small screens */
            [wire\:key="modal-container"] {
                padding: 0.5rem;
            }

            /* Ensure video container maintains aspect ratio on small screens */
            [wire\:key="modal-container"] [style="aspect-ratio: 16/9;"] {
                max-height: 50vh;
            }

            /* Ensure modal content is scrollable on small screens */
            [wire\:key="modal-container"] > div {
                max-height: 90vh;
            }
        }

        /* Fix for iOS Safari to properly handle fixed positioning */
        @supports (-webkit-touch-callout: none) {
            .fixed {
                position: fixed;
                -webkit-transform: translateZ(0);
            }
        }
    </style>

    <script>
        // Enhanced animations and interactions
        document.addEventListener('DOMContentLoaded', function() {
            // Add staggered animation to cards
            const cards = document.querySelectorAll('.netflix-card');
            cards.forEach((card, index) => {
                card.classList.add('animate-fade-in');
                card.style.animationDelay = `${index * 0.05}s`;

                // Add hover sound effect (subtle)
                card.addEventListener('mouseenter', () => {
                    if (window.navigator.vibrate) {
                        window.navigator.vibrate(5); // Subtle haptic feedback on supported devices
                    }
                });
            });

            // Enhance scroll behavior
            document.querySelectorAll('a[href^="#"]').forEach(anchor => {
                anchor.addEventListener('click', function(e) {
                    const targetId = this.getAttribute('href');
                    const targetElement = document.querySelector(targetId);

                    if (targetElement) {
                        e.preventDefault();
                        targetElement.scrollIntoView({
                            behavior: 'smooth',
                            block: 'start'
                        });
                    }
                });
            });

            // Add intersection observer for scroll animations
            const observer = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        entry.target.classList.add('animate-fade-in');
                        observer.unobserve(entry.target);
                    }
                });
            }, { threshold: 0.1 });

            // Observe season headers for scroll animations
            document.querySelectorAll('.season-content h2').forEach(header => {
                observer.observe(header);
            });
        });

        // Track video views and enhance user experience
        document.addEventListener('livewire:initialized', () => {
            // Listen for video view events
            Livewire.on('video-viewed', (data) => {
                // Log view for analytics
                console.log('Video viewed:', data);

                // Make sure the video plays for at least 3 seconds to count as a view
                setTimeout(() => {
                    // This ensures the view is counted by YouTube
                    const videoPlayer = document.getElementById('videoPlayer');
                    if (videoPlayer && videoPlayer.src.includes(data.videoId)) {
                        // The video is still playing after 3 seconds
                        console.log('View counted for:', data.title);
                    }
                }, 3000);
            });

            // Enhance modal interactions
            Livewire.on('modalOpened', () => {
                document.body.style.overflow = 'hidden'; // Prevent background scrolling
            });

            Livewire.on('modalClosed', () => {
                document.body.style.overflow = ''; // Restore scrolling
            });
        });

        // Handle keyboard navigation and mobile optimization
        document.addEventListener('keydown', (e) => {
            // Close modal on Escape key
            if (e.key === 'Escape') {
                const closeButton = document.querySelector('[wire\\:click="closeModal"]');
                if (closeButton) {
                    closeButton.click();
                }
            }
        });

        // Mobile optimization for video modal
        function adjustModalForMobile() {
            const modal = document.querySelector('[wire\\:key="modal-container"]');
            if (!modal) return;

            // Adjust height for mobile devices in portrait orientation
            if (window.innerWidth < window.innerHeight) {
                // Portrait mode - make sure modal fits in viewport
                const videoContainer = modal.querySelector('[style="aspect-ratio: 16/9;"]');
                if (videoContainer) {
                    // Ensure video doesn't take more than 50% of viewport height on mobile
                    const maxHeight = window.innerHeight * 0.5;
                    const currentWidth = videoContainer.offsetWidth;
                    const aspectRatio = 16/9;
                    const calculatedHeight = currentWidth / aspectRatio;

                    if (calculatedHeight > maxHeight) {
                        videoContainer.style.height = `${maxHeight}px`;
                        videoContainer.style.width = `${maxHeight * aspectRatio}px`;
                        videoContainer.style.margin = '0 auto';
                    }
                }
            }
        }

        // Run on modal open and window resize
        window.addEventListener('resize', adjustModalForMobile);
        Livewire.on('modalOpened', () => {
            setTimeout(adjustModalForMobile, 100); // Small delay to ensure DOM is updated
        });
    </script>
</div>
<?php /**PATH C:\Users\<USER>\Documents\GitHub\minewache-website\resources\views/livewire/youtube-manager.blade.php ENDPATH**/ ?>