import React, { useState } from 'react';
import type { AnalysisResult, Tag, CategorizedTagItem, HashtagPreference, VideoSummary } from '../types';
import { GlassCard } from './common/GlassCard';
import { HashtagIcon, TagIcon, CheckCircleIcon, ClipboardDocumentIcon, ArrowPathIcon, UploadCloudIcon, EyeIcon, CubeIcon, BoltIcon, UserCircleIcon, ChatBubbleLeftEllipsisIcon, InformationCircleIcon } from './common/Icons';

interface ResultsDisplayProps {
  result: AnalysisResult;
  onAnalyzeAnother: () => void;
  videoFileName: string;
  hashtagPreference: HashtagPreference;
  onRegenerate: () => void;
}

const TagChip: React.FC<{ text: string; onCopy: (text: string) => void; copiedId: string | null; id: string }> = ({ text, onCopy, copiedId, id }) => (
  <button
    onClick={() => onCopy(text)}
    className="flex items-center bg-slate-700 hover:bg-slate-600 text-slate-200 text-sm px-3 py-1.5 rounded-full shadow-md transition-all duration-200 transform hover:scale-105 focus:outline-none focus:ring-2 focus:ring-orange-500"
    aria-label={`Kopiere: ${text}`}
  >
    {text}
    {copiedId === id ? (
      <CheckCircleIcon className="w-4 h-4 ml-2 text-green-400" aria-label="Kopiert"/>
    ) : (
      <ClipboardDocumentIcon className="w-4 h-4 ml-2 text-slate-400 group-hover:text-orange-300" aria-label="Kopieren"/>
    )}
  </button>
);

const SummaryListItem: React.FC<{ item: string }> = ({ item }) => (
  <span className="inline-block bg-slate-700 text-slate-200 text-xs px-2 py-1 rounded-md mr-1 mb-1">{item}</span>
);


interface SummarySectionProps {
  title: string;
  items: string[];
  icon: React.ElementType;
  sectionId: string;
  onCopy: (text: string, id: string) => void;
  copiedId: string | null;
}

const SummarySection: React.FC<SummarySectionProps> = ({ title, items, icon: Icon, sectionId, onCopy, copiedId }) => {
  if (!items || items.length === 0) {
    return null; 
  }
  const handleCopySection = () => {
    const textToCopy = items.join(', ');
    onCopy(textToCopy, sectionId);
  };

  return (
    <div className="mb-3">
      <div className="flex justify-between items-center mb-1.5">
        <h4 className="text-md font-semibold text-slate-300 flex items-center">
          <Icon className="w-5 h-5 mr-2 text-orange-400" />
          {title}:
        </h4>
        <button
          onClick={handleCopySection}
          className="text-xs px-2 py-1 bg-slate-600 hover:bg-orange-700 text-slate-200 rounded-md flex items-center transition-colors duration-200"
          title={`Kopiere: ${title}`}
        >
          {copiedId === sectionId ? <CheckCircleIcon className="w-3 h-3 mr-1" /> : <ClipboardDocumentIcon className="w-3 h-3 mr-1" />}
          Kopieren
        </button>
      </div>
      <div className="pl-7"> {/* Indent content under icon+title */}
        {items.map((item, index) => (
          <SummaryListItem key={`${sectionId}_${index}`} item={item} />
        ))}
      </div>
    </div>
  );
};


export const ResultsDisplay: React.FC<ResultsDisplayProps> = ({ result, onAnalyzeAnother, videoFileName, hashtagPreference, onRegenerate }) => {
  const [copiedItemId, setCopiedItemId] = useState<string | null>(null);

  const handleCopy = (text: string, id: string) => {
    navigator.clipboard.writeText(text)
      .then(() => {
        setCopiedItemId(id);
        setTimeout(() => setCopiedItemId(null), 2000);
      })
      .catch(err => console.error('Kopieren fehlgeschlagen:', err));
  };
  
  const copyAllPlatformHashtags = (platform: 'tiktok' | 'instagram') => {
    const hashtagsToCopy = platform === 'tiktok' ? result.tiktokHashtags : result.instagramHashtags;
    const allHashtagsText = hashtagsToCopy.map(h => h.text).join(' ');
    const copyId = platform === 'tiktok' ? 'all_tiktok_hashtags' : 'all_instagram_hashtags';
    handleCopy(allHashtagsText, copyId);
  };

  const showTikTok = hashtagPreference === 'both' || hashtagPreference === 'tiktok';
  const showInstagram = hashtagPreference === 'both' || hashtagPreference === 'instagram';

  return (
    <div className="w-full animate-fadeIn">
      <GlassCard className="p-6 sm:p-8">
        <h2 className="text-2xl sm:text-3xl font-bold mb-1 text-center bg-clip-text text-transparent bg-gradient-to-r from-green-400 via-amber-400 to-orange-500">
          AI Analyse abgeschlossen!
        </h2>
        <p className="text-sm text-slate-400 text-center mb-6">Für Video: <span className="font-semibold">{videoFileName}</span></p>

        {/* Video Context Summary Section */}
        {result.videoSummary && (
          <GlassCard className="p-4 sm:p-6 mb-8 border border-orange-600/50 bg-slate-800/80">
            <h3 className="text-xl font-semibold text-orange-400 flex items-center mb-4">
              <InformationCircleIcon className="w-6 h-6 mr-2"/>
              Video Kontext Zusammenfassung
            </h3>
            <p className="text-slate-300 mb-3 text-sm italic">"{result.videoSummary.overallGerman}"</p>
            
            <SummarySection title="Visuelle Schlüsselelemente" items={result.videoSummary.keyVisualElements} icon={EyeIcon} sectionId="summary_visuals" onCopy={handleCopy} copiedId={copiedItemId} />
            <SummarySection title="Erkannte Objekte/Fahrzeuge" items={result.videoSummary.detectedObjects} icon={CubeIcon} sectionId="summary_objects" onCopy={handleCopy} copiedId={copiedItemId} />
            <SummarySection title="Erkannte Aktionen/Events" items={result.videoSummary.detectedActions} icon={BoltIcon} sectionId="summary_actions" onCopy={handleCopy} copiedId={copiedItemId} />
            <SummarySection title="Erwähnte Namen/Rollen" items={result.videoSummary.spokenNamesOrRoles} icon={UserCircleIcon} sectionId="summary_names" onCopy={handleCopy} copiedId={copiedItemId} />
            <SummarySection title="Dialogthemen" items={result.videoSummary.dialogueThemes} icon={ChatBubbleLeftEllipsisIcon} sectionId="summary_dialogue" onCopy={handleCopy} copiedId={copiedItemId} />
          </GlassCard>
        )}

        {/* TikTok Hashtags Section */}
        {showTikTok && (
          <section className="mb-8">
            <div className="flex justify-between items-center mb-4">
              <h3 className="text-xl font-semibold text-orange-400 flex items-center">
                <HashtagIcon className="w-6 h-6 mr-2" />
                TikTok Hashtags
              </h3>
              {result.tiktokHashtags && result.tiktokHashtags.length > 0 && (
                <button
                  onClick={() => copyAllPlatformHashtags('tiktok')}
                  className="text-xs px-3 py-1.5 bg-orange-600 hover:bg-orange-500 text-white rounded-md flex items-center transition-colors duration-200"
                  title="Alle TikTok Hashtags kopieren"
                >
                  {copiedItemId === 'all_tiktok_hashtags' ? <CheckCircleIcon className="w-4 h-4 mr-1" /> : <ClipboardDocumentIcon className="w-4 h-4 mr-1" />}
                  Alle kopieren
                </button>
              )}
            </div>
            {result.tiktokHashtags && result.tiktokHashtags.length > 0 ? (
              <div className="flex flex-wrap gap-2">
                {result.tiktokHashtags.map(hashtag => (
                  <TagChip key={hashtag.id} text={hashtag.text} id={hashtag.id} onCopy={(text) => handleCopy(text, hashtag.id)} copiedId={copiedItemId} />
                ))}
              </div>
            ) : (
              <p className="text-slate-400 text-sm">Keine TikTok Hashtags generiert.</p>
            )}
          </section>
        )}

        {/* Instagram Reels Hashtags Section */}
        {showInstagram && (
          <section className="mb-8">
            <div className="flex justify-between items-center mb-4">
              <h3 className="text-xl font-semibold text-amber-400 flex items-center">
                <HashtagIcon className="w-6 h-6 mr-2" />
                Instagram Reels Hashtags
              </h3>
              {result.instagramHashtags && result.instagramHashtags.length > 0 && (
                <button
                  onClick={() => copyAllPlatformHashtags('instagram')}
                  className="text-xs px-3 py-1.5 bg-amber-600 hover:bg-amber-500 text-white rounded-md flex items-center transition-colors duration-200"
                  title="Alle Instagram Reels Hashtags kopieren"
                >
                  {copiedItemId === 'all_instagram_hashtags' ? <CheckCircleIcon className="w-4 h-4 mr-1" /> : <ClipboardDocumentIcon className="w-4 h-4 mr-1" />}
                  Alle kopieren
                </button>
              )}
            </div>
            {result.instagramHashtags && result.instagramHashtags.length > 0 ? (
              <div className="flex flex-wrap gap-2">
                {result.instagramHashtags.map(hashtag => (
                  <TagChip key={hashtag.id} text={hashtag.text} id={hashtag.id} onCopy={(text) => handleCopy(text, hashtag.id)} copiedId={copiedItemId} />
                ))}
              </div>
            ) : (
              <p className="text-slate-400 text-sm">Keine Instagram Hashtags generiert.</p>
            )}
          </section>
        )}

        {/* Categorized Tags Section */}
        <section>
          <h3 className="text-xl font-semibold text-yellow-500 flex items-center mb-4">
            <TagIcon className="w-6 h-6 mr-2" />
            Kategorisierte Keywords
          </h3>
          {result.categorizedTags && result.categorizedTags.length > 0 ? (
            <div className="space-y-4">
              {result.categorizedTags.map(categoryItem => (
                <div key={categoryItem.id}>
                  <div className="flex justify-between items-center mb-2">
                    <h4 className="text-md font-medium text-slate-300">{categoryItem.category}</h4>
                    <button
                      onClick={() => {
                        const categoryTagsText = categoryItem.tags.map(t => t.text).join(', ');
                        handleCopy(categoryTagsText, `category_${categoryItem.id}`);
                      }}
                      className="text-xs px-2 py-1 bg-yellow-600 hover:bg-yellow-500 text-white rounded-md flex items-center transition-colors duration-200"
                      title={`Alle ${categoryItem.category} Tags kopieren`}
                    >
                      {copiedItemId === `category_${categoryItem.id}` ? <CheckCircleIcon className="w-3 h-3 mr-1" /> : <ClipboardDocumentIcon className="w-3 h-3 mr-1" />}
                      Sektion kopieren
                    </button>
                  </div>
                  <div className="flex flex-wrap gap-2">
                    {categoryItem.tags.map(tag => (
                       <TagChip key={tag.id} text={tag.text} id={tag.id} onCopy={(text) => handleCopy(text, tag.id)} copiedId={copiedItemId} />
                    ))}
                  </div>
                </div>
              ))}
            </div>
          ) : (
             <p className="text-slate-400 text-sm">Keine kategorisierten Keywords generiert.</p>
          )}
        </section>

        <div className="mt-10 grid grid-cols-1 sm:grid-cols-2 gap-4">
            <button
              onClick={onRegenerate}
              className="btn-secondary w-full flex items-center justify-center group" 
            >
              <ArrowPathIcon className="w-5 h-5 mr-2 transition-transform duration-300 group-hover:rotate-[180deg]" />
              Tags neu generieren
            </button>
            <button
              onClick={onAnalyzeAnother}
              className="btn-primary w-full flex items-center justify-center group"
            >
              <UploadCloudIcon className="w-5 h-5 mr-2 transition-transform duration-300 group-hover:opacity-75" />
              Anderes Video analysieren
            </button>
        </div>
      </GlassCard>
    </div>
  );
};