<x-app-layout>
    <x-slot name="heading">
        Fragen
    </x-slot>

    <x-slot name="header">
        <h2 class="font-display text-xl md:text-2xl text-gray-900 dark:text-white leading-tight animate-slide-down">
            {{ __('<PERSON><PERSON>') }}
        </h2>
    </x-slot>

    <div class="py-8 md:py-12 bg-base-200">
        <div class="max-w-2xl mx-auto px-4 sm:px-6 lg:px-8">
            <!-- Fortschrittsanzeige -->
            <div class="mb-8 animate-fade-in-up">
                <div class="w-full mb-4">
                    <div class="flex flex-col space-y-2">
                        <div class="flex justify-between text-xs text-base-content/70">
                            <span>Schritt 2 von 3</span>
                            <span>66% abgeschlossen</span>
                        </div>
                        <div class="relative pt-1">
                            <div class="h-2 bg-base-300 rounded-full overflow-hidden">
                                <div class="h-full bg-primary rounded-full w-2/3 transition-all duration-1000"></div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="flex items-center justify-between mt-4 flex-wrap gap-2">
                    <div class="flex items-center gap-2">
                        <div class="w-8 h-8 rounded-full bg-primary flex items-center justify-center">
                            <span class="text-primary-content text-sm font-bold">1</span>
                        </div>
                        <span class="text-sm font-medium opacity-70">Persönliche Daten</span>
                    </div>
                    <div class="flex-grow hidden md:block border-t-2 border-dashed border-primary/30 mx-2"></div>
                    <div class="flex items-center gap-2">
                        <div class="w-8 h-8 rounded-full bg-primary flex items-center justify-center">
                            <span class="text-primary-content text-sm font-bold">2</span>
                        </div>
                        <span class="text-sm font-medium">Rollenspezifisch</span>
                    </div>
                    <div class="flex-grow hidden md:block border-t-2 border-dashed border-base-300 mx-2"></div>
                    <div class="flex items-center gap-2">
                        <div class="w-8 h-8 rounded-full bg-base-300 flex items-center justify-center">
                            <span class="text-base-content/50 text-sm font-bold">3</span>
                        </div>
                        <span class="text-sm font-medium opacity-60">Über Dich</span>
                    </div>
                </div>
                
                <div class="mt-6 px-2">
                    <p class="text-sm text-base-content/80">
                        Im zweiten Schritt stellen wir dir einige Fragen zu deiner gewählten Tätigkeit, um deine Bewerbung besser einordnen zu können.
                    </p>
                </div>
            </div>

            <!-- Hauptfragenkarte -->
            <div class="bg-white dark:bg-base-100 rounded-xl shadow-lg overflow-hidden opacity-0 animate-slide-up">
                <div class="p-6 md:p-8 text-gray-900 dark:text-gray-100">
                    <div class="flex flex-col sm:flex-row sm:items-start sm:space-x-4 mb-8">
                        <div class="rounded-full bg-gray-100 p-3 flex-shrink-0 mb-4 sm:mb-0 animate-pulse">
                            <x-heroicon-o-question-mark-circle class="w-7 h-6 text-primary" />
                        </div>
                        <div>
                            <h3 class="font-geometric text-xl md:text-2xl font-medium text-gray-900 dark:text-white mb-3 opacity-0 animate-delayed-fade">Fragen</h3>
                        </div>
                    </div>
                    <div class="card bg-base-200 shadow-xl w-full max-w-4xl mx-auto">
                        <div class="card-body">
                            <div class="w-full mb-6">
                                <ul class="steps steps-horizontal w-full">
                                    <li class="step step-primary">Persönliche Daten</li>
                                    <li class="step step-primary">Rollenspezifisch</li>
                                    <li class="step">Über Dich</li>
                                </ul>
                            </div>

                            <form action="{{ route('questions.about_you') }}" method="get">
                                @csrf
                                @php
                                    $professions = request()->input('professions', []);
                                    $processedQuestions = collect();
                                    $checkboxQuestions = [];
                                    $otherQuestions = [];

                                    function shouldAskQuestion($professions, $roles) {
                                        return count(array_intersect($professions, $roles)) > 0;
                                    }
                                @endphp

                                @foreach($professions as $profession)
                                    @switch($profession)
                                        @case('developer')
                                            @php
                                                if (shouldAskQuestion($professions, ['developer', 'cameraman', 'designer', 'cutter', 'voice_actor', 'actor', 'actor_no_voice', 'modeler', 'builder'])) {
                                                    if (!$processedQuestions->contains('pc')) {
                                                        $processedQuestions->push('pc');
                                                        $checkboxQuestions[] = view('components.question', ['id' => 'pc', 'label' => 'Hast du einen PC?', 'type' => 'checkbox', 'optional' => false]);
                                                    }
                                                }
                                                if (shouldAskQuestion($professions, ['developer', 'cameraman', 'actor', 'builder', 'actor_no_voice'])) {
                                                    if (!$processedQuestions->contains('minecraft')) {
                                                        $processedQuestions->push('minecraft');
                                                        $checkboxQuestions[] = view('components.question', ['id' => 'minecraft', 'label' => 'Besitzt du Minecraft Java?', 'type' => 'checkbox', 'optional' => false]);
                                                    }
                                                }
                                                if (shouldAskQuestion($professions, ['developer', 'cameraman', 'builder'])) {
                                                    if (!$processedQuestions->contains('ram')) {
                                                        $processedQuestions->push('ram');
                                                        $otherQuestions[] = view('components.question', ['id' => 'ram', 'label' => 'RAM (in GB)', 'type' => 'number', 'optional' => false, 'maxlength' => 3]);
                                                    }
                                                }
                                                if (shouldAskQuestion($professions, ['developer'])) {
                                                    if (!$processedQuestions->contains('languages')) {
                                                        $processedQuestions->push('languages');
                                                        $otherQuestions[] = view('components.question', ['id' => 'languages', 'label' => 'Mit welchen Programmiersprachen hast du Erfahrung?', 'type' => 'textarea', 'optional' => false, 'maxlength' => 500]);
                                                    }
                                                    if (!$processedQuestions->contains('ide')) {
                                                        $processedQuestions->push('ide');
                                                        $otherQuestions[] = view('components.question', ['id' => 'ide', 'label' => 'Welche IDE nutzt du?', 'type' => 'select', 'options' => [
                                                            'vscode' => 'Visual Studio Code',
                                                            'intellij' => 'JetBrains Intellij IDEA',
                                                            'notepad++' => 'Notepad++',
                                                            'other' => 'Andere'
                                                        ], 'optional' => false]);
                                                    }
                                                    if (!$processedQuestions->contains('java_experience')) {
                                                        $processedQuestions->push('java_experience');
                                                        $checkboxQuestions[] = view('components.question', ['id' => 'java_experience', 'label' => 'Hast du Erfahrung in der Java-Programmierung?', 'type' => 'checkbox', 'optional' => false]);
                                                    }
                                                    if (!$processedQuestions->contains('forge_experience')) {
                                                        $processedQuestions->push('forge_experience');
                                                        $checkboxQuestions[] = view('components.question', ['id' => 'forge_experience', 'label' => 'Hast du Erfahrung mit Forge in der 1.12.2?', 'type' => 'checkbox', 'optional' => true]);
                                                    }
                                                    if (!$processedQuestions->contains('portfolio')) {
                                                        $processedQuestions->push('portfolio');
                                                        $otherQuestions[] = view('components.question', ['id' => 'portfolio', 'label' => 'Referenzen/Portfolio/Deine Projekte', 'type' => 'textarea', 'optional' => true, 'maxlength' => 1000]);
                                                    }
                                                }
                                            @endphp
                                            @break

                                        @case('music_producer')
                                            <!-- Add questions for music producer here -->
                                            @break

                                        @case('cameraman')
                                            @php
                                                if (shouldAskQuestion($professions, ['cameraman', 'actor', 'actor_no_voice'])) {
                                                    if (!$processedQuestions->contains('minecraft')) {
                                                        $processedQuestions->push('minecraft');
                                                        $checkboxQuestions[] = view('components.question', ['id' => 'minecraft', 'label' => 'Besitzt du Minecraft Java?', 'type' => 'checkbox', 'optional' => false]);
                                                    }
                                                }
                                                if (shouldAskQuestion($professions, ['cameraman'])) {
                                                    if (!$processedQuestions->contains('curseforge')) {
                                                        $processedQuestions->push('curseforge');
                                                        $checkboxQuestions[] = view('components.question', ['id' => 'curseforge', 'label' => 'Hast du CurseForge/Modrinth?', 'type' => 'checkbox', 'optional' => false]);
                                                    }
                                                    if (!$processedQuestions->contains('pc')) {
                                                        $processedQuestions->push('pc');
                                                        $checkboxQuestions[] = view('components.question', ['id' => 'pc', 'label' => 'Hast du einen PC?', 'type' => 'checkbox', 'optional' => false]);
                                                    }
                                                    if (!$processedQuestions->contains('ram')) {
                                                        $processedQuestions->push('ram');
                                                        $otherQuestions[] = view('components.question', ['id' => 'ram', 'label' => 'RAM (in GB)', 'type' => 'text', 'optional' => false, 'maxlength' => 3]);
                                                    }
                                                    if (!$processedQuestions->contains('obs')) {
                                                        $processedQuestions->push('obs');
                                                        $checkboxQuestions[] = view('components.question', ['id' => 'obs', 'label' => 'Hast du OBS installiert?', 'type' => 'checkbox', 'optional' => false]);
                                                    }
                                                    if (!$processedQuestions->contains('fps')) {
                                                        $processedQuestions->push('fps');
                                                        $otherQuestions[] = view('components.question', ['id' => 'fps', 'label' => 'Wie viel FPS hast du ungefähr in Minecraft?', 'type' => 'select', 'options' => [
                                                            '0' => '0 FPS',
                                                            '20' => '20 FPS',
                                                            '30' => '30 FPS',
                                                            '40' => '40 FPS',
                                                            '50' => '50 FPS',
                                                            '60+' => '+60 FPS'
                                                        ], 'optional' => false]);
                                                    }
                                                }
                                            @endphp
                                            @break

                                        @case('designer')
                                            @php
                                                if (shouldAskQuestion($professions, ['designer'])) {
                                                    if (!$processedQuestions->contains('pc')) {
                                                        $processedQuestions->push('pc');
                                                        $checkboxQuestions[] = view('components.question', ['id' => 'pc', 'label' => 'Hast du einen PC?', 'type' => 'checkbox', 'optional' => false]);
                                                    }
                                                    if (!$processedQuestions->contains('program')) {
                                                        $processedQuestions->push('program');
                                                        $otherQuestions[] = view('components.question', ['id' => 'program', 'label' => 'Welches Programm benutzt du?', 'type' => 'select', 'options' => [
                                                            'illustrator' => 'Adobe Illustrator',
                                                            'photoshop' => 'Adobe Photoshop',
                                                            'gimp' => 'GIMP',
                                                            'affinity' => 'Affinity Photo',
                                                            'paintnet' => 'Paint.net',
                                                            'other' => 'Andere'
                                                        ], 'optional' => false]);
                                                    }
                                                    if (!$processedQuestions->contains('design_style')) {
                                                        $processedQuestions->push('design_style');
                                                        $otherQuestions[] = view('components.question', ['id' => 'design_style', 'label' => 'Designstil', 'type' => 'select', 'options' => [
                                                            'pixel_art' => 'Pixel Art',
                                                            'cartoon' => 'Cartoon',
                                                            'realistic' => 'Realistisch',
                                                            'material_design' => 'Material Design',
                                                            'other' => 'Andere'
                                                        ], 'optional' => false]);
                                                    }
                                                    if (!$processedQuestions->contains('favorite_design')) {
                                                        $processedQuestions->push('favorite_design');
                                                        $otherQuestions[] = view('components.question', ['id' => 'favorite_design', 'label' => 'Was designest du am Liebsten?', 'type' => 'select', 'options' => [
                                                            'skins' => 'Skins',
                                                            'vehicle_skins' => 'Fahrzeugskins',
                                                            'textures' => 'Texturen',
                                                            'logos' => 'Logos',
                                                            'thumbnails' => 'Thumbnails',
                                                            'branding' => 'Branding',
                                                            'other' => 'Andere'
                                                        ], 'optional' => false]);
                                                    }
                                                    if (!$processedQuestions->contains('portfolio')) {
                                                        $processedQuestions->push('portfolio');
                                                        $otherQuestions[] = view('components.question', ['id' => 'portfolio', 'label' => 'Referenzen/Portfolio/Deine Projekte', 'type' => 'textarea', 'optional' => true, 'maxlength' => 1000]);
                                                    }
                                                }
                                            @endphp
                                            @break

                                        @case('cutter')
                                            @php
                                                if (shouldAskQuestion($professions, ['cutter'])) {
                                                    if (!$processedQuestions->contains('pc')) {
                                                        $processedQuestions->push('pc');
                                                        $checkboxQuestions[] = view('components.question', ['id' => 'pc', 'label' => 'Hast du einen PC?', 'type' => 'checkbox', 'optional' => false]);
                                                    }
                                                    if (!$processedQuestions->contains('ram')) {
                                                        $processedQuestions->push('ram');
                                                        $otherQuestions[] = view('components.question', ['id' => 'ram', 'label' => 'RAM (in GB)', 'type' => 'text', 'optional' => false, 'maxlength' => 3]);
                                                    }
                                                    if (!$processedQuestions->contains('gpu')) {
                                                        $processedQuestions->push('gpu');
                                                        $otherQuestions[] = view('components.question', ['id' => 'gpu', 'label' => 'Was für eine GPU besitzt du?', 'type' => 'text', 'optional' => false, 'maxlength' => 50]);
                                                    }
                                                    if (!$processedQuestions->contains('program')) {
                                                        $processedQuestions->push('program');
                                                        $otherQuestions[] = view('components.question', ['id' => 'program', 'label' => 'Welches Programm nutzt du?', 'type' => 'select', 'options' => [
                                                            'davinci' => 'Blackmagic Davinci Resolve',
                                                            'premiere' => 'Adobe Premiere Pro',
                                                            'vegas' => 'Vegas Pro',
                                                            'filmora' => 'Wondershare Filmora',
                                                            'final_cut' => 'Apple Final Cut',
                                                            'avid' => 'AVID Media Composer',
                                                            'other' => 'Andere'
                                                        ], 'optional' => false]);
                                                    }
                                                    if (!$processedQuestions->contains('portfolio')) {
                                                        $processedQuestions->push('portfolio');
                                                        $otherQuestions[] = view('components.question', ['id' => 'portfolio', 'label' => 'Referenzen/Portfolio/Deine Projekte', 'type' => 'textarea', 'optional' => true, 'maxlength' => 1000]);
                                                    }
                                                }
                                            @endphp
                                            @break

                                        @case('voice_actor')
                                            @php
                                                if (shouldAskQuestion($professions, ['voice_actor'])) {
                                                    if (!$processedQuestions->contains('voice_type')) {
                                                        $processedQuestions->push('voice_type');
                                                        $otherQuestions[] = view('components.question', ['id' => 'voice_type', 'label' => 'Wie Hoch ist deine Stimme?', 'type' => 'select', 'options' => [
                                                            'high' => 'Hoch',
                                                            'deep' => 'Tief',
                                                            'other' => 'Andere'
                                                        ], 'optional' => false]);
                                                    }
                                                    if (!$processedQuestions->contains('microphone')) {
                                                        $processedQuestions->push('microphone');
                                                        $otherQuestions[] = view('components.question', ['id' => 'microphone', 'label' => 'Mikrofon', 'type' => 'text', 'optional' => false, 'maxlength' => 50]);
                                                    }
                                                    if (!$processedQuestions->contains('daw')) {
                                                        $processedQuestions->push('daw');
                                                        $otherQuestions[] = view('components.question', ['id' => 'daw', 'label' => 'DAW', 'type' => 'text', 'optional' => false, 'maxlength' => 50]);
                                                    }
                                                    if (!$processedQuestions->contains('portfolio')) {
                                                        $processedQuestions->push('portfolio');
                                                        $otherQuestions[] = view('components.question', ['id' => 'portfolio', 'label' => 'Referenzen/Portfolio/Deine Projekte/Stimmprobe', 'type' => 'textarea', 'optional' => true, 'maxlength' => 1000]);
                                                    }
                                                }
                                            @endphp
                                            @break

                                        @case('actor')
                                            @php
                                                if (shouldAskQuestion($professions, ['actor'])) {
                                                    if (!$processedQuestions->contains('voice_type')) {
                                                        $processedQuestions->push('voice_type');
                                                        $otherQuestions[] = view('components.question', ['id' => 'voice_type', 'label' => 'Wie Hoch ist deine Stimme?', 'type' => 'select', 'options' => [
                                                            'high' => 'Hoch',
                                                            'deep' => 'Tief',
                                                            'other' => 'Andere'
                                                        ], 'optional' => false]);
                                                    }
                                                    if (!$processedQuestions->contains('minecraft')) {
                                                        $processedQuestions->push('minecraft');
                                                        $checkboxQuestions[] = view('components.question', ['id' => 'minecraft', 'label' => 'Besitzt du Minecraft Java?', 'type' => 'checkbox', 'optional' => false]);
                                                    }
                                                    if (!$processedQuestions->contains('curseforge')) {
                                                        $processedQuestions->push('curseforge');
                                                        $checkboxQuestions[] = view('components.question', ['id' => 'curseforge', 'label' => 'Hast du Modrinth?', 'type' => 'checkbox', 'optional' => false]);
                                                    }
                                                    if (!$processedQuestions->contains('pc')) {
                                                        $processedQuestions->push('pc');
                                                        $checkboxQuestions[] = view('components.question', ['id' => 'pc', 'label' => 'Hast du einen PC?', 'type' => 'checkbox', 'optional' => false]);
                                                    }
                                                    if (!$processedQuestions->contains('ram')) {
                                                        $processedQuestions->push('ram');
                                                        $otherQuestions[] = view('components.question', ['id' => 'ram', 'label' => 'RAM (in GB)', 'type' => 'number', 'optional' => false, 'maxlength' => 3]);
                                                    }
                                                    if (!$processedQuestions->contains('fps')) {
                                                        $processedQuestions->push('fps');
                                                        $otherQuestions[] = view('components.question', ['id' => 'fps', 'label' => 'Wie viel FPS hast du ungefähr in Minecraft?', 'type' => 'select', 'options' => [
                                                            '0' => '0',
                                                            '20' => '20 FPS',
                                                            '30' => '30 FPS',
                                                            '40' => '40 FPS',
                                                            '50' => '50 FPS',
                                                            '60+' => '+60 FPS'
                                                        ], 'optional' => false]);
                                                    }
                                                    if (!$processedQuestions->contains('fps')) {
                                                        $processedQuestions->push('fps');
                                                        $otherQuestions[] = view('components.question', ['id' => 'fps', 'label' => 'Wie viel FPS hast du ungefähr in Minecraft?', 'type' => 'select', 'options' => [
                                                            '0' => '0',
                                                            '20' => '20 FPS',
                                                            '30' => '30 FPS',
                                                            '40' => '40 FPS',
                                                            '50' => '50 FPS',
                                                            '60+' => '+60 FPS'
                                                        ], 'optional' => false]);
                                                    }
                                                    if (!$processedQuestions->contains('desired_role')) {
                                                        $processedQuestions->push('desired_role');
                                                        $otherQuestions[] = view('components.question', ['id' => 'desired_role', 'label' => 'Wunschrolle', 'type' => 'text', 'optional' => true, 'maxlength' => 100]);
                                                    }
                                                    if (!$processedQuestions->contains('portfolio')) {
                                                        $processedQuestions->push('portfolio');
                                                        $otherQuestions[] = view('components.question', ['id' => 'portfolio', 'label' => 'Referenzen/Portfolio/Deine Projekte', 'type' => 'textarea', 'optional' => true, 'maxlength' => 1000]);
                                                    }
                                                }
                                            @endphp
                                            @break

                                        @case('actor_no_voice')
                                            @php
                                                if (shouldAskQuestion($professions, ['actor_no_voice'])) {
                                                    if (!$processedQuestions->contains('minecraft')) {
                                                        $processedQuestions->push('minecraft');
                                                        $checkboxQuestions[] = view('components.question', ['id' => 'minecraft', 'label' => 'Besitzt du Minecraft Java?', 'type' => 'checkbox', 'optional' => false]);
                                                    }
                                                    if (!$processedQuestions->contains('curseforge')) {
                                                        $processedQuestions->push('curseforge');
                                                        $checkboxQuestions[] = view('components.question', ['id' => 'curseforge', 'label' => 'Hast du Modrinth?', 'type' => 'checkbox', 'optional' => false]);
                                                    }
                                                    if (!$processedQuestions->contains('pc')) {
                                                        $processedQuestions->push('pc');
                                                        $checkboxQuestions[] = view('components.question', ['id' => 'pc', 'label' => 'Hast du einen PC?', 'type' => 'checkbox', 'optional' => false]);
                                                    }
                                                    if (!$processedQuestions->contains('ram')) {
                                                        $processedQuestions->push('ram');
                                                        $otherQuestions[] = view('components.question', ['id' => 'ram', 'label' => 'RAM (in GB)', 'type' => 'number', 'optional' => false, 'maxlength' => 3]);
                                                    }
                                                    if (!$processedQuestions->contains('fps')) {
                                                        $processedQuestions->push('fps');
                                                        $otherQuestions[] = view('components.question', ['id' => 'fps', 'label' => 'Wie viel FPS hast du ungefähr in Minecraft?', 'type' => 'select', 'options' => [
                                                            '0' => '0',
                                                            '20' => '20 FPS',
                                                            '30' => '30 FPS',
                                                            '40' => '40 FPS',
                                                            '50' => '50 FPS',
                                                            '60+' => '+60 FPS'
                                                        ], 'optional' => false]);
                                                    }
                                                    if (!$processedQuestions->contains('desired_role')) {
                                                        $processedQuestions->push('desired_role');
                                                        $otherQuestions[] = view('components.question', ['id' => 'desired_role', 'label' => 'Wunschrolle', 'type' => 'text', 'optional' => true, 'maxlength' => 100]);
                                                    }
                                                    if (!$processedQuestions->contains('portfolio')) {
                                                        $processedQuestions->push('portfolio');
                                                        $otherQuestions[] = view('components.question', ['id' => 'portfolio', 'label' => 'Referenzen/Portfolio/Deine Projekte', 'type' => 'textarea', 'optional' => true, 'maxlength' => 1000]);
                                                    }
                                                }
                                            @endphp
                                            @break

                                        @case('modeler')
                                            @php
                                                if (shouldAskQuestion($professions, ['modeler'])) {
                                                    if (!$processedQuestions->contains('pc')) {
                                                        $processedQuestions->push('pc');
                                                        $checkboxQuestions[] = view('components.question', ['id' => 'pc', 'label' => 'Hast du einen PC?', 'type' => 'checkbox', 'optional' => false]);
                                                    }
                                                    if (!$processedQuestions->contains('program')) {
                                                        $processedQuestions->push('program');
                                                        $otherQuestions[] = view('components.question', ['id' => 'program', 'label' => 'Welches Programm benutzt du?', 'type' => 'select', 'options' => [
                                                            'blender' => 'Blender',
                                                            'maya' => 'Maya',
                                                            '3ds_max' => '3ds Max',
                                                            'cinema_4d' => 'Cinema 4D',
                                                            'other' => 'Andere'
                                                        ], 'optional' => false]);
                                                    }
                                                    if (!$processedQuestions->contains('portfolio')) {
                                                        $processedQuestions->push('portfolio');
                                                        $otherQuestions[] = view('components.question', ['id' => 'portfolio', 'label' => 'Referenzen/Portfolio/Deine Projekte', 'type' => 'textarea', 'optional' => true, 'maxlength' => 1000]);
                                                    }
                                                }
                                            @endphp
                                            @break

                                        @case('builder')
                                            @php
                                                if (shouldAskQuestion($professions, ['builder'])) {
                                                    if (!$processedQuestions->contains('minecraft')) {
                                                        $processedQuestions->push('minecraft');
                                                        $checkboxQuestions[] = view('components.question', ['id' => 'minecraft', 'label' => 'Besitzt du Minecraft Java?', 'type' => 'checkbox', 'optional' => false]);
                                                    }
                                                    if (!$processedQuestions->contains('curseforge')) {
                                                        $processedQuestions->push('curseforge');
                                                        $checkboxQuestions[] = view('components.question', ['id' => 'curseforge', 'label' => 'Hast du Modrinth?', 'type' => 'checkbox', 'optional' => false]);
                                                    }
                                                    if (!$processedQuestions->contains('pc')) {
                                                        $processedQuestions->push('pc');
                                                        $checkboxQuestions[] = view('components.question', ['id' => 'pc', 'label' => 'Hast du einen PC?', 'type' => 'checkbox', 'optional' => false]);
                                                    }
                                                    if (!$processedQuestions->contains('ram')) {
                                                        $processedQuestions->push('ram');
                                                        $otherQuestions[] = view('components.question', ['id' => 'ram', 'label' => 'RAM (in GB)', 'type' => 'number', 'optional' => false, 'maxlength' => 3]);
                                                    }
                                                    if (!$processedQuestions->contains('fps')) {
                                                        $processedQuestions->push('fps');
                                                        $otherQuestions[] = view('components.question', ['id' => 'fps', 'label' => 'Wie viel FPS hast du ungefähr in Minecraft?', 'type' => 'select', 'options' => [
                                                            '0' => '0',
                                                            '20' => '20 FPS',
                                                            '30' => '30 FPS',
                                                            '40' => '40 FPS',
                                                            '50' => '50 FPS',
                                                            '60+' => '+60 FPS'
                                                        ], 'optional' => false]);
                                                    }
                                                    if (!$processedQuestions->contains('portfolio')) {
                                                        $processedQuestions->push('portfolio');
                                                        $otherQuestions[] = view('components.question', ['id' => 'portfolio', 'label' => 'Referenzen/Portfolio/Deine Projekte', 'type' => 'textarea', 'optional' => true, 'maxlength' => 1000]);
                                                    }
                                                }
                                            @endphp
                                            @break
                                    @endswitch
                                @endforeach

                                @if($checkboxQuestions)
    <div class="form-control w-full">
        <div class="mb-4">
            <h3 class="text-lg font-medium pb-2 border-b border-base-300">Allgemeine Anforderungen</h3>
            <p class="text-sm text-base-content/70 mt-2">Bitte bestätige die folgenden Punkte, die für deine ausgewählte Tätigkeit wichtig sind.</p>
        </div>
        <div class="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-3 md:gap-4">
            @foreach($checkboxQuestions as $checkboxQuestion)
                <div wire:key="question-{{ $checkboxQuestion['id'] }}" class="relative">
                    <input type="checkbox" id="question-{{ $checkboxQuestion['id'] }}"
                           name="checkboxQuestions[]"
                           value="{{ $checkboxQuestion['id'] }}"
                           class="peer hidden @error('checkboxQuestions') checkbox-error @enderror">
                    <label for="question-{{ $checkboxQuestion['id'] }}"
                           class="flex flex-col items-center justify-center h-auto min-h-[7rem] p-4 border-2 rounded-lg cursor-pointer
                           transition-all duration-300 ease-in-out transform
                           hover:bg-base-300/30 hover:shadow-md
                           peer-checked:border-primary peer-checked:bg-primary/10
                           peer-checked:scale-[1.02] peer-checked:shadow-lg 
                           border-base-300">
                        <div class="w-8 h-8 flex items-center justify-center mb-2">
                            @if(str_contains($checkboxQuestion['label'], 'Minecraft'))
                                <x-heroicon-o-cube class="h-6 w-6 text-primary" />
                            @elseif(str_contains($checkboxQuestion['label'], 'PC'))
                                <x-heroicon-o-computer-desktop class="h-6 w-6 text-primary" />
                            @elseif(str_contains($checkboxQuestion['label'], 'Modrinth') || str_contains($checkboxQuestion['label'], 'CurseForge'))
                                <x-heroicon-o-puzzle-piece class="h-6 w-6 text-primary" />
                            @elseif(str_contains($checkboxQuestion['label'], 'OBS'))
                                <x-heroicon-o-video-camera class="h-6 w-6 text-primary" />
                            @elseif(str_contains($checkboxQuestion['label'], 'Java'))
                                <x-heroicon-o-code-bracket class="h-6 w-6 text-primary" />
                            @elseif(str_contains($checkboxQuestion['label'], 'Forge'))
                                <x-heroicon-o-wrench-screwdriver class="h-6 w-6 text-primary" />
                            @else
                                <x-heroicon-o-check-circle class="h-6 w-6 text-primary" />
                            @endif
                        </div>
                        <span class="text-center text-sm font-medium">{{ $checkboxQuestion['label'] }}</span>

                        <!-- Verbesserte Animation des Auswahlstatus -->
                        <div class="absolute top-2 right-2 w-6 h-6 rounded-full border-2 border-base-300 peer-checked:border-primary flex items-center justify-center transition-all duration-300">
                            <div class="w-3 h-3 bg-primary rounded-full scale-0 peer-checked:scale-100 transition-transform duration-300"></div>
                        </div>
                    </label>
                </div>
            @endforeach
        </div>
        @error('checkboxQuestions') 
            <div class="mt-2 text-error text-sm flex items-center gap-1">
                <x-heroicon-s-exclamation-circle class="h-4 w-4" />
                <span>{{ $message }}</span>
            </div>
        @enderror
    </div>
@endif

                                @foreach($otherQuestions as $otherQuestion)
                                    {!! $otherQuestion !!}
                                @endforeach

                                @foreach(request()->all() as $key => $value)
                                    @if(is_array($value))
                                        @foreach($value as $subValue)
                                            <input type="hidden" name="{{ $key }}[]" value="{{ $subValue }}">
                                        @endforeach
                                    @else
                                        <input type="hidden" name="{{ $key }}" value="{{ $value }}">
                                    @endif
                                @endforeach

                                <div class="card-actions justify-end mt-8">
                                    <x-modern-button variant="ghost" href="{{ route('home') }}" >Abbrechen</x-modern-button>
                                    <x-modern-button variant="primary" size="lg" type="submit">Weiter zum nächsten Schritt
                                        <x-heroicon-o-arrow-right class="h-5 w-5" /></x-modern-button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</x-app-layout>
