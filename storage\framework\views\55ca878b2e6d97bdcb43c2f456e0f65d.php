<?php $__env->startPush('styles'); ?>
    <?php echo app('Illuminate\Foundation\Vite')('resources/css/ticket-view-custom.css'); ?>
<?php $__env->stopPush(); ?>

<div
    x-data="ticketView()"
    x-init="init()"
    @message-added.window="handleMessageAdded($event.detail.message)"
    @play-notification.window="playNotificationSound()"
    @attachments-ready.window="handleAttachmentsReady($event.detail.messageId, $event.detail.attachments)"
    @typing-update.window="handleTypingUpdate($event.detail.users)"
    id="ticket-view"
    class="bg-gradient-modern py-6 px-4"
>
    <!-- Notification sound -->
    <audio id="notification-sound" preload="auto" class="hidden">
        <source src="<?php echo e(asset('audio/notification.mp3')); ?>" type="audio/mpeg">
        <source src="<?php echo e(asset('sounds/notification.opus')); ?>" type="audio/opus">
        <?php echo e(__('tickets.audio_not_supported')); ?>

    </audio>

    <div class="container mx-auto">
        <div class="grid grid-cols-1 lg:grid-cols-12 gap-4">
            <!-- First Column: Breadcrumbs, Header, Info -->
            <div class="lg:col-span-4 space-y-4">
            
            <?php echo $__env->make('livewire.ticket.partials.breadcrumbs', ['ticket' => $ticket], array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>

            
            <?php echo $__env->make('livewire.ticket.partials.header', [
                'ticket' => $ticket,
                'isSupporter' => $isSupporter,
            ], array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>

            
            <?php echo $__env->make('livewire.ticket.partials.info', ['ticket' => $ticket], array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
        </div>

            <!-- Second Column: AI, Conversation, Modals -->
            <div class="lg:col-span-8 space-y-4">
            
            <?php echo $__env->make('livewire.ticket.partials.ai-consent', [
                'showAiConsentPrompt' => $showAiConsentPrompt,
            ], array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>

            
            <?php echo $__env->make('livewire.ticket.partials.ai-pending', [
                'aiResponsePending' => $aiResponsePending,
            ], array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>

            
            <?php echo $__env->make('livewire.ticket.partials.conversation', [
                'ticket' => $ticket,
                'showAiConsentPrompt' => $showAiConsentPrompt,
                'aiResponsePending' => $aiResponsePending,
                'quotedMessage' => $quotedMessage,
                'form' => $form,
                'uploadError' => $uploadError,
                'isSupporter' => $isSupporter,
            ], array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>

                
                <?php echo $__env->make('livewire.ticket.partials.modals', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
            </div>
        </div>
    </div>
</div>

<?php $__env->startPush('scripts'); ?>
    <?php echo app('Illuminate\Foundation\Vite')('resources/js/ticket-view-custom.js'); ?>
<?php $__env->stopPush(); ?>
<?php /**PATH C:\Users\<USER>\Documents\GitHub\minewache-website\resources\views/livewire/ticket-view.blade.php ENDPATH**/ ?>