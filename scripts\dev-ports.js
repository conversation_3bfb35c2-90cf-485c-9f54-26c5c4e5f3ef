/**
 * This script manages port assignments for development services
 * to prevent port conflicts when running multiple services.
 */
import { existsSync, readFileSync, writeFileSync } from 'fs';
import { execSync } from 'child_process';
import path from 'path';
import { fileURLToPath } from 'url';

// Get the directory name of the current module
const __dirname = path.dirname(fileURLToPath(import.meta.url));

// Default port configuration
const DEFAULT_PORTS = {
  LARAVEL_SERVER: 8000,
  REVERB_SERVER: 8080,
  VITE_SERVER: 5173,
  DISCORD_BOT: 3001
};

// Port offsets for different development variants
const PORT_OFFSETS = {
  'standard': 0,      // Standard dev environment
  'no-discord': 10,   // Without Discord bot
  'no-reverb': 20,    // Without Reverb
  'minimal': 30       // Minimal environment
};

// Get the variant from command line arguments
const args = process.argv.slice(2);
const variant = args[0] || 'standard';

if (!PORT_OFFSETS[variant]) {
  console.error(`❌ Unknown variant: ${variant}`);
  console.error(`Available variants: ${Object.keys(PORT_OFFSETS).join(', ')}`);
  process.exit(1);
}

console.log(`🚀 Setting up ports for ${variant} development environment...`);

// Calculate ports for the current variant
const offset = PORT_OFFSETS[variant];
const ports = {
  LARAVEL_SERVER: DEFAULT_PORTS.LARAVEL_SERVER + offset,
  REVERB_SERVER: DEFAULT_PORTS.REVERB_SERVER + offset,
  VITE_SERVER: DEFAULT_PORTS.VITE_SERVER + offset,
  DISCORD_BOT: DEFAULT_PORTS.DISCORD_BOT + offset
};

console.log('📊 Port configuration:');
console.log(`   Laravel Server: ${ports.LARAVEL_SERVER}`);
console.log(`   Reverb Server: ${ports.REVERB_SERVER}`);
console.log(`   Vite Server: ${ports.VITE_SERVER}`);
console.log(`   Discord Bot: ${ports.DISCORD_BOT}`);

// Export the ports as environment variables
process.env.DEV_LARAVEL_PORT = ports.LARAVEL_SERVER;
process.env.DEV_REVERB_PORT = ports.REVERB_SERVER;
process.env.DEV_VITE_PORT = ports.VITE_SERVER;
process.env.DEV_DISCORD_PORT = ports.DISCORD_BOT;

// Create a temporary .env.dev file with the port configuration
const envDevPath = path.join(__dirname, '..', '.env.dev');
let envContent = '';

if (existsSync(envDevPath)) {
  envContent = readFileSync(envDevPath, 'utf8');
}

// Update or add port variables
const updateEnvVar = (name, value) => {
  const regex = new RegExp(`^${name}=.*$`, 'm');
  if (regex.test(envContent)) {
    envContent = envContent.replace(regex, `${name}=${value}`);
  } else {
    envContent += `\n${name}=${value}`;
  }
};

updateEnvVar('DEV_LARAVEL_PORT', ports.LARAVEL_SERVER);
updateEnvVar('DEV_REVERB_PORT', ports.REVERB_SERVER);
updateEnvVar('DEV_VITE_PORT', ports.VITE_SERVER);
updateEnvVar('DEV_DISCORD_PORT', ports.DISCORD_BOT);

writeFileSync(envDevPath, envContent);
console.log(`✅ Port configuration saved to ${envDevPath}`);

// Export the variant for use in other scripts
console.log(`export DEV_VARIANT=${variant}`);
