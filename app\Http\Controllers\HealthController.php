<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Artisan;
use App\Services\DiscordService;

class HealthController extends Controller
{
    /**
     * Check the health of the Laravel application
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function check()
    {
        $startTime = microtime(true);
        $checks = [];
        $isHealthy = true;

        // Check database connection
        try {
            $dbCheck = DB::select('SELECT 1');
            $checks['database'] = [
                'status' => 'ok',
                'message' => 'Database connection successful'
            ];
        } catch (\Exception $e) {
            $isHealthy = false;
            $checks['database'] = [
                'status' => 'error',
                'message' => 'Database connection failed',
                'error' => $e->getMessage()
            ];
            Log::error('Health check - Database connection failed', [
                'error' => $e->getMessage()
            ]);
        }

        // Check cache
        try {
            $cacheKey = 'health_check_' . time();
            Cache::put($cacheKey, true, 10);
            $cacheCheck = Cache::get($cacheKey);
            
            $checks['cache'] = [
                'status' => $cacheCheck ? 'ok' : 'error',
                'message' => $cacheCheck ? 'Cache is working' : 'Cache check failed'
            ];
            
            if (!$cacheCheck) {
                $isHealthy = false;
            }
        } catch (\Exception $e) {
            $isHealthy = false;
            $checks['cache'] = [
                'status' => 'error',
                'message' => 'Cache check failed',
                'error' => $e->getMessage()
            ];
            Log::error('Health check - Cache check failed', [
                'error' => $e->getMessage()
            ]);
        }

        // Check queue (optional)
        if (config('queue.default') !== 'sync') {
            try {
                // Simple check if the queue connection is available
                // This doesn't guarantee the queue is processing jobs
                $queueConnection = app('queue')->connection();
                $checks['queue'] = [
                    'status' => 'ok',
                    'message' => 'Queue connection available',
                    'driver' => config('queue.default')
                ];
            } catch (\Exception $e) {
                $isHealthy = false;
                $checks['queue'] = [
                    'status' => 'error',
                    'message' => 'Queue connection failed',
                    'error' => $e->getMessage()
                ];
                Log::error('Health check - Queue connection failed', [
                    'error' => $e->getMessage()
                ]);
            }
        }

        // Check storage
        try {
            $storageCheck = is_writable(storage_path('logs'));
            $checks['storage'] = [
                'status' => $storageCheck ? 'ok' : 'error',
                'message' => $storageCheck ? 'Storage is writable' : 'Storage is not writable'
            ];
            
            if (!$storageCheck) {
                $isHealthy = false;
            }
        } catch (\Exception $e) {
            $isHealthy = false;
            $checks['storage'] = [
                'status' => 'error',
                'message' => 'Storage check failed',
                'error' => $e->getMessage()
            ];
            Log::error('Health check - Storage check failed', [
                'error' => $e->getMessage()
            ]);
        }

        $responseTime = round((microtime(true) - $startTime) * 1000, 2);

        return response()->json([
            'status' => $isHealthy ? 'ok' : 'error',
            'message' => $isHealthy ? 'All systems operational' : 'One or more systems are experiencing issues',
            'checks' => $checks,
            'timestamp' => now()->toIso8601String(),
            'response_time_ms' => $responseTime
        ])->header('X-Response-Time', $responseTime . 'ms');
    }

    /**
     * Restart the Discord bot
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function restartDiscordBot(Request $request)
    {
        try {
            // Log the restart request
            Log::warning('Discord bot restart requested', [
                'user_agent' => $request->userAgent(),
                'ip' => $request->ip()
            ]);

            // Execute the restart command
            Artisan::call('discord:bot restart');
            $output = Artisan::output();

            return response()->json([
                'status' => 'success',
                'message' => 'Discord bot restart initiated',
                'details' => trim($output),
                'timestamp' => now()->toIso8601String()
            ]);
        } catch (\Exception $e) {
            Log::error('Failed to restart Discord bot', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'status' => 'error',
                'message' => 'Failed to restart Discord bot',
                'error' => $e->getMessage(),
                'timestamp' => now()->toIso8601String()
            ], 500);
        }
    }
}
