/**
 * resources/css/ticket-view-custom.css
 *
 * Moved all inline CSS (glass, bubbles, animations, responsive fixes)
 * from ticket-view.blade.php into this file.
 * Imported in Blade via: <link rel="stylesheet" href="{{ mix('css/ticket-view-custom.css') }}">
 */

/* -----------------------------------------
   Variables
----------------------------------------- */
:root {
  --bubble-color: rgba(255, 255, 255, 0.15);
  --glass-bg: rgba(255, 255, 255, 0.2);
  --glass-border: rgba(255, 255, 255, 0.3);
}

/* -----------------------------------------
   Background Gradient
----------------------------------------- */
.bg-gradient-modern {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

/* -----------------------------------------
   Ticket View Container
----------------------------------------- */
#ticket-view {
  position: relative;
  overflow: hidden;
  max-width: 1280px;
  margin: 0 auto;
}

/* -----------------------------------------
   Bubble Animations
----------------------------------------- */
.bubble {
  position: absolute;
  bottom: -20px;
  background-color: var(--bubble-color);
  border-radius: 50%;
  opacity: 0.7;
  pointer-events: none;
  animation-name: bubbleMove;
  animation-timing-function: linear;
  animation-iteration-count: infinite;
}

.bubble-1 {
  width: 80px;
  height: 80px;
  left: 10%;
  animation-duration: 12s;
  animation-delay: 0s;
}
.bubble-2 {
  width: 100px;
  height: 100px;
  left: 30%;
  animation-duration: 15s;
  animation-delay: 3s;
}
.bubble-3 {
  width: 60px;
  height: 60px;
  left: 50%;
  animation-duration: 10s;
  animation-delay: 5s;
}
.bubble-4 {
  width: 90px;
  height: 90px;
  left: 70%;
  animation-duration: 18s;
  animation-delay: 2s;
}
.bubble-5 {
  width: 120px;
  height: 120px;
  left: 85%;
  animation-duration: 20s;
  animation-delay: 6s;
}

@keyframes bubbleMove {
  0% {
    transform: translateY(0) scale(1);
    opacity: 0.7;
  }
  50% {
    opacity: 0.4;
  }
  100% {
    transform: translateY(-1000px) scale(1.2);
    opacity: 0;
  }
}

/* -----------------------------------------
   Glass Effect
----------------------------------------- */
.glass-effect {
  background: var(--glass-bg);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  border: 1px solid var(--glass-border);
}

/* -----------------------------------------
   Responsive Adjustments
----------------------------------------- */
@media (max-width: 1024px) {
  /* Reduce bubble sizes on tablets */
  .bubble-1 { width: 60px; height: 60px; }
  .bubble-2 { width: 80px; height: 80px; }
  .bubble-3 { width: 50px; height: 50px; }
  .bubble-4 { width: 70px; height: 70px; }
  .bubble-5 { width: 90px; height: 90px; }
}

@media (max-width: 768px) {
  #ticket-view {
    padding-top: 1.5rem;
    padding-left: 0.75rem;
    padding-right: 0.75rem;
  }

  .glass-effect {
    border: none;
    backdrop-filter: blur(6px);
  }

  /* Stack header elements vertically */
  .ticket-header {
    flex-direction: column !important;
    align-items: flex-start !important;
  }

  /* Conversation panel takes full width */
  .conversation-panel {
    max-height: none !important;
  }
}

@media (max-width: 480px) {
  /* Fine-tune padding for small phones */
  #ticket-view {
    padding-top: 1rem;
    padding-left: 0.25rem;
    padding-right: 0.25rem;
  }
}
