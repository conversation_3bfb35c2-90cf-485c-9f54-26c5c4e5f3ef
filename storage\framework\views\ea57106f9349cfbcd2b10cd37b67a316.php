<section>
    <header class="flex items-start gap-3 mb-6">
        <div class="p-2 bg-primary/20 rounded-lg">
            <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="w-6 h-6 text-primary">
                <path stroke-linecap="round" stroke-linejoin="round" d="M15.75 6a3.75 3.75 0 11-7.5 0 3.75 3.75 0 017.5 0zM4.501 20.118a7.5 7.5 0 0114.998 0A17.933 17.933 0 0112 21.75c-2.676 0-5.216-.584-7.499-1.632z" />
            </svg>
        </div>
        <div>
            <h2 class="text-xl font-semibold text-base-content">
                <?php echo e(__('Profil Informationen')); ?>

            </h2>
            <p class="mt-1 text-sm text-base-content/70">
                <?php echo e(__('Hier siehst du die Informationen deines Discord-Profils.')); ?>

            </p>
        </div>
    </header>

    <div class="space-y-6">
        <!-- GDPR Data Notice -->
        <?php if (isset($component)) { $__componentOriginald24eb1b892d70d64bf2e573a32cfdc43 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginald24eb1b892d70d64bf2e573a32cfdc43 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.gdpr-data-notice','data' => ['type' => 'profile','class' => 'mb-6 alert alert-info']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('gdpr-data-notice'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['type' => 'profile','class' => 'mb-6 alert alert-info']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginald24eb1b892d70d64bf2e573a32cfdc43)): ?>
<?php $attributes = $__attributesOriginald24eb1b892d70d64bf2e573a32cfdc43; ?>
<?php unset($__attributesOriginald24eb1b892d70d64bf2e573a32cfdc43); ?>
<?php endif; ?>
<?php if (isset($__componentOriginald24eb1b892d70d64bf2e573a32cfdc43)): ?>
<?php $component = $__componentOriginald24eb1b892d70d64bf2e573a32cfdc43; ?>
<?php unset($__componentOriginald24eb1b892d70d64bf2e573a32cfdc43); ?>
<?php endif; ?>

        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <?php if(Auth::user()->global_name): ?>
                <div class="form-control">
                    <label for="global_name" class="label">
                        <span class="label-text font-medium flex items-center gap-2">
                            <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="w-4 h-4 text-primary">
                                <path stroke-linecap="round" stroke-linejoin="round" d="M15.75 6a3.75 3.75 0 11-7.5 0 3.75 3.75 0 017.5 0zM4.501 20.118a7.5 7.5 0 0114.998 0A17.933 17.933 0 0112 21.75c-2.676 0-5.216-.584-7.499-1.632z" />
                            </svg>
                            <?php echo e(__('Anzeigename')); ?>

                        </span>
                    </label>
                    <?php if (isset($component)) { $__componentOriginal17d23b099a28c4e2c5f6c448deb256e5 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal17d23b099a28c4e2c5f6c448deb256e5 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.modern-input','data' => ['type' => 'text','name' => 'global_name','variant' => 'outlined','id' => 'global_name','value' => ''.e(old('global_name', $user->global_name)).'','required' => true,'autocomplete' => 'global_name','disabled' => true]] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('modern-input'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['type' => 'text','name' => 'global_name','variant' => 'outlined','id' => 'global_name','value' => ''.e(old('global_name', $user->global_name)).'','required' => true,'autocomplete' => 'global_name','disabled' => true]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal17d23b099a28c4e2c5f6c448deb256e5)): ?>
<?php $attributes = $__attributesOriginal17d23b099a28c4e2c5f6c448deb256e5; ?>
<?php unset($__attributesOriginal17d23b099a28c4e2c5f6c448deb256e5); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal17d23b099a28c4e2c5f6c448deb256e5)): ?>
<?php $component = $__componentOriginal17d23b099a28c4e2c5f6c448deb256e5; ?>
<?php unset($__componentOriginal17d23b099a28c4e2c5f6c448deb256e5); ?>
<?php endif; ?>
                    <label class="label">
                        <span class="label-text-alt text-base-content/70"><?php echo e(__('Dein Discord-Anzeigename')); ?></span>
                    </label>
                </div>
            <?php endif; ?>

            <div class="form-control">
                <label for="username" class="label">
                    <span class="label-text font-medium flex items-center gap-2">
                        <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="w-4 h-4 text-secondary">
                            <path stroke-linecap="round" stroke-linejoin="round" d="M17.982 18.725A7.488 7.488 0 0012 15.75a7.488 7.488 0 00-5.982 2.975m11.963 0a9 9 0 10-11.963 0m11.963 0A8.966 8.966 0 0112 21a8.966 8.966 0 01-5.982-2.275M15 9.75a3 3 0 11-6 0 3 3 0 016 0z" />
                        </svg>
                        <?php echo e(__('Benutzername')); ?>

                    </span>
                </label>
                <?php if (isset($component)) { $__componentOriginal17d23b099a28c4e2c5f6c448deb256e5 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal17d23b099a28c4e2c5f6c448deb256e5 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.modern-input','data' => ['type' => 'text','name' => 'username','variant' => 'outlined','id' => 'username','value' => ''.e(old('username', $user->username)).'','required' => true,'autocomplete' => 'username','disabled' => true]] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('modern-input'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['type' => 'text','name' => 'username','variant' => 'outlined','id' => 'username','value' => ''.e(old('username', $user->username)).'','required' => true,'autocomplete' => 'username','disabled' => true]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal17d23b099a28c4e2c5f6c448deb256e5)): ?>
<?php $attributes = $__attributesOriginal17d23b099a28c4e2c5f6c448deb256e5; ?>
<?php unset($__attributesOriginal17d23b099a28c4e2c5f6c448deb256e5); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal17d23b099a28c4e2c5f6c448deb256e5)): ?>
<?php $component = $__componentOriginal17d23b099a28c4e2c5f6c448deb256e5; ?>
<?php unset($__componentOriginal17d23b099a28c4e2c5f6c448deb256e5); ?>
<?php endif; ?>
                <label class="label">
                    <span class="label-text-alt text-base-content/70"><?php echo e(__('Dein Discord-Benutzername')); ?></span>
                </label>
                <?php $__errorArgs = ['username'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                    <label class="label">
                        <span class="label-text-alt text-error"><?php echo e($message); ?></span>
                    </label>
                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
            </div>

            <?php if(!Auth::user()->global_name): ?>
                <div class="form-control">
                    <label for="discriminator" class="label">
                        <span class="label-text font-medium flex items-center gap-2">
                            <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="w-4 h-4 text-accent">
                                <path stroke-linecap="round" stroke-linejoin="round" d="M5.25 8.25h15m-16.5 7.5h15m-1.8-13.5l-3.9 19.5m-2.1-19.5l-3.9 19.5" />
                            </svg>
                            <?php echo e(__('Discriminator')); ?>

                        </span>
                    </label>
                    <?php if (isset($component)) { $__componentOriginal17d23b099a28c4e2c5f6c448deb256e5 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal17d23b099a28c4e2c5f6c448deb256e5 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.modern-input','data' => ['type' => 'text','name' => 'discriminator','variant' => 'outlined','id' => 'discriminator','value' => ''.e(old('discriminator', $user->discriminator)).'','required' => true,'autocomplete' => 'discriminator','disabled' => true]] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('modern-input'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['type' => 'text','name' => 'discriminator','variant' => 'outlined','id' => 'discriminator','value' => ''.e(old('discriminator', $user->discriminator)).'','required' => true,'autocomplete' => 'discriminator','disabled' => true]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal17d23b099a28c4e2c5f6c448deb256e5)): ?>
<?php $attributes = $__attributesOriginal17d23b099a28c4e2c5f6c448deb256e5; ?>
<?php unset($__attributesOriginal17d23b099a28c4e2c5f6c448deb256e5); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal17d23b099a28c4e2c5f6c448deb256e5)): ?>
<?php $component = $__componentOriginal17d23b099a28c4e2c5f6c448deb256e5; ?>
<?php unset($__componentOriginal17d23b099a28c4e2c5f6c448deb256e5); ?>
<?php endif; ?>
                    <label class="label">
                        <span class="label-text-alt text-base-content/70"><?php echo e(__('Deine Discord-ID-Nummer')); ?></span>
                    </label>
                    <?php $__errorArgs = ['discriminator'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                        <label class="label">
                            <span class="label-text-alt text-error"><?php echo e($message); ?></span>
                        </label>
                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                </div>
            <?php endif; ?>

            <div class="form-control">
                <label for="last_synced" class="label">
                    <span class="label-text font-medium flex items-center gap-2">
                        <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="w-4 h-4 text-info">
                            <path stroke-linecap="round" stroke-linejoin="round" d="M16.023 9.348h4.992v-.001M2.985 19.644v-4.992m0 0h4.992m-4.993 0l3.181 3.183a8.25 8.25 0 0013.803-3.7M4.031 9.865a8.25 8.25 0 0113.803-3.7l3.181 3.182m0-4.991v4.99" />
                        </svg>
                        <?php echo e(__('Letzte Synchronisierung')); ?>

                    </span>
                </label>
                <?php if (isset($component)) { $__componentOriginal17d23b099a28c4e2c5f6c448deb256e5 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal17d23b099a28c4e2c5f6c448deb256e5 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.modern-input','data' => ['type' => 'text','name' => 'last_synced','variant' => 'outlined','id' => 'last_synced','value' => ''.e($user->last_synced_at ? $user->last_synced_at->format('d.m.Y H:i') : __('Nie')).'','disabled' => true]] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('modern-input'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['type' => 'text','name' => 'last_synced','variant' => 'outlined','id' => 'last_synced','value' => ''.e($user->last_synced_at ? $user->last_synced_at->format('d.m.Y H:i') : __('Nie')).'','disabled' => true]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal17d23b099a28c4e2c5f6c448deb256e5)): ?>
<?php $attributes = $__attributesOriginal17d23b099a28c4e2c5f6c448deb256e5; ?>
<?php unset($__attributesOriginal17d23b099a28c4e2c5f6c448deb256e5); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal17d23b099a28c4e2c5f6c448deb256e5)): ?>
<?php $component = $__componentOriginal17d23b099a28c4e2c5f6c448deb256e5; ?>
<?php unset($__componentOriginal17d23b099a28c4e2c5f6c448deb256e5); ?>
<?php endif; ?>
                <label class="label">
                    <span class="label-text-alt text-base-content/70"><?php echo e(__('Zeitpunkt der letzten Aktualisierung mit Discord')); ?></span>
                </label>
            </div>
        </div>

        <div class="alert alert-info mt-8">
            <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" class="stroke-current shrink-0 w-6 h-6"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path></svg>
            <div>
                <h3 class="font-bold"><?php echo e(__('Hinweis')); ?></h3>
                <div class="text-sm"><?php echo e(__('Deine Profildaten werden automatisch mit Discord synchronisiert. Änderungen in deinem Discord-Profil werden hier übernommen.')); ?></div>
            </div>
        </div>
    </div>
</section>
<?php /**PATH C:\Users\<USER>\Documents\GitHub\minewache-website\resources\views/profile/partials/update-profile-information-form.blade.php ENDPATH**/ ?>