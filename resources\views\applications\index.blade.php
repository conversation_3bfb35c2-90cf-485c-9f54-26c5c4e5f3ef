<x-app-layout>
    <x-slot name="header">
        <h2 class="font-display text-xl md:text-2xl text-gray-900 dark:text-white leading-tight animate-slide-down">
            {{ __('Alle Bewerbungen') }}
        </h2>
    </x-slot>

    <div class="min-h-screen py-8 md:py-12 bg-base-200">
        <div class="max-w-2xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="bg-base-300 dark:bg-base-100 rounded-xl shadow-lg overflow-hidden opacity-0 animate-slide-up">
                <div class="p-6 md:p-8 text-gray-900 dark:text-gray-100">
                    <ul class="space-y-4">
                        @foreach($applications as $application)
                            @php
                                $discordUser = \App\Models\User::where('id', $application->discord_id)->first();
                                $professions = is_array($application->professions) ? $application->professions : explode(',', $application->professions);
                            @endphp
                            @if($discordUser)
                                <li class="border-4 border-base-300 dark:border-base-content/10 hover:dark:border-base-200 rounded-lg overflow-hidden opacity-0 transform translate-y-4"
                                    x-data
                                    x-init="setTimeout(() => $el.classList.add('transition-all', 'duration-500', 'ease-out', 'opacity-100', 'translate-y-0'), {{ 200 + $loop->index * 100 }})">
                                    <a href="{{ route('applications.show', ['discord_id' => $application->discord_id, 'id' => $application->id]) }}"
                                       class="block p-7 bg-base-200 dark:bg-base-200 hover:dark:bg-primary text-gray-900 dark:text-white transition-all duration-300 shadow-md hover:shadow-lg">
                                        <div class="flex items-center space-x-4">
                                            <img src="{{ $discordUser->getAvatar(['extension' => 'webp', 'size' => 128]) }}" alt="{{ $discordUser->getTagAttribute() }}" class="w-16 h-16 rounded-full">
                                            <div class="flex-1">
                                                <h3 class="font-medium text-lg">{{ $application->name }}</h3>
                                                <p class="text-sm text-gray-500 dark:text-gray-400">Discord Name: {{ $discordUser->username }}</p>
                                                <p class="text-sm text-gray-500 dark:text-gray-400">Bewerbung:</p>
                                                <ul class="list-disc list-inside">
                                                    @foreach($professions as $profession)
                                                        <li>{{ $profession }}</li>
                                                    @endforeach
                                                </ul>
                                            </div>
                                            <div class="text-right">
                                                @if($application->status == 0)
                                                    <span class="bg-blue-500 text-white px-2 py-1 rounded">Offen</span>
                                                @elseif($application->status == 1)
                                                    <span class="text-red-500">Abgelehnt</span>
                                                @elseif($application->status == 2)
                                                    <span class="text-green-500">Angenommen</span>
                                                @endif
                                            </div>
                                        </div>
                                    </a>
                                </li>
                            @endif
                        @endforeach
                    </ul>
                </div>
            </div>
        </div>
    </div>
</x-app-layout>
