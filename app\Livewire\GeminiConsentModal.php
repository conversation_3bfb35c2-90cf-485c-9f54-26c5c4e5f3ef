<?php

namespace App\Livewire;

use App\Models\Ticket;
use Illuminate\Support\Facades\Log;
use Livewire\Component;

class GeminiConsentModal extends Component
{
    public $showModal = false;
    public $ticketId;
    public Ticket $ticket;

    public function mount($ticketId = null)
    {
        $this->ticketId = $ticketId;
        if ($ticketId) {
            $this->ticket = Ticket::findOrFail($ticketId);
        }
    }

    public function render()
    {
        return view('livewire.gemini-consent-modal');
    }

    public function open($ticketId)
    {
        $this->ticketId = $ticketId;
        $this->ticket = Ticket::findOrFail($ticketId);
        $this->showModal = true;
    }

    public function close()
    {
        $this->showModal = false;
    }

    public function giveConsent()
    {
        if (!$this->ticketId) {
            Log::error('Attempted to give Gemini consent without a ticket ID');
            $this->close();
            return;
        }

        $this->ticket->gemini_consent = true;
        $this->ticket->gemini_consent_at = now();
        $this->ticket->save();

        Log::info('User gave Gemini consent for ticket', [
            'ticket_id' => $this->ticket->id,
            'user_id' => auth()->id(),
        ]);

        $this->dispatch('gemini-consent-given', ticketId: $this->ticket->id);
        $this->close();
    }

    public function denyConsent()
    {
        if (!$this->ticketId) {
            Log::error('Attempted to deny Gemini consent without a ticket ID');
            $this->close();
            return;
        }

        $this->ticket->gemini_consent = false;
        $this->ticket->gemini_consent_at = now();
        $this->ticket->save();

        Log::info('User denied Gemini consent for ticket', [
            'ticket_id' => $this->ticket->id,
            'user_id' => auth()->id(),
        ]);

        $this->dispatch('gemini-consent-denied', ticketId: $this->ticket->id);
        $this->close();
    }
}
