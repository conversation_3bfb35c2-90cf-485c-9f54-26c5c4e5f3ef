<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\RedirectResponse;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Cookie;
use <PERSON><PERSON><PERSON><PERSON>\Larascord\Http\Controllers\DiscordController as LarascordController;
use <PERSON><PERSON><PERSON><PERSON>\Larascord\Http\Requests\StoreUserRequest;

class CustomLarascordController extends Controller
{
    /**
     * Handle the Discord OAuth callback.
     *
     * @param Request $request
     * @return RedirectResponse|JsonResponse
     */
    public function handle(Request $request)
    {
        Log::info('CustomLarascordController: Handling Discord OAuth callback', [
            'code_present' => $request->has('code'),
            'error' => $request->get('error'),
            'gdpr_consent' => Cookie::has('gdpr_consent'),
            'session_id' => session()->getId(),
            'intended_url' => session('url.intended'),
            'fresh_login' => session('fresh_discord_login', false),
            'login_timestamp' => session('discord_login_timestamp'),
            'time_since_login' => session('discord_login_timestamp') ? now()->timestamp - session('discord_login_timestamp') : null,
        ]);

        // If there's an error parameter, handle it
        if ($request->has('error')) {
            Log::error('Discord OAuth error', [
                'error' => $request->get('error'),
                'error_description' => $request->get('error_description'),
            ]);

            return redirect()->route('home')->with('error', 'Discord authentication failed: ' . $request->get('error_description', 'Unknown error'));
        }

        // If there's no code parameter, redirect to login
        if (!$request->has('code')) {
            Log::error('No code parameter in Discord callback');
            return redirect()->route('home')->with('error', 'Discord authentication failed: No authorization code received');
        }

        // Check if this is a stale login attempt (more than 5 minutes old)
        $loginTimestamp = session('discord_login_timestamp');
        if ($loginTimestamp && (now()->timestamp - $loginTimestamp > 300)) {
            Log::warning('Stale login attempt detected', [
                'time_since_login' => now()->timestamp - $loginTimestamp,
                'login_timestamp' => $loginTimestamp,
            ]);

            // Clear the stale login data
            session()->forget(['fresh_discord_login', 'discord_login_timestamp']);

            // Start a fresh login attempt
            return redirect()->route('auth.discord')
                ->with('info', 'Ihre Anmeldesitzung ist abgelaufen. Bitte versuchen Sie es erneut.');
        }

        try {
            // Create an instance of the Larascord controller
            $larascordController = app(LarascordController::class);

            // Convert the request to a StoreUserRequest
            $storeUserRequest = new StoreUserRequest();
            $storeUserRequest->merge($request->all());

            // Call the handle method and get the response
            $response = $larascordController->handle($storeUserRequest);

            // Log the response type for debugging
            Log::info('Response from Larascord controller', [
                'response_type' => get_class($response),
                'is_redirect' => $response instanceof RedirectResponse,
                'is_json' => $response instanceof JsonResponse,
                'status' => method_exists($response, 'getStatusCode') ? $response->getStatusCode() : 'unknown',
            ]);

            // Check if the response is a JSON response (usually an error)
            if ($response instanceof JsonResponse) {
                Log::info('Received JSON response from Larascord controller', [
                    'response' => $response->getContent(),
                ]);

                // Parse the JSON response
                $data = json_decode($response->getContent(), true);

                // Check if this is a guild membership error
                if (isset($data['larascord_message']) &&
                    isset($data['larascord_message']['message']) &&
                    strpos($data['larascord_message']['message'], 'not a member of the required guilds') !== false) {

                    Log::info('User is not a member of the required Discord guild');
                    return redirect()->route('discord.membership.required');
                }

                // Check for user ID mismatch error
                if (isset($data['larascord_message']['message']) &&
                    strpos($data['larascord_message']['message'], "The user ID doesn't match") !== false) {

                    Log::warning('User ID mismatch detected, clearing session and redirecting to fresh login');

                    // Clear all session data related to Discord login
                    session()->forget(['fresh_discord_login', 'discord_login_timestamp', 'url.intended']);

                    // Regenerate the session ID to ensure a completely fresh session
                    session()->regenerate();

                    // Set a new fresh login flag
                    session(['fresh_discord_login' => true]);
                    session(['discord_login_timestamp' => now()->timestamp]);

                    // Redirect to the login route for a fresh attempt
                    return redirect()->route('auth.discord')
                        ->with('info', 'Bitte versuchen Sie die Anmeldung erneut.');
                }

                // For other errors, redirect to home with error message
                $errorMessage = $data['larascord_message']['message'] ?? 'Unknown error';
                return redirect()->route('home')->with('error', "Discord authentication failed: {$errorMessage}");
            }

            // If we got here, the response is a redirect response (success)
            Log::info('Discord authentication successful, redirecting to dashboard');

            // Clear the fresh_discord_login flag to prevent double login issues
            session()->forget('fresh_discord_login');

            // Role synchronization is now handled by PermissionController->initialize
            // after redirect to '/permsinit' as configured in config/larascord.php
            Log::info('Authentication successful, will redirect to permsinit for role synchronization', [
                'user_id' => auth()->id(),
                'redirect' => config('larascord.redirect_login')
            ]);

            return $response;

        } catch (\Exception $e) {
            Log::error('Exception during Discord authentication', [
                'message' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            return redirect()->route('home')->with('error', "Discord authentication failed: {$e->getMessage()}");
        }
    }
}
