<?php

if (!function_exists('human_filesize')) {
    /**
     * Format bytes to human readable size.
     *
     * @param int $bytes
     * @param int $decimals
     * @return string
     */
    function human_filesize(int $bytes, int $decimals = 2): string
    {
        if ($bytes <= 0) {
            return '0 B';
        }
        $size = ['B', 'KB', 'MB', 'GB', 'TB', 'PB'];
        $factor = floor((strlen((string)$bytes) - 1) / 3);
        return sprintf("%.{$decimals}f", $bytes / pow(1024, $factor)) . ' ' . ($size[$factor] ?? '??');
    }
}
