<x-app-layout>
    <x-slot name="title">Bewerbung anzeigen - MineWache</x-slot>

    <div class="container mx-auto px-4 py-8">
        <div class="max-w-4xl mx-auto">
            <div class="flex justify-between items-center mb-6">
                <h1 class="text-2xl font-bold">Bewerbung #{{ $application->id }}</h1>
                <div class="flex flex-col md:flex-row gap-2">
                    <div>
                        <span class="mr-2">Status:</span>
                        @if($application->status === 'pending')
                            <span class="badge badge-warning">In Bearbeitung</span>
                        @elseif($application->status === 'approved')
                            <span class="badge badge-success">Angenommen</span>
                        @elseif($application->status === 'rejected')
                            <span class="badge badge-error">Abgelehnt</span>
                        @else
                            <span class="badge">{{ $application->status }}</span>
                        @endif
                    </div>
                    <div>
                        @if($application->editable)
                            <span class="badge badge-success gap-1">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 11V7a4 4 0 118 0m-4 8v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2z" />
                                </svg>
                                Bearbeitbar
                            </span>
                        @else
                            <span class="badge badge-neutral gap-1">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
                                </svg>
                                Nicht bearbeitbar
                            </span>
                        @endif
                    </div>
                </div>
            </div>

            <div class="flex flex-col space-y-2 mb-6 text-sm text-gray-500">
                <div>Eingereicht am {{ $application->created_at->format('d.m.Y') }} um {{ $application->created_at->format('H:i') }} Uhr</div>
                @if($application->updated_at->gt($application->created_at))
                    <div>Zuletzt aktualisiert am {{ $application->updated_at->format('d.m.Y') }} um {{ $application->updated_at->format('H:i') }} Uhr</div>
                @endif
            </div>

            <div class="card bg-base-200 shadow mb-6">
                <div class="card-body">
                    <h2 class="card-title mb-4">Persönliche Informationen</h2>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                            <p class="text-sm font-medium mb-1">Name</p>
                            <p>{{ $application->name ?? 'Nicht angegeben' }}</p>
                        </div>
                        <div>
                            <p class="text-sm font-medium mb-1">Alter</p>
                            <p>{{ $application->age ?? 'Nicht angegeben' }}</p>
                        </div>
                        <div>
                            <p class="text-sm font-medium mb-1">Geschlecht</p>
                            <p>{{ $application->gender ?? 'Nicht angegeben' }}</p>
                        </div>
                        <div>
                            <p class="text-sm font-medium mb-1">Pronomen</p>
                            <p>{{ $application->pronouns ?? 'Nicht angegeben' }}</p>
                        </div>
                    </div>
                </div>
            </div>

            <div class="card bg-base-200 shadow mb-6">
                <div class="card-body">
                    <h2 class="card-title mb-4">Tätigkeitsbereiche</h2>

                    @if(!empty($application->professions))
                        <div class="flex flex-wrap gap-2">
                            @foreach($application->professions as $profession)
                                <span class="badge badge-lg">
                                    @if($profession == 'actor')
                                        Schauspieler
                                    @elseif($profession == 'voice_actor')
                                        Synchronsprecher
                                    @elseif($profession == 'builder')
                                        Builder
                                    @elseif($profession == 'designer')
                                        Designer
                                    @elseif($profession == 'cutter')
                                        Cutter
                                    @elseif($profession == 'cameraman')
                                        Kameramann
                                    @elseif($profession == 'developer')
                                        Entwickler
                                    @elseif($profession == 'modeler')
                                        3D-Modellierer
                                    @elseif($profession == 'music_producer')
                                        Musikproduzent
                                    @elseif($profession == 'other')
                                        Andere
                                    @else
                                        {{ $profession }}
                                    @endif
                                </span>
                            @endforeach
                        </div>
                    @else
                        <p>Keine Tätigkeitsbereiche ausgewählt</p>
                    @endif
                </div>
            </div>

            <div class="card bg-base-200 shadow mb-6">
                <div class="card-body">
                    <h2 class="card-title mb-4">Über dich</h2>
                    <div class="mb-4">
                        <p class="text-sm font-medium mb-1">Erfahre mehr über dich</p>
                        <p class="whitespace-pre-wrap">{{ $application->about_you ?? 'Nicht angegeben' }}</p>
                    </div>
                    <div class="mb-4">
                        <p class="text-sm font-medium mb-1">Stärken und Schwächen</p>
                        <p class="whitespace-pre-wrap">{{ $application->strengths_weaknesses ?? 'Nicht angegeben' }}</p>
                    </div>
                    <div>
                        <p class="text-sm font-medium mb-1">Abschließende Worte</p>
                        <p class="whitespace-pre-wrap">{{ $application->final_words ?? 'Nicht angegeben' }}</p>
                    </div>
                </div>
            </div>

            @if(in_array('voice_actor', $application->professions ?? []))
                <div class="card bg-base-200 shadow mb-6">
                    <div class="card-body">
                        <h2 class="card-title mb-4">Synchronsprecher-Informationen</h2>
                        <div class="mb-4">
                            <p class="text-sm font-medium mb-1">Stimmtyp</p>
                            <p>{{ $application->voice_type ?? 'Nicht angegeben' }}</p>
                        </div>
                        <div>
                            <p class="text-sm font-medium mb-1">Mikrofon</p>
                            <p>{{ $application->microphone ?? 'Nicht angegeben' }}</p>
                        </div>
                    </div>
                </div>
            @endif

            @if(in_array('developer', $application->professions ?? []))
                <div class="card bg-base-200 shadow mb-6">
                    <div class="card-body">
                        <h2 class="card-title mb-4">Entwickler-Informationen</h2>
                        <div class="mb-4">
                            <p class="text-sm font-medium mb-1">Programmiersprachen</p>
                            <p>{{ $application->languages ?? 'Nicht angegeben' }}</p>
                        </div>
                        <div>
                            <p class="text-sm font-medium mb-1">IDE</p>
                            <p>{{ $application->ide ?? 'Nicht angegeben' }}</p>
                        </div>
                    </div>
                </div>
            @endif

            @if(in_array('designer', $application->professions ?? []))
                <div class="card bg-base-200 shadow mb-6">
                    <div class="card-body">
                        <h2 class="card-title mb-4">Designer-Informationen</h2>
                        <div class="mb-4">
                            <p class="text-sm font-medium mb-1">Programme</p>
                            <p>{{ $application->program ?? 'Nicht angegeben' }}</p>
                        </div>
                        <div class="mb-4">
                            <p class="text-sm font-medium mb-1">Design-Stil</p>
                            <p>{{ $application->design_style ?? 'Nicht angegeben' }}</p>
                        </div>
                        <div>
                            <p class="text-sm font-medium mb-1">Portfolio</p>
                            <p>{{ $application->portfolio ?? 'Nicht angegeben' }}</p>
                        </div>
                    </div>
                </div>
            @endif

            <div class="flex justify-between mt-8">
                <a href="{{ route('my.applications') }}" class="btn btn-ghost">
                    Zurück zur Übersicht
                </a>

                <div class="flex flex-col gap-4">
                    @if($application->editable)
                        <a href="{{ route('my.applications.edit', $application->id) }}" class="btn btn-primary">
                            Bewerbung bearbeiten
                        </a>
                    @else
                        <div class="tooltip" data-tip="Ein Administrator muss die Bearbeitung freischalten">
                            <button class="btn btn-disabled">
                                Bewerbung bearbeiten
                            </button>
                        </div>
                    @endif

                    <div class="alert alert-info shadow-lg">
                        <div>
                            <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" class="stroke-current flex-shrink-0 w-6 h-6"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path></svg>
                            <div>
                                <p class="text-sm">Möchtest du dich für weitere Bereiche bewerben? <a href="{{ route('bewerben') }}" class="link link-primary">Reiche eine neue Bewerbung ein</a>.</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</x-app-layout>