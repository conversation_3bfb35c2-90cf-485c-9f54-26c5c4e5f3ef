<?php if (isset($component)) { $__componentOriginal9ac128a9029c0e4701924bd2d73d7f54 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54 = $attributes; } ?>
<?php $component = App\View\Components\AppLayout::resolve([] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('app-layout'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\App\View\Components\AppLayout::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
     <?php $__env->slot('heading', null, []); ?> 
        <?php echo e(__('navigation.partners')); ?>

     <?php $__env->endSlot(); ?>

     <?php $__env->slot('breadcrumbs', null, []); ?> 
        <?php if (isset($component)) { $__componentOriginal360d002b1b676b6f84d43220f22129e2 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal360d002b1b676b6f84d43220f22129e2 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.breadcrumbs','data' => ['items' => [
            ['label' => __('navigation.partners')]
        ]]] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('breadcrumbs'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['items' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute([
            ['label' => __('navigation.partners')]
        ])]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal360d002b1b676b6f84d43220f22129e2)): ?>
<?php $attributes = $__attributesOriginal360d002b1b676b6f84d43220f22129e2; ?>
<?php unset($__attributesOriginal360d002b1b676b6f84d43220f22129e2); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal360d002b1b676b6f84d43220f22129e2)): ?>
<?php $component = $__componentOriginal360d002b1b676b6f84d43220f22129e2; ?>
<?php unset($__componentOriginal360d002b1b676b6f84d43220f22129e2); ?>
<?php endif; ?>
     <?php $__env->endSlot(); ?>

    <main class="flex flex-col min-h-dvh w-full">
        <!-- Hero Section with improved accessibility and readability -->
        <section class="relative bg-base-200 py-16 md:py-24 lg:py-28 overflow-hidden rounded-xl md:rounded-2xl" aria-labelledby="hero-heading">
            <div class="container mx-auto px-4 md:px-6 lg:px-8 xl:max-w-[90%] 2xl:max-w-[85%] relative z-10">
                <div class="max-w-5xl mx-auto text-center">
                    <?php if (isset($component)) { $__componentOriginal6d96dd7d01ac0e896137f5280cb99f17 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal6d96dd7d01ac0e896137f5280cb99f17 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.glassmorphism-card','data' => ['level' => 'light','padding' => 'p-8 md:p-10','rounded' => 'rounded-3xl','shadow' => 'shadow-2xl','hover' => 'true']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('glassmorphism-card'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['level' => 'light','padding' => 'p-8 md:p-10','rounded' => 'rounded-3xl','shadow' => 'shadow-2xl','hover' => 'true']); ?>
                        <div class="inline-block bg-primary/15 px-4 py-2 rounded-full mb-4" aria-hidden="true">
                            <span class="text-primary font-medium text-sm"><?php echo e(__('partners.trusted_partners')); ?></span>
                        </div>
                        <h1 id="hero-heading" class="font-display font-bold text-base-content text-3xl md:text-4xl lg:text-5xl xl:text-6xl tracking-tight mb-6 leading-tight">
                            <span class="text-primary"><?php echo e(__('partners.our')); ?></span> <?php echo e(__('partners.partners')); ?>

                        </h1>
                        <p class="font-modern text-base-content/90 text-lg md:text-xl max-w-3xl mx-auto mb-8 leading-relaxed">
                            <?php echo e(__('partners.intro_text')); ?>

                        </p>
                        <div class="flex flex-wrap justify-center gap-4">
                            <?php if (isset($component)) { $__componentOriginal19987cd45c699eefead8b7a8ac222798 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal19987cd45c699eefead8b7a8ac222798 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.modern-button','data' => ['variant' => 'primary','href' => '#partners','ariaLabel' => ''.e(__('partners.view_partners')).'']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('modern-button'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['variant' => 'primary','href' => '#partners','aria-label' => ''.e(__('partners.view_partners')).'']); ?><?php echo e(__('partners.view_partners')); ?> <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal19987cd45c699eefead8b7a8ac222798)): ?>
<?php $attributes = $__attributesOriginal19987cd45c699eefead8b7a8ac222798; ?>
<?php unset($__attributesOriginal19987cd45c699eefead8b7a8ac222798); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal19987cd45c699eefead8b7a8ac222798)): ?>
<?php $component = $__componentOriginal19987cd45c699eefead8b7a8ac222798; ?>
<?php unset($__componentOriginal19987cd45c699eefead8b7a8ac222798); ?>
<?php endif; ?>
                            <?php if (isset($component)) { $__componentOriginal19987cd45c699eefead8b7a8ac222798 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal19987cd45c699eefead8b7a8ac222798 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.modern-button','data' => ['variant' => 'primary','href' => '#become-partner','ariaLabel' => ''.e(__('partners.become_partner')).'']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('modern-button'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['variant' => 'primary','href' => '#become-partner','aria-label' => ''.e(__('partners.become_partner')).'']); ?><?php echo e(__('partners.become_partner')); ?> <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal19987cd45c699eefead8b7a8ac222798)): ?>
<?php $attributes = $__attributesOriginal19987cd45c699eefead8b7a8ac222798; ?>
<?php unset($__attributesOriginal19987cd45c699eefead8b7a8ac222798); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal19987cd45c699eefead8b7a8ac222798)): ?>
<?php $component = $__componentOriginal19987cd45c699eefead8b7a8ac222798; ?>
<?php unset($__componentOriginal19987cd45c699eefead8b7a8ac222798); ?>
<?php endif; ?>
                        </div>
                     <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal6d96dd7d01ac0e896137f5280cb99f17)): ?>
<?php $attributes = $__attributesOriginal6d96dd7d01ac0e896137f5280cb99f17; ?>
<?php unset($__attributesOriginal6d96dd7d01ac0e896137f5280cb99f17); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal6d96dd7d01ac0e896137f5280cb99f17)): ?>
<?php $component = $__componentOriginal6d96dd7d01ac0e896137f5280cb99f17; ?>
<?php unset($__componentOriginal6d96dd7d01ac0e896137f5280cb99f17); ?>
<?php endif; ?>
                </div>
            </div>

            <!-- Decorative elements -->
            <div class="absolute top-0 left-0 w-full h-full overflow-hidden pointer-events-none" aria-hidden="true">
                <div class="absolute -top-24 -left-24 w-64 h-64 bg-primary/5 rounded-full blur-3xl"></div>
                <div class="absolute top-1/2 -right-32 w-80 h-80 bg-primary/5 rounded-full blur-3xl"></div>
                <div class="absolute -bottom-16 left-1/4 w-72 h-72 bg-primary/5 rounded-full blur-3xl"></div>
            </div>
        </section>

        <!-- Partners Showcase with Slide-in Effect -->
        <section id="partners" class="py-16 md:py-20 lg:py-24 bg-base-100 overflow-hidden rounded-xl md:rounded-2xl" aria-labelledby="partners-heading">
            <div class="container mx-auto px-4 md:px-6 lg:px-8 xl:max-w-[90%] 2xl:max-w-[85%]">
                <div class="text-center mb-16 slide-in-element" data-direction="top">
                    <?php if (isset($component)) { $__componentOriginal6d96dd7d01ac0e896137f5280cb99f17 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal6d96dd7d01ac0e896137f5280cb99f17 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.glassmorphism-card','data' => ['class' => 'inline-block max-w-3xl mx-auto','padding' => 'px-8 py-6','shadow' => 'shadow-xl','rounded' => 'rounded-2xl','hover' => 'true']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('glassmorphism-card'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['class' => 'inline-block max-w-3xl mx-auto','padding' => 'px-8 py-6','shadow' => 'shadow-xl','rounded' => 'rounded-2xl','hover' => 'true']); ?>
                        <h2 id="partners-heading" class="text-3xl md:text-4xl font-bold mb-4 text-base-content font-display"><?php echo e(__('partners.our_partners')); ?></h2>
                        <p class="text-base-content/80 max-w-3xl mx-auto text-lg md:text-xl leading-relaxed font-modern"><?php echo e(__('partners.partners_description')); ?></p>
                     <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal6d96dd7d01ac0e896137f5280cb99f17)): ?>
<?php $attributes = $__attributesOriginal6d96dd7d01ac0e896137f5280cb99f17; ?>
<?php unset($__attributesOriginal6d96dd7d01ac0e896137f5280cb99f17); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal6d96dd7d01ac0e896137f5280cb99f17)): ?>
<?php $component = $__componentOriginal6d96dd7d01ac0e896137f5280cb99f17; ?>
<?php unset($__componentOriginal6d96dd7d01ac0e896137f5280cb99f17); ?>
<?php endif; ?>
                </div>

                <div class="space-y-16 md:space-y-24 lg:space-y-32"> <!-- Adjusted spacing for slide-in effect -->
                    <!-- Bisect Hosting (Slide in from left) -->
                    <div class="partner-section relative h-[400px] sm:h-[450px] md:h-[400px] lg:h-[450px]" data-scroll="left" aria-labelledby="bisect-heading">
                        <div class="absolute inset-0 overflow-hidden">
                            <img src="https://www.bisecthosting.com/_ipx/q_100&s_860x480/static/img/common/BH_WebVids_Minecraft.png"
                                 alt="Grand Theft Warzone Spielszene"
                                 class="w-full h-full object-cover fade-to-left">
                        </div>

                        <div class="relative z-10 h-full flex items-center">
                            <?php if (isset($component)) { $__componentOriginal6d96dd7d01ac0e896137f5280cb99f17 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal6d96dd7d01ac0e896137f5280cb99f17 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.glassmorphism-card','data' => ['border' => 'primary-all','level' => 'light','class' => 'max-w-xs sm:max-w-sm md:max-w-md lg:max-w-lg ml-4 sm:ml-8 md:ml-12','shadow' => 'shadow-xl','hover' => 'true']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('glassmorphism-card'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['border' => 'primary-all','level' => 'light','class' => 'max-w-xs sm:max-w-sm md:max-w-md lg:max-w-lg ml-4 sm:ml-8 md:ml-12','shadow' => 'shadow-xl','hover' => 'true']); ?>
                                <div class="inline-block bg-primary/90 text-white px-3 py-1 rounded-full text-sm font-medium mb-4" aria-hidden="true">
                                    <?php echo e(__('partners.hosting_partner')); ?>

                                </div>

                                <h3 id="bisect-heading" class="text-2xl md:text-3xl font-bold mb-3 text-base-content font-display">Bisect Hosting</h3>

                                <p class="text-base-content/90 mb-6 leading-relaxed font-modern">
                                    Professionelle Minecraft-Server zu günstigen Preisen. Nutze den Code <strong class="text-primary font-semibold">"SAROCESCH"</strong> für 25% Rabatt auf deinen ersten Monat.
                                </p>

                                <div class="flex flex-wrap items-center gap-4">
                                    <?php if (isset($component)) { $__componentOriginal19987cd45c699eefead8b7a8ac222798 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal19987cd45c699eefead8b7a8ac222798 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.modern-button','data' => ['variant' => 'primary','href' => 'https://www.bisecthosting.com/sarocesch','ariaLabel' => 'Mehr über Bisect Hosting erfahren','rel' => 'noopener noreferrer','target' => '_blank']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('modern-button'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['variant' => 'primary','href' => 'https://www.bisecthosting.com/sarocesch','aria-label' => 'Mehr über Bisect Hosting erfahren','rel' => 'noopener noreferrer','target' => '_blank']); ?><svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor" aria-hidden="true">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14" />
                                        </svg>
                                        <?php echo e(__('messages.learn_more')); ?> <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal19987cd45c699eefead8b7a8ac222798)): ?>
<?php $attributes = $__attributesOriginal19987cd45c699eefead8b7a8ac222798; ?>
<?php unset($__attributesOriginal19987cd45c699eefead8b7a8ac222798); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal19987cd45c699eefead8b7a8ac222798)): ?>
<?php $component = $__componentOriginal19987cd45c699eefead8b7a8ac222798; ?>
<?php unset($__componentOriginal19987cd45c699eefead8b7a8ac222798); ?>
<?php endif; ?>

                                    <div class="flex items-center gap-2" aria-label="Aktiver Partner seit 2024">
                                        <span class="inline-block w-2 h-2 rounded-full bg-green-500" aria-hidden="true"></span>
                                        <span class="text-sm text-base-content/80"><?php echo e(__('partners.active_partner')); ?> <?php echo e(__('partners.since')); ?> 2022</span>
                                    </div>
                                </div>
                             <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal6d96dd7d01ac0e896137f5280cb99f17)): ?>
<?php $attributes = $__attributesOriginal6d96dd7d01ac0e896137f5280cb99f17; ?>
<?php unset($__attributesOriginal6d96dd7d01ac0e896137f5280cb99f17); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal6d96dd7d01ac0e896137f5280cb99f17)): ?>
<?php $component = $__componentOriginal6d96dd7d01ac0e896137f5280cb99f17; ?>
<?php unset($__componentOriginal6d96dd7d01ac0e896137f5280cb99f17); ?>
<?php endif; ?>
                        </div>
                    </div>

                    <!-- GTW (Grand Theft Warzone) (Slide in from right) -->
                    <div class="partner-section relative h-[400px] sm:h-[450px] md:h-[400px] lg:h-[450px]" data-scroll="right" aria-labelledby="gtw-heading">
                        <div class="absolute inset-0 overflow-hidden">
                            <img src="https://cdn.craftingstore.net/rPPmDHlLQ1/0750f7c98f796a8a805bb9c1e6b48b53/bp1jpvtswpwbkcegnr67.jpg"
                                 alt="Bisect Hosting Minecraft Server"
                                 class="w-full h-full object-cover fade-to-right">
                        </div>

                        <div class="relative z-10 h-full flex items-center justify-end">
                            <?php if (isset($component)) { $__componentOriginal6d96dd7d01ac0e896137f5280cb99f17 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal6d96dd7d01ac0e896137f5280cb99f17 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.glassmorphism-card','data' => ['border' => 'secondary-all','level' => 'light','class' => 'max-w-xs sm:max-w-sm md:max-w-md lg:max-w-lg mr-4 sm:mr-8 md:mr-12','shadow' => 'shadow-xl','hover' => 'true']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('glassmorphism-card'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['border' => 'secondary-all','level' => 'light','class' => 'max-w-xs sm:max-w-sm md:max-w-md lg:max-w-lg mr-4 sm:mr-8 md:mr-12','shadow' => 'shadow-xl','hover' => 'true']); ?>
                                <div class="inline-block bg-secondary/90 text-white px-3 py-1 rounded-full text-sm font-medium mb-4" aria-hidden="true">
                                    <?php echo e(__('partners.community_partner')); ?>

                                </div>

                                <h3 id="gtw-heading" class="text-2xl md:text-3xl font-bold mb-3 text-base-content font-display">GTW (Grand Theft Warzone)</h3>

                                <p class="text-base-content/90 mb-6 leading-relaxed font-modern">
                                    Erleben Sie spannende Abenteuer in der Welt von Grand Theft Warzone. Werden Sie Teil einer wachsenden Community mit einzigartigen Spielerlebnissen.
                                </p>

                                <div class="flex flex-wrap items-center gap-4">
                                    <?php if (isset($component)) { $__componentOriginal19987cd45c699eefead8b7a8ac222798 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal19987cd45c699eefead8b7a8ac222798 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.modern-button','data' => ['variant' => 'primary','href' => '#','ariaLabel' => 'Mehr über Grand Theft Warzone erfahren']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('modern-button'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['variant' => 'primary','href' => '#','aria-label' => 'Mehr über Grand Theft Warzone erfahren']); ?><svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor" aria-hidden="true">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14" />
                                        </svg>
                                        <?php echo e(__('messages.learn_more')); ?> <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal19987cd45c699eefead8b7a8ac222798)): ?>
<?php $attributes = $__attributesOriginal19987cd45c699eefead8b7a8ac222798; ?>
<?php unset($__attributesOriginal19987cd45c699eefead8b7a8ac222798); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal19987cd45c699eefead8b7a8ac222798)): ?>
<?php $component = $__componentOriginal19987cd45c699eefead8b7a8ac222798; ?>
<?php unset($__componentOriginal19987cd45c699eefead8b7a8ac222798); ?>
<?php endif; ?>

                                    <div class="flex items-center gap-2" aria-label="Aktiver Partner seit 2023">
                                        <span class="inline-block w-2 h-2 rounded-full bg-green-500" aria-hidden="true"></span>
                                        <span class="text-sm text-base-content/80"><?php echo e(__('partners.active_partner')); ?> <?php echo e(__('partners.since')); ?> 2024</span>
                                    </div>
                                </div>
                             <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal6d96dd7d01ac0e896137f5280cb99f17)): ?>
<?php $attributes = $__attributesOriginal6d96dd7d01ac0e896137f5280cb99f17; ?>
<?php unset($__attributesOriginal6d96dd7d01ac0e896137f5280cb99f17); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal6d96dd7d01ac0e896137f5280cb99f17)): ?>
<?php $component = $__componentOriginal6d96dd7d01ac0e896137f5280cb99f17; ?>
<?php unset($__componentOriginal6d96dd7d01ac0e896137f5280cb99f17); ?>
<?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Become a Partner (with slide-in effect) -->
        <section id="become-partner" class="py-16 md:py-20 lg:py-24 bg-base-200 overflow-hidden rounded-xl md:rounded-2xl" aria-labelledby="become-partner-heading">
            <div class="container mx-auto px-4 md:px-6 lg:px-8 xl:max-w-[90%] 2xl:max-w-[85%]">
                <!-- Partner section with slide-in effect -->
                <!-- Modern Partner Application Section -->
                <div class="flex flex-col lg:flex-row gap-8 lg:gap-12 items-stretch">
                    <!-- Left side: Image and visual elements -->
                    <div class="w-full lg:w-5/12 relative rounded-xl overflow-hidden h-[300px] lg:h-auto">
                        <img src="https://sarocesch.de/file/i/f87e375f37764362.png"
                            alt="Partnerschaft mit Minewache"
                            class="w-full h-full object-cover">
                        <div class="absolute inset-0 bg-gradient-to-t from-base-200/80 to-transparent"></div>
                        <div class="absolute bottom-4 left-4 right-4 text-center lg:text-left">
                            <div class="inline-block bg-primary/90 text-white px-4 py-2 rounded-full text-sm font-medium">
                                <?php echo e(__('partners.become_our_partner')); ?>

                            </div>
                        </div>
                    </div>

                    <!-- Right side: Content -->
                    <div class="w-full lg:w-7/12">
                        <?php if (isset($component)) { $__componentOriginal6d96dd7d01ac0e896137f5280cb99f17 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal6d96dd7d01ac0e896137f5280cb99f17 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.glassmorphism-card','data' => ['border' => 'primary-all','level' => 'light','padding' => 'p-6 md:p-8','rounded' => 'rounded-2xl','shadow' => 'shadow-2xl']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('glassmorphism-card'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['border' => 'primary-all','level' => 'light','padding' => 'p-6 md:p-8','rounded' => 'rounded-2xl','shadow' => 'shadow-2xl']); ?>
                            <div class="flex flex-col gap-6">
                                <div class="flex items-center gap-4">
                                    <div class="p-4 bg-primary/20 rounded-xl flex-shrink-0" aria-hidden="true">
                                        <svg xmlns="http://www.w3.org/2000/svg" class="h-10 w-10 text-primary" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
                                        </svg>
                                    </div>
                                    <h2 id="become-partner-heading" class="text-2xl md:text-3xl font-bold text-base-content"><?php echo e(__('partners.become_our_partner')); ?></h2>
                                </div>

                                <p class="text-base-content/90 text-lg leading-relaxed">
                                    Interessiert an einer Partnerschaft mit Minewache? Wir sind immer auf der Suche nach spannenden Kooperationen, die unserer Community einen Mehrwert bieten.
                                </p>

                                <div class="grid grid-cols-1 sm:grid-cols-2 gap-6 my-4">
                                    <div class="flex items-start gap-3 benefit-item bg-base-100/30 p-4 rounded-xl hover:bg-base-100/50 transition-colors">
                                        <div class="bg-primary/20 p-2 rounded-full" aria-hidden="true">
                                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-primary" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
                                            </svg>
                                        </div>
                                        <div>
                                            <h3 class="font-medium text-base-content"><?php echo e(__('partners.benefit_1_title')); ?></h3>
                                            <p class="text-sm text-base-content/80 leading-relaxed"><?php echo e(__('partners.benefit_1_desc')); ?></p>
                                        </div>
                                    </div>

                                    <div class="flex items-start gap-3 benefit-item bg-base-100/30 p-4 rounded-xl hover:bg-base-100/50 transition-colors">
                                        <div class="bg-primary/20 p-2 rounded-full" aria-hidden="true">
                                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-primary" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
                                            </svg>
                                        </div>
                                        <div>
                                            <h3 class="font-medium text-base-content"><?php echo e(__('partners.benefit_2_title')); ?></h3>
                                            <p class="text-sm text-base-content/80 leading-relaxed"><?php echo e(__('partners.benefit_2_desc')); ?></p>
                                        </div>
                                    </div>

                                    <div class="flex items-start gap-3 benefit-item bg-base-100/30 p-4 rounded-xl hover:bg-base-100/50 transition-colors">
                                        <div class="bg-primary/20 p-2 rounded-full" aria-hidden="true">
                                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-primary" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
                                            </svg>
                                        </div>
                                        <div>
                                            <h3 class="font-medium text-base-content"><?php echo e(__('partners.benefit_3_title')); ?></h3>
                                            <p class="text-sm text-base-content/80 leading-relaxed"><?php echo e(__('partners.benefit_3_desc')); ?></p>
                                        </div>
                                    </div>

                                    <div class="flex items-start gap-3 benefit-item bg-base-100/30 p-4 rounded-xl hover:bg-base-100/50 transition-colors">
                                        <div class="bg-primary/20 p-2 rounded-full" aria-hidden="true">
                                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-primary" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
                                            </svg>
                                        </div>
                                        <div>
                                            <h3 class="font-medium text-base-content"><?php echo e(__('partners.benefit_4_title')); ?></h3>
                                            <p class="text-sm text-base-content/80 leading-relaxed"><?php echo e(__('partners.benefit_4_desc')); ?></p>
                                        </div>
                                    </div>
                                </div>

                                <div class="flex flex-col sm:flex-row gap-4 mt-4">
                                    <?php if (isset($component)) { $__componentOriginal19987cd45c699eefead8b7a8ac222798 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal19987cd45c699eefead8b7a8ac222798 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.modern-button','data' => ['variant' => 'primary','href' => 'mailto:<EMAIL>','ariaLabel' => ''.e(__('partners.contact_us')).' per E-Mail']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('modern-button'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['variant' => 'primary','href' => 'mailto:<EMAIL>','aria-label' => ''.e(__('partners.contact_us')).' per E-Mail']); ?><svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor" aria-hidden="true">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                                        </svg>
                                        <?php echo e(__('partners.contact_us')); ?> <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal19987cd45c699eefead8b7a8ac222798)): ?>
<?php $attributes = $__attributesOriginal19987cd45c699eefead8b7a8ac222798; ?>
<?php unset($__attributesOriginal19987cd45c699eefead8b7a8ac222798); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal19987cd45c699eefead8b7a8ac222798)): ?>
<?php $component = $__componentOriginal19987cd45c699eefead8b7a8ac222798; ?>
<?php unset($__componentOriginal19987cd45c699eefead8b7a8ac222798); ?>
<?php endif; ?>
                                    <?php if (isset($component)) { $__componentOriginal19987cd45c699eefead8b7a8ac222798 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal19987cd45c699eefead8b7a8ac222798 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.modern-button','data' => ['variant' => 'primary','href' => ''.e(route('bewerben')).'','ariaLabel' => ''.e(__('navigation.apply')).' als Partner']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('modern-button'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['variant' => 'primary','href' => ''.e(route('bewerben')).'','aria-label' => ''.e(__('navigation.apply')).' als Partner']); ?><svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor" aria-hidden="true">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                                        </svg>
                                        <?php echo e(__('navigation.apply')); ?> <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal19987cd45c699eefead8b7a8ac222798)): ?>
<?php $attributes = $__attributesOriginal19987cd45c699eefead8b7a8ac222798; ?>
<?php unset($__attributesOriginal19987cd45c699eefead8b7a8ac222798); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal19987cd45c699eefead8b7a8ac222798)): ?>
<?php $component = $__componentOriginal19987cd45c699eefead8b7a8ac222798; ?>
<?php unset($__componentOriginal19987cd45c699eefead8b7a8ac222798); ?>
<?php endif; ?>
                                </div>
                            </div>
                         <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal6d96dd7d01ac0e896137f5280cb99f17)): ?>
<?php $attributes = $__attributesOriginal6d96dd7d01ac0e896137f5280cb99f17; ?>
<?php unset($__attributesOriginal6d96dd7d01ac0e896137f5280cb99f17); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal6d96dd7d01ac0e896137f5280cb99f17)): ?>
<?php $component = $__componentOriginal6d96dd7d01ac0e896137f5280cb99f17; ?>
<?php unset($__componentOriginal6d96dd7d01ac0e896137f5280cb99f17); ?>
<?php endif; ?>
                    </div>
                </div>
            </div>
        </section>
    </main>

    <!-- JavaScript for Modern Animations with Slide-in Effect (Optimized for performance and accessibility) -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Check if user prefers reduced motion
            const prefersReducedMotion = window.matchMedia('(prefers-reduced-motion: reduce)').matches;

            // Initialize Intersection Observer for scroll animations
            const animatedElements = document.querySelectorAll('.slide-in-element');

            const observer = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        // If user prefers reduced motion, just show the element without animation
                        if (prefersReducedMotion) {
                            entry.target.style.opacity = '1';
                            entry.target.style.transform = 'none';
                        } else {
                            entry.target.classList.add('animate-in');
                        }
                        observer.unobserve(entry.target); // Stop observing once animated
                    }
                });
            }, {
                root: null, // Use viewport as root
                threshold: 0.15, // Trigger when 15% of element is visible
                rootMargin: '0px 0px -50px 0px' // Slightly before element enters viewport
            });

            // Observe all animated elements
            animatedElements.forEach(el => {
                observer.observe(el);
            });

            // Handle partner sections with slide-in effect
            const partnerSections = document.querySelectorAll('.partner-section');

            // Use requestAnimationFrame for better performance
            let ticking = false;

            const handleScroll = () => {
                if (!ticking) {
                    window.requestAnimationFrame(() => {
                        const scrollPosition = window.scrollY + window.innerHeight;

                        partnerSections.forEach(section => {
                            const sectionTop = section.getBoundingClientRect().top + window.scrollY;
                            const direction = section.dataset.scroll;

                            // If user prefers reduced motion, just show the element without animation
                            if (prefersReducedMotion) {
                                section.style.opacity = '1';
                                section.style.transform = 'none';
                                return;
                            }

                            if (scrollPosition > sectionTop + 100) {
                                // Calculate how far into the viewport the section is
                                const progress = Math.min((scrollPosition - sectionTop) / (window.innerHeight * 0.8), 1);

                                // Apply different transforms based on direction
                                if (direction === 'left') {
                                    section.style.transform = `translateX(${(1 - progress) * -50}%)`;
                                } else if (direction === 'right') {
                                    section.style.transform = `translateX(${(1 - progress) * 50}%)`;
                                } else if (direction === 'top') {
                                    section.style.transform = `translateY(${(1 - progress) * 30}%)`;
                                }

                                // Apply opacity based on progress
                                section.style.opacity = progress;
                            } else {
                                // Set initial position off-screen
                                if (direction === 'left') {
                                    section.style.transform = 'translateX(-50%)';
                                } else if (direction === 'right') {
                                    section.style.transform = 'translateX(50%)';
                                } else if (direction === 'top') {
                                    section.style.transform = 'translateY(30%)';
                                }
                                section.style.opacity = 0;
                            }
                        });

                        ticking = false;
                    });

                    ticking = true;
                }
            };

            // Add staggered animation to benefit items (respecting reduced motion preference)
            const benefitItems = document.querySelectorAll('.benefit-item');
            benefitItems.forEach((item, index) => {
                if (prefersReducedMotion) {
                    item.style.opacity = '1';
                    item.style.transform = 'none';
                } else {
                    item.style.opacity = '0';
                    item.style.transform = 'translateY(20px)';
                    item.style.transition = 'opacity 0.5s ease, transform 0.5s ease';
                    item.style.transitionDelay = `${index * 0.1 + 0.3}s`;

                    setTimeout(() => {
                        item.style.opacity = '1';
                        item.style.transform = 'translateY(0)';
                    }, 100);
                }
            });

            // Use passive event listener for better performance
            window.addEventListener('scroll', handleScroll, { passive: true });
            handleScroll(); // Initial call to set positions

            // Add keyboard navigation for interactive elements
            const focusableElements = document.querySelectorAll('a, button, input, select, textarea, [tabindex]:not([tabindex="-1"])');
            focusableElements.forEach(el => {
                el.addEventListener('keydown', function(e) {
                    // If Enter key is pressed, trigger click
                    if (e.key === 'Enter') {
                        e.preventDefault();
                        el.click();
                    }
                });
            });
        });
    </script>

    <style>
        /* Modern animation styles with enhanced slide-in effect */
        .partner-section {
            transition: transform 0.8s cubic-bezier(0.16, 1, 0.3, 1), opacity 0.8s cubic-bezier(0.16, 1, 0.3, 1);
            will-change: transform, opacity;
        }

        .slide-in-element {
            opacity: 0;
            transform: translateY(30px);
            transition: opacity 0.8s cubic-bezier(0.16, 1, 0.3, 1), transform 0.8s cubic-bezier(0.16, 1, 0.3, 1);
        }

        .slide-in-element.animate-in {
            opacity: 1;
            transform: translateY(0);
        }

        /* Hover effects for cards */
        .benefit-item {
            transition: all 0.3s ease;
        }

        .benefit-item:hover {
            transform: translateY(-5px);
        }

        /* Enhanced fade effects with proper syntax */
        .fade-to-left {
            -webkit-mask-image: -webkit-linear-gradient(left, black 80%, transparent 100%);
            mask-image: linear-gradient(to right, black 80%, transparent 100%);
        }

        .fade-to-right {
            -webkit-mask-image: -webkit-linear-gradient(right, black 80%, transparent 100%);
            mask-image: linear-gradient(to left, black 80%, transparent 100%);
        }

        /* Smooth scrolling for anchor links (with reduced motion preference) */
        @media (prefers-reduced-motion: no-preference) {
            html {
                scroll-behavior: smooth;
            }
        }

        /* Improved focus states for accessibility */
        a:focus-visible, button:focus-visible, input:focus-visible, select:focus-visible, textarea:focus-visible {
            outline: 3px solid #265FC2;
            outline-offset: 3px;
            box-shadow: 0 0 0 3px rgba(38, 95, 194, 0.3);
        }

        /* High contrast mode support */
        @media (forced-colors: active) {
            .partner-section, .slide-in-element {
                border: 1px solid transparent;
                border-radius: 0.75rem;
                overflow: hidden; /* Wichtig, damit Bilder innerhalb der abgerundeten Ecken bleiben */
            }

            a:focus-visible, button:focus-visible {
                outline: 3px solid CanvasText;
            }
        }

        /* Prevent horizontal scrolling */
        body {
            overflow-x: hidden;
        }

        /* Improved text readability */
        p, h1, h2, h3, h4, h5, h6 {
            max-width: 70ch; /* Optimal reading width */
        }


        /* Custom styles for this page */
        /* Note: The glassmorphism effect is now handled by the x-glassmorphism-card component */


    </style>
 <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54)): ?>
<?php $attributes = $__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54; ?>
<?php unset($__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal9ac128a9029c0e4701924bd2d73d7f54)): ?>
<?php $component = $__componentOriginal9ac128a9029c0e4701924bd2d73d7f54; ?>
<?php unset($__componentOriginal9ac128a9029c0e4701924bd2d73d7f54); ?>
<?php endif; ?>
<?php /**PATH C:\Users\<USER>\Documents\GitHub\minewache-website\resources\views/partner.blade.php ENDPATH**/ ?>