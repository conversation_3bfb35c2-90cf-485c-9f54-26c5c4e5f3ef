<?php

namespace Tests\Unit\Utilities;

use PHPUnit\Framework\TestCase;

class StringUtilsTest extends TestCase
{
    /**
     * Test string truncation functionality.
     */
    public function test_string_truncation(): void
    {
        // Simple truncation test
        $longString = "This is a very long string that needs to be truncated";
        $truncated = $this->truncateString($longString, 20);

        $this->assertEquals("This is a very long ...", $truncated);
        $this->assertTrue(strlen($truncated) <= 23); // 20 + 3 for ellipsis
    }

    /**
     * Helper function to truncate a string.
     *
     * @param string $string The string to truncate
     * @param int $length The maximum length
     * @return string The truncated string
     */
    private function truncateString(string $string, int $length): string
    {
        if (strlen($string) <= $length) {
            return $string;
        }

        return substr($string, 0, $length) . '...';
    }
}
