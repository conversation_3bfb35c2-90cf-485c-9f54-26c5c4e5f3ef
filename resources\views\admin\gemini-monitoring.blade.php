@extends('layouts.app')

@section('title', 'Gemini AI Monitoring')

@section('content')
<div class="container mx-auto px-4 py-8">
    <div class="mb-6">
        <h1 class="text-2xl font-bold text-white mb-2">Gemini AI Monitoring</h1>
        <p class="text-gray-300">Monitor the usage and performance of the Gemini AI integration.</p>
    </div>

    @if(session('success'))
    <div class="bg-green-500/20 border border-green-500/30 text-green-200 px-4 py-3 rounded mb-6">
        {{ session('success') }}
    </div>
    @endif

    <!-- Rate Limit Stats -->
    <x-modern-card variant="default" size="md">
        <div class="flex justify-between items-center mb-4">
            <h2 class="text-xl font-semibold text-white">Rate Limit Usage</h2>
            <form action="{{ route('admin.gemini.reset-limits') }}" method="POST">
                @csrf
                <button type="submit" class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg text-sm">
                    Reset Rate Limits
                </button>
            </form>
        </x-modern-card>

        <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
            <!-- Per Minute -->
            <div class="bg-slate-800/50 rounded-lg p-4">
                <h3 class="text-lg font-medium text-white mb-2">Per Minute</h3>
                <div class="flex justify-between mb-2">
                    <span class="text-gray-300">Usage:</span>
                    <span class="text-white font-medium">{{ $usageStats['minute']['count'] }} / {{ $usageStats['minute']['limit'] }}</span>
                </div>
                <div class="w-full bg-slate-700 rounded-full h-2.5">
                    <div class="bg-blue-600 h-2.5 rounded-full" style="width: {{ min(100, ($usageStats['minute']['count'] / max(1, $usageStats['minute']['limit'])) * 100) }}%"></div>
                </div>
                <p class="text-xs text-gray-400 mt-2">Resets every minute</p>
            </div>

            <!-- Per Hour -->
            <div class="bg-slate-800/50 rounded-lg p-4">
                <h3 class="text-lg font-medium text-white mb-2">Per Hour</h3>
                <div class="flex justify-between mb-2">
                    <span class="text-gray-300">Usage:</span>
                    <span class="text-white font-medium">{{ $usageStats['hour']['count'] }} / {{ $usageStats['hour']['limit'] }}</span>
                </div>
                <div class="w-full bg-slate-700 rounded-full h-2.5">
                    <div class="bg-blue-600 h-2.5 rounded-full" style="width: {{ min(100, ($usageStats['hour']['count'] / max(1, $usageStats['hour']['limit'])) * 100) }}%"></div>
                </div>
                <p class="text-xs text-gray-400 mt-2">Resets every hour</p>
            </div>

            <!-- Per Day -->
            <div class="bg-slate-800/50 rounded-lg p-4">
                <h3 class="text-lg font-medium text-white mb-2">Per Day</h3>
                <div class="flex justify-between mb-2">
                    <span class="text-gray-300">Usage:</span>
                    <span class="text-white font-medium">{{ $usageStats['day']['count'] }} / {{ $usageStats['day']['limit'] }}</span>
                </div>
                <div class="w-full bg-slate-700 rounded-full h-2.5">
                    <div class="bg-blue-600 h-2.5 rounded-full" style="width: {{ min(100, ($usageStats['day']['count'] / max(1, $usageStats['day']['limit'])) * 100) }}%"></div>
                </div>
                <p class="text-xs text-gray-400 mt-2">Resets every day</p>
            </div>
        </div>
    </div>

    <!-- AI Message Stats -->
    <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
        <!-- Message Counts -->
        <x-modern-card variant="default" size="md">
            <h2 class="text-xl font-semibold text-white mb-4">AI Message Statistics</h2>
            <div class="space-y-4">
                <div class="flex justify-between items-center">
                    <span class="text-gray-300">Total AI Messages:</span>
                    <span class="text-white font-medium text-lg">{{ $totalAIMessages }}</span>
                </x-modern-card>
                <div class="flex justify-between items-center">
                    <span class="text-gray-300">Today:</span>
                    <span class="text-white font-medium">{{ $aiMessagesToday }}</span>
                </div>
                <div class="flex justify-between items-center">
                    <span class="text-gray-300">This Week:</span>
                    <span class="text-white font-medium">{{ $aiMessagesThisWeek }}</span>
                </div>
                <div class="flex justify-between items-center">
                    <span class="text-gray-300">This Month:</span>
                    <span class="text-white font-medium">{{ $aiMessagesThisMonth }}</span>
                </div>
            </div>
        </div>

        <!-- Consent Stats -->
        <x-modern-card variant="default" size="md">
            <h2 class="text-xl font-semibold text-white mb-4">AI Consent Statistics</h2>
            <div class="mb-4">
                <div class="flex justify-between items-center mb-2">
                    <span class="text-gray-300">Tickets with AI Consent:</span>
                    <span class="text-white font-medium">{{ $ticketsWithConsent }} / {{ $totalTickets }}</span>
                </x-modern-card>
                <div class="w-full bg-slate-700 rounded-full h-2.5">
                    <div class="bg-green-600 h-2.5 rounded-full" style="width: {{ $consentPercentage }}%"></div>
                </div>
                <p class="text-right text-sm text-gray-300 mt-1">{{ $consentPercentage }}% of tickets</p>
            </div>
        </div>
    </div>

    <!-- Hourly Distribution Chart -->
    <x-modern-card variant="default" size="md">
        <h2 class="text-xl font-semibold text-white mb-4">AI Message Distribution (Last 7 Days)</h2>
        <div class="h-64">
            <canvas id="hourlyChart"></canvas>
        </x-modern-card>
    </div>

    <!-- Recent AI Messages -->
    <x-modern-card variant="default" size="md">
        <h2 class="text-xl font-semibold text-white mb-4">Recent AI Messages</h2>
        
        @if($recentMessages->isEmpty())
            <p class="text-gray-400 text-center py-4">No AI messages found.</p>
        @else
            <div class="overflow-x-auto">
                <table class="w-full text-sm text-left text-gray-300">
                    <thead class="text-xs uppercase bg-slate-800/50 text-gray-400">
                        <tr>
                            <th scope="col" class="px-6 py-3 rounded-l-lg">Ticket</th>
                            <th scope="col" class="px-6 py-3">Message Preview</th>
                            <th scope="col" class="px-6 py-3">Created</th>
                            <th scope="col" class="px-6 py-3 rounded-r-lg">Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach($recentMessages as $message)
                            <tr class="border-b border-gray-700/30">
                                <td class="px-6 py-4">
                                    <a href="{{ route('tickets.show', $message->ticket) }}" class="text-blue-400 hover:underline">
                                        #{{ $message->ticket->id }} - {{ Str::limit($message->ticket->title, 30) }}
                                    </a>
                                </td>
                                <td class="px-6 py-4">
                                    {{ Str::limit($message->message, 50) }}
                                </td>
                                <td class="px-6 py-4">
                                    {{ $message->created_at->diffForHumans() }}
                                </td>
                                <td class="px-6 py-4">
                                    <a href="{{ route('tickets.show', $message->ticket) }}" class="text-blue-400 hover:text-blue-300">
                                        View
                                    </a>
                                </td>
                            </tr>
                        @endforeach
                    </tbody>
                </table>
            </x-modern-card>
        @endif
    </div>
</div>
@endsection

@push('scripts')
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
document.addEventListener('DOMContentLoaded', function() {
    const ctx = document.getElementById('hourlyChart').getContext('2d');
    
    const hourlyData = @json($hourlyData);
    const labels = Array.from({length: 24}, (_, i) => `${i}:00`);
    const data = Object.values(hourlyData);
    
    new Chart(ctx, {
        type: 'bar',
        data: {
            labels: labels,
            datasets: [{
                label: 'AI Messages',
                data: data,
                backgroundColor: 'rgba(59, 130, 246, 0.5)',
                borderColor: 'rgba(59, 130, 246, 1)',
                borderWidth: 1
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            scales: {
                y: {
                    beginAtZero: true,
                    ticks: {
                        color: 'rgba(255, 255, 255, 0.7)'
                    },
                    grid: {
                        color: 'rgba(255, 255, 255, 0.1)'
                    }
                },
                x: {
                    ticks: {
                        color: 'rgba(255, 255, 255, 0.7)'
                    },
                    grid: {
                        color: 'rgba(255, 255, 255, 0.1)'
                    }
                }
            },
            plugins: {
                legend: {
                    labels: {
                        color: 'rgba(255, 255, 255, 0.7)'
                    }
                }
            }
        }
    });
});
</script>
@endpush
