<x-app-layout>
    <x-slot name="title"><PERSON><PERSON>bu<PERSON>n - MineWache</x-slot>

    <div class="container mx-auto px-4 py-8">
        <div class="max-w-4xl mx-auto">
            <div class="flex justify-between items-center mb-6">
                <h1 class="text-2xl font-bold">Meine Bewerbungen</h1>
                <a href="{{ route('bewerben') }}" class="btn btn-primary btn-sm gap-2">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                    </svg>
                    Neue Bewerbung
                </a>
            </div>

            @if(count($applications) > 0)
            <div class="card bg-base-100 shadow-xl mb-6 overflow-hidden">
                <div class="card-body p-4">
                    <h3 class="card-title text-lg mb-2">Bewerbungsübersicht</h3>
                    <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
                        <div class="stat bg-primary/10 rounded-box p-3 border border-primary/20">
                            <div class="stat-figure text-primary">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-8 w-8" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                                </svg>
                            </div>
                            <div class="stat-title text-xs md:text-sm">Gesamt</div>
                            <div class="stat-value text-primary text-2xl md:text-3xl">{{ count($applications) }}</div>
                            <div class="stat-desc text-xs">Bewerbungen</div>
                        </div>

                        <div class="stat bg-warning/10 rounded-box p-3 border border-warning/20">
                            <div class="stat-figure text-warning">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-8 w-8" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                                </svg>
                            </div>
                            <div class="stat-title text-xs md:text-sm">Ausstehend</div>
                            <div class="stat-value text-warning text-2xl md:text-3xl">{{ $applications->where('status', 'pending')->count() }}</div>
                            <div class="stat-desc text-xs">In Bearbeitung</div>
                        </div>

                        <div class="stat bg-success/10 rounded-box p-3 border border-success/20">
                            <div class="stat-figure text-success">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-8 w-8" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                                </svg>
                            </div>
                            <div class="stat-title text-xs md:text-sm">Angenommen</div>
                            <div class="stat-value text-success text-2xl md:text-3xl">{{ $applications->where('status', 'approved')->count() }}</div>
                            <div class="stat-desc text-xs">Erfolgreiche</div>
                        </div>

                        <div class="stat bg-error/10 rounded-box p-3 border border-error/20">
                            <div class="stat-figure text-error">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-8 w-8" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 14l2-2m0 0l2-2m-2 2l-2-2m2 2l2 2m7-2a9 9 0 11-18 0 9 9 0 0118 0z" />
                                </svg>
                            </div>
                            <div class="stat-title text-xs md:text-sm">Abgelehnt</div>
                            <div class="stat-value text-error text-2xl md:text-3xl">{{ $applications->where('status', 'rejected')->count() }}</div>
                            <div class="stat-desc text-xs">Nicht angenommen</div>
                        </div>
                    </div>

                    <div class="mt-4 text-sm text-base-content/70 flex items-center gap-2">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-info" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                        </svg>
                        <span>{{ $applications->where('editable', true)->count() }} Bewerbung(en) können aktuell bearbeitet werden.</span>
                    </div>
                </div>
            </div>
            @endif

            @if(session('success'))
                <div class="alert alert-success mb-6">
                    <div>
                        <svg xmlns="http://www.w3.org/2000/svg" class="stroke-current flex-shrink-0 h-6 w-6" fill="none" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" /></svg>
                        <span>{{ session('success') }}</span>
                    </div>
                </div>
            @endif

            @if(session('error'))
                <div class="alert alert-error mb-6">
                    <div>
                        <svg xmlns="http://www.w3.org/2000/svg" class="stroke-current flex-shrink-0 h-6 w-6" fill="none" viewBox="0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 14l2-2m0 0l2-2m-2 2l-2-2m2 2l2 2m7-2a9 9 0 11-18 0 9 9 0 0118 0z" /></svg>
                        <span>{{ session('error') }}</span>
                    </div>
                </div>
            @endif

            @if(count($applications) > 0)
                <div class="card bg-base-100 shadow-xl mb-6 overflow-hidden">
                    <div class="card-body">
                        <h3 class="card-title text-lg">Hinweise zur Bearbeitung</h3>
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mt-2">
                            <div class="bg-info/10 p-4 rounded-box border border-info/30">
                                <div class="flex items-start gap-3">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-info flex-shrink-0 mt-0.5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                                    </svg>
                                    <div>
                                        <h4 class="font-semibold mb-1">Bearbeitung von Bewerbungen</h4>
                                        <p class="text-sm">Bewerbungen können nur bearbeitet werden, wenn ein Administrator die Bearbeitung freigibt. Du kannst alle Angaben bearbeiten, außer die Tätigkeitsbereiche.</p>
                                    </div>
                                </div>
                            </div>
                            <div class="bg-warning/10 p-4 rounded-box border border-warning/30">
                                <div class="flex items-start gap-3">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-warning flex-shrink-0 mt-0.5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
                                    </svg>
                                    <div>
                                        <h4 class="font-semibold mb-1">Neue Tätigkeitsbereiche</h4>
                                        <p class="text-sm">Tätigkeitsbereiche können nicht nachträglich geändert werden. Für weitere Bereiche <a href="{{ route('bewerben') }}" class="link link-primary">reiche bitte eine neue Bewerbung ein</a>.</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="card bg-base-100 shadow-xl overflow-hidden">
                    <div class="card-body p-0">
                        <div class="overflow-x-auto">
                            <table class="table w-full table-zebra">
                                <thead class="bg-base-200 text-base-content">
                                    <tr>
                                        <th class="hidden md:table-cell">ID</th>
                                        <th>Datum</th>
                                        <th class="hidden lg:table-cell">Bereiche</th>
                                        <th>Status</th>
                                        <th class="hidden md:table-cell">Bearbeitbar</th>
                                        <th class="hidden lg:table-cell">Letzte Aktualisierung</th>
                                        <th>Aktionen</th>
                                    </tr>
                                </thead>
                        <tbody>
                            @foreach($applications as $application)
                                <tr class="hover:bg-base-300 transition-colors duration-200">
                                    <td class="hidden md:table-cell">{{ $application->id }}</td>
                                    <td>
                                        <div class="flex flex-col">
                                            <span>{{ $application->created_at->format('d.m.Y') }}</span>
                                            <span class="text-xs opacity-70 md:hidden">ID: {{ $application->id }}</span>
                                        </div>
                                    </td>
                                    <td class="hidden lg:table-cell">
                                        <div class="flex flex-wrap gap-1">
                                            @foreach($application->professions as $profession)
                                                @switch($profession)
                                                    @case('actor')
                                                        <span class="badge badge-sm badge-primary">Schauspieler</span>
                                                        @break
                                                    @case('voice_actor')
                                                        <span class="badge badge-sm badge-primary">Synchronsprecher</span>
                                                        @break
                                                    @case('builder')
                                                        <span class="badge badge-sm badge-secondary">Builder</span>
                                                        @break
                                                    @case('designer')
                                                        <span class="badge badge-sm badge-accent">Designer</span>
                                                        @break
                                                    @case('developer')
                                                        <span class="badge badge-sm badge-info">Entwickler</span>
                                                        @break
                                                    @case('cutter')
                                                        <span class="badge badge-sm badge-info">Cutter</span>
                                                        @break
                                                    @case('cameraman')
                                                        <span class="badge badge-sm badge-info">Kameramann</span>
                                                        @break
                                                    @case('modeler')
                                                        <span class="badge badge-sm badge-accent">3D-Modellierer</span>
                                                        @break
                                                    @case('music_producer')
                                                        <span class="badge badge-sm badge-primary">Musikproduzent</span>
                                                        @break
                                                    @default
                                                        <span class="badge badge-sm">{{ $profession }}</span>
                                                @endswitch
                                            @endforeach
                                        </div>
                                    </td>
                                    <td>
                                        @switch($application->status)
                                            @case('pending')
                                                <span class="badge badge-warning">In Bearbeitung</span>
                                                @break
                                            @case('approved')
                                                <span class="badge badge-success">Angenommen</span>
                                                @break
                                            @case('rejected')
                                                <span class="badge badge-error">Abgelehnt</span>
                                                @break
                                            @default
                                                <span class="badge">{{ $application->status }}</span>
                                        @endswitch
                                    </td>
                                    <td class="hidden md:table-cell">
                                        @if($application->editable)
                                            <span class="badge badge-success gap-1">
                                                <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 11V7a4 4 0 118 0m-4 8v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2z" />
                                                </svg>
                                                Ja
                                            </span>
                                        @else
                                            <span class="badge badge-neutral gap-1">
                                                <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
                                                </svg>
                                                Nein
                                            </span>
                                        @endif
                                    </td>
                                    <td class="hidden lg:table-cell">{{ $application->updated_at->format('d.m.Y H:i') }}</td>
                                    <td>
                                        <div class="flex flex-col sm:flex-row gap-2">
                                            <a href="{{ route('my.applications.show', $application->id) }}" class="btn btn-sm btn-info">
                                                <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 sm:mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                                                </svg>
                                                <span class="hidden sm:inline">Details</span>
                                            </a>
                                            @if($application->editable)
                                                <a href="{{ route('my.applications.edit', $application->id) }}" class="btn btn-sm btn-primary">
                                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 sm:mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                                                    </svg>
                                                    <span class="hidden sm:inline">Bearbeiten</span>
                                                </a>
                                            @else
                                                <div class="tooltip" data-tip="Ein Administrator muss die Bearbeitung freischalten">
                                                    <button class="btn btn-sm btn-disabled">
                                                        <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 sm:mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
                                                        </svg>
                                                        <span class="hidden sm:inline">Gesperrt</span>
                                                    </button>
                                                </div>
                                            @endif

                                            <!-- Mobile: Zeige Bearbeitbarkeit und Update-Zeit -->
                                            <div class="flex flex-col md:hidden text-xs mt-1">
                                                <span class="opacity-70">Bearbeitbar:
                                                    @if($application->editable)
                                                        <span class="text-success">Ja</span>
                                                    @else
                                                        <span class="text-error">Nein</span>
                                                    @endif
                                                </span>
                                                <span class="opacity-70">Update: {{ $application->updated_at->format('d.m.Y') }}</span>
                                            </div>
                                        </div>
                                    </td>
                                </tr>
                            @endforeach
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            @else
                <div class="bg-base-200 p-8 rounded-box text-center">
                    <div class="flex flex-col items-center justify-center space-y-4">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-16 w-16 text-base-content/30" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                        </svg>
                        <h3 class="text-lg font-semibold">Du hast noch keine Bewerbungen eingereicht</h3>
                        <p class="text-base-content/70 max-w-md">Werde Teil des Minewache-Teams! Reiche deine Bewerbung ein und zeige uns deine Fähigkeiten.</p>
                        <div class="alert alert-info shadow-lg max-w-md mt-2">
                            <div>
                                <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" class="stroke-current flex-shrink-0 w-6 h-6"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path></svg>
                                <span>Hinweis: Bewerbungen können nur bearbeitet werden, wenn ein Administrator die Bearbeitung freigibt.</span>
                            </div>
                        </div>
                        <a href="{{ route('bewerben') }}" class="btn btn-primary mt-2 gap-2">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                            </svg>
                            Jetzt bewerben
                        </a>
                    </div>
                </div>
            @endif
        </div>
    </div>
</x-app-layout>