#!/bin/bash

# Production monitoring script using tmux
# This script creates a tmux session to monitor all services
# It does NOT start or manage the services - they should be running via systemd

# Check if tmux is installed
if ! command -v tmux &> /dev/null; then
    echo "tmux is not installed. Please install it first:"
    echo "sudo apt-get install tmux"
    exit 1
fi

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[0;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to display colored messages
function echo_color() {
  local color=$1
  local message=$2
  echo -e "${color}${message}${NC}"
}

# Session name
SESSION_NAME="minewache-monitor"

# Kill existing session if it exists
tmux kill-session -t "$SESSION_NAME" 2>/dev/null

echo_color $BLUE "Starting Minewache monitoring session..."

# Create a new session with service status
tmux new-session -d -s "$SESSION_NAME" -n "Services" "watch -n 2 'systemctl status minewache-queue.service minewache-reverb.service minewache-discord.service | cat'"

# Create window for Queue logs
tmux new-window -t "$SESSION_NAME:1" -n "Queue Logs" "tail -f /var/log/minewache-queue.log"

# Create window for Reverb logs
tmux new-window -t "$SESSION_NAME:2" -n "Reverb Logs" "tail -f /var/log/minewache-reverb.log"

# Create window for Discord Bot logs
tmux new-window -t "$SESSION_NAME:3" -n "Discord Logs" "tail -f /var/log/minewache-discord.log"

# Create window for Laravel logs
tmux new-window -t "$SESSION_NAME:4" -n "Laravel Logs" "tail -f /var/www/minewache-website/storage/logs/laravel.log"

# Create a window for commands
tmux new-window -t "$SESSION_NAME:5" -n "Shell" "bash"

# Select the first window
tmux select-window -t "$SESSION_NAME:0"

# Set up a help message
tmux set-option -g status-right "#[fg=yellow]Ctrl+B then 0-5 to switch windows | Ctrl+B then d to detach"

echo_color $GREEN "Monitoring session started!"
echo_color $YELLOW "Navigation:"
echo "  - Ctrl+B then 0-5: Switch between windows"
echo "  - Ctrl+B then d: Detach from session (keeps it running)"
echo "  - tmux attach -t $SESSION_NAME: Reattach to session"
echo "  - tmux kill-session -t $SESSION_NAME: Kill the session"

# Attach to the session
tmux attach-session -t "$SESSION_NAME"

echo_color $GREEN "Monitoring session ended."
