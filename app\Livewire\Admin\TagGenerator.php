<?php

namespace App\Livewire\Admin;

use Livewire\Component;
use Livewire\WithFileUploads;
use App\Services\GeminiTagService;
use Illuminate\Support\Facades\Log;
use Exception;

class TagGenerator extends Component
{
    use WithFileUploads;

    public $videoFile;
    public $analysisResult;
    public $isLoading = false;
    public $error;
    public $progressMessage = '';
    public $hashtagPreference = 'both'; // 'both', 'tiktok', 'instagram'

    // Supported video types from the original TSX constants
    public const SUPPORTED_VIDEO_TYPES = ['video/mp4', 'video/quicktime', 'video/webm', 'video/x-matroska', 'video/x-msvideo']; // .mov, .mkv, .avi
    public const MAX_FILE_SIZE_MB = 50; // Max file size in MB, adjust as needed

    public function rules()
    {
        return [
            'videoFile' => [
                'required',
                'file',
                'mimes:mp4,mov,webm,mkv,avi', // Derived from SUPPORTED_VIDEO_TYPES
                'max:' . (self::MAX_FILE_SIZE_MB * 1024), // Max size in kilobytes
            ],
            'hashtagPreference' => 'required|in:both,tiktok,instagram',
        ];
    }

    public function updatedVideoFile()
    {
        $this->validateOnly('videoFile');
        $this->resetState(true); // Keep video file, reset others
    }

    protected function resetState($keepFile = false)
    {
        if (!$keepFile) {
            $this->videoFile = null;
            // Resetting the file input in Livewire requires JS or a specific component key change
            // For now, we just nullify the property. The user can select a new file.
            $this->dispatch('fileInputReset');
        }
        $this->analysisResult = null;
        $this->isLoading = false;
        $this->error = null;
        $this->progressMessage = '';
    }

    public function mount()
    {
        // Ensure the user is an admin
        if (!auth()->check() || !auth()->user()->hasRole(\App\Enums\Role::MINEWACHE_TEAM)) {
            abort(403, "You don't have permission to access this page.");
        }
    }

    public function processVideo()
    {
        $this->validate();
        $this->isLoading = true;
        $this->error = null;
        $this->analysisResult = null;
        $this->progressMessage = 'Reading and preparing video...';

        try {
            $mimeType = $this->videoFile->getMimeType();
            if (!in_array($mimeType, self::SUPPORTED_VIDEO_TYPES)) {
                 throw new Exception("Unsupported file type: {$mimeType}. Please upload MP4, MOV, WEBM, MKV or AVI.");
            }

            $base64Data = base64_encode($this->videoFile->get());

            $this->progressMessage = 'Analyzing video with AI (this may take a moment)...';

            // Introduce a small delay for UX if needed, or use wire:loading.long
            // sleep(1); // Simulate initial processing before hitting AI

            $geminiService = app(GeminiTagService::class);
            $this->analysisResult = $geminiService->generateTagsForVideo($base64Data, $mimeType);
            $this->progressMessage = 'Analysis complete!';

        } catch (Exception $e) {
            Log::error('Tag Generator Error: ' . $e->getMessage(), ['trace' => $e->getTraceAsString()]);
            $this->error = "An error occurred: " . $e->getMessage();
            // Attempt to extract a more user-friendly message if it's a Gemini API error
            if (str_contains($e->getMessage(), 'Gemini API request failed')) {
                // Example: Extracting a JSON error message from the body if available
                // This part would need to be more robust based on actual Gemini error structures
                $body = substr($e->getMessage(), strpos($e->getMessage(), '{'));
                $decodedError = json_decode($body, true);
                if (isset($decodedError['error']['message'])) {
                    $this->error = "Gemini API Error: " . $decodedError['error']['message'];
                }
            }
        } finally {
            $this->isLoading = false;
            // Consider removing the uploaded file from Livewire's temp storage if not needed
            // $this->videoFile->delete(); // If you want to clear it after processing
        }
    }

    public function analyzeAnother()
    {
        $this->resetState();
    }

    public function render()
    {
        return view('livewire.admin.tag-generator')->layout('layouts.app');
    }
}
