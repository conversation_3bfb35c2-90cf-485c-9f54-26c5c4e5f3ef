
import React from 'react';
import type { LoadingPhase } from '../types';
import { FilmIcon, CogIcon, DocumentMagnifyingGlassIcon } from './common/Icons';

interface ProgressBarDisplayProps {
  fileName: string;
  progress: number;
  phase: LoadingPhase;
}

export const ProgressBarDisplay: React.FC<ProgressBarDisplayProps> = ({ fileName, progress, phase }) => {
  let phaseText = 'Verarbeite...';
  let PhaseIcon = CogIcon;

  switch (phase) {
    case 'reading':
      phaseText = 'Lese Datei & bereite vor...';
      PhaseIcon = DocumentMagnifyingGlassIcon;
      break;
    case 'analyzing':
      phaseText = 'Analysiere mit KI...';
      PhaseIcon = CogIcon;
      break;
    case 'configuringKey':
      phaseText = 'Konfiguriere...';
      PhaseIcon = CogIcon; 
      break;
    case 'idle':
        phaseText = 'Warte...';
        PhaseIcon = CogIcon; 
        break;
    default:
      phaseText = 'Bitte warten...';
      PhaseIcon = CogIcon; 
      break;
  }


  return (
    <div className="w-full max-w-xl mx-auto p-6 bg-slate-800/70 backdrop-blur-md rounded-xl shadow-2xl border border-slate-700">
      <div className="flex items-center justify-between mb-3 text-slate-300">
        <div className="flex items-center space-x-2">
          <PhaseIcon className={`w-5 h-5 ${phase === 'analyzing' ? 'animate-spin-slow' : ''} text-orange-400`} />
          <span className="text-sm font-medium">{phaseText}</span>
        </div>
        <span className="text-sm font-medium text-orange-400">{Math.round(progress)}%</span>
      </div>
      <p className="text-xs text-slate-400 truncate mb-3" title={fileName}>Datei: {fileName}</p>
      <div className="w-full bg-slate-700 rounded-full h-3 overflow-hidden relative">
        <div
          className="bg-gradient-to-r from-amber-500 to-orange-600 h-3 rounded-full transition-all duration-300 ease-out"
          style={{ width: `${progress}%` }}
        ></div>
        <div 
          className="absolute top-0 left-0 h-full rounded-full opacity-50 animate-pulse"
          style={{ width: `${progress}%`, background: 'linear-gradient(90deg, rgba(251,146,60,0.4) 0%, rgba(251,146,60,0) 100%)' }} /* amber-500 based gradient */
        ></div>
      </div>
    </div>
  );
};