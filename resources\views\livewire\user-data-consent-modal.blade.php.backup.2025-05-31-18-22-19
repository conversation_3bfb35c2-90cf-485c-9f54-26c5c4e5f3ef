<div>
    @if($show)
        <div class="fixed inset-0 z-50 overflow-y-auto" aria-labelledby="modal-title" role="dialog" aria-modal="true">
            <div class="flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
                <!-- Background overlay -->
                <div class="fixed inset-0 bg-black/70 backdrop-blur-sm transition-opacity" aria-hidden="true"></div>

                <!-- Modal panel -->
                <div class="inline-block align-bottom glass rounded-xl sm:rounded-2xl text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full">
                    <!-- Modal header -->
                    <div class="px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
                        <div class="sm:flex sm:items-start">
                            <div class="mx-auto flex-shrink-0 flex items-center justify-center h-12 w-12 rounded-full bg-blue-100 sm:mx-0 sm:h-10 sm:w-10">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-blue-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                                </svg>
                            </div>
                            <div class="mt-3 text-center sm:mt-0 sm:ml-4 sm:text-left">
                                <h3 class="text-lg leading-6 font-medium text-white" id="modal-title">
                                    {{ __('tickets.data_consent_title') }}
                                </h3>
                                <div class="mt-2">
                                    <p class="text-sm text-gray-300">
                                        {{ __('tickets.data_consent_description', ['dataType' => $requestedDataName]) }}
                                    </p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Modal explanation -->
                    <div class="px-4 sm:px-6 pb-4">
                        <div class="bg-slate-700/50 rounded-lg p-4 mb-4 text-sm text-gray-300">
                            <p class="mb-2">{{ __('tickets.data_consent_explanation') }}</p>
                            <p class="mt-4 text-xs text-gray-400">
                                {{ __('tickets.data_consent_privacy_note') }}
                            </p>
                        </div>
                    </div>

                    <!-- Modal actions -->
                    <div class="px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
                        <button wire:click="grantConsent" class="w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-blue-600 text-base font-medium text-white hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 sm:ml-3 sm:w-auto sm:text-sm">
                            {{ __('tickets.data_consent_grant') }}
                        </button>
                        <button wire:click="denyConsent" class="mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-slate-700 text-base font-medium text-gray-300 hover:bg-slate-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-slate-500 sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm">
                            {{ __('tickets.data_consent_deny') }}
                        </button>
                    </div>
                </div>
            </div>
        </div>
    @endif
</div>
