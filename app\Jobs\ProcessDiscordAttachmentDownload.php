<?php

namespace App\Jobs;

use App\Models\TicketAttachment;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str; // For Str::random

class ProcessDiscordAttachmentDownload implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public TicketAttachment $ticketAttachment;
    public int $tries = 3; // Max tries for the job

    /**
     * Create a new job instance.
     */
    public function __construct(TicketAttachment $ticketAttachment)
    {
        $this->ticketAttachment = $ticketAttachment;
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        if ($this->ticketAttachment->local_path && Storage::disk('local')->exists($this->ticketAttachment->local_path)) {
            Log::info("ProcessDiscordAttachmentDownload: Attachment ID {$this->ticketAttachment->id} already downloaded to {$this->ticketAttachment->local_path}. Skipping.");
            return;
        }

        Log::info("ProcessDiscordAttachmentDownload: Starting download for attachment ID {$this->ticketAttachment->id} from {$this->ticketAttachment->url}");

        try {
            $response = Http::timeout(60)->get($this->ticketAttachment->url); // 60 second timeout

            if ($response->successful()) {
                $contents = $response->body();
                // Generate a unique path, e.g., ticket_attachments/{ticket_id}/{random_filename}
                // Ensure the filename from Discord is sanitized.
                $safeFilename = Str::slug(pathinfo($this->ticketAttachment->filename, PATHINFO_FILENAME)) . '.' . pathinfo($this->ticketAttachment->filename, PATHINFO_EXTENSION);
                // Access ticket_id via the ticketMessage relationship
                $directory = "ticket_attachments/{$this->ticketAttachment->ticketMessage->ticket_id}";
                $localPath = "{$directory}/{$this->ticketAttachment->discord_attachment_id}_{$safeFilename}";

                Storage::disk('local')->put($localPath, $contents);

                $this->ticketAttachment->local_path = $localPath;
                $this->ticketAttachment->save();

                Log::info("ProcessDiscordAttachmentDownload: Successfully downloaded attachment ID {$this->ticketAttachment->id} to {$localPath}.");
            } else {
                Log::error("ProcessDiscordAttachmentDownload: Failed to download attachment ID {$this->ticketAttachment->id}. Status: {$response->status()}", [
                    'url' => $this->ticketAttachment->url
                ]);
                // Optionally, throw an exception to retry the job based on status code
                if ($response->status() >= 500) { // Server error on Discord's side
                    $this->release(60); // Retry after 60 seconds
                } else if ($response->status() === 404) { // Not found, likely expired
                    // Don't retry, mark as failed or log permanently
                }
            }
        } catch (\Exception $e) {
            Log::error("ProcessDiscordAttachmentDownload: Exception during download for attachment ID {$this->ticketAttachment->id}.", [
                'exception' => $e->getMessage(),
                'url' => $this->ticketAttachment->url
            ]);
            $this->release(60); // Retry after 60 seconds on general exception
        }
    }
}
