@props(['position' => 'bottom-right'])

@php
    $positionClasses = [
        'top-right' => 'top-4 right-4',
        'top-left' => 'top-4 left-4',
        'bottom-right' => 'bottom-4 right-4',
        'bottom-left' => 'bottom-4 left-4',
    ][$position] ?? 'bottom-4 right-4';
@endphp

<div
    x-data="oldThemeManager()"
    {{ $attributes->merge(['class' => "fixed {$positionClasses} z-50"]) }}
    role="region"
    aria-label="Theme switcher"
>
    <button
        @click="toggleTheme()"
        class="btn btn-circle btn-md bg-primary text-primary-content hover:bg-primary-focus border-primary shadow-lg"
        aria-label="Toggle theme"
        :aria-pressed="currentTheme === 'minewache-light'"
    >
        <span class="sr-only" x-text="currentTheme === 'minewache-light' ? 'Switch to dark mode' : 'Switch to light mode'"></span>
        <!-- Sun icon for dark mode (shown when in dark mode) -->
        <svg
            x-show="currentTheme === 'minewache'"
            xmlns="http://www.w3.org/2000/svg"
            class="h-5 w-5"
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
        >
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 3v1m0 16v1m9-9h-1M4 12H3m15.364 6.364l-.707-.707M6.343 6.343l-.707-.707m12.728 0l-.707.707M6.343 17.657l-.707.707M16 12a4 4 0 11-8 0 4 4 0 018 0z" />
        </svg>
        <!-- Moon icon for light mode (shown when in light mode) -->
        <svg
            x-show="currentTheme === 'minewache-light'"
            xmlns="http://www.w3.org/2000/svg"
            class="h-5 w-5"
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
        >
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20.354 15.354A9 9 0 018.646 3.646 9.003 9.003 0 0012 21a9.003 9.003 0 008.354-5.646z" />
        </svg>
    </button>
</div>

<!-- Add CSS variables for theme colors -->
<style>
    :root {
        --color-primary-rgb: 38, 95, 194; /* Default RGB value for primary color */
    }

    [data-theme="minewache"] {
        --color-primary-rgb: 38, 95, 194; /* Dark theme primary RGB */
    }

    [data-theme="minewache-light"] {
        --color-primary-rgb: 38, 95, 194; /* Light theme primary RGB */
    }
</style>

@push('scripts')
<script>
    document.addEventListener('alpine:init', () => {
        Alpine.data('oldThemeManager', () => ({
            currentTheme: 'minewache', // Default theme

            init() {
                // Check if user has a saved theme preference
                const savedTheme = localStorage.getItem('theme');
                if (savedTheme) {
                    this.currentTheme = savedTheme;
                    this.applyTheme(savedTheme);
                } else {
                    // Check if user prefers dark mode at system level
                    const prefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches;
                    this.currentTheme = prefersDark ? 'minewache' : 'minewache-light';
                    this.applyTheme(this.currentTheme);
                }

                // Listen for system preference changes
                window.matchMedia('(prefers-color-scheme: dark)').addEventListener('change', e => {
                    if (!localStorage.getItem('theme')) { // Only auto-switch if user hasn't manually set a preference
                        const newTheme = e.matches ? 'minewache' : 'minewache-light';
                        this.currentTheme = newTheme;
                        this.applyTheme(newTheme);
                    }
                });

                // Debug output
                console.log('Theme Manager initialized with theme:', this.currentTheme);
                console.log('Current data-theme attribute:', document.documentElement.getAttribute('data-theme'));
            },

            toggleTheme() {
                const newTheme = this.currentTheme === 'minewache' ? 'minewache-light' : 'minewache';
                this.currentTheme = newTheme;
                this.applyTheme(newTheme);
                localStorage.setItem('theme', newTheme);

                // Debug output
                console.log('Theme toggled to:', newTheme);
                console.log('New data-theme attribute:', document.documentElement.getAttribute('data-theme'));

                // Announce theme change to screen readers
                this.announceThemeChange(newTheme);
            },

            applyTheme(theme) {
                // Set the theme attribute on the html element
                document.documentElement.setAttribute('data-theme', theme);
                console.log('Theme switcher: Applied theme to HTML element:', theme);

                // Add appropriate class to body for additional styling if needed
                if (theme === 'minewache-light') {
                    document.body.classList.add('light-mode');
                    document.body.classList.remove('dark-mode');
                    console.log('Theme switcher: Added light-mode class to body');
                } else {
                    document.body.classList.add('dark-mode');
                    document.body.classList.remove('light-mode');
                    console.log('Theme switcher: Added dark-mode class to body');
                }

                // Force CSS refresh by toggling a class
                document.documentElement.classList.add('theme-refresh');
                setTimeout(() => {
                    document.documentElement.classList.remove('theme-refresh');
                    console.log('Theme switcher: Forced CSS refresh');
                }, 10);

                // Dispatch a custom event for other components to react to theme changes
                window.dispatchEvent(new CustomEvent('theme-changed', {
                    detail: { theme: theme, isDarkMode: theme === 'minewache' }
                }));
                console.log('Theme switcher: Dispatched theme-changed event');
            },

            announceThemeChange(theme) {
                const announcement = document.createElement('div');
                announcement.setAttribute('aria-live', 'polite');
                announcement.classList.add('sr-only');
                announcement.textContent = theme === 'minewache-light' ? 'Light mode activated' : 'Dark mode activated';
                document.body.appendChild(announcement);

                // Remove after announcement is read
                setTimeout(() => {
                    document.body.removeChild(announcement);
                }, 3000);
            }
        }));
    });
</script>
@endpush
