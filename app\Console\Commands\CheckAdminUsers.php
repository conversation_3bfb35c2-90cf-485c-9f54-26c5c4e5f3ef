<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\User;
use App\Enums\Role;

class CheckAdminUsers extends Command
{
    protected $signature = 'check:admin-users';
    protected $description = 'Check admin users and their permissions';

    public function handle()
    {
        $this->info('Checking admin users...');
        
        $adminValue = Role::MINEWACHE_TEAM->value;
        $this->info("MINEWACHE_TEAM value: {$adminValue}");
        
        $users = User::all();
        
        foreach ($users as $user) {
            $hasRole = $user->hasRole(Role::MINEWACHE_TEAM);
            $this->line("User: {$user->name} - Permissions: {$user->permissions} - hasRole(MINEWACHE_TEAM): " . ($hasRole ? 'true' : 'false'));
        }
        
        // Check users with admin permissions
        $adminUsers = User::where('permissions', (string)$adminValue)->get();
        $this->info("\nUsers with admin permissions ({$adminValue}):");
        foreach ($adminUsers as $user) {
            $this->line("- {$user->name} ({$user->email})");
        }
        
        return 0;
    }
}
