#!/bin/bash

# Minewache Entwicklungsumgebung mit tmux
# Dieses Skript startet alle benötigten Dienste in einer tmux-Sitzung
# und stellt sicher, dass alle benötigten Ports frei sind.

# Farben für die Ausgabe
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[0;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Funktion für farbige Ausgabe
echo_color() {
  echo -e "${!1}${2}${NC}"
}

# Prüfen, ob tmux installiert ist
if ! command -v tmux &> /dev/null; then
    echo_color "RED" "tmux ist nicht installiert. Bitte installieren Sie es zuerst:"
    echo "sudo apt-get install tmux"
    exit 1
fi

# Projektverzeichnis
PROJECT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")/.." && pwd)"
cd "$PROJECT_DIR"

# Discord Bot Verzeichnis
BOT_DIR="$PROJECT_DIR/discord-bot"

# Sitzungsname
SESSION_NAME="minewache-dev"

# Ports, die verwendet werden
LARAVEL_PORT=8000
VITE_PORT=5173
REVERB_PORT=6001
DISCORD_BOT_PORT=3001

# Funktion zum Prüfen, ob ein Port belegt ist
check_port() {
    local port=$1
    local process_info=$(lsof -i:$port -sTCP:LISTEN -t 2>/dev/null)
    if [ -n "$process_info" ]; then
        echo "$process_info"
        return 0
    else
        return 1
    fi
}

# Funktion zum Beenden eines Prozesses
kill_process() {
    local pid=$1
    local port=$2
    local force=$3

    if [ "$force" = "force" ]; then
        echo_color "YELLOW" "Beende Prozess $pid auf Port $port mit SIGKILL..."
        kill -9 $pid
    else
        echo_color "YELLOW" "Beende Prozess $pid auf Port $port..."
        kill $pid
    fi
}

# Bestehende tmux-Sitzung beenden, falls vorhanden
if tmux has-session -t "$SESSION_NAME" 2>/dev/null; then
    echo_color "YELLOW" "Bestehende tmux-Sitzung '$SESSION_NAME' gefunden."
    read -p "Möchten Sie diese beenden? (j/n): " kill_session
    if [[ "$kill_session" == "j" || "$kill_session" == "J" ]]; then
        tmux kill-session -t "$SESSION_NAME"
        echo_color "GREEN" "Bestehende tmux-Sitzung beendet."
    else
        echo_color "RED" "Abbruch. Bitte beenden Sie zuerst die bestehende Sitzung."
        exit 1
    fi
fi

# Prüfen und Freigeben der benötigten Ports
echo_color "BLUE" "Prüfe, ob benötigte Ports frei sind..."

# Array mit Ports und ihren Beschreibungen
declare -A ports
ports[$LARAVEL_PORT]="Laravel Server"
ports[$VITE_PORT]="Vite"
ports[$REVERB_PORT]="Reverb WebSocket"
ports[$DISCORD_BOT_PORT]="Discord Bot API"

# Prüfen, ob Ports belegt sind
ports_in_use=false
for port in "${!ports[@]}"; do
    if pid=$(check_port $port); then
        echo_color "YELLOW" "Port $port (${ports[$port]}) wird von Prozess $pid verwendet."
        ports_in_use=true
    else
        echo_color "GREEN" "Port $port (${ports[$port]}) ist frei."
    fi
done

# Wenn Ports belegt sind, fragen, ob sie freigegeben werden sollen
if [ "$ports_in_use" = true ]; then
    echo_color "YELLOW" "Einige Ports werden bereits verwendet."
    read -p "Möchten Sie alle belegten Ports freigeben? (j/n): " free_ports

    if [[ "$free_ports" == "j" || "$free_ports" == "J" ]]; then
        for port in "${!ports[@]}"; do
            if pid=$(check_port $port); then
                kill_process $pid $port
                sleep 1

                # Prüfen, ob der Prozess noch läuft
                if check_port $port > /dev/null; then
                    echo_color "YELLOW" "Prozess läuft noch. Versuche mit SIGKILL..."
                    kill_process $pid $port "force"
                    sleep 1
                fi

                if ! check_port $port > /dev/null; then
                    echo_color "GREEN" "Port $port wurde erfolgreich freigegeben."
                else
                    echo_color "RED" "Port $port konnte nicht freigegeben werden. Bitte manuell prüfen."
                    exit 1
                fi
            fi
        done
    else
        echo_color "RED" "Abbruch. Bitte geben Sie die benötigten Ports manuell frei."
        exit 1
    fi
fi

# Sicherstellen, dass der Discord Bot gestoppt ist
echo_color "BLUE" "Stoppe Discord Bot, falls er läuft..."
php artisan discord:bot stop

# Discord Bot Umgebungsvariablen synchronisieren
echo_color "BLUE" "Synchronisiere Discord Bot Umgebungsvariablen..."
cd "$BOT_DIR" && node sync-env.js && node sync-port.js
cd "$PROJECT_DIR"

# Neue tmux-Sitzung erstellen
echo_color "BLUE" "Erstelle neue tmux-Sitzung..."
tmux new-session -d -s "$SESSION_NAME" -n "Laravel" "php artisan serve"

# Fenster für Queue Worker
tmux new-window -t "$SESSION_NAME:1" -n "Queue" "php artisan queue:work-reverb --queue=default --timeout=0"

# Fenster für Vite
tmux new-window -t "$SESSION_NAME:2" -n "Vite" "npx vite"

# Fenster für Reverb
tmux new-window -t "$SESSION_NAME:3" -n "Reverb" "php artisan reverb:start --host=127.0.0.1 --port=6001 --debug"

# Fenster für Discord Bot (direkter Start)
tmux new-window -t "$SESSION_NAME:4" -n "Discord" "cd $BOT_DIR && node index-tickets.js"

# Fenster für Befehle
tmux new-window -t "$SESSION_NAME:6" -n "Shell" "bash"

# Erstes Fenster auswählen
tmux select-window -t "$SESSION_NAME:0"

# Anweisungen anzeigen
echo_color "GREEN" "tmux-Sitzung '$SESSION_NAME' wurde mit folgenden Fenstern erstellt:"
echo "  0: Laravel Server"
echo "  1: Queue Worker"
echo "  2: Vite"
echo "  3: Reverb"
echo "  4: Discord Bot (direkter Start)"
echo "  6: Shell für Befehle"
echo ""
echo "Zur Sitzung verbinden: tmux attach -t $SESSION_NAME"
echo "Sitzung beenden: tmux kill-session -t $SESSION_NAME"
echo ""
echo_color "YELLOW" "Nach dem Beenden bitte folgende Befehle ausführen, um alle Prozesse zu stoppen:"
echo "pkill -f \"node.*index-tickets.js\""
echo "pkill -f \"php artisan serve\""
echo "pkill -f \"php artisan queue:work-reverb\""
echo "pkill -f \"php artisan reverb:start\""
echo "pkill -f \"vite\""
echo ""

# Fragen, ob der Benutzer zur Sitzung verbinden möchte
read -p "Möchten Sie sich jetzt zur tmux-Sitzung verbinden? (j/n): " answer
if [[ "$answer" == "j" || "$answer" == "J" ]]; then
    tmux attach-session -t "$SESSION_NAME"

    # Nach dem Trennen fragen, ob alle Prozesse beendet werden sollen
    read -p "Möchten Sie alle Prozesse beenden? (j/n): " cleanup
    if [[ "$cleanup" == "j" || "$cleanup" == "J" ]]; then
        echo_color "BLUE" "Beende alle Prozesse..."
        pkill -f "node.*index-tickets.js"
        pkill -f "php artisan serve"
        pkill -f "php artisan queue:work-reverb"
        pkill -f "php artisan reverb:start"
        pkill -f "vite"
        echo_color "GREEN" "Alle Prozesse wurden beendet."
    else
        echo_color "YELLOW" "Prozesse laufen weiterhin. Um sie später zu beenden, führen Sie aus:"
        echo "tmux kill-session -t $SESSION_NAME"
        echo "pkill -f \"node.*index-tickets.js\""
        echo "pkill -f \"php artisan serve\""
        echo "pkill -f \"php artisan queue:work-reverb\""
        echo "pkill -f \"php artisan reverb:start\""
        echo "pkill -f \"vite\""
    fi
fi
