# Discord Authentication Flow

This document explains the complete authentication flow when a user logs in with Discord on the MineWache website.

## Overview

The authentication process involves several steps and components:

1. User initiates login
2. GDPR consent check
3. Discord OAuth authorization
4. Callback handling
5. Guild membership verification
6. Redirection based on membership status

## Detailed Flow

### 1. User Initiates Login

The process begins when a user clicks the "Login with Discord" button on the website. This triggers a request to the `/auth/discord` route, which is handled by the `CustomLoginController`.

### 2. GDPR Consent Check

Before proceeding with Discord authentication, the system checks if the user has provided GDPR consent:

- If the user has already given consent (cookie exists), they proceed directly to Discord OAuth
- If not, they are redirected to the consent page first

### 3. Discord OAuth Authorization

Once GDPR consent is confirmed, the user is redirected to Discord's OAuth authorization page:
```
https://discord.com/api/oauth2/authorize
    ?client_id={CLIENT_ID}
    &redirect_uri={CALLBACK_URL}
    &response_type=code
    &scope={SCOPES}
    &prompt={PROMPT}
```

The user must authorize the application to access their Discord account information.

### 4. Callback Handling

After authorization, Discord redirects the user back to our application's callback URL (`/larascord/callback`), which is handled by our custom `CustomLarascordController`.

The controller:
1. Receives the authorization code from Discord
2. Creates an instance of the Larascord DiscordController
3. Calls its callback method to handle the OAuth token exchange
4. Examines the response for guild membership errors

### 5. Guild Membership Verification

The Larascord package checks if the user is a member of the required Discord guild (server):

- If the user is a member, authentication proceeds normally
- If not, our custom controller intercepts the error response and redirects to the membership required page

### 6. Redirection Based on Membership Status

- **Success**: If the user is authenticated and is a guild member, they are redirected to the dashboard or their intended destination
- **Not a Guild Member**: If the user is not a member of the required guild, they are redirected to `/discord/membership-required`
- **Error**: If any errors occur during the process, the user is redirected to an appropriate error page

## Membership Required Page

The membership required page:
1. Explains that guild membership is required
2. Provides a link to join the Discord server (opens in a new tab)
3. Includes a "Try Again" button to restart the authentication process after joining

## Flow Diagram

```mermaid
flowchart TD
    A[User clicks Login] --> B{GDPR Consent?}
    B -->|Yes| C[Redirect to Discord OAuth]
    B -->|No| D[Show Consent Page]
    D -->|User Consents| C
    
    C --> E[Discord Authorization Page]
    E -->|User Authorizes| F[Callback to /larascord/callback]
    
    F --> G[CustomLarascordController]
    G --> H[Larascord Authentication]
    
    H --> I{Guild Member?}
    I -->|Yes| J[Authentication Success]
    I -->|No| K[Redirect to Membership Required]
    
    K --> L[User Joins Discord Server]
    L --> M[User Clicks Try Again]
    M --> C
    
    J --> N[Redirect to Dashboard]
```

## Error Handling

The system includes several error handling mechanisms:

1. **Missing Code**: If the authorization code is missing, the user is redirected to the login page
2. **Guild Membership Error**: If the user is not a member of the required guild, they are redirected to the membership required page
3. **API Errors**: If there are errors communicating with Discord's API, appropriate error messages are displayed
4. **Fallback Implementation**: If the Larascord controller is unavailable, a fallback implementation handles the authentication process

## Technical Implementation

The key components of this flow are:

1. **CustomLoginController**: Handles the initial login request and GDPR consent check
2. **CustomLarascordController**: Intercepts the callback from Discord and handles guild membership errors
3. **Membership Required Page**: Provides instructions for joining the Discord server and trying again
4. **JavaScript Handler**: Detects and handles JSON responses related to guild membership errors

## Configuration

The flow is configured through several settings:

1. **Larascord Configuration** (`config/larascord.php`):
   - OAuth scopes
   - Required guild IDs
   - Error messages and redirects

2. **Services Configuration** (`config/services.php`):
   - Discord API credentials
   - Guild ID

## Troubleshooting

Common issues and their solutions:

1. **Raw JSON Response**: If you see a raw JSON response instead of being redirected, ensure the CustomLarascordController is properly handling the callback
2. **Redirect Loop**: If you experience a redirect loop, check the GDPR consent middleware and cookie settings
3. **Authentication Failures**: Verify your Discord API credentials and OAuth configuration
