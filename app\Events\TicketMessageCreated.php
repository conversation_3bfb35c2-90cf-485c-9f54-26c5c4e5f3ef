<?php

namespace App\Events;

use App\Models\TicketMessage;
use Illuminate\Broadcasting\Channel;
use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Broadcasting\PresenceChannel;
use Illuminate\Broadcasting\PrivateChannel;
use Illuminate\Contracts\Broadcasting\ShouldBroadcast;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;

class TicketMessageCreated implements ShouldBroadcast
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    /**
     * Determine if this event should broadcast.
     *
     * @return bool
     */
    public function broadcastWhen()
    {
        return app()->environment() !== 'testing';
    }

    public $message;

    /**
     * Create a new event instance.
     */
    public function __construct(TicketMessage $message)
    {
        $this->message = $message;
        // Load relationships to include in the broadcast
        $this->message->load(['user', 'attachments']);

        // Log the event creation
        Log::info('TicketMessageCreated event created', [
            'message_id' => $message->id,
            'ticket_id' => $message->ticket_id,
        ]);
    }

    /**
     * Get the channels the event should broadcast on.
     *
     * @return \Illuminate\Broadcasting\Channel|array
     */
    public function broadcastOn()
    {
        return new PrivateChannel('tickets.' . $this->message->ticket_id);
    }

    /**
     * Get the data to broadcast.
     *
     * @return array
     */
    public function broadcastWith()
    {
        return [
            'message' => [
                'id' => $this->message->id,
                'ticket_id' => $this->message->ticket_id,
                'user_id' => $this->message->user_id,
                'message' => $this->message->message,
                'is_from_discord' => $this->message->is_from_discord,
                'discord_message_id' => $this->message->discord_message_id,
                'created_at' => $this->message->created_at->format('Y-m-d H:i:s'),
                'user' => [
                    'id' => $this->message->user->id,
                    'username' => $this->message->user->username,
                ],
                'attachments' => $this->message->attachments->map(function ($attachment) {
                    return [
                        'id' => $attachment->id,
                        'original_filename' => $attachment->original_filename,
                        'is_image' => $attachment->is_image,
                        'is_video' => $attachment->is_video,
                        'is_audio' => $attachment->is_audio,
                        'is_pdf' => $attachment->is_pdf,
                        'processing_status' => $attachment->processing_status,
                    ];
                }),
            ]
        ];
    }

    /**
     * Specify the broadcasting connection to use.
     *
     * @return string
     */
    public function broadcastVia(): string
    {
        Log::info('Broadcasting TicketMessageCreated via reverb', ['message_id' => $this->message->id]);
        return 'reverb';
    }
}
