<x-app-layout>
    <x-slot name="header">
        <h2 class="font-display text-xl md:text-2xl text-gray-900 dark:text-white leading-tight">
            {{ __('Datenschutzanfragen verwalten') }}
        </h2>
    </x-slot>

    <div class="py-12">
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
            <div class="bg-white dark:bg-base-100 overflow-hidden shadow-sm rounded-lg">
                <div class="p-6">
                    <h3 class="text-lg font-medium mb-4">{{ __('Offene Datenschutzanfragen') }}</h3>
                    
                    @if($requests->count() > 0)
                        <div class="overflow-x-auto">
                            <table class="table w-full">
                                <thead>
                                    <tr>
                                        <th>ID</th>
                                        <th>Benutzer</th>
                                        <th>Anfrageart</th>
                                        <th>Status</th>
                                        <th>Erstellt am</th>
                                        <th>Aktionen</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach($requests as $request)
                                        <tr>
                                            <td>{{ $request->id }}</td>
                                            <td>
                                                @if($request->user)
                                                    {{ $request->user->username }}
                                                @else
                                                    <span class="text-error">Gelöschter Benutzer</span>
                                                @endif
                                            </td>
                                            <td>
                                                @if($request->request_type === 'full_deletion')
                                                    <span class="badge badge-error">Vollständige Löschung</span>
                                                @elseif($request->request_type === 'partial_deletion')
                                                    <span class="badge badge-warning">Teilweise Löschung</span>
                                                @elseif($request->request_type === 'data_export')
                                                    <span class="badge badge-info">Datenexport</span>
                                                @endif
                                            </td>
                                            <td>
                                                @if($request->status === 'pending')
                                                    <span class="badge badge-warning">Ausstehend</span>
                                                @elseif($request->status === 'processing')
                                                    <span class="badge badge-info">In Bearbeitung</span>
                                                @elseif($request->status === 'completed')
                                                    <span class="badge badge-success">Abgeschlossen</span>
                                                @elseif($request->status === 'rejected')
                                                    <span class="badge badge-error">Abgelehnt</span>
                                                @endif
                                            </td>
                                            <td>{{ $request->created_at->format('d.m.Y H:i') }}</td>
                                            <td>
                                                <x-modern-button variant="primary" href="{{ route('admin.data-requests.show', $request) }}" >Details</x-modern-button>
                                            </td>
                                        </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>
                        
                        <div class="mt-4">
                            {{ $requests->links() }}
                        </div>
                    @else
                        <div class="text-center py-4">
                            <p>{{ __('Keine offenen Datenschutzanfragen vorhanden.') }}</p>
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </div>
</x-app-layout>
