<x-app-layout>
    <x-slot name="heading">
        {{ __('apply.title') }}
    </x-slot>

    <x-slot name="header">
        <h2 class="font-display text-xl md:text-2xl text-base-content leading-tight animate-slide-down">
            {{ __('apply.your_application') }}
        </h2>
    </x-slot>

    <x-slot name="breadcrumbs">
        <x-breadcrumbs :items="[
            ['label' => __('apply.title')]
        ]" />
    </x-slot>


    <div class="py-8 md:py-12 bg-base-200">
        <div class="max-w-2xl mx-auto px-4 sm:px-6 lg:px-8">
            <!-- Hauptbewerbungskarte (Verbessert) -->
            <div class="bg-base-100 rounded-xl shadow-lg overflow-hidden opacity-0 animate-slide-up border-2 border-primary/20">
                <div class="bg-primary/10 px-6 py-4 border-b border-primary/20">
                    <h3 class="font-geometric text-xl md:text-2xl font-medium text-base-content opacity-0 animate-delayed-fade flex items-center gap-3">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-7 w-7 text-primary" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                        </svg>
                        {{ __('apply.application_process') }}
                    </h3>
                </div>
                <div class="p-6 md:p-8 text-base-content">
                    <div class="flex flex-col sm:flex-row sm:items-start sm:space-x-4 mb-8">
                        <div>
                            <p class="mb-4 text-lg">
                                {{ __('apply.welcome_text') }}
                            </p>
                        </div>
                    </div>

                    <!-- Ablauf-Informationsbox -->
                    <div class="alert mb-6">
                        <x-heroicon-o-information-circle class="h-6 w-6 shrink-0 stroke-current" />
                        <div>
                            <h4 class="font-medium">So läuft deine Bewerbung ab:</h4>
                            <ol class="space-y-2 mt-2 pl-2">
                                <li class="flex items-start gap-2">
                                    <span class="flex-shrink-0 rounded-full bg-primary/20 p-1 w-6 h-6 flex items-center justify-center font-medium">1</span>
                                    <span>Du füllst die Bewerbung aus</span>
                                </li>
                                <li class="flex items-start gap-2">
                                    <span class="flex-shrink-0 rounded-full bg-primary/20 p-1 w-6 h-6 flex items-center justify-center font-medium">2</span>
                                    <span>Wir prüfen deine Bewerbung in der Regel innerhalb von einer Woche</span>
                                </li>
                                <li class="flex items-start gap-2">
                                    <span class="flex-shrink-0 rounded-full bg-primary/20 p-1 w-6 h-6 flex items-center justify-center font-medium">3</span>
                                    <span>Du wirst über Discord benachrichtigt, wenn deine Bewerbung angenommen wurde</span>
                                </li>
                            </ol>
                        </div>
                    </div>

                    <!-- Bewerbungsassistent-Beschreibung -->
                    <div class="bg-base-200/50 rounded-lg p-4 mb-6">
                        <h4 class="font-medium flex items-center gap-2 mb-2">
                            <x-heroicon-o-sparkles class="h-5 w-5 text-primary" />
                            <span>{{ __('apply.application_assistant') }}</span>
                        </h4>
                        <p class="text-sm mb-3">
                            {{ __('apply.assistant_description') }}
                        </p>
                        <ul class="space-y-1 text-sm">
                            <li class="flex items-start gap-2">
                                <x-heroicon-o-check-circle class="h-5 w-5 text-success shrink-0" />
                                <span>{{ __('apply.step_by_step') }}</span>
                            </li>
                            <li class="flex items-start gap-2">
                                <x-heroicon-o-check-circle class="h-5 w-5 text-success shrink-0" />
                                <span>{{ __('apply.auto_save') }}</span>
                            </li>
                            <li class="flex items-start gap-2">
                                <x-heroicon-o-check-circle class="h-5 w-5 text-success shrink-0" />
                                <span>Formularfelder angepasst an deine gewählten Berufe</span>
                            </li>
                            <li class="flex items-start gap-2">
                                <x-heroicon-o-check-circle class="h-5 w-5 text-success shrink-0" />
                                <span>Überprüfung aller Daten vor der endgültigen Einreichung</span>
                            </li>
                        </ul>
                    </div>

                    <form action="{{ route('application_wizard') }}" method="get" class="space-y-6">
                        @csrf

                        <!-- Datenschutz Checkbox -->
                        <div class="form-control">
                            <div x-data="{ checked: false }" class="flex items-start space-x-3">
                                <div class="flex items-center">
                                    <input id="privacy_policy" name="privacy_policy" type="checkbox" required x-model="checked"
                                           class="w-7 h-7 rounded-full border-gray-300 bg-white text-primary focus:ring-primary
                   dark:focus:ring-primary dark:ring-offset-base-300 dark:bg-white dark:border-base-300 border-2
                   @error('privacy_policy') border-error @enderror" />
                                    <div class="ms-3 flex items-start space-x-2">
                                        <x-heroicon-o-shield-check class="h-5 w-5 shrink-0 mt-0.5" />
                                        <label for="privacy_policy" class="text-sm font-medium text-gray-900 dark:text-gray-300 cursor-pointer">
                                            {!! __('apply.privacy_policy', ['privacy_url' => route('datenschutz')]) !!}
                                        </label>
                                    </div>
                                </div>
                            </div>
                            @error('privacy_policy') <span class="text-xs text-error mt-1 ml-8">{{ $message }}</span> @enderror
                        </div>

                        <!-- Submit Button -->
                        <div class="card-actions justify-end mt-8">
                            <a href="{{ route('home') }}" class="btn btn-ghost">{{ __('apply.cancel') }}</a>
                            <button type="submit" class="btn btn-primary btn-lg gap-2">
                                {{ __('apply.start_application') }}
                                <x-heroicon-o-arrow-right class="h-5 w-5" />
                            </button>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Zusätzliche FAQ-Box -->
            <div class="mt-8 bg-white dark:bg-base-100 rounded-xl shadow-md p-6 md:p-8 opacity-0 transform translate-y-4"
                 x-data
                 x-init="setTimeout(() => $el.classList.add('transition-all', 'duration-500', 'ease-out', 'opacity-100', 'translate-y-0'), 800)">
                <h3 class="font-geometric text-lg md:text-xl font-medium flex items-center gap-2 mb-6 text-gray-900 dark:text-white">
                    <x-heroicon-o-question-mark-circle class="w-6 h-6 text-primary animate-pulse" />
                    <span>{{ __('apply.faq') }}</span>
                </h3>
                <div class="space-y-4">
                    @foreach([
                        [
                            'question' => __('apply.faq_process_time'),
                            'answer' => __('apply.faq_process_time_answer')
                        ],
                        [
                            'question' => __('apply.faq_requirements'),
                            'answer' => __('apply.faq_requirements_answer')
                        ],
                        [
                            'question' => __('apply.faq_discord'),
                            'answer' => __('apply.faq_discord_answer')
                        ],
                        [
                            'question' => __('apply.faq_age'),
                            'answer' => __('apply.faq_age_answer')
                        ]
                    ] as $index => $faq)
                        <div class="border border-gray-200 dark:border-base-content/10 rounded-lg overflow-hidden opacity-0 transform translate-y-4"
                             x-data
                             x-init="setTimeout(() => $el.classList.add('transition-all', 'duration-500', 'ease-out', 'opacity-100', 'translate-y-0'), {{ 900 + $index * 200 }})">
                            <details class="group transition-all duration-300 hover:bg-base-200/50">
                                <summary class="flex justify-between items-center p-4 cursor-pointer bg-gray-50 dark:bg-base-300 text-gray-900 dark:text-white">
                                    <span class="font-medium">{{ $faq['question'] }}</span>
                                    <x-heroicon-s-chevron-down class="w-5 h-5 text-gray-600 dark:text-gray-400 transition-transform duration-300 group-open:rotate-180" />
                                </summary>
                                <div class="p-4 bg-white dark:bg-base-100 text-gray-800 dark:text-gray-200">
                                    <p>{{ $faq['answer'] }}</p>
                                </div>
                            </details>
                        </div>
                    @endforeach
                </div>
            </div>
        </div>
    </div>
</x-app-layout>
