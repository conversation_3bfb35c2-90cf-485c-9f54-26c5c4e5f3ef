<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('applications', function (Blueprint $table) {
            // Add parent_id field for application merging
            $table->unsignedBigInteger('parent_id')->nullable()->after('id')
                ->comment('ID of the parent application if this is a revision');

            // Add foreign key constraint
            $table->foreign('parent_id')
                ->references('id')
                ->on('applications')
                ->onDelete('set null');

            // Set default value for editable to false (applications need to be unlocked by admin)
            $table->boolean('editable')->default(false)->change();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('applications', function (Blueprint $table) {
            // Drop foreign key constraint first
            $table->dropForeign(['parent_id']);

            // Then drop the column
            $table->dropColumn('parent_id');

            // Reset editable default value
            $table->boolean('editable')->default(true)->change();
        });
    }
};
