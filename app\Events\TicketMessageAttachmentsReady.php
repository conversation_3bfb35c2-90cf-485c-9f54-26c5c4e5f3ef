<?php

namespace App\Events;

use App\Models\TicketMessage;
use Illuminate\Broadcasting\Channel;
use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Broadcasting\PresenceChannel;
use Illuminate\Broadcasting\PrivateChannel;
use Illuminate\Contracts\Broadcasting\ShouldBroadcast;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;

class TicketMessageAttachmentsReady implements ShouldBroadcast
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    /**
     * The ticket message instance.
     *
     * @var \App\Models\TicketMessage
     */
    protected $message;

    /**
     * The message ID.
     *
     * @var int
     */
    public $messageId;

    /**
     * The ticket ID.
     *
     * @var int
     */
    public $ticketId;

    /**
     * Create a new event instance.
     *
     * @param  \App\Models\TicketMessage  $message
     * @return void
     */
    public function __construct(TicketMessage $message)
    {
        // Store only the IDs to avoid serialization issues
        $this->messageId = $message->id;
        $this->ticketId = $message->ticket_id;

        // Keep the message instance for use in broadcastWith
        $this->message = $message;

        // Load relationships to include in the broadcast
        $this->message->load(['attachments']);

        Log::debug('TicketMessageAttachmentsReady event created', [
            'message_id' => $this->messageId,
            'ticket_id' => $this->ticketId,
            'attachments_count' => $message->attachments->count()
        ]);
    }

    /**
     * Get the channels the event should broadcast on.
     *
     * @return \Illuminate\Broadcasting\Channel|array
     */
    public function broadcastOn()
    {
        return new PrivateChannel('tickets.' . $this->ticketId);
    }

    /**
     * Get the data to broadcast.
     *
     * @return array
     */
    public function broadcastWith()
    {
        try {
            // Ensure the message is loaded with attachments
            if (!$this->message->relationLoaded('attachments')) {
                $this->message->load('attachments');
            }

            $attachments = $this->message->attachments->map(function ($attachment) {
                try {
                    return [
                        'id' => $attachment->id,
                        'original_filename' => $attachment->original_filename,
                        'is_image' => $attachment->is_image,
                        'is_video' => $attachment->is_video,
                        'is_audio' => $attachment->is_audio,
                        'is_pdf' => $attachment->is_pdf,
                        'media_url' => $attachment->media_url,
                        'download_url' => route('tickets.attachments.download', $attachment),
                        'processing_status' => $attachment->processing_status,
                    ];
                } catch (\Exception $e) {
                    Log::error('Error serializing attachment for broadcast', [
                        'attachment_id' => $attachment->id,
                        'error' => $e->getMessage()
                    ]);

                    // Return a minimal attachment object with error information
                    return [
                        'id' => $attachment->id,
                        'original_filename' => $attachment->original_filename ?? 'Unknown file',
                        'processing_status' => 'error',
                        'error' => 'Failed to serialize attachment: ' . $e->getMessage()
                    ];
                }
            })->filter()->values()->toArray();

            Log::debug('Broadcasting attachment data', [
                'message_id' => $this->messageId,
                'attachments_count' => count($attachments)
            ]);

            return [
                'message_id' => $this->messageId,
                'attachments' => $attachments,
            ];
        } catch (\Exception $e) {
            Log::error('Error preparing broadcast data', [
                'message_id' => $this->messageId,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            // Return minimal data to avoid broadcast failure
            return [
                'message_id' => $this->messageId,
                'attachments' => [],
                'error' => 'Failed to prepare broadcast data'
            ];
        }
    }

    /**
     * Specify the broadcasting connection to use.
     *
     * @return string
     */
    public function broadcastVia(): string
    {
        Log::info('Broadcasting TicketMessageAttachmentsReady via reverb', [
            'message_id' => $this->messageId,
            'ticket_id' => $this->ticketId,
            'timestamp' => now()->toDateTimeString()
        ]);

        // Make sure we're using the reverb driver
        return 'reverb';
    }
}
