
import { GoogleGenerativeAI } from "@google/generative-ai";
import { GEMINI_MODEL_NAME, MANDATORY_HASHTAG } from '../constants';
import type { AnalysisResult, GeminiResponseJson, Tag, CategorizedTagItem, VideoSummary } from '../types';
import { generateUniqueId } from '../components/common/Icons';

const SYSTEM_PROMPT_FOR_VIDEO_ANALYSIS = `Du bist ein KI-Assistent, der auf die Analyse von deutschen Minecraft-Story-Videos spezialisiert ist, um SEO-optimierte Tags und Zusammenfassungen für Social Media (TikTok, Instagram) zu generieren.
Analysiere den Videoclip, der in dieser Anfrage mitgesendet wird.
Gib deine Antwort NUR im JSON-Format zurück. Das JSON-Objekt muss exakt die folgende Struktur haben:
{
  "videoSummary": {
    "overallGerman": "Eine prägnante, fesselnde Zusammenfassung des Videoinhalts in deutscher Sprache (max. 4-7 Sätze).",
    "keyVisualElements": ["Liste der auffälligsten visuellen Elemente oder Szenen (max. 5 Elemente)."],
    "detectedObjects": ["Liste der erkannten Objekte, Gegenstände oder Fahrzeuge im Video (max. 5 Elemente)."],
    "detectedActions": ["Liste der wichtigsten Aktionen, Events oder Handlungen im Video (max. 5 Elemente)."],
    "spokenNamesOrRoles": ["Liste der im Video gesprochenen Namen von Charakteren oder deren Rollen (z.B. 'Dorfbewohner', 'König') (max. 5 Elemente)."],
    "dialogueThemes": ["Liste der Hauptthemen oder Kernaussagen aus den Dialogen im Video (max. 5 Elemente)."]
  },
  "tiktokHashtags": ["#beispielTikTok1", "#beispielTikTok2", "#${MANDATORY_HASHTAG}", "... (weitere 12-17 Hashtags)"],
  "instagramHashtags": ["#beispielInstagram1", "#beispielInstagram2", "#${MANDATORY_HASHTAG}", "... (weitere 12-17 Hashtags)"],
  "categorizedTags": [
    { "category": "Hauptthema des Videos", "tags": ["Keyword1", "Keyword2", "Keyword3"] },
    { "category": "Stimmung/Atmosphäre", "tags": ["Keyword1", "Keyword2", "Keyword3"] },
    { "category": "Charaktere/Rollen", "tags": ["Keyword1", "Keyword2", "Keyword3"] },
    { "category": "Orte/Settings im Video", "tags": ["Keyword1", "Keyword2", "Keyword3"] },
    { "category": "Minecraft-spezifische Elemente", "tags": ["Redstone", "PvP", "Roleplay", "Survival", "Building"] }
  ]
}

Wichtig:
- Alle Texte müssen auf Deutsch sein.
- Die Hashtags müssen für ein deutsches Publikum optimiert sein und mit '#' beginnen. Für tiktokHashtags und instagramHashtags, liefere jeweils 15-20 relevante Hashtags.
- Analysiere sowohl visuelle als auch auditive Aspekte des Videos.
- Konzentriere dich auf Aspekte, die für Minecraft-Story-Videos relevant sind.
- Für \`categorizedTags\`, liefere 2-5 Keywords pro Kategorie.
- Stelle sicher, dass der Hashtag '${MANDATORY_HASHTAG}' in tiktokHashtags und instagramHashtags enthalten ist. Wenn der Nutzer spezifische Gameplay-Elemente wie 'Redstone-Falle', 'automatisierte Farm' oder 'komplexes Bauwerk' zeigt, erwähne diese explizit in den relevanten Kategorien.
- Versuche, aus Dialogen oder gezeigten Schildern konkrete Namen oder Projekttitel zu extrahieren, falls vorhanden, und nutze sie in den Tags/Keywords.
`;

export const generateTagsForVideo = async (
  apiKey: string, // Accept API key from the caller (App.tsx)
  base64Data: string,
  mimeType: string
): Promise<AnalysisResult> => {
  if (!apiKey) {
    throw new Error("API key is not provided to the service function.");
  }
  const genAI = new GoogleGenerativeAI(apiKey);
  const model = genAI.getGenerativeModel({ model: GEMINI_MODEL_NAME });

  const videoPart = {
    inlineData: {
      mimeType: mimeType,
      data: base64Data,
    },
  };

  const response = await model.generateContent([
    SYSTEM_PROMPT_FOR_VIDEO_ANALYSIS,
    videoPart
  ]);

  let jsonStr = response.response.text().trim();
  const fenceRegex = /^```(\w*)?\s*\n?(.*?)\n?\s*```$/s;
  const match = jsonStr.match(fenceRegex);
  if (match && match[2]) {
    jsonStr = match[2].trim();
  }

  let parsedData: GeminiResponseJson;
  try {
    parsedData = JSON.parse(jsonStr);
  } catch (e) {
    console.error("Failed to parse JSON response from Gemini:", jsonStr, e);
    throw new Error(`Failed to parse AI response. The response was not valid JSON. Raw response: ${jsonStr}`);
  }

  const ensureMandatoryHashtag = (hashtags: string[] = []): string[] => {
    const cleanedHashtags = hashtags.map(h => h.startsWith('#') ? h : `#${h.trim()}`).filter(h => h.length > 1);
    const uniqueHashtags = Array.from(new Set(cleanedHashtags));

    if (!uniqueHashtags.includes(MANDATORY_HASHTAG)) {
      return [MANDATORY_HASHTAG, ...uniqueHashtags];
    }
    return uniqueHashtags;
  };

  const tiktokHashtags: Tag[] = ensureMandatoryHashtag(parsedData.tiktokHashtags).map(tagStr => ({
    id: generateUniqueId(),
    text: tagStr,
  }));

  const instagramHashtags: Tag[] = ensureMandatoryHashtag(parsedData.instagramHashtags).map(tagStr => ({
    id: generateUniqueId(),
    text: tagStr,
  }));

  const categorizedTags: CategorizedTagItem[] = (parsedData.categorizedTags || []).map(ct => ({
    id: generateUniqueId(),
    category: ct.category,
    tags: (ct.tags || []).map(tagStr => ({
      id: generateUniqueId(),
      text: tagStr.trim(),
    })).filter(t => t.text.length > 0),
  })).filter(ct => ct.tags.length > 0 && ct.category && ct.category.trim().length > 0);

  const defaultVideoSummary: VideoSummary = {
    overallGerman: "Keine Zusammenfassung verfügbar.",
    keyVisualElements: [],
    detectedObjects: [],
    detectedActions: [],
    spokenNamesOrRoles: [],
    dialogueThemes: [],
  };

  const videoSummary: VideoSummary = {
      ...defaultVideoSummary,
      ...(parsedData.videoSummary || {}),
      overallGerman: parsedData.videoSummary?.overallGerman || defaultVideoSummary.overallGerman,
      keyVisualElements: parsedData.videoSummary?.keyVisualElements || [],
      detectedObjects: parsedData.videoSummary?.detectedObjects || [],
      detectedActions: parsedData.videoSummary?.detectedActions || [],
      spokenNamesOrRoles: parsedData.videoSummary?.spokenNamesOrRoles || [],
      dialogueThemes: parsedData.videoSummary?.dialogueThemes || [],
  };

  const result: AnalysisResult = {
    videoSummary,
    tiktokHashtags,
    instagramHashtags,
    categorizedTags,
  };
  return result;
};
