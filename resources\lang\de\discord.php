<?php

return [
    // General errors
    'errors.admin_only_command' => 'Du hast keine Berechtigung, diesen Befehl auszuführen.',
    'errors.generic_error_admin_contact' => 'Ein unerwarteter Fehler ist aufgetreten. Bitte kontaktiere einen Administrator.',
    'errors.user_not_linked_short' => 'Dein Discord-Account ist nicht mit einem Minewache-Benutzerkonto verknüpft.',


    // Ticket system related
    'ticket.db_error_channel_removed' => 'Entschuldigung, beim Speichern deines Tickets in der Datenbank ist ein Fehler aufgetreten. Der erstellte Kanal wird entfernt.',
    'ticket.embed_title' => 'Ticket #:id: :title',
    'ticket.embed_field_created_by' => 'Erstellt von',
    'ticket.embed_field_status' => 'Status',
    'ticket.status_open' => 'Offen',
    'ticket.embed_footer' => 'Minewache Support System',
    'ticket.welcome_message' => 'Willkommen :userMention! Dein Ticket wurde erstellt. Ein Teammitglied wird dir in Kürze helfen.',
    'ticket.embed_creation_error' => '*Konnte keine Einbettungsvorschau erstellen.*',
    'ticket.created_success_in_channel' => 'Ticket #:id erfolgreich in :channelMention erstellt!',
    'ticket.channel_creation_error' => 'Entschuldigung, beim Erstellen des Ticket-Kanals ist ein Fehler aufgetreten. Bitte versuche es später erneut.',
    'ticket.unexpected_error_admin_contact' => 'Bei der Bearbeitung deiner Ticket-Anfrage ist ein unerwarteter Fehler aufgetreten. Bitte kontaktiere einen Administrator.',
    
    'ticket.closed_by_user_default_reason' => 'Geschlossen von :userTag',
    'ticket.close_not_in_ticket_channel' => 'Dieser Befehl kann nur in einem aktiven Ticket-Kanal verwendet werden, oder benutze den Button auf einer Ticket-Nachricht.',
    'ticket.close_no_context' => 'Der Ticket-Kontext zum Schließen konnte nicht ermittelt werden.',
    'ticket.closed_success' => 'Ticket #:id wurde geschlossen.',
    'ticket.close_error' => 'Ticket #:id konnte nicht geschlossen werden. Bitte überprüfe die Logs oder den Ticket-Status.',

    'ticket.progress_message' => 'Ticket #:id wird jetzt von :userTag bearbeitet.',
    'ticket.progress_embed_title' => 'Ticket in Bearbeitung',
    'ticket.progress_success_ephemeral' => 'Ticket #:id wurde als "In Bearbeitung" markiert.',
    'ticket.progress_error' => 'Ticket #:id konnte nicht als "In Bearbeitung" markiert werden. Bitte überprüfe die Logs.',

    'ticket.invalid_id_format' => 'Die angegebene Ticket-ID hat kein gültiges numerisches Format.',
    'ticket.command_guild_only' => 'Dieser Befehl kann nur in einem Serverkanal verwendet werden.',

    // Ticket Assignment
    'ticket.assign_not_in_ticket_channel' => 'Ticket-ID muss angegeben werden oder der Befehl in einem Ticket-Kanal verwendet werden.',
    'ticket.assign_target_not_linked' => 'Der Zielbenutzer :userMention ist nicht mit einem Minewache-Konto verknüpft.',
    'ticket.assigned_success_confirmation' => 'Ticket #:id erfolgreich :assigneeName zugewiesen.',
    'ticket.unassigned_success_confirmation' => 'Ticket #:id erfolgreich freigegeben (nicht mehr zugewiesen).',
    'ticket.assign_error_generic' => 'Fehler beim Aktualisieren der Ticket-Zuweisung. Fehler: :message',
    
    'ticket.assigned_to_user_channel' => 'Dieses Ticket wurde :assigneeMention von :assignerName zugewiesen.',
    'ticket.unassigned_channel' => 'Dieses Ticket wurde von :assignerName freigegeben (nicht mehr zugewiesen).',
    'ticket.assignment_update_title' => 'Ticket-Zuweisung aktualisiert',
    'ticket.assigned_dm_notification' => "Dir wurde Ticket #:ticketId - ':ticketTitle' von :assignerName zugewiesen. Sieh es dir in :channelMention an.",
    'ticket.assigned_dm_title' => 'Dir wurde ein Ticket zugewiesen',

    // AI Responses
    'ai.response_prefix' => 'KI-Assistent Vorschlag:',
    'ai.response_footer_disclaimer' => 'Dies ist eine KI-generierte Antwort. Bitte überprüfe wichtige Informationen.',
    'ai.response_author_name' => 'KI-Assistent',

    // Ticket Reconstruct
    'ticket.reconstruct_ticket_not_found' => 'Ticket mit ID :ticketId nicht in der Datenbank gefunden.',
    'ticket.reconstruct_already_linked' => 'Ticket #:ticketId ist bereits mit dem Discord-Kanal :channelMention verknüpft. Hebe die Verknüpfung zuerst auf, wenn du sie ändern möchtest.',
    'ticket.reconstruct_channel_not_text' => 'Der ausgewählte Kanal :channelMention ist kein gültiger Textkanal oder Thread.',
    'ticket.reconstruct_channel_fetch_error' => 'Discord-Kanaldetails für ID :channelId konnten nicht abgerufen werden. Stelle sicher, dass der Bot Zugriff hat und der Kanal existiert.',
    'ticket.reconstruct_system_message' => 'Ticket manuell mit Discord-Kanal #:channelName von :adminName verknüpft.',
    'ticket.reconstruct_channel_topic' => 'Ticket ID: :ticketId | Verknüpft von: :adminName',
    'ticket.reconstruct_success' => 'Discord-Kanal :channelMention erfolgreich mit Ticket-ID :ticketId verknüpft.',
    'ticket.reconstruct_success_topic_fail' => 'Discord-Kanal :channelMention erfolgreich mit Ticket-ID :ticketId verknüpft, aber das Kanalthema konnte nicht aktualisiert werden.',


    // Buttons
    'buttons.close_ticket' => 'Ticket schließen',
    'buttons.mark_in_progress' => 'In Bearbeitung markieren',

    // Setup command related
    'setup.error_user_not_linked' => 'Dein Discord-Account ist nicht mit einem Minewache-Benutzerkonto verknüpft. Bitte verknüpfe zuerst dein Konto.',
    'setup.log_permission_granted' => 'Benutzer: :userName (Discord: :userTag, ID: :userId) hat die Einrichtung mit entsprechenden Berechtigungen initiiert.',
    'setup.error_guild_id_not_configured' => 'Discord Guild ID ist nicht in `config/minewache_discord_bot.php` oder .env konfiguriert.',
    'setup.error_bot_token_not_configured' => 'Discord Bot Token ist nicht konfiguriert.',
    'setup.error_fetch_guild_details' => 'Fehler beim Abrufen der Guild-Details für ID: :guildId. Stelle sicher, dass der Bot Mitglied dieser Guild ist.',
    'setup.log_category_found' => 'Vorhandene Ticket-Kategorie gefunden: \':categoryName\' (ID: :categoryId).',
    'setup.log_category_not_found_or_invalid' => 'Konfigurierte Ticket-Kategorie-ID \':categoryId\' nicht gefunden oder ist keine Kategorie. Es wird versucht, eine neue zu erstellen.',
    'setup.default_category_name' => 'Support Tickets',
    'setup.log_category_created' => 'Neue Ticket-Kategorie erfolgreich erstellt: \':categoryName\' (ID: :categoryId).',
    'setup.error_category_creation_failed' => 'Fehler beim Erstellen einer neuen Ticket-Kategorie namens \':categoryName\'.',
    'setup.log_no_support_roles_configured' => 'Keine Support-Rollen-IDs konfiguriert. Überspringe Rollen-Berechtigungseinstellungen für Support-Mitarbeiter.',
    'setup.error_failed_fetch_guild_roles' => 'Fehler beim Abrufen der Guild-Rollen. Konfigurierte Support-Rollen können nicht überprüft werden.',
    'setup.log_support_role_found' => 'Konfigurierte Support-Rolle gefunden: \':roleName\' (ID: :roleId).',
    'setup.error_support_role_not_found' => 'Konfigurierte Support-Rollen-ID \':roleId\' in der Guild nicht gefunden. Bitte erstelle sie oder aktualisiere die Konfiguration.',
    'setup.log_no_valid_support_roles_found' => 'Keine konfigurierten Support-Rollen wurden in der Guild gefunden. Spezifische Berechtigungen für Support-Mitarbeiter werden übersprungen.',
    'setup.log_permissions_applied' => 'Berechtigungen erfolgreich auf Kategorie \':categoryName\' angewendet.',
    'setup.error_permissions_apply_failed' => 'Fehler beim Anwenden der Berechtigungen auf Kategorie \':categoryName\'.',
    'setup.error_category_not_available_for_perms' => 'Überspringe Kategorie-Berechtigungseinstellungen, da die Kategorie nicht gefunden oder erstellt wurde.',
    'setup.summary_header' => 'Ticket-Einrichtungsprozess abgeschlossen:',
    'setup.summary_log_title' => 'Protokoll',
    'setup.summary_errors_title' => 'Fehler/Warnungen',
    'setup.summary_action_required_title' => 'WICHTIGE AKTION ERFORDERLICH',
    'setup.summary_env_update_instructions' => 'Eine neue Ticket-Kategorie wurde erstellt. Bitte aktualisiere deine `.env`-Datei mit `DISCORD_TICKET_CATEGORY_ID=:categoryId` und führe dann `php artisan config:cache` aus (falls in Produktion).',
    'setup.summary_complete_no_action' => 'Einrichtung scheint abgeschlossen. Bitte überprüfe das Protokoll oben.',
    'setup.summary_complete_action_needed' => 'Einrichtung abgeschlossen mit erforderlichen Aktionen. Bitte überprüfe das Protokoll und die Anweisungen oben.',

];
