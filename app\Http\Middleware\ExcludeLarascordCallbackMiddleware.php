<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;

class ExcludeLarascordCallbackMiddleware
{
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure  $next
     * @return mixed
     */
    public function handle(Request $request, Closure $next)
    {
        // If this is the Larascord callback route, bypass the GDPR consent middleware
        if ($request->is('larascord/callback')) {
            // Only log in debug mode
            if (config('app.debug')) {
                Log::debug('Bypassing GDPR consent middleware for Larascord callback', [
                    'fresh_login' => session('fresh_discord_login', false),
                    'has_code' => $request->has('code'),
                    'session_id' => session()->getId(),
                    'login_timestamp' => session('discord_login_timestamp'),
                    'time_since_login' => session('discord_login_timestamp') ? now()->timestamp - session('discord_login_timestamp') : null,
                ]);
            }

            // Check if this is a stale login attempt (more than 5 minutes old)
            $loginTimestamp = session('discord_login_timestamp');
            if ($loginTimestamp && (now()->timestamp - $loginTimestamp > 300)) {
                if (config('app.debug')) {
                    Log::warning('Stale login attempt detected in middleware', [
                        'time_since_login' => now()->timestamp - $loginTimestamp,
                    ]);
                }
                // Let the controller handle the stale login
            }

            // If this is not a fresh login attempt and we have a code parameter,
            // it might be a redirect loop. Let's set the fresh_discord_login flag
            // to prevent further loops.
            if (!session('fresh_discord_login', false) && $request->has('code')) {
                if (config('app.debug')) {
                    Log::info('Setting fresh_discord_login flag for callback without fresh login flag');
                }

                // Set the fresh login flag and timestamp
                session(['fresh_discord_login' => true]);

                // Also set a timestamp if it doesn't exist
                if (!session('discord_login_timestamp')) {
                    session(['discord_login_timestamp' => now()->timestamp]);
                }

                // Ensure the session is saved immediately
                session()->save();
            }

            return $next($request);
        }

        // For all other routes, apply the GDPR consent middleware
        return app(GdprConsentMiddleware::class)->handle($request, $next);
    }
}
