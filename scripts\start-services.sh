#!/bin/bash

echo "Starting Minewache Ticket System Services..."

# Start Laravel development server
php artisan serve &
LARAVEL_PID=$!

# Start Vite development server
npm run dev &
VITE_PID=$!

# Start Reverb WebSocket server
php artisan reverb:start --host=127.0.0.1 --port=6001 &
REVERB_PID=$!

# Start Queue Worker with Reverb broadcasting
php artisan queue:work-reverb --queue=default,media --tries=3 --timeout=600 &
QUEUE_PID=$!

echo "All services started successfully!"
echo ""
echo "Press Ctrl+C to stop all services..."

# Function to kill all services
function cleanup {
    echo ""
    echo "Stopping all services..."
    kill $LARAVEL_PID $VITE_PID $REVERB_PID $QUEUE_PID
    echo "All services stopped successfully!"
    exit 0
}

# Register the cleanup function for when Ctrl+C is pressed
trap cleanup SIGINT

# Wait indefinitely
while true; do
    sleep 1
done
