<x-app-layout>
    <x-slot name="heading">
        Discord-Mitgliedschaft erforderlich
    </x-slot>

    <x-slot name="header">
        <h2 class="font-display text-xl md:text-2xl text-base-content leading-tight animate-slide-down">
            {{ __('Discord-Mitgliedschaft erforderlich') }}
        </h2>
    </x-slot>

    <div class="py-8 md:py-12 bg-base-200">
        <div class="max-w-2xl mx-auto px-4 sm:px-6 lg:px-8">
            <!-- Hauptkarte -->
            <div class="bg-base-100 rounded-xl shadow-lg overflow-hidden opacity-0 animate-slide-up">
                <div class="p-6 md:p-8 text-base-content">
                    <div class="flex flex-col sm:flex-row sm:items-start sm:space-x-4 mb-8">
                        <div class="rounded-full bg-primary/20 p-3 flex-shrink-0 mb-4 sm:mb-0 animate-pulse">
                            <x-heroicon-o-exclamation-circle class="w-7 h-6 text-primary" />
                        </div>
                        <div>
                            <h3 class="font-geometric text-xl md:text-2xl font-medium text-base-content mb-3 opacity-0 animate-delayed-fade">Discord-Mitgliedschaft erforderlich</h3>
                            <p class="mb-4">
                                Um dich bei der MineWache bewerben zu können, musst du Mitglied auf unserem Discord-Server sein.
                            </p>
                            <p class="mb-4">
                                Dies ist notwendig, damit wir dich bei Fragen zu deiner Bewerbung kontaktieren können und um sicherzustellen, dass du Teil unserer Community bist.
                            </p>
                        </div>
                    </div>

                    <!-- Discord-Server-Beschreibung -->
                    <div class="bg-base-200/50 rounded-lg p-4 mb-6">
                        <h4 class="font-medium flex items-center gap-2 mb-2">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-primary" viewBox="0 0 24 24" fill="currentColor">
                                <path d="M20.317 4.3698a19.7913 19.7913 0 00-4.8851-1.5152.0741.0741 0 00-.0785.0371c-.211.3753-.4447.8648-.6083 1.2495-1.8447-.2762-3.68-.2762-5.4868 0-.1636-.3933-.4058-.8742-.6177-1.2495a.077.077 0 00-.0785-.037 19.7363 19.7363 0 00-4.8852 1.515.0699.0699 0 00-.0321.0277C.5334 9.0458-.319 13.5799.0992 18.0578a.0824.0824 0 00.0312.0561c2.0528 1.5076 4.0413 2.4228 5.9929 3.0294a.0777.0777 0 00.0842-.0276c.4616-.6304.8731-1.2952 1.226-1.9942a.076.076 0 00-.0416-.1057c-.6528-.2476-1.2743-.5495-1.8722-.8923a.077.077 0 01-.0076-.1277c.1258-.0943.2517-.1923.3718-.2914a.0743.0743 0 01.0776-.0105c3.9278 1.7933 8.18 1.7933 12.0614 0a.0739.0739 0 01.0785.0095c.1202.099.246.1981.3728.2924a.077.077 0 01-.0066.1276 12.2986 12.2986 0 01-1.873.8914.0766.0766 0 00-.0407.1067c.3604.698.7719 1.3628 1.225 1.9932a.076.076 0 00.0842.0286c1.961-.6067 3.9495-1.5219 6.0023-3.0294a.077.077 0 00.0313-.0552c.5004-5.177-.8382-9.6739-3.5485-13.6604a.061.061 0 00-.0312-.0286zM8.02 15.3312c-1.1825 0-2.1569-1.0857-2.1569-2.419 0-1.3332.9555-2.4189 2.157-2.4189 1.2108 0 2.1757 1.0952 2.1568 2.419 0 1.3332-.9555 2.4189-2.1569 2.4189zm7.9748 0c-1.1825 0-2.1569-1.0857-2.1569-2.419 0-1.3332.9554-2.4189 2.1569-2.4189 1.2108 0 2.1757 1.0952 2.1568 2.419 0 1.3332-.946 2.4189-2.1568 2.4189Z"/>
                            </svg>
                            <span>Unser Discord-Server</span>
                        </h4>
                        <p class="text-sm mb-3">
                            Auf unserem Discord-Server findest du:
                        </p>
                        <ul class="space-y-1 text-sm">
                            <li class="flex items-start gap-2">
                                <x-heroicon-o-check-circle class="h-5 w-5 text-success shrink-0" />
                                <span>Neuigkeiten und Updates zu unseren Projekten</span>
                            </li>
                            <li class="flex items-start gap-2">
                                <x-heroicon-o-check-circle class="h-5 w-5 text-success shrink-0" />
                                <span>Eine hilfsbereite Community</span>
                            </li>
                            <li class="flex items-start gap-2">
                                <x-heroicon-o-check-circle class="h-5 w-5 text-success shrink-0" />
                                <span>Direkten Kontakt zu unserem Team</span>
                            </li>
                            <li class="flex items-start gap-2">
                                <x-heroicon-o-check-circle class="h-5 w-5 text-success shrink-0" />
                                <span>Informationen zum Bewerbungsprozess</span>
                            </li>
                        </ul>
                    </div>

                    <!-- Aktionsbuttons -->
                    <div class="card-actions flex flex-col md:flex-row justify-center gap-4 mt-8">
                        <x-modern-button variant="primary" href="{{ route('discord') }}" target="_blank"><svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 24 24" fill="currentColor">
                                <path d="M20.317 4.3698a19.7913 19.7913 0 00-4.8851-1.5152.0741.0741 0 00-.0785.0371c-.211.3753-.4447.8648-.6083 1.2495-1.8447-.2762-3.68-.2762-5.4868 0-.1636-.3933-.4058-.8742-.6177-1.2495a.077.077 0 00-.0785-.037 19.7363 19.7363 0 00-4.8852 1.515.0699.0699 0 00-.0321.0277C.5334 9.0458-.319 13.5799.0992 18.0578a.0824.0824 0 00.0312.0561c2.0528 1.5076 4.0413 2.4228 5.9929 3.0294a.0777.0777 0 00.0842-.0276c.4616-.6304.8731-1.2952 1.226-1.9942a.076.076 0 00-.0416-.1057c-.6528-.2476-1.2743-.5495-1.8722-.8923a.077.077 0 01-.0076-.1277c.1258-.0943.2517-.1923.3718-.2914a.0743.0743 0 01.0776-.0105c3.9278 1.7933 8.18 1.7933 12.0614 0a.0739.0739 0 01.0785.0095c.1202.099.246.1981.3728.2924a.077.077 0 01-.0066.1276 12.2986 12.2986 0 01-1.873.8914.0766.0766 0 00-.0407.1067c.3604.698.7719 1.3628 1.225 1.9932a.076.076 0 00.0842.0286c1.961-.6067 3.9495-1.5219 6.0023-3.0294a.077.077 0 00.0313-.0552c.5004-5.177-.8382-9.6739-3.5485-13.6604a.061.061 0 00-.0312-.0286zM8.02 15.3312c-1.1825 0-2.1569-1.0857-2.1569-2.419 0-1.3332.9555-2.4189 2.157-2.4189 1.2108 0 2.1757 1.0952 2.1568 2.419 0 1.3332-.9555 2.4189-2.1569 2.4189zm7.9748 0c-1.1825 0-2.1569-1.0857-2.1569-2.419 0-1.3332.9554-2.4189 2.1569-2.4189 1.2108 0 2.1757 1.0952 2.1568 2.419 0 1.3332-.946 2.4189-2.1568 2.4189Z"/>
                            </svg>
                            Discord-Server beitreten</x-modern-button>
                        <x-modern-button variant="primary" href="{{ route('discord.refresh.membership') }}" ><svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                                <path fill-rule="evenodd" d="M4 2a1 1 0 011 1v2.101a7.002 7.002 0 0111.601 2.566 1 1 0 11-1.885.666A5.002 5.002 0 005.999 7H9a1 1 0 010 2H4a1 1 0 01-1-1V3a1 1 0 011-1zm.008 9.057a1 1 0 011.276.61A5.002 5.002 0 0014.001 13H11a1 1 0 110-2h5a1 1 0 011 1v5a1 1 0 11-2 0v-2.101a7.002 7.002 0 01-11.601-2.566 1 1 0 01.61-1.276z" clip-rule="evenodd" />
                            </svg>
                            Mitgliedschaft aktualisieren</x-modern-button>
                        <x-modern-button variant="primary" href="{{ route('auth.discord') }}" ><svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-8.707l-3-3a1 1 0 00-1.414 0l-3 3a1 1 0 001.414 1.414L9 9.414V13a1 1 0 102 0V9.414l1.293 1.293a1 1 0 001.414-1.414z" clip-rule="evenodd" />
                            </svg>
                            Erneut anmelden</x-modern-button>
                        <x-modern-button variant="ghost" href="{{ route('home') }}" >Zurück zur Startseite</x-modern-button>
                    </div>

                    <!-- Hinweis -->
                    <div class="mt-6 text-sm text-center text-base-content/70">
                        <div class="bg-info/10 p-4 rounded-lg border border-info/30 mb-4">
                            <h4 class="font-medium text-info mb-2">So geht's weiter:</h4>
                            <ol class="list-decimal list-inside text-left space-y-2">
                                <li>Tritt dem Discord-Server bei (öffnet sich in einem neuen Tab)</li>
                                <li>Klicke auf <strong>"Mitgliedschaft aktualisieren"</strong>, wenn du bereits Mitglied bist</li>
                                <li>Oder klicke auf <strong>"Erneut anmelden"</strong>, um die Authentifizierung neu zu starten</li>
                                <li>Erteile die erforderlichen Berechtigungen bei Discord</li>
                            </ol>
                        </div>

                        <div class="mt-4 flex flex-wrap justify-center gap-2">
                            <x-modern-button variant="primary" href="{{ route('discord') }}" target="_blank"><svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" viewBox="0 0 24 24" fill="currentColor">
                                    <path d="M20.317 4.3698a19.7913 19.7913 0 00-4.8851-1.5152.0741.0741 0 00-.0785.0371c-.211.3753-.4447.8648-.6083 1.2495-1.8447-.2762-3.68-.2762-5.4868 0-.1636-.3933-.4058-.8742-.6177-1.2495a.077.077 0 00-.0785-.037 19.7363 19.7363 0 00-4.8852 1.515.0699.0699 0 00-.0321.0277C.5334 9.0458-.319 13.5799.0992 18.0578a.0824.0824 0 00.0312.0561c2.0528 1.5076 4.0413 2.4228 5.9929 3.0294a.0777.0777 0 00.0842-.0276c.4616-.6304.8731-1.2952 1.226-1.9942a.076.076 0 00-.0416-.1057c-.6528-.2476-1.2743-.5495-1.8722-.8923a.077.077 0 01-.0076-.1277c.1258-.0943.2517-.1923.3718-.2914a.0743.0743 0 01.0776-.0105c3.9278 1.7933 8.18 1.7933 12.0614 0a.0739.0739 0 01.0785.0095c.1202.099.246.1981.3728.2924a.077.077 0 01-.0066.1276 12.2986 12.2986 0 01-1.873.8914.0766.0766 0 00-.0407.1067c.3604.698.7719 1.3628 1.225 1.9932a.076.076 0 00.0842.0286c1.961-.6067 3.9495-1.5219 6.0023-3.0294a.077.077 0 00.0313-.0552c.5004-5.177-.8382-9.6739-3.5485-13.6604a.061.061 0 00-.0312-.0286zM8.02 15.3312c-1.1825 0-2.1569-1.0857-2.1569-2.419 0-1.3332.9555-2.4189 2.157-2.4189 1.2108 0 2.1757 1.0952 2.1568 2.419 0 1.3332-.9555 2.4189-2.1569 2.4189zm7.9748 0c-1.1825 0-2.1569-1.0857-2.1569-2.419 0-1.3332.9554-2.4189 2.1569-2.4189 1.2108 0 2.1757 1.0952 2.1568 2.419 0 1.3332-.946 2.4189-2.1568 2.4189Z"/>
                                </svg>
                                Discord-Server beitreten</x-modern-button>
                            <x-modern-button variant="primary" href="{{ route('discord.refresh.membership') }}" ><svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" viewBox="0 0 20 20" fill="currentColor">
                                    <path fill-rule="evenodd" d="M4 2a1 1 0 011 1v2.101a7.002 7.002 0 0111.601 2.566 1 1 0 11-1.885.666A5.002 5.002 0 005.999 7H9a1 1 0 010 2H4a1 1 0 01-1-1V3a1 1 0 011-1zm.008 9.057a1 1 0 011.276.61A5.002 5.002 0 0014.001 13H11a1 1 0 110-2h5a1 1 0 011 1v5a1 1 0 11-2 0v-2.101a7.002 7.002 0 01-11.601-2.566 1 1 0 01.61-1.276z" clip-rule="evenodd" />
                                </svg>
                                Aktualisieren</x-modern-button>
                            <x-modern-button variant="primary" href="{{ route('auth.discord') }}" ><svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" viewBox="0 0 20 20" fill="currentColor">
                                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-8.707l-3-3a1 1 0 00-1.414 0l-3 3a1 1 0 001.414 1.414L9 9.414V13a1 1 0 102 0V9.414l1.293 1.293a1 1 0 001.414-1.414z" clip-rule="evenodd" />
                                </svg>
                                Erneut anmelden</x-modern-button>
                        </div>

                        @if(config('app.debug') || !app()->environment('production'))
                            <div class="mt-8 p-4 bg-info/10 border border-info/30 rounded-lg">
                                <h3 class="font-medium text-info mb-2">Entwicklermodus</h3>
                                <p class="mb-3">Du kannst die Discord-Mitgliedschaft simulieren:</p>
                                <div class="flex justify-center gap-2">
                                    <x-modern-button variant="primary" href="{{ route('dev.simulate.discord.membership', 'true') }}" ><svg xmlns="http://www.w3.org/2000/svg" class="h-3 w-3" viewBox="0 0 20 20" fill="currentColor">
                                            <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" />
                                        </svg>
                                        Als Mitglied</x-modern-button>
                                    <x-modern-button variant="primary" href="{{ route('dev.simulate.discord.membership', 'false') }}" ><svg xmlns="http://www.w3.org/2000/svg" class="h-3 w-3" viewBox="0 0 20 20" fill="currentColor">
                                            <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd" />
                                        </svg>
                                        Als Nicht-Mitglied</x-modern-button>
                                    <x-modern-button variant="ghost" href="{{ route('dev.simulate.discord.membership', 'reset') }}" >Zurücksetzen</x-modern-button>
                                </div>
                            </div>
                        @endif

                        @if(session('error'))
                            <div class="mt-4 p-2 bg-error/10 text-error rounded-lg">
                                {{ session('error') }}
                            </div>
                        @endif


                    </div>
                </div>
            </div>
        </div>
    </div>
</x-app-layout>
