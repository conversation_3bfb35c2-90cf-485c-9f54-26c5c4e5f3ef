# Minewache Website CI/CD Pipeline

This directory contains the configuration files for the CI/CD pipeline for the Minewache website.

## Overview

The CI/CD pipeline automates the testing and deployment processes to ensure code quality and streamline the release process. The pipeline consists of three main workflows:

1. **Test Workflow**: Runs automated tests on every push and pull request to the `main` and `dev` branches.
2. **Development Deployment Workflow**: Automatically deploys changes to the development server when code is pushed to the `dev` branch.
3. **Production Deployment Workflow**: Automatically deploys changes to the production server when code is pushed to the `main` branch.

## Workflow Files

- `test.yml`: Configuration for the test workflow.
- `deploy-dev.yml`: Configuration for the development deployment workflow.
- `deploy-prod.yml`: Configuration for the production deployment workflow.

## Required Secrets

The following secrets need to be configured in the GitHub repository settings:

### For Development Deployment

- `DEV_SSH_PRIVATE_KEY`: SSH private key for connecting to the development server.
- `DEV_SERVER_IP`: IP address of the development server.
- `DEV_SERVER_USER`: Username for SSH connection to the development server.
- `DEV_SERVER_PATH`: Path to the website directory on the development server.

### For Production Deployment

- `PROD_SSH_PRIVATE_KEY`: SSH private key for connecting to the production server.
- `PROD_SERVER_IP`: IP address of the production server.
- `PROD_SERVER_USER`: Username for SSH connection to the production server.
- `PROD_SERVER_PATH`: Path to the website directory on the production server.

## Setting Up Secrets

1. Generate an SSH key pair for GitHub Actions:
   ```bash
   ssh-keygen -t rsa -b 4096 -C "github-actions" -f github-actions
   ```

2. Add the public key to the authorized_keys file on the server:
   ```bash
   cat github-actions.pub >> ~/.ssh/authorized_keys
   ```

3. Add the private key as a secret in the GitHub repository settings:
   - Go to your GitHub repository
   - Navigate to "Settings" > "Secrets and variables" > "Actions"
   - Click "New repository secret"
   - Name: `DEV_SSH_PRIVATE_KEY` or `PROD_SSH_PRIVATE_KEY`
   - Value: Contents of the `github-actions` private key file

4. Add the other required secrets in the same way.

## Branch Protection Rules

To ensure code quality and prevent direct pushes to the `main` and `dev` branches, set up branch protection rules:

1. Go to your GitHub repository settings
2. Navigate to "Branches"
3. Add a rule for the `main` branch:
   - Check "Require a pull request before merging"
   - Check "Require status checks to pass before merging"
   - Select the "Run Tests" workflow as a required status check
   - Check "Require branches to be up to date before merging"
   - Check "Include administrators"

4. Add a similar rule for the `dev` branch.

## Documentation

For more detailed information about the CI/CD pipeline, refer to the following documentation:

- [CI/CD Pipeline Documentation](../docs/ci-cd-pipeline.md): Comprehensive guide to the CI/CD pipeline.
- [Testing Recommendations](../docs/testing-recommendations.md): Recommendations for improving the testing process.

## Troubleshooting

If you encounter issues with the CI/CD pipeline, check the following:

1. **Workflow Runs**: Check the workflow runs in the "Actions" tab of the GitHub repository to see detailed logs of the failed workflow.

2. **Secrets**: Ensure that all required secrets are properly configured in the repository settings.

3. **SSH Access**: Verify that the SSH key has the necessary permissions to access the server and the website directory.

4. **Server Configuration**: Check the server configuration to ensure that all required services are installed and properly configured.

If you need further assistance, contact the development team.

## Workflow Maintenance

- The `actions/upload-artifact` GitHub Action was updated from v3 to v4 in all workflow files (`deploy-dev.yml`, `deploy-prod.yml`, `test.yml`) to resolve an issue with missing download information for uploaded artifacts.
