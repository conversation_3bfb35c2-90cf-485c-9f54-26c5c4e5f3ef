<?php

namespace App\Events;

use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Broadcasting\PrivateChannel;
use Illuminate\Contracts\Broadcasting\ShouldBroadcast;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;

class TicketTypingEvent implements ShouldBroadcast
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    public $ticketId;
    public $userData;

    /**
     * Create a new event instance.
     */
    public function __construct(int $ticketId, array $userData)
    {
        $this->ticketId = $ticketId;
        $this->userData = $userData;

        // Log when the event is constructed
        Log::debug('TicketTypingEvent constructed', [
            'ticket_id' => $ticketId,
            'user_data' => $userData
        ]);
    }

    /**
     * Get the channels the event should broadcast on.
     *
     * @return array<int, \Illuminate\Broadcasting\Channel>
     */
    public function broadcastOn(): array
    {
        $channelName = 'tickets.' . $this->ticketId;

        // Log the channel the event is broadcasting on
        Log::debug('TicketTypingEvent broadcasting on channel', [
            'channel' => $channelName
        ]);

        return [
            new PrivateChannel($channelName),
        ];
    }

    /**
     * The event's broadcast name.
     */
    public function broadcastAs(): string
    {
        // Log the event name
        Log::debug('TicketTypingEvent broadcast name', [
            'name' => 'typing'
        ]);

        return 'typing'; // Match frontend listener
    }

    /**
     * Get the data to broadcast.
     *
     * @return array<string, mixed>
     */
    public function broadcastWith(): array
    {
        $data = [
            'user' => $this->userData,
            'timestamp' => now()->timestamp, // Add timestamp for auto-expiry on frontend
        ];

        // Log the data being broadcast
        Log::debug('TicketTypingEvent broadcast data', [
            'data' => $data
        ]);

        return $data;
    }

    /**
     * Specify the broadcasting connection to use.
     *
     * @return string
     */
    public function broadcastVia(): string
    {
        Log::info('Broadcasting TicketTypingEvent via reverb', ['ticket_id' => $this->ticketId]);
        return 'reverb';
    }
}
