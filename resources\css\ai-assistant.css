/* AI Assistant Styles */

/* AI Message Bubble */
.message-bubble-ai {
    background: rgba(37, 99, 235, 0.7);
    border-radius: 1rem 1rem 1rem 0.25rem;
    border-left: 3px solid rgba(59, 130, 246, 0.9);
}

/* Typing Animation */
.typing-dot {
    display: inline-block;
    width: 8px;
    height: 8px;
    border-radius: 50%;
    margin-right: 4px;
    background: white;
    animation: typing 1.5s infinite ease-in-out;
}

.typing-dot:nth-child(1) {
    animation-delay: 0s;
}

.typing-dot:nth-child(2) {
    animation-delay: 0.3s;
}

.typing-dot:nth-child(3) {
    animation-delay: 0.6s;
}

@keyframes typing {
    0%, 100% {
        transform: translateY(0);
        opacity: 0.5;
    }
    50% {
        transform: translateY(-5px);
        opacity: 1;
    }
}

/* AI Badge */
.ai-badge {
    background-color: rgba(37, 99, 235, 0.9);
    color: white;
    font-size: 0.75rem;
    padding: 0.125rem 0.5rem;
    border-radius: 9999px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
    display: flex;
    align-items: center;
    gap: 0.25rem;
}

/* AI Consent Prompt */
.ai-consent-prompt {
    background-color: rgba(30, 58, 138, 0.3);
    border: 1px solid rgba(59, 130, 246, 0.3);
    border-radius: 0.75rem;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    animation: fadeIn 0.5s ease-in-out;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* AI Response Pending */
.ai-response-pending {
    background-color: rgba(30, 58, 138, 0.3);
    border: 1px solid rgba(59, 130, 246, 0.3);
    border-radius: 0.75rem;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% {
        box-shadow: 0 0 0 0 rgba(59, 130, 246, 0.4);
    }
    70% {
        box-shadow: 0 0 0 10px rgba(59, 130, 246, 0);
    }
    100% {
        box-shadow: 0 0 0 0 rgba(59, 130, 246, 0);
    }
}
