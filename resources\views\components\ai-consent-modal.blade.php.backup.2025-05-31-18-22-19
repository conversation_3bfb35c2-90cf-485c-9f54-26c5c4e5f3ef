<div
    id="ai-consent-modal"
    class="hidden"
    data-consent="{{ auth()->user()->hasConsentedToAi() ? 'true' : 'false' }}"
    data-consent-url="{{ route('tickets.ai.consent') }}"
>
    <!-- Modal backdrop -->
    <div class="fixed inset-0 bg-black bg-opacity-50 z-40"></div>

    <!-- Modal content -->
    <div class="fixed inset-0 flex items-center justify-center z-50 p-4">
        <div class="bg-base-200 rounded-lg shadow-xl max-w-md w-full max-h-[90vh] overflow-y-auto">
            <div class="p-6">
                <div class="flex items-center gap-3 mb-4">
                    <x-heroicon-o-chip class="w-8 h-8 text-primary" />
                    <h3 class="text-xl font-semibold text-base-content">{{ __('tickets.ai_consent_title') }}</h3>
                </div>
                <p class="text-sm text-base-content/80 mb-4">
                    {{ __('tickets.ai_consent_description') }}
                </p>
                <p class="text-xs text-base-content/70 mb-6">
                    {{ __('tickets.ai_consent_data_usage') }}
                </p>
                <div class="flex justify-end gap-3">
                    <button id="ai-consent-decline-button" class="btn btn-ghost">{{ __('tickets.ai_consent_decline') }}</button>
                    <button id="ai-consent-accept-button" class="btn btn-primary">{{ __('tickets.ai_consent_accept') }}</button>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        const modal = document.getElementById('ai-consent-modal');
        const acceptButton = document.getElementById('ai-consent-accept-button');
        const declineButton = document.getElementById('ai-consent-decline-button');
        const consentUrl = modal.dataset.consentUrl;

        // Function to show the modal if consent is not given
        function checkAndShowModal() {
            if (modal.dataset.consent === 'false') {
                modal.classList.remove('hidden');
            }
        }

        // Function to handle consent action
        async function handleConsent(hasConsented) {
            try {
                const response = await fetch(consentUrl, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content') // Ensure CSRF token is sent
                    },
                    body: JSON.stringify({ has_consented: hasConsented })
                });
                const data = await response.json();
                if (data.success) {
                    modal.dataset.consent = data.has_consented ? 'true' : 'false';
                    modal.classList.add('hidden');
                    if (data.has_consented) {
                        // Optionally, trigger an event or refresh part of the page
                        // that depends on consent status, e.g., enabling AI features.
                        window.dispatchEvent(new CustomEvent('ai-consent-updated', { detail: { consented: true } }));
                    }
                } else {
                    // Handle error - perhaps show a message within the modal
                    console.error('Failed to update consent:', data.message);
                }
            } catch (error) {
                console.error('Error during consent update:', error);
            }
        }

        if (acceptButton) {
            acceptButton.addEventListener('click', () => handleConsent(true));
        }
        if (declineButton) {
            declineButton.addEventListener('click', () => {
                modal.classList.add('hidden'); // Just hide modal on decline
                 window.dispatchEvent(new CustomEvent('ai-consent-updated', { detail: { consented: false } }));
            });
        }

        // Initial check
        checkAndShowModal();

        // Listen for an event to re-check consent (e.g., if user tries to use AI feature)
        // Example: document.addEventListener('request-ai-feature', checkAndShowModal);
    });
</script>
