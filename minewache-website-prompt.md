# Prompt zur Erstellung der Minewache-Website

## Projektübersicht

Erstelle eine moderne, responsive Website für "Die Minewache", eine beliebte Minecraft-Polizeiserie. Die Website soll als zentrale Plattform für Bewerbungen, Support und Community-Engagement dienen. Das Design soll modern, immersiv und professionell sein, mit einem dunklen Farbschema als Standard und einer optionalen hellen Variante.

### Hauptzweck der Website
- Verwaltung von Bewerbungen für verschiedene Rollen im Minewache-Team (Schauspieler, Builder, Designer, etc.)
- Support-Ticket-System mit Discord-Integration
- Präsentation von Inhalten wie YouTube-Videos, Mods und Partnerschaften
- Bereitstellung von Branding-Ressourcen und offiziellen Links

## Technischer Stack

### Backend
- Laravel 11.x als PHP-Framework
- MySQL/MariaDB für die Datenbank
- Laravel Livewire 3.x für interaktive Komponenten
- Laravel Reverb für WebSocket-Kommunikation
- Laravel Queue mit Database-Treiber für Hintergrundverarbeitung
- FFmpeg für Medienverarbeitung (via Laravel FFmpeg)

### Frontend
- Tailwind CSS für Styling
- DaisyUI als UI-Komponenten-Bibliothek für Tailwind
- Alpine.js für clientseitiges JavaScript
- Blade als Template-Engine
- Vite für Asset-Bundling

### Authentifizierung
- Discord OAuth2 via Larascord
- Rollenbasierte Berechtigungen mit Bitmasking

### Entwicklungstools
- Laravel Telescope für Debugging
- Laravel Pulse für Monitoring
- Laravel Pint für Code-Formatierung

## Design-System

### Farbschema
- **Primärfarbe**: Blau (#3b82f6, blue-500)
- **Sekundärfarbe**: Slate (#475569, slate-600)
- **Akzentfarbe**: Slate (#1e293b, slate-800)
- **Hintergrund**: Gradient von slate-900 zu slate-800
- **Heller Modus**: Gradient von slate-100 zu weiß

### Typografie
- **Hauptschrift**: Geist Sans, Proxima Nova, Sans-Serif
- **Moderne Schrift**: Geist, SK-Modernist, Sans-Serif
- **Serifenschrift**: Literata, Harmond Display, Serif
- **Display-Schrift**: Anona, Stanley, Sans-Serif
- **Überschriften**: Font-Display, Bold
- **Fließtext**: Font-Modern

### UI-Komponenten
- Glassmorphismus-Effekt für Karten und Container
- Abgerundete Ecken (Radius: 1rem bis 3rem)
- Subtile Schatten für Tiefe
- Animierte Übergänge und Hover-Effekte
- Responsive Design mit Mobile-First-Ansatz

## Hauptfunktionen

### 1. Bewerbungssystem
- Mehrstufiger Bewerbungsassistent mit verschiedenen Formularen je nach Rolle
- Übersicht über eigene Bewerbungen für Benutzer
- Admin-Dashboard zur Bewertung und Verwaltung von Bewerbungen
- Automatische Discord-Rollenaktualisierung bei Annahme

### 2. Ticket-System
- Erstellung und Verwaltung von Support-Tickets
- Echtzeit-Chat mit WebSocket-Integration
- Dateianhänge und Medienverarbeitung
- Synchronisation mit Discord-Kanälen
- Statusverfolgung und Zuweisung an Teammitglieder

### 3. Discord-Integration
- OAuth2-Authentifizierung mit Discord
- Überprüfung der Server-Mitgliedschaft
- Rollensynchronisation zwischen Website und Discord
- Discord-Bot für Benachrichtigungen und Rollenaktualisierungen

### 4. Content-Präsentation
- YouTube-Video-Showcase
- Mod-Übersicht mit Download-Links
- Partner-Sektion mit Logos und Beschreibungen
- Branding-Ressourcen zum Download
- Offizielle Links und Social Media

### 5. Benutzer-Management
- Profilbearbeitung
- Datenschutz- und Löschanfragen
- Rollenbasierte Zugriffssteuerung
- Mehrsprachige Unterstützung (Deutsch/Englisch)

## Datenmodelle

### User
- Discord-ID (als Primärschlüssel)
- Discord-Benutzername und Avatar
- Berechtigungen (Bitmask für Rollen)
- Letzte Synchronisierung
- Beziehungen zu Bewerbungen und Tickets

### Application
- Benutzer-ID (Fremdschlüssel)
- Persönliche Informationen (Name, Alter, etc.)
- Rollenspezifische Informationen
- Portfolio-Links
- Status (ausstehend, angenommen, abgelehnt)
- Team-Kommentare und Bewertungen

### Ticket
- Benutzer-ID (Ersteller)
- Titel und Beschreibung
- Status (offen, in Bearbeitung, geschlossen)
- Discord-Kanal-ID
- Zugewiesener Supporter

### TicketMessage
- Ticket-ID (Fremdschlüssel)
- Benutzer-ID (Absender)
- Nachrichteninhalt
- Zeitstempel
- Discord-Synchronisationsstatus

## Seitenstruktur

### Öffentliche Seiten
- Startseite mit Hero-Bereich und Funktionsübersicht
- YouTube-Seite mit eingebetteten Videos
- Mods-Seite mit Download-Links
- Partner-Seite
- Branding-Seite mit Ressourcen
- Links-Seite mit offiziellen Verweisen

### Authentifizierte Seiten
- Bewerbungsformular und -assistent
- Meine Bewerbungen (Übersicht und Details)
- Ticket-System (Erstellen, Anzeigen, Verwalten)
- Profil-Einstellungen
- Datenschutz- und Löschanfragen

### Admin-Bereich
- Bewerbungsverwaltung
- Ticket-Übersicht und -Zuweisung
- Benutzer-Management
- System-Einstellungen

## Responsive Design

- Mobile-First-Ansatz mit flexiblen Layouts
- Breakpoints für verschiedene Gerätetypen
- Angepasste Navigation für mobile Geräte
- Optimierte Formulare für Touch-Eingabe
- Performante Ladezeiten durch optimierte Assets

## Besondere Anforderungen

### Accessibility
- Semantisches HTML
- ARIA-Attribute für komplexe Komponenten
- Tastaturnavigation
- Ausreichende Farbkontraste
- Screen-Reader-Unterstützung

### Performance
- Lazy Loading für Bilder und Komponenten
- Optimierte Assets durch Vite
- Caching-Strategien
- Effiziente Datenbankabfragen

### Sicherheit
- CSRF-Schutz
- XSS-Prävention
- Rate Limiting für API-Endpunkte
- Sichere Cookie-Einstellungen
- Berechtigungsprüfungen auf allen Routen

## Deployment und Infrastruktur

- Produktionsumgebung mit Nginx
- Systemd-Services für Hintergrundprozesse
- Automatisierte Backups
- CI/CD-Pipeline für Deployments
- Monitoring und Logging

## Zusätzliche Funktionen

- Theme-Switcher zwischen dunklem und hellem Modus
- Animierte UI-Elemente für bessere Benutzererfahrung
- Mehrsprachige Unterstützung (Deutsch als Standard, Englisch als Fallback)
- Cookie-Consent-Banner gemäß DSGVO
- Breadcrumbs für bessere Navigation

## Technische Details

### Discord-Bot
- Node.js-basierter Bot für Discord-Integration
- Rollensynchronisation
- Ticket-System-Integration
- Benachrichtigungen bei Bewerbungsänderungen

### WebSocket-Integration
- Echtzeit-Updates für Tickets
- Typing-Indikatoren
- Status-Aktualisierungen
- Benachrichtigungen

### Medienverarbeitung
- Bildoptimierung für Uploads
- Video-Thumbnail-Generierung
- Dateigrößenbeschränkungen
- Unterstützte Dateitypen

Diese Spezifikation soll als Grundlage für die Entwicklung der Minewache-Website dienen. Das Endergebnis soll eine moderne, benutzerfreundliche und funktionsreiche Plattform sein, die sowohl den Bedürfnissen des Minewache-Teams als auch der Community gerecht wird.
