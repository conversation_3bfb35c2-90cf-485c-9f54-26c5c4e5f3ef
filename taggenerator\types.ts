export interface Tag {
  text: string;
  id: string;
}

export interface CategorizedTagItem {
  category: string;
  tags: Tag[];
  id:string;
}

export interface VideoSummary {
  overallGerman: string;
  keyVisualElements: string[];
  detectedObjects: string[];
  detectedActions: string[];
  spokenNamesOrRoles: string[];
  dialogueThemes: string[];
}

export interface AnalysisResult {
  videoSummary: VideoSummary;
  tiktokHashtags: Tag[];
  instagramHashtags: Tag[];
  categorizedTags: CategorizedTagItem[];
}

// Expected structure from Gemini API
export interface GeminiResponseJson {
  videoSummary: VideoSummary;
  tiktokHashtags: string[];
  instagramHashtags: string[];
  categorizedTags: Array<{ category: string; tags: string[] }>;
}

export type LoadingPhase = 'idle' | 'reading' | 'analyzing' | 'configuringKey';

export type HashtagPreference = 'both' | 'tiktok' | 'instagram';