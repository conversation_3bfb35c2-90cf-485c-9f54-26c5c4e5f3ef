<?php

/**
 * UI Component Migration Script
 *
 * This script helps automate the migration from old UI components to new modern components.
 * Run this script from the Laravel root directory.
 *
 * Usage: php scripts/migrate-ui-components.php [--dry-run] [--file=path/to/file.blade.php]
 */

class UIComponentMigrator
{
    private $dryRun = false;
    private $specificFile = null;
    private $migratedFiles = [];
    private $errors = [];
    private $basePath;

    public function __construct($options = [])
    {
        $this->dryRun = $options['dry-run'] ?? false;
        $this->specificFile = $options['file'] ?? null;
        $this->basePath = $this->getBasePath();
    }

    private function getBasePath()
    {
        // Get the directory where this script is located
        $scriptDir = dirname(__FILE__);
        // Go up one level to get the Laravel root
        return dirname($scriptDir);
    }

    private function isAbsolutePath($path)
    {
        // Check for Windows absolute path (C:\ or \\)
        if (DIRECTORY_SEPARATOR === '\\') {
            return preg_match('/^[a-zA-Z]:\\\\/', $path) || substr($path, 0, 2) === '\\\\';
        }
        // Check for Unix absolute path (/)
        return substr($path, 0, 1) === '/';
    }

    public function migrate()
    {
        echo "🚀 Starting UI Component Migration\n";
        echo "Mode: " . ($this->dryRun ? "DRY RUN" : "LIVE MIGRATION") . "\n";
        echo "Base Path: " . $this->basePath . "\n\n";

        if ($this->specificFile) {
            $this->migrateFile($this->specificFile);
        } else {
            $this->migrateAllFiles();
        }

        $this->printSummary();
    }

    private function migrateAllFiles()
    {
        $viewsPath = $this->basePath . DIRECTORY_SEPARATOR . 'resources' . DIRECTORY_SEPARATOR . 'views';
        if (!is_dir($viewsPath)) {
            $this->errors[] = "Views directory not found: $viewsPath";
            return;
        }

        $files = $this->getBladeFiles($viewsPath);

        foreach ($files as $file) {
            // Skip already migrated files and component files
            if ($this->shouldSkipFile($file)) {
                continue;
            }

            $this->migrateFile($file);
        }
    }

    private function getBladeFiles($directory)
    {
        $files = [];
        $iterator = new RecursiveIteratorIterator(
            new RecursiveDirectoryIterator($directory)
        );

        foreach ($iterator as $file) {
            if ($file->isFile() && $file->getExtension() === 'php') {
                $files[] = $file->getPathname();
            }
        }

        return $files;
    }

    private function shouldSkipFile($file)
    {
        $skipPatterns = [
            '/components/',
            '/livewire/',
            '/vendor/',
            'modern-',
            'enhanced-theme-switcher',
            'ui-demo.blade.php'
        ];

        foreach ($skipPatterns as $pattern) {
            if (strpos($file, $pattern) !== false) {
                return true;
            }
        }

        return false;
    }

    private function migrateFile($filePath)
    {
        // Handle relative paths
        if (!$this->isAbsolutePath($filePath)) {
            $filePath = $this->basePath . DIRECTORY_SEPARATOR . $filePath;
        }

        if (!file_exists($filePath)) {
            $this->errors[] = "File not found: $filePath";
            return;
        }

        $content = file_get_contents($filePath);
        $originalContent = $content;

        // Apply migration patterns
        $content = $this->migrateThemeSwitcher($content);
        $content = $this->migrateCards($content);
        $content = $this->migrateButtons($content);
        $content = $this->migrateInputs($content);

        // Check if content changed
        if ($content !== $originalContent) {
            if (!$this->dryRun) {
                // Create backup
                $backupPath = $filePath . '.backup.' . date('Y-m-d-H-i-s');
                copy($filePath, $backupPath);

                // Write migrated content
                file_put_contents($filePath, $content);
            }

            $this->migratedFiles[] = $filePath;
            echo "✅ Migrated: " . str_replace($this->basePath, '', $filePath) . "\n";
        }
    }

    private function migrateThemeSwitcher($content)
    {
        // Replace old theme switcher with enhanced version
        $patterns = [
            '/<x-theme-switcher\s*([^>]*)\s*\/?>/' => '<x-enhanced-theme-switcher$1 type="simple" />',
        ];

        foreach ($patterns as $pattern => $replacement) {
            $content = preg_replace($pattern, $replacement, $content);
        }

        return $content;
    }

    private function migrateCards($content)
    {
        // Migrate glass divs to modern cards
        $patterns = [
            // Simple glass div
            '/<div\s+class="([^"]*glass[^"]*)"([^>]*)>(.*?)<\/div>/s' => function($matches) {
                $classes = $matches[1];
                $attributes = $matches[2];
                $innerContent = $matches[3];

                // Determine variant based on existing classes
                $variant = 'default';
                if (strpos($classes, 'shadow-2xl') !== false) {
                    $variant = 'elevated';
                } elseif (strpos($classes, 'border-2') !== false) {
                    $variant = 'outlined';
                }

                // Determine size based on padding
                $size = 'md';
                if (strpos($classes, 'p-8') !== false || strpos($classes, 'p-10') !== false) {
                    $size = 'lg';
                } elseif (strpos($classes, 'p-4') !== false) {
                    $size = 'sm';
                }

                return "<x-modern-card variant=\"$variant\" size=\"$size\"$attributes>$innerContent</x-modern-card>";
            }
        ];

        foreach ($patterns as $pattern => $replacement) {
            if (is_callable($replacement)) {
                $content = preg_replace_callback($pattern, $replacement, $content);
            } else {
                $content = preg_replace($pattern, $replacement, $content);
            }
        }

        return $content;
    }

    private function migrateButtons($content)
    {
        // Migrate standard buttons to modern buttons
        $patterns = [
            // Button with btn-primary class
            '/<button\s+([^>]*class="[^"]*btn-primary[^"]*"[^>]*)>(.*?)<\/button>/s' => function($matches) {
                $attributes = $matches[1];
                $innerContent = trim($matches[2]);

                // Extract size from classes
                $size = 'md';
                if (strpos($attributes, 'btn-sm') !== false) $size = 'sm';
                if (strpos($attributes, 'btn-lg') !== false) $size = 'lg';

                // Clean up attributes
                $attributes = preg_replace('/class="[^"]*"/', '', $attributes);
                $attributes = trim($attributes);

                return "<x-modern-button variant=\"primary\" size=\"$size\" $attributes>$innerContent</x-modern-button>";
            },

            // Anchor with btn classes
            '/<a\s+([^>]*class="[^"]*btn[^"]*"[^>]*)>(.*?)<\/a>/s' => function($matches) {
                $attributes = $matches[1];
                $innerContent = trim($matches[2]);

                // Extract href
                preg_match('/href="([^"]*)"/', $attributes, $hrefMatches);
                $href = $hrefMatches[1] ?? '#';

                // Determine variant
                $variant = 'primary';
                if (strpos($attributes, 'btn-secondary') !== false) $variant = 'secondary';
                if (strpos($attributes, 'btn-ghost') !== false) $variant = 'ghost';

                // Clean up attributes
                $attributes = preg_replace('/class="[^"]*"/', '', $attributes);
                $attributes = preg_replace('/href="[^"]*"/', '', $attributes);
                $attributes = trim($attributes);

                return "<x-modern-button variant=\"$variant\" href=\"$href\" $attributes>$innerContent</x-modern-button>";
            }
        ];

        foreach ($patterns as $pattern => $replacement) {
            if (is_callable($replacement)) {
                $content = preg_replace_callback($pattern, $replacement, $content);
            } else {
                $content = preg_replace($pattern, $replacement, $content);
            }
        }

        return $content;
    }

    private function migrateInputs($content)
    {
        // Migrate standard inputs to modern inputs
        $patterns = [
            // Input with input-bordered class
            '/<input\s+([^>]*class="[^"]*input-bordered[^"]*"[^>]*)>/s' => function($matches) {
                $attributes = $matches[1];

                // Extract common attributes
                preg_match('/type="([^"]*)"/', $attributes, $typeMatches);
                preg_match('/placeholder="([^"]*)"/', $attributes, $placeholderMatches);
                preg_match('/name="([^"]*)"/', $attributes, $nameMatches);

                $type = $typeMatches[1] ?? 'text';
                $placeholder = $placeholderMatches[1] ?? '';
                $name = $nameMatches[1] ?? '';

                // Clean up attributes
                $cleanAttributes = preg_replace('/class="[^"]*"/', '', $attributes);
                $cleanAttributes = preg_replace('/(type|placeholder|name)="[^"]*"/', '', $cleanAttributes);
                $cleanAttributes = trim($cleanAttributes);

                $props = [];
                $props[] = "type=\"$type\"";
                if ($name) $props[] = "name=\"$name\"";
                if ($placeholder) $props[] = "placeholder=\"$placeholder\"";
                $props[] = "variant=\"outlined\"";

                return "<x-modern-input " . implode(' ', $props) . " $cleanAttributes />";
            }
        ];

        foreach ($patterns as $pattern => $replacement) {
            if (is_callable($replacement)) {
                $content = preg_replace_callback($pattern, $replacement, $content);
            } else {
                $content = preg_replace($pattern, $replacement, $content);
            }
        }

        return $content;
    }

    private function printSummary()
    {
        echo "\n📊 Migration Summary\n";
        echo "==================\n";
        echo "Files migrated: " . count($this->migratedFiles) . "\n";
        echo "Errors: " . count($this->errors) . "\n";

        if (!empty($this->migratedFiles)) {
            echo "\n✅ Successfully migrated files:\n";
            foreach ($this->migratedFiles as $file) {
                echo "  - " . str_replace($this->basePath, '', $file) . "\n";
            }
        }

        if (!empty($this->errors)) {
            echo "\n❌ Errors:\n";
            foreach ($this->errors as $error) {
                echo "  - $error\n";
            }
        }

        if ($this->dryRun) {
            echo "\n💡 This was a dry run. No files were actually modified.\n";
            echo "   Run without --dry-run to apply changes.\n";
        } else {
            echo "\n💾 Backup files created with .backup.YYYY-MM-DD-HH-MM-SS extension\n";
        }

        echo "\n🎉 Migration complete!\n";
    }
}

// Parse command line arguments
$options = [];
$args = array_slice($argv, 1);

foreach ($args as $arg) {
    if ($arg === '--dry-run') {
        $options['dry-run'] = true;
    } elseif (strpos($arg, '--file=') === 0) {
        $options['file'] = substr($arg, 7);
    }
}

// Run migration
try {
    $migrator = new UIComponentMigrator($options);
    $migrator->migrate();
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
    exit(1);
}
