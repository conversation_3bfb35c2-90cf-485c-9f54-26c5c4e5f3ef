@if($showAiConsentPrompt)
<div class="mb-4 p-4 bg-blue-900/30 border border-blue-500/30 rounded-xl shadow-lg animate-fadeIn ai-consent-prompt">
    <div class="flex items-start gap-3">
        <div class="shrink-0">
            <div class="w-10 h-10 bg-blue-500/20 rounded-full flex items-center justify-center">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-blue-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z" />
                </svg>
            </div>
        </div>
        <div class="flex-1">
            <h3 class="text-base font-semibold text-white mb-1">{{ __('tickets.gemini_consent_title') }}</h3>
            <p class="text-sm text-blue-100 mb-3">
                {{ __('tickets.gemini_consent_description') }}
            </p>
            <div class="flex flex-wrap gap-2">
                <x-modern-button
                    wire:click="setGeminiConsent(false)"
                    variant="ghost"
                    size="sm"
                >
                    {{ __('tickets.gemini_consent_decline') }}
                </x-modern-button>
                <x-modern-button
                    wire:click="setGeminiConsent(true)"
                    variant="primary"
                    size="sm"
                >
                    {{ __('tickets.gemini_consent_accept') }}
                </x-modern-button>
            </div>
        </div>
    </div>
</div>
@endif