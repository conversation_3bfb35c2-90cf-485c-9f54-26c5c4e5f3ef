/**
 * <PERSON><PERSON> startet den Reverb-Server in einem separaten Fenster.
 */
import { exec } from 'child_process';
import path from 'path';
import { fileURLToPath } from 'url';

console.log('🚀 Starte Reverb-Server in einem separaten Fenster...');

// Pfad zum aktuellen Verzeichnis (ES Module Ersatz für __dirname)
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Pfad zum Projektverzeichnis
const projectPath = path.resolve(__dirname, '..');

try {
  // Starte Reverb in einem neuen Fenster
  const command = process.platform === 'win32'
    ? `start cmd.exe /K "cd ${projectPath} && php artisan reverb:start --host=127.0.0.1 --port=6001 --debug"`
    : `gnome-terminal -- bash -c "cd ${projectPath} && php artisan reverb:start --host=127.0.0.1 --port=6001 --debug; exec bash"`;

  exec(command, (error, stdout, stderr) => {
    if (error) {
      console.error(`❌ Fehler beim Starten des Reverb-Servers: ${error.message}`);
      return;
    }
    if (stderr) {
      console.error(`❌ Fehler: ${stderr}`);
      return;
    }
    console.log(`✅ Reverb-Server wurde in einem separaten Fenster gestartet.`);
  });
} catch (error) {
  console.error('❌ Fehler beim Starten des Reverb-Servers:', error.message);
}
