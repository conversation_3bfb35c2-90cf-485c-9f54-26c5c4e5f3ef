<?php

namespace App\Services;

use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;

/**
 * Discord Bot Service
 * 
 * This service provides a simplified interface for Discord bot status management.
 * It acts as a wrapper around the DiscordService for bot-specific operations.
 */
class DiscordBotService
{
    protected DiscordService $discordService;

    public function __construct()
    {
        $this->discordService = app(DiscordService::class);
    }

    /**
     * Get the Discord bot status
     *
     * @param bool $useCache Whether to use cached status (default: true)
     * @return array Bot status information
     */
    public function getStatus(bool $useCache = true): array
    {
        if (!$useCache) {
            // Clear cache to force fresh check
            Cache::forget('discord_bot_status');
        }

        return $this->discordService->getBotStatus();
    }

    /**
     * Check if the Discord bot is running
     *
     * @return bool True if the bot is running
     */
    public function isRunning(): bool
    {
        return $this->discordService->isBotRunning();
    }

    /**
     * Get cached bot status or return offline status
     *
     * @return array Bot status information
     */
    public function getCachedStatus(): array
    {
        $cacheKey = 'discord_bot_status';
        $cachedStatus = Cache::get($cacheKey);

        if ($cachedStatus) {
            return $cachedStatus;
        }

        // Return default offline status if no cache
        return [
            'online' => false,
            'uptime' => null,
            'last_check' => now(),
            'users_synced' => null,
            'error' => 'No cached status available'
        ];
    }

    /**
     * Force refresh the bot status
     *
     * @return array Fresh bot status information
     */
    public function refreshStatus(): array
    {
        Cache::forget('discord_bot_status');
        return $this->getStatus(false);
    }

    /**
     * Get bot uptime in a human-readable format
     *
     * @return string|null Formatted uptime or null if not available
     */
    public function getFormattedUptime(): ?string
    {
        $status = $this->getStatus();
        
        if (!isset($status['uptime']) || !$status['uptime']) {
            return null;
        }

        // If uptime is in seconds, convert to human readable format
        if (is_numeric($status['uptime'])) {
            $seconds = (int) $status['uptime'];
            $hours = floor($seconds / 3600);
            $minutes = floor(($seconds % 3600) / 60);
            $seconds = $seconds % 60;

            if ($hours > 0) {
                return sprintf('%dh %dm %ds', $hours, $minutes, $seconds);
            } elseif ($minutes > 0) {
                return sprintf('%dm %ds', $minutes, $seconds);
            } else {
                return sprintf('%ds', $seconds);
            }
        }

        // Return as-is if it's already formatted
        return $status['uptime'];
    }

    /**
     * Get a simple status indicator
     *
     * @return string 'online', 'offline', or 'unknown'
     */
    public function getStatusIndicator(): string
    {
        $status = $this->getStatus();
        
        if (isset($status['online'])) {
            return $status['online'] ? 'online' : 'offline';
        }

        return 'unknown';
    }

    /**
     * Get the last check timestamp
     *
     * @return \Carbon\Carbon|null
     */
    public function getLastCheck(): ?\Carbon\Carbon
    {
        $status = $this->getStatus();
        
        if (isset($status['last_check'])) {
            return $status['last_check'];
        }

        return null;
    }

    /**
     * Check if the bot status is stale (older than 5 minutes)
     *
     * @return bool True if status is stale
     */
    public function isStatusStale(): bool
    {
        $lastCheck = $this->getLastCheck();
        
        if (!$lastCheck) {
            return true;
        }

        return $lastCheck->diffInMinutes(now()) > 5;
    }
}
