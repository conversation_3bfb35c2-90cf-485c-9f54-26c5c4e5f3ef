@props([
    'variant' => 'default', // default, elevated, outlined, filled
    'size' => 'md', // sm, md, lg, xl
    'interactive' => false,
    'href' => null,
    'gradient' => false,
    'blur' => false
])

@php
$baseClasses = 'rounded-2xl transition-all duration-300 ease-out';

$variantClasses = match($variant) {
    'elevated' => 'glass shadow-2xl border border-base-300/20 hover:shadow-3xl hover:border-primary/30',
    'outlined' => 'border-2 border-base-300 hover:border-primary bg-base-100/50 backdrop-blur-sm',
    'filled' => 'bg-base-200 hover:bg-base-300/50',
    'gradient' => 'bg-gradient-to-br from-primary/10 via-secondary/5 to-accent/10 border border-primary/20',
    default => 'glass shadow-lg border border-base-300/10 hover:shadow-xl'
};

$sizeClasses = match($size) {
    'sm' => 'p-4',
    'md' => 'p-6',
    'lg' => 'p-8',
    'xl' => 'p-10',
    default => 'p-6'
};

$interactiveClasses = $interactive ? 'cursor-pointer hover:scale-[1.02] hover:-translate-y-1 active:scale-[0.98]' : '';

$blurClasses = $blur ? 'backdrop-blur-xl' : '';

$classes = trim("{$baseClasses} {$variantClasses} {$sizeClasses} {$interactiveClasses} {$blurClasses}");
@endphp

@if($href)
    <a href="{{ $href }}" {{ $attributes->merge(['class' => $classes]) }}>
        {{ $slot }}
    </a>
@else
    <div {{ $attributes->merge(['class' => $classes]) }}>
        {{ $slot }}
    </div>
@endif

<style>
    /* Enhanced shadow for elevated cards */
    .shadow-3xl {
        box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25), 0 0 0 1px rgba(255, 255, 255, 0.05);
    }
    
    [data-theme="minewache-light"] .shadow-3xl {
        box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.15), 0 0 0 1px rgba(0, 0, 0, 0.05);
    }
    
    /* Smooth hover transitions */
    .modern-card-hover {
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    }
    
    .modern-card-hover:hover {
        transform: translateY(-4px) scale(1.02);
    }
    
    /* Gradient overlays for enhanced visual appeal */
    .card-gradient-overlay {
        position: relative;
        overflow: hidden;
    }
    
    .card-gradient-overlay::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: linear-gradient(135deg, rgba(59, 130, 246, 0.1) 0%, rgba(139, 92, 246, 0.05) 100%);
        opacity: 0;
        transition: opacity 0.3s ease;
        pointer-events: none;
    }
    
    .card-gradient-overlay:hover::before {
        opacity: 1;
    }
    
    /* High contrast theme adjustments */
    [data-theme="minewache-high-contrast"] .glass {
        background: rgba(0, 0, 0, 0.9);
        border: 2px solid #0066ff;
    }
    
    [data-theme="minewache-high-contrast"] .border-base-300 {
        border-color: #ffffff !important;
    }
    
    /* Colorful theme enhancements */
    [data-theme="minewache-colorful"] .glass {
        background: linear-gradient(135deg, rgba(139, 92, 246, 0.1) 0%, rgba(6, 182, 212, 0.05) 50%, rgba(245, 158, 11, 0.1) 100%);
        border: 1px solid rgba(139, 92, 246, 0.2);
    }
    
    /* Reduced motion support */
    @media (prefers-reduced-motion: reduce) {
        .modern-card-hover {
            transition: none;
        }
        
        .modern-card-hover:hover {
            transform: none;
        }
    }
</style>
