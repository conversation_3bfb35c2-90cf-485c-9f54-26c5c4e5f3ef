<div>
    <!-- Dashboard Header mit Statistiken -->
    <div class="mb-8">
        <h1 class="text-3xl font-bold mb-6 text-primary">{{ __('admin.application_overview') }}</h1>

        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <!-- Statistik-Kacheln -->
            <div class="stat bg-white dark:bg-base-100 shadow-lg rounded-lg">
                <div class="stat-figure text-primary">
                    <x-heroicon-o-document-text class="w-8 h-8"/>
                </div>
                <div class="stat-title">{{ __('admin.statistics.total') }}</div>
                <div class="stat-value text-primary">{{ $totalApplications }}</div>
                <div class="stat-desc">{{ __('admin.statistics.total_applications') }}</div>
            </div>

            <div class="stat bg-white dark:bg-base-100 shadow-lg rounded-lg">
                <div class="stat-figure text-warning">
                    <x-heroicon-o-clock class="w-8 h-8"/>
                </div>
                <div class="stat-title">{{ __('admin.statistics.pending') }}</div>
                <div class="stat-value text-warning">{{ $pendingApplications }}</div>
                <div class="stat-desc">{{ __('admin.statistics.waiting_for_processing') }}</div>
            </div>

            <div class="stat bg-white dark:bg-base-100 shadow-lg rounded-lg">
                <div class="stat-figure text-success">
                    <x-heroicon-o-check-circle class="w-8 h-8"/>
                </div>
                <div class="stat-title">{{ __('admin.statistics.approved') }}</div>
                <div class="stat-value text-success">{{ $approvedApplications }}</div>
                <div class="stat-desc">{{ __('admin.statistics.approved_applications') }}</div>
            </div>

            <div class="stat bg-white dark:bg-base-100 shadow-lg rounded-lg">
                <div class="stat-figure text-error">
                    <x-heroicon-o-x-circle class="w-8 h-8"/>
                </div>
                <div class="stat-title">{{ __('admin.statistics.rejected') }}</div>
                <div class="stat-value text-error">{{ $rejectedApplications }}</div>
                <div class="stat-desc">{{ __('admin.statistics.rejected_applications') }}</div>
            </div>
        </div>
    </div>

    <!-- Filter und Suchbereich -->
    <div class="bg-white dark:bg-base-100 p-6 rounded-xl shadow-lg mb-6">
        <div class="mb-6">
            <x-modern-button variant="primary" href="{{ route('admin.dashboard') }}" ><svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18" />
                </svg>
                Zurück</x-modern-button>
        </div>
        <div class="flex flex-col md:flex-row gap-4">
            <!-- Suchfeld mit Verzögerung für bessere Performance -->
            <div class="form-control md:w-1/3">
                <div class="input-group">
                    <x-modern-input type="text" placeholder="{{ __('admin.search') }}" variant="outlined" wire:model.live.debounce.500ms="searchTerm"
                           
                           / />
                    <x-modern-button variant="primary" size="md" ><x-heroicon-o-magnifying-glass class="w-5 h-5"/></x-modern-button>
                </div>
            </div>

            <!-- Status-Filter -->
            <div class="form-control md:w-1/5">
                <select wire:model.live="status" class="select select-bordered w-full">
                    <option value="all">{{ __('admin.all_statuses') }}</option>
                    <option value="pending">{{ __('admin.statistics.pending') }}</option>
                    <option value="approved">{{ __('admin.statistics.approved') }}</option>
                    <option value="rejected">{{ __('admin.statistics.rejected') }}</option>
                </select>
            </div>

            <!-- Tätigkeits-Filter - dynamisch aus verfügbaren Berufen -->
            <div class="form-control md:w-1/5">
                <select wire:model.live="profession" class="select select-bordered w-full">
                    <option value="all">{{ __('admin.all_professions') }}</option>
                    @foreach($availableProfessions as $key => $label)
                        <option value="{{ $key }}">{{ $label }}</option>
                    @endforeach
                </select>
            </div>

            <!-- Reset-Button -->
            <button wire:click="resetFilters" class="btn btn-outline">
                <x-heroicon-o-x-mark class="w-5 h-5 mr-2"/>
                {{ __('admin.reset_filters') }}
            </button>
        </div>
    </div>

    <!-- Bewerbungstabelle -->
    <div class="bg-white dark:bg-base-100 rounded-xl shadow-lg overflow-hidden">
        <div class="overflow-x-auto">
            <table class="table w-full">
                <thead>
                <tr>
                    <!-- Datum -->
                    <th class="cursor-pointer" wire:click="sortBy('created_at')">
                        <div class="flex items-center">
                            {{ $sortableFields['created_at'] }}
                            @if($sortField === 'created_at')
                                @if($sortDirection === 'asc')
                                    <x-heroicon-o-chevron-up class="w-4 h-4 ml-1"/>
                                @else
                                    <x-heroicon-o-chevron-down class="w-4 h-4 ml-1"/>
                                @endif
                            @endif
                        </div>
                    </th>

                    <!-- Name -->
                    <th class="cursor-pointer" wire:click="sortBy('name')">
                        <div class="flex items-center">
                            {{ $sortableFields['name'] }}
                            @if($sortField === 'name')
                                @if($sortDirection === 'asc')
                                    <x-heroicon-o-chevron-up class="w-4 h-4 ml-1"/>
                                @else
                                    <x-heroicon-o-chevron-down class="w-4 h-4 ml-1"/>
                                @endif
                            @endif
                        </div>
                    </th>

                    <!-- Tätigkeiten (nicht sortierbar) -->
                    <th>{{ __('admin.professions') }}</th>

                    <!-- Status -->
                    <th class="cursor-pointer" wire:click="sortBy('status')">
                        <div class="flex items-center">
                            {{ $sortableFields['status'] }}
                            @if($sortField === 'status')
                                @if($sortDirection === 'asc')
                                    <x-heroicon-o-chevron-up class="w-4 h-4 ml-1"/>
                                @else
                                    <x-heroicon-o-chevron-down class="w-4 h-4 ml-1"/>
                                @endif
                            @endif
                        </div>
                    </th>

                    <!-- Aktionen (nicht sortierbar) -->
                    <th>{{ __('admin.actions') }}</th>
                </tr>
                </thead>
                <tbody>
                @forelse($applications as $application)
                    <tr class="hover">
                        <td>{{ $application->created_at->format('d.m.Y') }}</td>
                        <td>
                            <div class="font-medium">{{ $application->name }}</div>
                            <div
                                class="text-sm opacity-70">{{ $application->age }} {{ __('admin.years') }}</div>
                        </td>
                        <td>
                            <div class="flex flex-wrap gap-1">
                                @if(is_array($application->professions))
                                    @foreach($application->professions as $profession)
                                        <span class="badge badge-primary badge-sm">
                                                {{ $availableProfessions[$profession] ?? $profession }}
                                            </span>
                                    @endforeach
                                @endif
                            </div>
                        </td>
                        <td>
                            @if($application->status === 'approved')
                                <div class="badge badge-success gap-1">
                                    <x-heroicon-o-check class="w-4 h-4"/>
                                    {{ __('admin.statistics.approved') }}
                                </div>
                            @elseif($application->status === 'rejected')
                                <div class="badge badge-error gap-1">
                                    <x-heroicon-o-x-mark class="w-4 h-4"/>
                                    {{ __('admin.statistics.rejected') }}
                                </div>
                            @else
                                <div class="badge badge-warning gap-1">
                                    <x-heroicon-o-clock class="w-4 h-4"/>
                                    {{ __('admin.statistics.pending') }}
                                </div>
                            @endif
                        </td>
                        <td>
                            <div class="flex gap-2">
                                <button wire:click="viewApplication({{ $application->id }})"
                                        class="btn btn-sm btn-circle btn-ghost tooltip"
                                        data-tip="{{ __('admin.view_details') }}">
                                    <x-heroicon-o-eye class="w-5 h-5 text-primary"/>
                                </button>
                                <button wire:click="editApplication({{ $application->id }})"
                                        class="btn btn-sm btn-circle btn-ghost tooltip"
                                        data-tip="{{ __('admin.edit') }}">
                                    <x-heroicon-o-pencil-square class="w-5 h-5 text-secondary"/>
                                </button>
                            </div>
                        </td>
                    </tr>
                @empty
                    <tr>
                        <td colspan="5" class="text-center py-6">
                            <div class="flex flex-col items-center justify-center">
                                <x-heroicon-o-inbox class="w-12 h-12 text-base-300 mb-2"/>
                                <h3 class="text-lg font-medium">{{ __('admin.no_applications_found') }}</h3>
                                <p class="text-sm text-base-content/70">{{ __('admin.no_applications_matching') }}</p>
                            </div>
                        </td>
                    </tr>
                @endforelse
                </tbody>
            </table>
        </div>

        <!-- Pagination -->
        <div class="p-4">
            {{ $applications->links() }}
        </div>
    </div>
</div>
