<x-app-layout>
    <x-slot name="header">
        <h2 class="font-display text-xl md:text-2xl text-gray-900 dark:text-white leading-tight">
            {{ __('Bewerbungsverwaltung') }}
        </h2>
    </x-slot>

    <div class="py-12">
        <!-- Livewire-Komponente für die Bewerbungsverwaltung -->
        <livewire:application-manager />
    </div>

    <!-- Mit Livewire die interaktiven Elemente steuern -->
    @push('scripts')
        <script>
            document.addEventListener('livewire:init', () => {
                // Tooltips und andere UI-Elemente initialisieren
                Livewire.hook('morph.updated', ({ el }) => {
                    // UI-Elemente nach Livewire-Updates wieder initialisieren
                });
            });
        </script>
    @endpush
</x-app-layout>