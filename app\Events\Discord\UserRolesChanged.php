<?php

namespace App\Events\Discord;

use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

class UserRolesChanged
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    public string $discordUserId;
    public array $newRoles; // Array of role details, e.g., [['id' => 'role_id', 'name' => 'Role Name'], ...]
    public string $guildId;

    /**
     * Create a new event instance.
     *
     * @param string $discordUserId
     * @param array $newRoles Array of role details, e.g., [['id' => 'role_id', 'name' => 'Role Name'], ...]
     * @param string $guildId
     */
    public function __construct(string $discordUserId, array $newRoles, string $guildId)
    {
        $this->discordUserId = $discordUserId;
        $this->newRoles = $newRoles;
        $this->guildId = $guildId;
    }
}
