<x-app-layout>
    <x-slot name="header">
        <h2 class="font-display text-xl md:text-2xl text-gray-900 dark:text-white leading-tight">
            {{ __('Datenschutzanfrage Details') }}
        </h2>
    </x-slot>

    <div class="py-12">
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
            <div class="bg-white dark:bg-base-100 overflow-hidden shadow-sm rounded-lg">
                <div class="p-6">
                    <div class="flex justify-between items-center mb-6">
                        <h3 class="text-lg font-medium">Anfrage #{{ $deletionRequest->id }}</h3>
                        <x-modern-button variant="primary" href="{{ route('admin.data-requests.index') }}" >Zur<PERSON> zur Übersicht</x-modern-button>
                    </div>
                    
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div class="space-y-4">
                            <div class="bg-base-200 p-4 rounded-lg">
                                <h4 class="font-medium mb-2">Anfrage-Details</h4>
                                <div class="space-y-2">
                                    <div class="flex justify-between">
                                        <span class="text-base-content/70">Anfrageart:</span>
                                        <span>
                                            @if($deletionRequest->request_type === 'full_deletion')
                                                <span class="badge badge-error">Vollständige Löschung</span>
                                            @elseif($deletionRequest->request_type === 'partial_deletion')
                                                <span class="badge badge-warning">Teilweise Löschung</span>
                                            @elseif($deletionRequest->request_type === 'data_export')
                                                <span class="badge badge-info">Datenexport</span>
                                            @endif
                                        </span>
                                    </div>
                                    <div class="flex justify-between">
                                        <span class="text-base-content/70">Status:</span>
                                        <span>
                                            @if($deletionRequest->status === 'pending')
                                                <span class="badge badge-warning">Ausstehend</span>
                                            @elseif($deletionRequest->status === 'processing')
                                                <span class="badge badge-info">In Bearbeitung</span>
                                            @elseif($deletionRequest->status === 'completed')
                                                <span class="badge badge-success">Abgeschlossen</span>
                                            @elseif($deletionRequest->status === 'rejected')
                                                <span class="badge badge-error">Abgelehnt</span>
                                            @endif
                                        </span>
                                    </div>
                                    <div class="flex justify-between">
                                        <span class="text-base-content/70">Erstellt am:</span>
                                        <span>{{ $deletionRequest->created_at->format('d.m.Y H:i') }}</span>
                                    </div>
                                    @if($deletionRequest->processed_at)
                                    <div class="flex justify-between">
                                        <span class="text-base-content/70">Bearbeitet am:</span>
                                        <span>{{ $deletionRequest->processed_at->format('d.m.Y H:i') }}</span>
                                    </div>
                                    @endif
                                    @if($deletionRequest->processor)
                                    <div class="flex justify-between">
                                        <span class="text-base-content/70">Bearbeitet von:</span>
                                        <span>{{ $deletionRequest->processor->username }}</span>
                                    </div>
                                    @endif
                                </div>
                            </div>
                            
                            <div class="bg-base-200 p-4 rounded-lg">
                                <h4 class="font-medium mb-2">Benutzer-Details</h4>
                                @if($deletionRequest->user)
                                <div class="space-y-2">
                                    <div class="flex justify-between">
                                        <span class="text-base-content/70">Benutzername:</span>
                                        <span>{{ $deletionRequest->user->username }}</span>
                                    </div>
                                    <div class="flex justify-between">
                                        <span class="text-base-content/70">Discord ID:</span>
                                        <span>{{ $deletionRequest->user->id }}</span>
                                    </div>
                                    <div class="flex justify-between">
                                        <span class="text-base-content/70">Registriert seit:</span>
                                        <span>{{ $deletionRequest->user->created_at->format('d.m.Y') }}</span>
                                    </div>
                                </div>
                                @else
                                <p class="text-error">Benutzer wurde bereits gelöscht</p>
                                @endif
                            </div>
                        </div>
                        
                        <div class="space-y-4">
                            <div class="bg-base-200 p-4 rounded-lg">
                                <h4 class="font-medium mb-2">Anfrage-Details</h4>
                                <div class="whitespace-pre-wrap bg-base-100 p-3 rounded border border-base-300 min-h-[100px]">
                                    {{ $deletionRequest->request_details ?: 'Keine Details angegeben' }}
                                </div>
                            </div>
                            
                            @if($deletionRequest->status === 'pending')
                            <div class="bg-base-200 p-4 rounded-lg">
                                <h4 class="font-medium mb-4">Anfrage bearbeiten</h4>
                                
                                <form method="post" action="{{ route('admin.data-requests.process', $deletionRequest) }}" class="space-y-4">
                                    @csrf
                                    
                                    <div class="form-control">
                                        <label class="label cursor-pointer justify-start space-x-2">
                                            <input type="radio" name="action" value="approve" class="radio radio-success" checked />
                                            <span>Anfrage genehmigen und ausführen</span>
                                        </label>
                                    </div>
                                    
                                    <div class="form-control">
                                        <label class="label cursor-pointer justify-start space-x-2">
                                            <input type="radio" name="action" value="reject" class="radio radio-error" />
                                            <span>Anfrage ablehnen</span>
                                        </label>
                                    </div>
                                    
                                    <div class="form-control" id="rejection-reason-container" style="display: none;">
                                        <label for="rejection_reason" class="label">
                                            <span class="label-text">Ablehnungsgrund</span>
                                        </label>
                                        <textarea id="rejection_reason" name="rejection_reason" rows="3" class="textarea textarea-bordered"></textarea>
                                    </div>
                                    
                                    <div class="alert alert-warning">
                                        <svg xmlns="http://www.w3.org/2000/svg" class="stroke-current shrink-0 h-6 w-6" fill="none" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" /></svg>
                                        <span>Achtung: Das Genehmigen einer Löschanfrage führt zur sofortigen Löschung der Daten und kann nicht rückgängig gemacht werden!</span>
                                    </div>
                                    
                                    <div class="flex justify-end space-x-3">
                                        <x-modern-button variant="ghost" href="{{ route('admin.data-requests.index') }}" >Abbrechen</x-modern-button>
                                        <x-modern-button variant="primary" size="md" type="submit">Anfrage bearbeiten</x-modern-button>
                                    </div>
                                </form>
                            </div>
                            @endif
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    @push('scripts')
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const actionRadios = document.querySelectorAll('input[name="action"]');
            const rejectionContainer = document.getElementById('rejection-reason-container');
            
            actionRadios.forEach(radio => {
                radio.addEventListener('change', function() {
                    if (this.value === 'reject') {
                        rejectionContainer.style.display = 'block';
                    } else {
                        rejectionContainer.style.display = 'none';
                    }
                });
            });
        });
    </script>
    @endpush
</x-app-layout>
