<x-app-layout>
    <x-slot name="heading">
        Design System - Glassmorphism
    </x-slot>
    
    <x-slot name="breadcrumbs">
        <x-breadcrumbs :items="[
            ['label' => 'Design System', 'route' => 'design'],
            ['label' => 'Glassmorphism']
        ]" />
    </x-slot>

    <div class="container mx-auto px-4 py-12">
        <div class="max-w-5xl mx-auto">
            <!-- Introduction -->
            <x-glassmorphism-card class="mb-12">
                <h1 class="text-3xl font-bold mb-6">Glassmorphism Design System</h1>
                <p class="text-lg mb-4">
                    Unser Glassmorphismus-Design bietet eine moderne, elegante Ästhetik mit verbessertem Kontrast und Lesbarkeit.
                    Diese Komponenten können in der gesamten Anwendung verwendet werden, um ein konsistentes Erscheinungsbild zu gewährleisten.
                </p>
                <p>
                    Verwende die <code class="bg-base-300/30 px-2 py-1 rounded">x-glassmorphism-card</code> Komponente, um diesen Effekt in deinen Layouts zu implementieren.
                </p>
            </x-glassmorphism-card>
            
            <!-- Basic Usage -->
            <h2 class="text-2xl font-bold mb-4">Grundlegende Verwendung</h2>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-12">
                <div>
                    <x-glassmorphism-card>
                        <h3 class="text-xl font-bold mb-2">Standard Card</h3>
                        <p>Dies ist eine Standard-Glassmorphismus-Karte ohne spezielle Anpassungen.</p>
                    </x-glassmorphism-card>
                    
                    <div class="mt-4 bg-base-300/20 p-4 rounded-lg">
                        <pre class="text-sm overflow-x-auto"><code>&lt;x-glassmorphism-card&gt;
    &lt;h3 class="text-xl font-bold mb-2"&gt;Standard Card&lt;/h3&gt;
    &lt;p&gt;Dies ist eine Standard-Glassmorphismus-Karte ohne spezielle Anpassungen.&lt;/p&gt;
&lt;/x-glassmorphism-card&gt;</code></pre>
                    </div>
                </div>
                
                <div>
                    <x-glassmorphism-card border="primary-all" hover="true">
                        <h3 class="text-xl font-bold mb-2">Hervorgehobene Karte</h3>
                        <p>Diese Karte verwendet einen primären Rahmen und einen Hover-Effekt.</p>
                    </x-glassmorphism-card>
                    
                    <div class="mt-4 bg-base-300/20 p-4 rounded-lg">
                        <pre class="text-sm overflow-x-auto"><code>&lt;x-glassmorphism-card border="primary-all" hover="true"&gt;
    &lt;h3 class="text-xl font-bold mb-2"&gt;Hervorgehobene Karte&lt;/h3&gt;
    &lt;p&gt;Diese Karte verwendet einen primären Rahmen und einen Hover-Effekt.&lt;/p&gt;
&lt;/x-glassmorphism-card&gt;</code></pre>
                    </div>
                </div>
            </div>
            
            <!-- Opacity Levels -->
            <h2 class="text-2xl font-bold mb-4">Transparenzstufen</h2>
            <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-12">
                <div>
                    <x-glassmorphism-card level="light">
                        <h3 class="text-xl font-bold mb-2">Leicht</h3>
                        <p>Eine leichtere Variante mit höherer Transparenz.</p>
                    </x-glassmorphism-card>
                    
                    <div class="mt-4 bg-base-300/20 p-4 rounded-lg">
                        <pre class="text-sm overflow-x-auto"><code>&lt;x-glassmorphism-card level="light"&gt;
    &lt;h3&gt;Leicht&lt;/h3&gt;
    &lt;p&gt;Beschreibung&lt;/p&gt;
&lt;/x-glassmorphism-card&gt;</code></pre>
                    </div>
                </div>
                
                <div>
                    <x-glassmorphism-card level="default">
                        <h3 class="text-xl font-bold mb-2">Standard</h3>
                        <p>Die Standardvariante mit ausgewogener Transparenz.</p>
                    </x-glassmorphism-card>
                    
                    <div class="mt-4 bg-base-300/20 p-4 rounded-lg">
                        <pre class="text-sm overflow-x-auto"><code>&lt;x-glassmorphism-card level="default"&gt;
    &lt;h3&gt;Standard&lt;/h3&gt;
    &lt;p&gt;Beschreibung&lt;/p&gt;
&lt;/x-glassmorphism-card&gt;</code></pre>
                    </div>
                </div>
                
                <div>
                    <x-glassmorphism-card level="dark">
                        <h3 class="text-xl font-bold mb-2">Dunkel</h3>
                        <p>Eine dunklere Variante mit geringerer Transparenz für besseren Kontrast.</p>
                    </x-glassmorphism-card>
                    
                    <div class="mt-4 bg-base-300/20 p-4 rounded-lg">
                        <pre class="text-sm overflow-x-auto"><code>&lt;x-glassmorphism-card level="dark"&gt;
    &lt;h3&gt;Dunkel&lt;/h3&gt;
    &lt;p&gt;Beschreibung&lt;/p&gt;
&lt;/x-glassmorphism-card&gt;</code></pre>
                    </div>
                </div>
            </div>
            
            <!-- Border Styles -->
            <h2 class="text-2xl font-bold mb-4">Rahmenstile</h2>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-12">
                <div>
                    <x-glassmorphism-card border="primary">
                        <h3 class="text-xl font-bold mb-2">Primärer Rahmen (links)</h3>
                        <p>Ein linker Rahmen in der primären Farbe.</p>
                    </x-glassmorphism-card>
                    
                    <div class="mt-4 bg-base-300/20 p-4 rounded-lg">
                        <pre class="text-sm overflow-x-auto"><code>&lt;x-glassmorphism-card border="primary"&gt;
    &lt;h3&gt;Primärer Rahmen (links)&lt;/h3&gt;
    &lt;p&gt;Beschreibung&lt;/p&gt;
&lt;/x-glassmorphism-card&gt;</code></pre>
                    </div>
                </div>
                
                <div>
                    <x-glassmorphism-card border="secondary">
                        <h3 class="text-xl font-bold mb-2">Sekundärer Rahmen (links)</h3>
                        <p>Ein linker Rahmen in der sekundären Farbe.</p>
                    </x-glassmorphism-card>
                    
                    <div class="mt-4 bg-base-300/20 p-4 rounded-lg">
                        <pre class="text-sm overflow-x-auto"><code>&lt;x-glassmorphism-card border="secondary"&gt;
    &lt;h3&gt;Sekundärer Rahmen (links)&lt;/h3&gt;
    &lt;p&gt;Beschreibung&lt;/p&gt;
&lt;/x-glassmorphism-card&gt;</code></pre>
                    </div>
                </div>
                
                <div>
                    <x-glassmorphism-card border="primary-all">
                        <h3 class="text-xl font-bold mb-2">Primärer Rahmen (rundum)</h3>
                        <p>Ein Rahmen in der primären Farbe um die gesamte Karte.</p>
                    </x-glassmorphism-card>
                    
                    <div class="mt-4 bg-base-300/20 p-4 rounded-lg">
                        <pre class="text-sm overflow-x-auto"><code>&lt;x-glassmorphism-card border="primary-all"&gt;
    &lt;h3&gt;Primärer Rahmen (rundum)&lt;/h3&gt;
    &lt;p&gt;Beschreibung&lt;/p&gt;
&lt;/x-glassmorphism-card&gt;</code></pre>
                    </div>
                </div>
                
                <div>
                    <x-glassmorphism-card border="secondary-all">
                        <h3 class="text-xl font-bold mb-2">Sekundärer Rahmen (rundum)</h3>
                        <p>Ein Rahmen in der sekundären Farbe um die gesamte Karte.</p>
                    </x-glassmorphism-card>
                    
                    <div class="mt-4 bg-base-300/20 p-4 rounded-lg">
                        <pre class="text-sm overflow-x-auto"><code>&lt;x-glassmorphism-card border="secondary-all"&gt;
    &lt;h3&gt;Sekundärer Rahmen (rundum)&lt;/h3&gt;
    &lt;p&gt;Beschreibung&lt;/p&gt;
&lt;/x-glassmorphism-card&gt;</code></pre>
                    </div>
                </div>
            </div>
            
            <!-- Custom Styling -->
            <h2 class="text-2xl font-bold mb-4">Benutzerdefinierte Stile</h2>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-12">
                <div>
                    <x-glassmorphism-card padding="p-10" rounded="rounded-3xl" shadow="shadow-2xl">
                        <h3 class="text-xl font-bold mb-2">Benutzerdefinierte Abstände und Rundungen</h3>
                        <p>Diese Karte verwendet benutzerdefinierte Padding-, Border-Radius- und Schatten-Werte.</p>
                    </x-glassmorphism-card>
                    
                    <div class="mt-4 bg-base-300/20 p-4 rounded-lg">
                        <pre class="text-sm overflow-x-auto"><code>&lt;x-glassmorphism-card 
    padding="p-10" 
    rounded="rounded-3xl" 
    shadow="shadow-2xl"&gt;
    &lt;h3&gt;Benutzerdefinierte Abstände und Rundungen&lt;/h3&gt;
    &lt;p&gt;Beschreibung&lt;/p&gt;
&lt;/x-glassmorphism-card&gt;</code></pre>
                    </div>
                </div>
                
                <div>
                    <x-glassmorphism-card class="bg-gradient-to-br from-primary/10 to-secondary/10">
                        <h3 class="text-xl font-bold mb-2">Mit zusätzlichen Klassen</h3>
                        <p>Du kannst zusätzliche Klassen hinzufügen, um den Stil weiter anzupassen.</p>
                    </x-glassmorphism-card>
                    
                    <div class="mt-4 bg-base-300/20 p-4 rounded-lg">
                        <pre class="text-sm overflow-x-auto"><code>&lt;x-glassmorphism-card class="bg-gradient-to-br from-primary/10 to-secondary/10"&gt;
    &lt;h3&gt;Mit zusätzlichen Klassen&lt;/h3&gt;
    &lt;p&gt;Beschreibung&lt;/p&gt;
&lt;/x-glassmorphism-card&gt;</code></pre>
                    </div>
                </div>
            </div>
            
            <!-- Accessibility Considerations -->
            <h2 class="text-2xl font-bold mb-4">Barrierefreiheit</h2>
            <x-glassmorphism-card level="dark" class="mb-12">
                <h3 class="text-xl font-bold mb-4">Barrierefreiheitshinweise</h3>
                <ul class="list-disc pl-6 space-y-2">
                    <li>Verwende die dunklere Variante (<code>level="dark"</code>) für Textelemente, um ausreichenden Kontrast zu gewährleisten.</li>
                    <li>Stelle sicher, dass Text innerhalb der Glassmorphismus-Karten einen ausreichenden Kontrast zum Hintergrund hat (WCAG AA-konform).</li>
                    <li>Verwende semantisch korrekte HTML-Elemente innerhalb der Karten.</li>
                    <li>Füge bei Bedarf ARIA-Attribute hinzu, um die Zugänglichkeit zu verbessern.</li>
                    <li>Teste die Lesbarkeit auf verschiedenen Bildschirmgrößen und unter verschiedenen Lichtbedingungen.</li>
                </ul>
            </x-glassmorphism-card>
            
            <!-- Best Practices -->
            <h2 class="text-2xl font-bold mb-4">Best Practices</h2>
            <x-glassmorphism-card border="primary-all">
                <h3 class="text-xl font-bold mb-4">Empfehlungen für die Verwendung</h3>
                <ul class="list-disc pl-6 space-y-2">
                    <li>Verwende Glassmorphismus sparsam und gezielt, um wichtige Elemente hervorzuheben.</li>
                    <li>Achte auf ausreichenden Kontrast zwischen Text und Hintergrund.</li>
                    <li>Kombiniere Glassmorphismus mit subtilen Schatten und Rahmen für mehr Tiefe.</li>
                    <li>Verwende konsistente Transparenzstufen in ähnlichen Kontexten.</li>
                    <li>Berücksichtige die Leistung auf älteren Geräten, da Backdrop-Filter rechenintensiv sein kann.</li>
                    <li>Biete Fallbacks für Browser, die Backdrop-Filter nicht unterstützen.</li>
                </ul>
            </x-glassmorphism-card>
        </div>
    </div>
</x-app-layout>
