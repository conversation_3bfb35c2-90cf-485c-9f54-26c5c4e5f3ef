<?php if (isset($component)) { $__componentOriginal9ac128a9029c0e4701924bd2d73d7f54 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54 = $attributes; } ?>
<?php $component = App\View\Components\AppLayout::resolve([] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('app-layout'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\App\View\Components\AppLayout::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
     <?php $__env->slot('heading', null, []); ?> 
        <?php echo e(__('Branding')); ?>

     <?php $__env->endSlot(); ?>

     <?php $__env->slot('breadcrumbs', null, []); ?> 
        <?php if (isset($component)) { $__componentOriginal360d002b1b676b6f84d43220f22129e2 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal360d002b1b676b6f84d43220f22129e2 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.breadcrumbs','data' => ['items' => [
            ['label' => 'Branding']
        ]]] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('breadcrumbs'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['items' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute([
            ['label' => 'Branding']
        ])]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal360d002b1b676b6f84d43220f22129e2)): ?>
<?php $attributes = $__attributesOriginal360d002b1b676b6f84d43220f22129e2; ?>
<?php unset($__attributesOriginal360d002b1b676b6f84d43220f22129e2); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal360d002b1b676b6f84d43220f22129e2)): ?>
<?php $component = $__componentOriginal360d002b1b676b6f84d43220f22129e2; ?>
<?php unset($__componentOriginal360d002b1b676b6f84d43220f22129e2); ?>
<?php endif; ?>
     <?php $__env->endSlot(); ?>

    <main class="flex flex-col min-h-dvh w-full">
        <!-- Hero Section -->
        <section class="relative bg-base-200 py-16 md:py-24 lg:py-28 overflow-hidden rounded-xl md:rounded-2xl" aria-labelledby="hero-heading">
            <div class="container mx-auto px-4 md:px-6 lg:px-8 xl:max-w-[90%] 2xl:max-w-[85%] relative z-10">
                <div class="max-w-5xl mx-auto text-center">
                    <?php if (isset($component)) { $__componentOriginal6d96dd7d01ac0e896137f5280cb99f17 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal6d96dd7d01ac0e896137f5280cb99f17 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.glassmorphism-card','data' => ['level' => 'light','padding' => 'p-8 md:p-10','rounded' => 'rounded-3xl','shadow' => 'shadow-2xl','hover' => 'true']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('glassmorphism-card'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['level' => 'light','padding' => 'p-8 md:p-10','rounded' => 'rounded-3xl','shadow' => 'shadow-2xl','hover' => 'true']); ?>
                        <div class="inline-block bg-primary/15 px-4 py-2 rounded-full mb-4" aria-hidden="true">
                            <span class="text-primary font-medium text-sm">Offizielles Branding</span>
                        </div>
                        <h1 id="hero-heading" class="font-display font-bold text-base-content text-3xl md:text-4xl lg:text-5xl xl:text-6xl tracking-tight mb-6 leading-tight">
                            <span class="text-primary">Die Minewache</span> Branding
                        </h1>
                        <p class="font-modern text-base-content/90 text-lg md:text-xl max-w-3xl mx-auto mb-8 leading-relaxed">
                            Hier finden Sie alle Informationen und Ressourcen zum offiziellen Branding von "Die Minewache".
                        </p>
                     <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal6d96dd7d01ac0e896137f5280cb99f17)): ?>
<?php $attributes = $__attributesOriginal6d96dd7d01ac0e896137f5280cb99f17; ?>
<?php unset($__attributesOriginal6d96dd7d01ac0e896137f5280cb99f17); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal6d96dd7d01ac0e896137f5280cb99f17)): ?>
<?php $component = $__componentOriginal6d96dd7d01ac0e896137f5280cb99f17; ?>
<?php unset($__componentOriginal6d96dd7d01ac0e896137f5280cb99f17); ?>
<?php endif; ?>
                </div>
            </div>

            <!-- Decorative elements -->
            <div class="absolute top-0 left-0 w-full h-full overflow-hidden pointer-events-none" aria-hidden="true">
                <div class="absolute -top-24 -left-24 w-64 h-64 bg-primary/5 rounded-full blur-3xl"></div>
                <div class="absolute top-1/2 -right-32 w-80 h-80 bg-primary/5 rounded-full blur-3xl"></div>
                <div class="absolute -bottom-16 left-1/4 w-72 h-72 bg-primary/5 rounded-full blur-3xl"></div>
            </div>
        </section>

        <!-- Logo Section -->
        <section class="py-16 md:py-20 lg:py-24 bg-base-100 overflow-hidden rounded-xl md:rounded-2xl mt-8" aria-labelledby="logo-heading">
            <div class="container mx-auto px-4 md:px-6 lg:px-8 xl:max-w-[90%] 2xl:max-w-[85%]">
                <div class="text-center mb-12 slide-in-element" data-direction="top">
                    <?php if (isset($component)) { $__componentOriginal6d96dd7d01ac0e896137f5280cb99f17 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal6d96dd7d01ac0e896137f5280cb99f17 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.glassmorphism-card','data' => ['class' => 'inline-block max-w-3xl mx-auto','padding' => 'px-8 py-6','shadow' => 'shadow-xl','rounded' => 'rounded-2xl','hover' => 'true']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('glassmorphism-card'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['class' => 'inline-block max-w-3xl mx-auto','padding' => 'px-8 py-6','shadow' => 'shadow-xl','rounded' => 'rounded-2xl','hover' => 'true']); ?>
                        <h2 id="logo-heading" class="text-3xl md:text-4xl font-bold mb-4 text-base-content font-display">Logo</h2>
                        <p class="text-base-content/80 max-w-3xl mx-auto text-lg md:text-xl leading-relaxed font-modern">
                            Unser Logo ist ein wichtiger Bestandteil unserer Markenidentität. Bitte verwenden Sie es gemäß unseren Richtlinien.
                        </p>
                     <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal6d96dd7d01ac0e896137f5280cb99f17)): ?>
<?php $attributes = $__attributesOriginal6d96dd7d01ac0e896137f5280cb99f17; ?>
<?php unset($__attributesOriginal6d96dd7d01ac0e896137f5280cb99f17); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal6d96dd7d01ac0e896137f5280cb99f17)): ?>
<?php $component = $__componentOriginal6d96dd7d01ac0e896137f5280cb99f17; ?>
<?php unset($__componentOriginal6d96dd7d01ac0e896137f5280cb99f17); ?>
<?php endif; ?>
                </div>

                <div class="grid grid-cols-1 md:grid-cols-2 gap-8 md:gap-12 mt-12">
                    <!-- Primary Logo -->
                    <div class="bg-base-200 p-8 rounded-2xl flex flex-col items-center">
                        <div class="bg-white p-8 rounded-xl mb-6 w-full flex justify-center">
                            <?php if (isset($component)) { $__componentOriginal8892e718f3d0d7a916180885c6f012e7 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal8892e718f3d0d7a916180885c6f012e7 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.application-logo','data' => ['class' => 'w-48 h-48 text-primary']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('application-logo'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['class' => 'w-48 h-48 text-primary']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal8892e718f3d0d7a916180885c6f012e7)): ?>
<?php $attributes = $__attributesOriginal8892e718f3d0d7a916180885c6f012e7; ?>
<?php unset($__attributesOriginal8892e718f3d0d7a916180885c6f012e7); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal8892e718f3d0d7a916180885c6f012e7)): ?>
<?php $component = $__componentOriginal8892e718f3d0d7a916180885c6f012e7; ?>
<?php unset($__componentOriginal8892e718f3d0d7a916180885c6f012e7); ?>
<?php endif; ?>
                        </div>
                        <h3 class="text-xl font-bold mb-2">Primäres Logo</h3>
                        <p class="text-base-content/80 text-center mb-4">Das Hauptlogo von "Die Minewache" in der Primärfarbe.</p>
                        <?php if (isset($component)) { $__componentOriginal19987cd45c699eefead8b7a8ac222798 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal19987cd45c699eefead8b7a8ac222798 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.modern-button','data' => ['variant' => 'primary','href' => '#']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('modern-button'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['variant' => 'primary','href' => '#']); ?>Logo herunterladen <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal19987cd45c699eefead8b7a8ac222798)): ?>
<?php $attributes = $__attributesOriginal19987cd45c699eefead8b7a8ac222798; ?>
<?php unset($__attributesOriginal19987cd45c699eefead8b7a8ac222798); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal19987cd45c699eefead8b7a8ac222798)): ?>
<?php $component = $__componentOriginal19987cd45c699eefead8b7a8ac222798; ?>
<?php unset($__componentOriginal19987cd45c699eefead8b7a8ac222798); ?>
<?php endif; ?>
                    </div>

                    <!-- Logo with Text -->
                    <div class="bg-base-200 p-8 rounded-2xl flex flex-col items-center">
                        <div class="bg-white p-8 rounded-xl mb-6 w-full flex justify-center items-center">
                            <div class="flex items-center">
                                <?php if (isset($component)) { $__componentOriginal8892e718f3d0d7a916180885c6f012e7 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal8892e718f3d0d7a916180885c6f012e7 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.application-logo','data' => ['class' => 'w-24 h-24 text-primary']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('application-logo'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['class' => 'w-24 h-24 text-primary']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal8892e718f3d0d7a916180885c6f012e7)): ?>
<?php $attributes = $__attributesOriginal8892e718f3d0d7a916180885c6f012e7; ?>
<?php unset($__attributesOriginal8892e718f3d0d7a916180885c6f012e7); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal8892e718f3d0d7a916180885c6f012e7)): ?>
<?php $component = $__componentOriginal8892e718f3d0d7a916180885c6f012e7; ?>
<?php unset($__componentOriginal8892e718f3d0d7a916180885c6f012e7); ?>
<?php endif; ?>
                                <span class="ml-4 font-bold text-3xl text-slate-800">Die Minewache</span>
                            </div>
                        </div>
                        <h3 class="text-xl font-bold mb-2">Logo mit Text</h3>
                        <p class="text-base-content/80 text-center mb-4">Logo mit dem offiziellen Schriftzug "Die Minewache".</p>
                        <?php if (isset($component)) { $__componentOriginal19987cd45c699eefead8b7a8ac222798 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal19987cd45c699eefead8b7a8ac222798 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.modern-button','data' => ['variant' => 'primary','href' => '#']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('modern-button'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['variant' => 'primary','href' => '#']); ?>Logo mit Text herunterladen <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal19987cd45c699eefead8b7a8ac222798)): ?>
<?php $attributes = $__attributesOriginal19987cd45c699eefead8b7a8ac222798; ?>
<?php unset($__attributesOriginal19987cd45c699eefead8b7a8ac222798); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal19987cd45c699eefead8b7a8ac222798)): ?>
<?php $component = $__componentOriginal19987cd45c699eefead8b7a8ac222798; ?>
<?php unset($__componentOriginal19987cd45c699eefead8b7a8ac222798); ?>
<?php endif; ?>
                    </div>
                </div>
            </div>
        </section>

        <!-- Colors Section -->
        <section class="py-16 md:py-20 lg:py-24 bg-base-200 overflow-hidden rounded-xl md:rounded-2xl mt-8" aria-labelledby="colors-heading">
            <div class="container mx-auto px-4 md:px-6 lg:px-8 xl:max-w-[90%] 2xl:max-w-[85%]">
                <div class="text-center mb-12 slide-in-element" data-direction="top">
                    <?php if (isset($component)) { $__componentOriginal6d96dd7d01ac0e896137f5280cb99f17 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal6d96dd7d01ac0e896137f5280cb99f17 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.glassmorphism-card','data' => ['class' => 'inline-block max-w-3xl mx-auto','padding' => 'px-8 py-6','shadow' => 'shadow-xl','rounded' => 'rounded-2xl','hover' => 'true']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('glassmorphism-card'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['class' => 'inline-block max-w-3xl mx-auto','padding' => 'px-8 py-6','shadow' => 'shadow-xl','rounded' => 'rounded-2xl','hover' => 'true']); ?>
                        <h2 id="colors-heading" class="text-3xl md:text-4xl font-bold mb-4 text-base-content font-display">Farbpalette</h2>
                        <p class="text-base-content/80 max-w-3xl mx-auto text-lg md:text-xl leading-relaxed font-modern">
                            Unsere Markenfarben sind sorgfältig ausgewählt, um unsere Identität zu repräsentieren.
                        </p>
                     <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal6d96dd7d01ac0e896137f5280cb99f17)): ?>
<?php $attributes = $__attributesOriginal6d96dd7d01ac0e896137f5280cb99f17; ?>
<?php unset($__attributesOriginal6d96dd7d01ac0e896137f5280cb99f17); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal6d96dd7d01ac0e896137f5280cb99f17)): ?>
<?php $component = $__componentOriginal6d96dd7d01ac0e896137f5280cb99f17; ?>
<?php unset($__componentOriginal6d96dd7d01ac0e896137f5280cb99f17); ?>
<?php endif; ?>
                </div>

                <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6 mt-12">
                    <!-- Primary Color -->
                    <div class="rounded-xl overflow-hidden shadow-lg">
                        <div class="h-32 bg-primary"></div>
                        <div class="p-4 bg-base-100">
                            <h3 class="font-bold mb-1">Primärfarbe</h3>
                            <p class="text-sm text-base-content/70 mb-2">Blau (#3b82f6)</p>
                            <div class="flex gap-2">
                                <span class="text-xs bg-base-200 px-2 py-1 rounded">RGB: 59, 130, 246</span>
                                <span class="text-xs bg-base-200 px-2 py-1 rounded">HEX: #3b82f6</span>
                            </div>
                        </div>
                    </div>

                    <!-- Secondary Color -->
                    <div class="rounded-xl overflow-hidden shadow-lg">
                        <div class="h-32 bg-slate-800"></div>
                        <div class="p-4 bg-base-100">
                            <h3 class="font-bold mb-1">Sekundärfarbe</h3>
                            <p class="text-sm text-base-content/70 mb-2">Slate (#1e293b)</p>
                            <div class="flex gap-2">
                                <span class="text-xs bg-base-200 px-2 py-1 rounded">RGB: 30, 41, 59</span>
                                <span class="text-xs bg-base-200 px-2 py-1 rounded">HEX: #1e293b</span>
                            </div>
                        </div>
                    </div>

                    <!-- Accent Color -->
                    <div class="rounded-xl overflow-hidden shadow-lg">
                        <div class="h-32 bg-slate-50"></div>
                        <div class="p-4 bg-base-100">
                            <h3 class="font-bold mb-1">Akzentfarbe (Hell)</h3>
                            <p class="text-sm text-base-content/70 mb-2">Slate Light (#f8fafc)</p>
                            <div class="flex gap-2">
                                <span class="text-xs bg-base-200 px-2 py-1 rounded">RGB: 248, 250, 252</span>
                                <span class="text-xs bg-base-200 px-2 py-1 rounded">HEX: #f8fafc</span>
                            </div>
                        </div>
                    </div>

                    <!-- Success Color -->
                    <div class="rounded-xl overflow-hidden shadow-lg">
                        <div class="h-32 bg-green-500"></div>
                        <div class="p-4 bg-base-100">
                            <h3 class="font-bold mb-1">Erfolgsfarbe</h3>
                            <p class="text-sm text-base-content/70 mb-2">Grün (#22c55e)</p>
                            <div class="flex gap-2">
                                <span class="text-xs bg-base-200 px-2 py-1 rounded">RGB: 34, 197, 94</span>
                                <span class="text-xs bg-base-200 px-2 py-1 rounded">HEX: #22c55e</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Typography Section -->
        <section class="py-16 md:py-20 lg:py-24 bg-base-100 overflow-hidden rounded-xl md:rounded-2xl mt-8" aria-labelledby="typography-heading">
            <div class="container mx-auto px-4 md:px-6 lg:px-8 xl:max-w-[90%] 2xl:max-w-[85%]">
                <div class="text-center mb-12 slide-in-element" data-direction="top">
                    <?php if (isset($component)) { $__componentOriginal6d96dd7d01ac0e896137f5280cb99f17 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal6d96dd7d01ac0e896137f5280cb99f17 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.glassmorphism-card','data' => ['class' => 'inline-block max-w-3xl mx-auto','padding' => 'px-8 py-6','shadow' => 'shadow-xl','rounded' => 'rounded-2xl','hover' => 'true']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('glassmorphism-card'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['class' => 'inline-block max-w-3xl mx-auto','padding' => 'px-8 py-6','shadow' => 'shadow-xl','rounded' => 'rounded-2xl','hover' => 'true']); ?>
                        <h2 id="typography-heading" class="text-3xl md:text-4xl font-bold mb-4 text-base-content font-display">Typografie</h2>
                        <p class="text-base-content/80 max-w-3xl mx-auto text-lg md:text-xl leading-relaxed font-modern">
                            Unsere Schriftarten wurden ausgewählt, um Lesbarkeit und Modernität zu gewährleisten.
                        </p>
                     <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal6d96dd7d01ac0e896137f5280cb99f17)): ?>
<?php $attributes = $__attributesOriginal6d96dd7d01ac0e896137f5280cb99f17; ?>
<?php unset($__attributesOriginal6d96dd7d01ac0e896137f5280cb99f17); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal6d96dd7d01ac0e896137f5280cb99f17)): ?>
<?php $component = $__componentOriginal6d96dd7d01ac0e896137f5280cb99f17; ?>
<?php unset($__componentOriginal6d96dd7d01ac0e896137f5280cb99f17); ?>
<?php endif; ?>
                </div>

                <div class="grid grid-cols-1 md:grid-cols-2 gap-8 mt-12">
                    <!-- Primary Font -->
                    <?php if (isset($component)) { $__componentOriginal6d96dd7d01ac0e896137f5280cb99f17 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal6d96dd7d01ac0e896137f5280cb99f17 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.glassmorphism-card','data' => ['level' => 'light','padding' => 'p-6 md:p-8','rounded' => 'rounded-xl','shadow' => 'shadow-lg']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('glassmorphism-card'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['level' => 'light','padding' => 'p-6 md:p-8','rounded' => 'rounded-xl','shadow' => 'shadow-lg']); ?>
                        <h3 class="text-2xl font-bold mb-4">Geist Sans</h3>
                        <p class="mb-6 text-base-content/80">Unsere primäre Schriftart für Überschriften und Texte.</p>
                        
                        <div class="space-y-4">
                            <div>
                                <h4 class="text-sm font-medium text-base-content/60 mb-2">Überschrift 1</h4>
                                <p class="text-4xl font-bold font-display">Die Minewache</p>
                            </div>
                            
                            <div>
                                <h4 class="text-sm font-medium text-base-content/60 mb-2">Überschrift 2</h4>
                                <p class="text-3xl font-bold font-display">Die Minewache</p>
                            </div>
                            
                            <div>
                                <h4 class="text-sm font-medium text-base-content/60 mb-2">Überschrift 3</h4>
                                <p class="text-2xl font-bold font-display">Die Minewache</p>
                            </div>
                            
                            <div>
                                <h4 class="text-sm font-medium text-base-content/60 mb-2">Fließtext</h4>
                                <p class="text-base font-modern">Die Minewache ist eine beliebte Minecraft-Polizeiserie mit spannenden Verfolgungsjagden, Kriminalfällen und Rollenspiel im Minecraft-Universum.</p>
                            </div>
                        </div>
                     <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal6d96dd7d01ac0e896137f5280cb99f17)): ?>
<?php $attributes = $__attributesOriginal6d96dd7d01ac0e896137f5280cb99f17; ?>
<?php unset($__attributesOriginal6d96dd7d01ac0e896137f5280cb99f17); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal6d96dd7d01ac0e896137f5280cb99f17)): ?>
<?php $component = $__componentOriginal6d96dd7d01ac0e896137f5280cb99f17; ?>
<?php unset($__componentOriginal6d96dd7d01ac0e896137f5280cb99f17); ?>
<?php endif; ?>
                    
                    <!-- Secondary Font -->
                    <?php if (isset($component)) { $__componentOriginal6d96dd7d01ac0e896137f5280cb99f17 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal6d96dd7d01ac0e896137f5280cb99f17 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.glassmorphism-card','data' => ['level' => 'light','padding' => 'p-6 md:p-8','rounded' => 'rounded-xl','shadow' => 'shadow-lg']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('glassmorphism-card'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['level' => 'light','padding' => 'p-6 md:p-8','rounded' => 'rounded-xl','shadow' => 'shadow-lg']); ?>
                        <h3 class="text-2xl font-bold mb-4">Sora & Literata</h3>
                        <p class="mb-6 text-base-content/80">Unsere sekundären Schriftarten für spezielle Elemente.</p>
                        
                        <div class="space-y-4">
                            <div>
                                <h4 class="text-sm font-medium text-base-content/60 mb-2">Geometrisch (Sora)</h4>
                                <p class="text-2xl font-bold font-geometric">Die Minewache</p>
                            </div>
                            
                            <div>
                                <h4 class="text-sm font-medium text-base-content/60 mb-2">Serifenschrift (Literata)</h4>
                                <p class="text-2xl font-bold font-serif">Die Minewache</p>
                            </div>
                            
                            <div>
                                <h4 class="text-sm font-medium text-base-content/60 mb-2">Fließtext (Sora)</h4>
                                <p class="text-base font-geometric">Die Minewache ist eine beliebte Minecraft-Polizeiserie mit spannenden Verfolgungsjagden, Kriminalfällen und Rollenspiel im Minecraft-Universum.</p>
                            </div>
                            
                            <div>
                                <h4 class="text-sm font-medium text-base-content/60 mb-2">Fließtext (Literata)</h4>
                                <p class="text-base font-serif">Die Minewache ist eine beliebte Minecraft-Polizeiserie mit spannenden Verfolgungsjagden, Kriminalfällen und Rollenspiel im Minecraft-Universum.</p>
                            </div>
                        </div>
                     <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal6d96dd7d01ac0e896137f5280cb99f17)): ?>
<?php $attributes = $__attributesOriginal6d96dd7d01ac0e896137f5280cb99f17; ?>
<?php unset($__attributesOriginal6d96dd7d01ac0e896137f5280cb99f17); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal6d96dd7d01ac0e896137f5280cb99f17)): ?>
<?php $component = $__componentOriginal6d96dd7d01ac0e896137f5280cb99f17; ?>
<?php unset($__componentOriginal6d96dd7d01ac0e896137f5280cb99f17); ?>
<?php endif; ?>
                </div>
            </div>
        </section>

        <!-- Usage Guidelines Section -->
        <section class="py-16 md:py-20 lg:py-24 bg-base-200 overflow-hidden rounded-xl md:rounded-2xl mt-8" aria-labelledby="guidelines-heading">
            <div class="container mx-auto px-4 md:px-6 lg:px-8 xl:max-w-[90%] 2xl:max-w-[85%]">
                <div class="text-center mb-12 slide-in-element" data-direction="top">
                    <?php if (isset($component)) { $__componentOriginal6d96dd7d01ac0e896137f5280cb99f17 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal6d96dd7d01ac0e896137f5280cb99f17 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.glassmorphism-card','data' => ['class' => 'inline-block max-w-3xl mx-auto','padding' => 'px-8 py-6','shadow' => 'shadow-xl','rounded' => 'rounded-2xl','hover' => 'true']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('glassmorphism-card'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['class' => 'inline-block max-w-3xl mx-auto','padding' => 'px-8 py-6','shadow' => 'shadow-xl','rounded' => 'rounded-2xl','hover' => 'true']); ?>
                        <h2 id="guidelines-heading" class="text-3xl md:text-4xl font-bold mb-4 text-base-content font-display">Nutzungsrichtlinien</h2>
                        <p class="text-base-content/80 max-w-3xl mx-auto text-lg md:text-xl leading-relaxed font-modern">
                            Bitte beachten Sie diese Richtlinien bei der Verwendung unserer Markenelemente.
                        </p>
                     <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal6d96dd7d01ac0e896137f5280cb99f17)): ?>
<?php $attributes = $__attributesOriginal6d96dd7d01ac0e896137f5280cb99f17; ?>
<?php unset($__attributesOriginal6d96dd7d01ac0e896137f5280cb99f17); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal6d96dd7d01ac0e896137f5280cb99f17)): ?>
<?php $component = $__componentOriginal6d96dd7d01ac0e896137f5280cb99f17; ?>
<?php unset($__componentOriginal6d96dd7d01ac0e896137f5280cb99f17); ?>
<?php endif; ?>
                </div>

                <div class="grid grid-cols-1 md:grid-cols-2 gap-8 mt-12">
                    <!-- Do's -->
                    <?php if (isset($component)) { $__componentOriginal6d96dd7d01ac0e896137f5280cb99f17 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal6d96dd7d01ac0e896137f5280cb99f17 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.glassmorphism-card','data' => ['border' => 'primary-all','level' => 'light','padding' => 'p-6 md:p-8','rounded' => 'rounded-xl','shadow' => 'shadow-lg']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('glassmorphism-card'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['border' => 'primary-all','level' => 'light','padding' => 'p-6 md:p-8','rounded' => 'rounded-xl','shadow' => 'shadow-lg']); ?>
                        <h3 class="text-2xl font-bold mb-6 text-primary flex items-center">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-8 w-8 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
                            </svg>
                            Erlaubt
                        </h3>
                        
                        <ul class="space-y-4">
                            <li class="flex items-start">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-primary mr-2 flex-shrink-0" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
                                </svg>
                                <span>Verwenden Sie immer den vollständigen Namen "Die Minewache"</span>
                            </li>
                            <li class="flex items-start">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-primary mr-2 flex-shrink-0" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
                                </svg>
                                <span>Halten Sie einen Freiraum um das Logo ein</span>
                            </li>
                            <li class="flex items-start">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-primary mr-2 flex-shrink-0" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
                                </svg>
                                <span>Verwenden Sie die offiziellen Markenfarben</span>
                            </li>
                            <li class="flex items-start">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-primary mr-2 flex-shrink-0" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
                                </svg>
                                <span>Verwenden Sie das Logo in seiner ursprünglichen Form</span>
                            </li>
                            <li class="flex items-start">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-primary mr-2 flex-shrink-0" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
                                </svg>
                                <span>Kontaktieren Sie uns bei Fragen zur Verwendung</span>
                            </li>
                        </ul>
                     <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal6d96dd7d01ac0e896137f5280cb99f17)): ?>
<?php $attributes = $__attributesOriginal6d96dd7d01ac0e896137f5280cb99f17; ?>
<?php unset($__attributesOriginal6d96dd7d01ac0e896137f5280cb99f17); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal6d96dd7d01ac0e896137f5280cb99f17)): ?>
<?php $component = $__componentOriginal6d96dd7d01ac0e896137f5280cb99f17; ?>
<?php unset($__componentOriginal6d96dd7d01ac0e896137f5280cb99f17); ?>
<?php endif; ?>
                    
                    <!-- Don'ts -->
                    <?php if (isset($component)) { $__componentOriginal6d96dd7d01ac0e896137f5280cb99f17 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal6d96dd7d01ac0e896137f5280cb99f17 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.glassmorphism-card','data' => ['border' => 'error-all','level' => 'light','padding' => 'p-6 md:p-8','rounded' => 'rounded-xl','shadow' => 'shadow-lg']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('glassmorphism-card'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['border' => 'error-all','level' => 'light','padding' => 'p-6 md:p-8','rounded' => 'rounded-xl','shadow' => 'shadow-lg']); ?>
                        <h3 class="text-2xl font-bold mb-6 text-error flex items-center">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-8 w-8 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                            </svg>
                            Nicht erlaubt
                        </h3>
                        
                        <ul class="space-y-4">
                            <li class="flex items-start">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-error mr-2 flex-shrink-0" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                                </svg>
                                <span>Verwenden Sie nicht nur "Minewache" ohne "Die"</span>
                            </li>
                            <li class="flex items-start">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-error mr-2 flex-shrink-0" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                                </svg>
                                <span>Verzerren oder verformen Sie nicht das Logo</span>
                            </li>
                            <li class="flex items-start">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-error mr-2 flex-shrink-0" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                                </svg>
                                <span>Ändern Sie nicht die Farben des Logos</span>
                            </li>
                            <li class="flex items-start">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-error mr-2 flex-shrink-0" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                                </svg>
                                <span>Verwenden Sie das Logo nicht für unautorisierte Produkte</span>
                            </li>
                            <li class="flex items-start">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-error mr-2 flex-shrink-0" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                                </svg>
                                <span>Platzieren Sie das Logo nicht auf störenden Hintergründen</span>
                            </li>
                        </ul>
                     <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal6d96dd7d01ac0e896137f5280cb99f17)): ?>
<?php $attributes = $__attributesOriginal6d96dd7d01ac0e896137f5280cb99f17; ?>
<?php unset($__attributesOriginal6d96dd7d01ac0e896137f5280cb99f17); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal6d96dd7d01ac0e896137f5280cb99f17)): ?>
<?php $component = $__componentOriginal6d96dd7d01ac0e896137f5280cb99f17; ?>
<?php unset($__componentOriginal6d96dd7d01ac0e896137f5280cb99f17); ?>
<?php endif; ?>
                </div>
            </div>
        </section>

        <!-- Contact Section -->
        <section class="py-16 md:py-20 lg:py-24 bg-base-100 overflow-hidden rounded-xl md:rounded-2xl mt-8 mb-8" aria-labelledby="contact-heading">
            <div class="container mx-auto px-4 md:px-6 lg:px-8 xl:max-w-[90%] 2xl:max-w-[85%]">
                <div class="max-w-3xl mx-auto text-center">
                    <?php if (isset($component)) { $__componentOriginal6d96dd7d01ac0e896137f5280cb99f17 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal6d96dd7d01ac0e896137f5280cb99f17 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.glassmorphism-card','data' => ['level' => 'light','padding' => 'p-8 md:p-10','rounded' => 'rounded-xl','shadow' => 'shadow-xl']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('glassmorphism-card'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['level' => 'light','padding' => 'p-8 md:p-10','rounded' => 'rounded-xl','shadow' => 'shadow-xl']); ?>
                        <h2 id="contact-heading" class="text-2xl md:text-3xl font-bold mb-4 text-base-content">Kontakt</h2>
                        <p class="text-base-content/80 mb-6">
                            Haben Sie Fragen zur Verwendung unseres Brandings? Kontaktieren Sie uns gerne.
                        </p>
                        <?php if (isset($component)) { $__componentOriginal19987cd45c699eefead8b7a8ac222798 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal19987cd45c699eefead8b7a8ac222798 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.modern-button','data' => ['variant' => 'primary','href' => 'mailto:<EMAIL>']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('modern-button'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['variant' => 'primary','href' => 'mailto:<EMAIL>']); ?><svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                            </svg>
                            Kontakt aufnehmen <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal19987cd45c699eefead8b7a8ac222798)): ?>
<?php $attributes = $__attributesOriginal19987cd45c699eefead8b7a8ac222798; ?>
<?php unset($__attributesOriginal19987cd45c699eefead8b7a8ac222798); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal19987cd45c699eefead8b7a8ac222798)): ?>
<?php $component = $__componentOriginal19987cd45c699eefead8b7a8ac222798; ?>
<?php unset($__componentOriginal19987cd45c699eefead8b7a8ac222798); ?>
<?php endif; ?>
                     <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal6d96dd7d01ac0e896137f5280cb99f17)): ?>
<?php $attributes = $__attributesOriginal6d96dd7d01ac0e896137f5280cb99f17; ?>
<?php unset($__attributesOriginal6d96dd7d01ac0e896137f5280cb99f17); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal6d96dd7d01ac0e896137f5280cb99f17)): ?>
<?php $component = $__componentOriginal6d96dd7d01ac0e896137f5280cb99f17; ?>
<?php unset($__componentOriginal6d96dd7d01ac0e896137f5280cb99f17); ?>
<?php endif; ?>
                </div>
            </div>
        </section>
    </main>

    <style>
        /* Custom styles for this page */
        .border-error-all {
            border: 2px solid var(--color-error);
        }
    </style>
 <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54)): ?>
<?php $attributes = $__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54; ?>
<?php unset($__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal9ac128a9029c0e4701924bd2d73d7f54)): ?>
<?php $component = $__componentOriginal9ac128a9029c0e4701924bd2d73d7f54; ?>
<?php unset($__componentOriginal9ac128a9029c0e4701924bd2d73d7f54); ?>
<?php endif; ?>
<?php /**PATH C:\Users\<USER>\Documents\GitHub\minewache-website\resources\views/branding.blade.php ENDPATH**/ ?>