<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Storage;

class TicketAttachment extends Model
{
    use HasFactory;

    protected $fillable = [
        'ticket_message_id',
        'filename',
        'original_filename',
        'file_path',
        'processed_file_path',
        'thumbnail_path',
        'mime_type',
        'media_type',
        'media_metadata',
        'processing_status',
        'processing_error',
        'file_size',
        'discord_attachment_id',
        'is_from_discord',
    ];

    protected $casts = [
        'is_from_discord' => 'boolean',
        'file_size' => 'integer',
        'media_metadata' => 'array',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    /**
     * Relationship to the message
     */
    public function message()
    {
        return $this->belongsTo(TicketMessage::class, 'ticket_message_id');
    }

    /**
     * Get the full storage path
     */
    public function getFullPathAttribute()
    {
        return storage_path('app/' . $this->file_path);
    }

    /**
     * Get the download URL
     */
    public function getDownloadUrlAttribute()
    {
        return route('tickets.attachments.download', $this->id);
    }

    /**
     * Get the processed file URL if available, otherwise return the original download URL
     */
    public function getMediaUrlAttribute()
    {
        if ($this->processed_file_path && Storage::exists($this->processed_file_path)) {
            return Storage::url($this->processed_file_path);
        }

        return $this->download_url;
    }

    /**
     * Get the thumbnail URL if available
     */
    public function getThumbnailUrlAttribute()
    {
        if ($this->thumbnail_path && Storage::exists($this->thumbnail_path)) {
            return Storage::url($this->thumbnail_path);
        }

        return null;
    }

    /**
     * Check if the attachment is an image
     */
    public function getIsImageAttribute()
    {
        return $this->media_type === 'image' ||
               str_starts_with($this->mime_type, 'image/');
    }

    /**
     * Check if the attachment is a video
     */
    public function getIsVideoAttribute()
    {
        return $this->media_type === 'video' ||
               str_starts_with($this->mime_type, 'video/');
    }

    /**
     * Check if the attachment is an audio file
     */
    public function getIsAudioAttribute()
    {
        return $this->media_type === 'audio' ||
               str_starts_with($this->mime_type, 'audio/');
    }

    /**
     * Check if the attachment is a PDF
     */
    public function getIsPdfAttribute()
    {
        return $this->media_type === 'pdf' ||
               $this->mime_type === 'application/pdf';
    }

    /**
     * Check if the attachment is a document (not image, video, audio, or PDF)
     */
    public function getIsDocumentAttribute()
    {
        return !$this->is_image && !$this->is_video && !$this->is_audio && !$this->is_pdf;
    }

    /**
     * Check if the attachment has been processed
     */
    public function getIsProcessedAttribute()
    {
        return $this->processing_status === 'completed';
    }

    /**
     * Check if the attachment is currently being processed
     */
    public function getIsProcessingAttribute()
    {
        return $this->processing_status === 'processing';
    }

    /**
     * Check if the attachment processing has failed
     */
    public function getHasProcessingFailedAttribute()
    {
        return $this->processing_status === 'failed';
    }

    /**
     * Check if the attachment is pending processing
     */
    public function getIsPendingAttribute()
    {
        return $this->processing_status === 'pending';
    }
}
