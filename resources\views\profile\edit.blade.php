<x-app-layout>
    <x-slot:heading>
        Profile
    </x-slot:heading>
    <x-slot name="header">
        <h2 class="font-display text-xl md:text-2xl text-base-content leading-tight">
            {{ __('Dein Profil') }}
        </h2>
    </x-slot>

    <div class="py-12">
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8 space-y-8">
            <div class="p-6 sm:p-8 bg-base-200 shadow-lg rounded-box transition-all duration-300 hover:shadow-xl">
                <div class="flex flex-col md:flex-row md:items-center gap-6 mb-8">
                    <div class="avatar">
                        <div class="w-32 h-32 rounded-full ring ring-primary ring-offset-base-100 ring-offset-2 overflow-hidden transition-all duration-300 hover:ring-primary-focus hover:scale-105">
                            <img class="object-cover w-full h-full" src="{{ Auth::user()->getAvatar(['extension' => 'webp', 'size' => 256]) }}" alt="{{ Auth::user()->getTagAttribute() }}" />
                        </div>
                    </div>
                    <div class="flex flex-col">
                        <h3 class="text-2xl font-bold text-base-content">{{ Auth::user()->global_name ?: Auth::user()->username }}</h3>
                        <p class="text-base text-base-content/70">{{ '@' . Auth::user()->username }}</p>
                        @can('MINEWACHE_TEAM')
                            <div class="badge badge-warning gap-2 mt-2">
                                <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="w-4 h-4">
                                    <path stroke-linecap="round" stroke-linejoin="round" d="M9 12.75L11.25 15 15 9.75M21 12c0 1.268-.63 2.39-1.593 3.068a3.745 3.745 0 01-1.043 3.296 3.745 3.745 0 01-3.296 1.043A3.745 3.745 0 0112 21c-1.268 0-2.39-.63-3.068-1.593a3.746 3.746 0 01-3.296-1.043 3.745 3.745 0 01-1.043-3.296A3.745 3.745 0 013 12c0-1.268.63-2.39 1.593-3.068a3.745 3.745 0 011.043-3.296 3.746 3.746 0 013.296-1.043A3.746 3.746 0 0112 3c1.268 0 2.39.63 3.068 1.593a3.746 3.746 0 013.296 1.043 3.746 3.746 0 011.043 3.296A3.745 3.745 0 0121 12z" />
                                </svg>
                                Minewache Team
                            </div>
                        @endif
                        <div class="mt-3">
                            <span class="text-sm text-base-content/70">Mitglied seit {{ Auth::user()->created_at->format('d.m.Y') }}</span>
                        </div>
                    </div>
                </div>
                <div class="divider"></div>
                <div class="max-w-xl mx-auto">
                    @include('profile.partials.update-profile-information-form')
                </div>
            </div>

            <!-- Bewerbungsbereich (Hervorgehoben) -->
            <div class="p-6 sm:p-8 bg-primary/10 shadow-lg rounded-box transition-all duration-300 hover:shadow-xl border-2 border-primary/30">
                <div class="max-w-xl mx-auto">
                    <header class="flex items-start gap-4 mb-6">
                        <div class="p-3 bg-primary/20 rounded-lg">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-8 w-8 text-primary" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                            </svg>
                        </div>
                        <div>
                            <h2 class="text-xl font-semibold text-base-content">
                                {{ __('navigation.apply') }}
                            </h2>
                            <p class="text-base-content/70 mt-1">
                                Werde Teil des Minewache-Teams und bewirb dich jetzt für eine Position!
                            </p>
                        </div>
                    </header>

                    <div class="flex flex-col sm:flex-row gap-4 justify-between items-center">
                        <div>
                            <p class="text-base-content mb-2">Wir suchen regelmäßig nach neuen Talenten für verschiedene Positionen.</p>
                            <p class="text-base-content/70 text-sm">Schauspieler, Builder, Entwickler und mehr - finde deinen Platz im Team!</p>
                        </div>
                        <x-modern-button variant="primary" href="{{ route('bewerben') }}" ><svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                            </svg>
                            Jetzt bewerben</x-modern-button>
                    </div>

                    <div class="mt-6 pt-4 border-t border-primary/20">
                        <x-modern-button variant="ghost" href="{{ route('my.applications') }}" ><svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
                            </svg>
                            Meine Bewerbungen anzeigen</x-modern-button>
                    </div>
                </div>
            </div>

            <!-- KI-Datenzugriff -->
            <div class="p-6 sm:p-8 bg-blue-900/20 shadow-lg rounded-box transition-all duration-300 hover:shadow-xl border border-blue-500/30">
                <div class="max-w-xl mx-auto">
                    @livewire('user-data-consent-settings')
                </div>
            </div>

            <div class="p-6 sm:p-8 bg-base-200 shadow-lg rounded-box transition-all duration-300 hover:shadow-xl">
                <div class="max-w-xl mx-auto">
                    <section>
                        <header class="flex items-start gap-3">
                            <div class="p-2 bg-info/20 rounded-lg">
                                <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="w-6 h-6 text-info">
                                    <path stroke-linecap="round" stroke-linejoin="round" d="M9 12.75L11.25 15 15 9.75m-3-7.036A11.959 11.959 0 013.598 6 11.99 11.99 0 003 9.749c0 5.592 3.824 10.29 9 11.623 5.176-1.332 9-6.03 9-11.622 0-1.31-.21-2.571-.598-3.751h-.152c-3.196 0-6.1-1.248-8.25-3.285z" />
                                </svg>
                            </div>
                            <div>
                                <h2 class="text-xl font-semibold text-base-content">
                                    {{ __('Datenschutzrechte') }}
                                </h2>
                                <p class="mt-1 text-sm text-base-content/70">
                                    {{ __('Hier kannst du deine Datenschutzrechte gemäß DSGVO ausüben, einschließlich Datenexport und Löschung.') }}
                                </p>
                            </div>
                        </header>

                        <div class="mt-6 flex justify-center md:justify-start">
                            <x-modern-button variant="primary" href="{{ route('profile.data') }}" ><svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="w-5 h-5">
                                    <path stroke-linecap="round" stroke-linejoin="round" d="M19.5 14.25v-2.625a3.375 3.375 0 00-3.375-3.375h-1.5A1.125 1.125 0 0113.5 7.125v-1.5a3.375 3.375 0 00-3.375-3.375H8.25m6.75 12l-3-3m0 0l-3 3m3-3v6m-1.5-15H5.625c-.621 0-1.125.504-1.125 1.125v17.25c0 .621.504 1.125 1.125 1.125h12.75c.621 0 1.125-.504 1.125-1.125V11.25a9 9 0 00-9-9z" />
                                </svg>
                                {{ __('Datenschutzrechte ausüben') }}</x-modern-button>
                        </div>
                    </section>
                </div>
            </div>

            <div class="p-6 sm:p-8 bg-base-200 shadow-lg rounded-box transition-all duration-300 hover:shadow-xl">
                <div class="max-w-xl mx-auto">
                    @include('profile.partials.delete-user-form')
                </div>
            </div>
        </div>
    </div>

</x-app-layout>
