<x-app-layout>
<div class="container mx-auto px-4 py-8">
    <div class="bg-white dark:bg-gray-800 shadow-md rounded-lg p-6">
        <h1 class="text-2xl font-bold mb-6">WebSocket Debug</h1>

        <div class="mb-6">
            <h2 class="text-xl font-semibold mb-2">Connection Status</h2>
            <div class="flex items-center mb-2">
                <span class="mr-2">Status:</span>
                <span id="connection-status" class="px-2 py-1 rounded text-white bg-gray-500">Checking...</span>
            </div>
            <div class="flex items-center mb-2">
                <span class="mr-2">Connection ID:</span>
                <span id="connection-id">-</span>
            </div>
            <div class="flex items-center">
                <span class="mr-2">Socket ID:</span>
                <span id="socket-id">-</span>
            </div>
        </div>

        <div class="mb-6">
            <h2 class="text-xl font-semibold mb-2">Echo Configuration</h2>
            <pre id="echo-config" class="bg-gray-100 dark:bg-gray-700 p-4 rounded overflow-auto max-h-60"></pre>
        </div>

        <div class="mb-6">
            <h2 class="text-xl font-semibold mb-2">Channel Subscription</h2>
            <div class="mb-4">
                <div class="flex items-center mb-2">
                    <input type="text" id="channel-name" class="border rounded p-2 mr-2 flex-grow dark:bg-gray-700 dark:border-gray-600" placeholder="Channel name (e.g. tickets.1)" value="tickets.{{ auth()->user()->tickets()->latest()->first()->id ?? '26' }}">
                    <button id="subscribe-btn" class="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded">Subscribe</button>
                </div>
                <div class="text-xs text-gray-500 mt-1 mb-2">
                    <p>Available tickets:
                        @php
                            $userTickets = auth()->user()->tickets()->latest()->get();
                            $assignedTickets = auth()->user()->assignedTickets()->latest()->get();
                            $allTickets = $userTickets->merge($assignedTickets)->unique('id');
                        @endphp
                        @forelse($allTickets as $ticket)
                            <span class="inline-block bg-gray-200 dark:bg-gray-700 rounded px-2 py-1 text-xs font-semibold mr-1 mb-1 cursor-pointer hover:bg-gray-300 dark:hover:bg-gray-600" onclick="document.getElementById('channel-name').value = 'tickets.{{ $ticket->id }}'">{{ $ticket->id }}</span>
                        @empty
                            <span class="text-gray-500">No tickets found</span>
                        @endforelse
                    </p>
                </div>
            </div>
            <div class="flex items-center mb-2">
                <span class="mr-2">Subscription Status:</span>
                <span id="subscription-status" class="px-2 py-1 rounded text-white bg-gray-500">Not Subscribed</span>
            </div>
        </div>

        <div class="mb-6">
            <h2 class="text-xl font-semibold mb-2">Event Listener</h2>
            <div class="flex items-center mb-4">
                <input type="text" id="event-name" class="border rounded p-2 mr-2 flex-grow dark:bg-gray-700 dark:border-gray-600" placeholder="Event name (e.g. .new-message)" value=".new-message">
                <button id="listen-btn" class="bg-green-500 hover:bg-green-600 text-white px-4 py-2 rounded">Listen</button>
            </div>
            <div class="flex items-center mb-2">
                <span class="mr-2">Listening Status:</span>
                <span id="listening-status" class="px-2 py-1 rounded text-white bg-gray-500">Not Listening</span>
            </div>
        </div>

        <div>
            <h2 class="text-xl font-semibold mb-2">Event Log</h2>
            <div id="event-log" class="bg-gray-100 dark:bg-gray-700 p-4 rounded h-60 overflow-auto"></div>
        </div>
    </div>
</div>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        const connectionStatus = document.getElementById('connection-status');
        const connectionId = document.getElementById('connection-id');
        const socketId = document.getElementById('socket-id');
        const echoConfig = document.getElementById('echo-config');
        const channelName = document.getElementById('channel-name');
        const subscribeBtn = document.getElementById('subscribe-btn');
        const subscriptionStatus = document.getElementById('subscription-status');
        const eventName = document.getElementById('event-name');
        const listenBtn = document.getElementById('listen-btn');
        const listeningStatus = document.getElementById('listening-status');
        const eventLog = document.getElementById('event-log');

        let channel = null;

        // Log function
        function log(message, type = 'info') {
            const timestamp = new Date().toISOString().split('T')[1].split('.')[0];
            const entry = document.createElement('div');
            entry.className = `mb-1 ${type === 'error' ? 'text-red-500' : type === 'success' ? 'text-green-500' : 'text-gray-700 dark:text-gray-300'}`;
            entry.textContent = `[${timestamp}] ${message}`;
            eventLog.appendChild(entry);
            eventLog.scrollTop = eventLog.scrollHeight;

            // Also log to console
            if (type === 'error') {
                console.error(message);
            } else if (type === 'success') {
                console.log('%c' + message, 'color: green');
            } else {
                console.log(message);
            }
        }

        // Check if Echo is available
        if (window.Echo) {
            log('Echo is available', 'success');

            // Display Echo configuration
            try {
                echoConfig.textContent = JSON.stringify(window.Echo, null, 2);
            } catch (e) {
                echoConfig.textContent = 'Error displaying Echo configuration: ' + e.message;
            }

            // Check connection status
            if (window.Echo.connector && window.Echo.connector.pusher) {
                const pusher = window.Echo.connector.pusher;

                // Update initial connection status
                updateConnectionStatus(pusher.connection.state);

                // Listen for connection state changes
                pusher.connection.bind('state_change', (states) => {
                    log(`Connection state changed: ${states.previous} -> ${states.current}`);
                    updateConnectionStatus(states.current);
                });

                // Listen for connection established
                pusher.connection.bind('connected', () => {
                    log('Connection established', 'success');
                    connectionId.textContent = pusher.connection.connectionId || 'Unknown';
                    socketId.textContent = pusher.connection.socket_id || 'Unknown';
                });

                // Listen for connection errors
                pusher.connection.bind('error', (err) => {
                    log(`Connection error: ${JSON.stringify(err)}`, 'error');
                });

                // Subscribe to channel button
                subscribeBtn.addEventListener('click', () => {
                    const channelNameValue = channelName.value.trim();

                    if (!channelNameValue) {
                        log('Please enter a channel name', 'error');
                        return;
                    }

                    try {
                        // Determine channel type
                        let newChannel;
                        if (channelNameValue.startsWith('private-')) {
                            // If the user manually entered 'private-' prefix, remove it
                            const name = channelNameValue.replace('private-', '');
                            log(`Subscribing to private channel: ${name}`);
                            newChannel = window.Echo.private(name);
                        } else if (channelNameValue.startsWith('presence-')) {
                            // If the user manually entered 'presence-' prefix, remove it
                            const name = channelNameValue.replace('presence-', '');
                            log(`Subscribing to presence channel: ${name}`);
                            newChannel = window.Echo.join(name);
                        } else {
                            // Check if this is likely a private channel (contains a dot)
                            if (channelNameValue.includes('.')) {
                                log(`Subscribing to private channel: ${channelNameValue}`);
                                newChannel = window.Echo.private(channelNameValue);
                            } else {
                                log(`Subscribing to public channel: ${channelNameValue}`);
                                newChannel = window.Echo.channel(channelNameValue);
                            }
                        }

                        // Update channel reference
                        channel = newChannel;

                        // Update subscription status
                        subscriptionStatus.textContent = 'Subscribing...';
                        subscriptionStatus.className = 'px-2 py-1 rounded text-white bg-yellow-500';

                        // Add specific error handlers for this channel
                        channel.error((err) => {
                            log(`Channel subscription error: ${JSON.stringify(err)}`, 'error');

                            // Log detailed error information
                            console.error('Channel error details:', {
                                type: err.type,
                                data: err.data,
                                error: err.error,
                                context: err.context
                            });

                            // Update UI to show error
                            subscriptionStatus.textContent = 'Subscription Error';
                            subscriptionStatus.className = 'px-2 py-1 rounded text-white bg-red-500';

                            // Show a more user-friendly error message
                            const errorDiv = document.createElement('div');
                            errorDiv.className = 'mt-2 p-3 bg-red-100 dark:bg-red-900 text-red-800 dark:text-red-200 rounded';
                            errorDiv.innerHTML = `
                                <h3 class="font-bold">Channel Error</h3>
                                <p class="text-sm mt-1">There was an error with the channel subscription:</p>
                                <pre class="text-xs mt-1 p-2 bg-red-50 dark:bg-red-950 rounded overflow-auto max-h-24">${JSON.stringify(err, null, 2)}</pre>
                                <p class="text-sm mt-2">Try refreshing the page or selecting a different ticket.</p>
                                <button id="retry-subscription" class="mt-2 px-3 py-1 bg-blue-500 text-white rounded hover:bg-blue-600 text-sm">Retry Subscription</button>
                            `;

                            // Add the error message to the page
                            const subscriptionStatusContainer = document.querySelector('#subscription-status').parentNode;

                            // Remove any existing error messages
                            const existingError = subscriptionStatusContainer.querySelector('.bg-red-100, .bg-red-900, .bg-yellow-100, .bg-yellow-900');
                            if (existingError) {
                                existingError.remove();
                            }

                            subscriptionStatusContainer.appendChild(errorDiv);

                            // Add retry button functionality
                            document.getElementById('retry-subscription').addEventListener('click', function() {
                                log('Retrying subscription...', 'info');
                                subscribeToChannel();
                            });
                        });

                        // Check if subscription succeeded
                        setTimeout(() => {
                            // Check the connection state
                            const connectionState = window.Echo.connector.pusher.connection.state;
                            log(`Current connection state: ${connectionState}`);

                            // Check if the channel exists in the subscriptions
                            const subscriptions = window.Echo.connector.pusher.channels.channels;
                            const channelName = channelNameValue.startsWith('private-') ? channelNameValue : `private-${channelNameValue}`;

                            log(`Checking for channel: ${channelName} in subscriptions`);
                            log(`Available channels: ${Object.keys(subscriptions).join(', ')}`);

                            if (subscriptions[channelName]) {
                                const channelState = subscriptions[channelName].subscriptionPending ? 'pending' : 'subscribed';
                                log(`Channel state: ${channelState}`);

                                if (!subscriptions[channelName].subscriptionPending) {
                                    subscriptionStatus.textContent = 'Subscribed';
                                    subscriptionStatus.className = 'px-2 py-1 rounded text-white bg-green-500';
                                    log(`Successfully subscribed to channel: ${channelNameValue}`, 'success');
                                } else {
                                    subscriptionStatus.textContent = 'Subscription Pending';
                                    subscriptionStatus.className = 'px-2 py-1 rounded text-white bg-yellow-500';
                                    log(`Subscription pending for channel: ${channelNameValue}`);
                                }
                            } else {
                                subscriptionStatus.textContent = 'Subscription Failed';
                                subscriptionStatus.className = 'px-2 py-1 rounded text-white bg-red-500';
                                log(`Channel not found in subscriptions: ${channelName}`, 'error');
                            }
                        }, 2000);

                    } catch (error) {
                        log(`Error subscribing to channel: ${error.message}`, 'error');
                        subscriptionStatus.textContent = 'Subscription Failed';
                        subscriptionStatus.className = 'px-2 py-1 rounded text-white bg-red-500';
                    }
                });

                // Listen for event button
                listenBtn.addEventListener('click', () => {
                    const eventNameValue = eventName.value.trim();

                    if (!channel) {
                        log('Please subscribe to a channel first', 'error');
                        return;
                    }

                    if (!eventNameValue) {
                        log('Please enter an event name', 'error');
                        return;
                    }

                    try {
                        // Listen for the event
                        channel.listen(eventNameValue, (data) => {
                            log(`Received event '${eventNameValue}': ${JSON.stringify(data)}`, 'success');
                        });

                        // Update listening status
                        listeningStatus.textContent = `Listening for '${eventNameValue}'`;
                        listeningStatus.className = 'px-2 py-1 rounded text-white bg-green-500';

                        log(`Now listening for event: ${eventNameValue}`, 'success');
                    } catch (error) {
                        log(`Error setting up event listener: ${error.message}`, 'error');
                        listeningStatus.textContent = 'Listening Failed';
                        listeningStatus.className = 'px-2 py-1 rounded text-white bg-red-500';
                    }
                });

            } else {
                log('Echo connector or pusher not available', 'error');
                connectionStatus.textContent = 'Not Available';
                connectionStatus.className = 'px-2 py-1 rounded text-white bg-red-500';
            }
        } else {
            log('Echo is not available', 'error');
            connectionStatus.textContent = 'Not Available';
            connectionStatus.className = 'px-2 py-1 rounded text-white bg-red-500';
        }

        // Helper function to update connection status
        function updateConnectionStatus(state) {
            connectionStatus.textContent = state;

            switch (state) {
                case 'connected':
                    connectionStatus.className = 'px-2 py-1 rounded text-white bg-green-500';
                    break;
                case 'connecting':
                    connectionStatus.className = 'px-2 py-1 rounded text-white bg-yellow-500';
                    break;
                case 'disconnected':
                case 'failed':
                    connectionStatus.className = 'px-2 py-1 rounded text-white bg-red-500';
                    break;
                default:
                    connectionStatus.className = 'px-2 py-1 rounded text-white bg-gray-500';
            }
        }
    });
</script>
</x-app-layout>
