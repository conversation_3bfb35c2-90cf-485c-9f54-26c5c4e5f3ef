# File Upload System Documentation

## Overview

The Minewache ticket system includes a file upload functionality that allows users to attach files to their ticket messages. The system supports various file types including images, videos, audio files, PDFs, and other documents. Files are uploaded, processed, and then displayed in the ticket view.

## System Architecture

The file upload system consists of several components:

1. **Frontend Components**:
   - File input field in the ticket reply form
   - Upload progress indicators
   - Attachment previews
   - Media viewers for different file types

2. **Backend Components**:
   - TicketReplyForm for handling file uploads
   - ProcessTicketMediaJob for processing uploaded files
   - TicketAttachment model for storing file metadata
   - Events and listeners for real-time updates

3. **Processing Pipeline**:
   - Files are uploaded to the server
   - Files are stored in the filesystem
   - Files are processed based on their type (resize, convert, generate thumbnails)
   - Events are dispatched to notify clients of processing completion

4. **Real-time Updates**:
   - WebSockets (Laravel Reverb) for real-time updates
   - Events for notifying clients of attachment processing status

## File Upload Process

1. **Upload Initiation**:
   - User selects files using the file input field
   - Files are validated on the client-side (size, type)
   - Upload progress is displayed to the user

2. **Server-side Processing**:
   - Files are received by the server
   - Files are validated on the server-side
   - Files are stored in the filesystem
   - Attachment records are created in the database
   - Processing jobs are dispatched to the queue

3. **Media Processing**:
   - ProcessTicketMediaJob processes files based on their type
   - Images are resized and thumbnails are generated
   - Videos are converted to MP4 and thumbnails are generated
   - Audio files are converted to MP3
   - PDFs have thumbnails generated
   - Other documents are stored as-is

4. **Notification**:
   - AttachmentProcessingFinished event is dispatched when processing is complete
   - HandleAttachmentProcessingFinished listener checks if all attachments are processed
   - TicketMessageAttachmentsReady event is broadcasted to clients
   - Clients update the UI to display the processed attachments

## Identified Issues

1. **Queue Processing Issues**:
   - Failed jobs in the queue related to broadcasting events
   - Queue worker may not be running consistently
   - Jobs may be failing due to missing dependencies (e.g., FFmpeg, Ghostscript)

2. **WebSocket Communication Issues**:
   - WebSocket connection may not be properly established
   - Events may not be properly broadcasted or received
   - Authentication issues with WebSocket connections

3. **UI/UX Issues**:
   - Limited feedback during the upload and processing stages
   - No progress indicators for individual files
   - Limited error handling for individual file uploads

4. **File Processing Issues**:
   - PDF processing requires Ghostscript which may not be installed
   - Video and audio processing require FFmpeg which may not be installed
   - Large files may cause timeouts or memory issues

## Recommendations

1. **Queue Configuration**:
   - Ensure the queue worker is running consistently
   - Consider using a supervisor to manage the queue worker
   - Increase the timeout for media processing jobs
   - Add better error handling for failed jobs

2. **WebSocket Configuration**:
   - Ensure the Reverb server is running consistently
   - Add better error handling for WebSocket connections
   - Implement fallback mechanisms for when WebSockets fail

3. **UI/UX Improvements**:
   - Add individual progress indicators for each file
   - Improve error messaging with retry options
   - Add better preview functionality for uploaded files
   - Implement client-side validation to prevent common upload issues

4. **File Processing Improvements**:
   - Ensure all required dependencies are installed (FFmpeg, Ghostscript)
   - Add better error handling for file processing
   - Implement file size limits and type restrictions
   - Add better logging for troubleshooting

## Testing Procedures

1. **Upload Testing**:
   - Test uploading different file types (images, videos, audio, PDFs, documents)
   - Test uploading multiple files simultaneously
   - Test uploading large files
   - Test uploading files with special characters in filenames

2. **Processing Testing**:
   - Verify that files are properly processed
   - Check that thumbnails are generated correctly
   - Ensure that converted files are playable/viewable
   - Verify that processing errors are handled gracefully

3. **UI Testing**:
   - Verify that upload progress is displayed correctly
   - Check that error messages are clear and helpful
   - Ensure that attachment previews are displayed correctly
   - Test the media viewers for different file types

4. **WebSocket Testing**:
   - Verify that real-time updates are received
   - Test what happens when the WebSocket connection is lost
   - Check that the UI updates correctly when attachments are processed
   - Test the fallback mechanisms for when WebSockets fail

## Conclusion

The file upload system in the Minewache ticket system is a complex but essential feature. By addressing the identified issues and implementing the recommended improvements, the system can provide a more reliable and user-friendly experience for uploading and viewing attachments in tickets.

The most critical issues to address are the queue processing issues and the WebSocket communication issues, as these directly impact the reliability of the file upload functionality. The UI/UX improvements and file processing improvements will enhance the user experience and make the system more robust.
