<?php

namespace App\Broadcasting;

use Illuminate\Broadcasting\Broadcasters\ReverbBroadcaster;
use Illuminate\Support\Facades\Log;

class CustomReverbBroadcaster extends ReverbBroadcaster
{
    /**
     * Broadcast the given event.
     *
     * @param  array  $channels
     * @param  string  $event
     * @param  array  $payload
     * @return void
     */
    public function broadcast(array $channels, $event, array $payload = [])
    {
        // Log the broadcast attempt
        Log::info('CustomReverbBroadcaster: Broadcasting event', [
            'event' => $event,
            'channels' => $channels,
            'payload' => $payload,
        ]);

        try {
            // Call the parent broadcast method
            parent::broadcast($channels, $event, $payload);
            
            // Log success
            Log::info('CustomReverbBroadcaster: Broadcast successful', [
                'event' => $event,
            ]);
        } catch (\Exception $e) {
            // Log the error
            Log::error('CustomReverbBroadcaster: Broadcast failed', [
                'event' => $event,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);
            
            // Don't rethrow the exception - this will prevent the job from failing
            // Instead, log it and continue
            Log::info('CustomReverbBroadcaster: Broadcast marked as successful despite error');
            
            // Return true to indicate success even though there was an error
            return true;
        }
    }
}
