{{-- resources/views/livewire/ticket/partials/info.blade.php --}}
@props(['ticket'])

<div class="grid grid-cols-1 md:grid-cols-2 lg:col-span-4 gap-4">
    {{-- Description --}}
    <div>
        <h2 class="text-lg font-semibold mb-3 flex items-center gap-2">
            <svg class="w-5 h-5 opacity-70" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 8h10M7 12h10M7 16h10"></path>
            </svg>
            {{ __('tickets.description') }}
        </h2>
        <div class="prose prose-sm md:prose-base dark:prose-invert max-w-none whitespace-pre-wrap">
            {{ $ticket->description }}
        </div>
    </div>

    {{-- Details --}}
    <div class="border-t md:border-t-0 md:border-l border-base-300/30 pt-4 md:pt-0 lg:pt-0 md:pl-6">
        <h2 class="text-lg font-semibold mb-3 flex items-center gap-2">
            <svg class="w-5 h-5 opacity-70" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
            </svg>
            {{ __('tickets.details') }}
        </h2>
        <ul class="space-y-3 text-sm md:text-base">
            <li class="flex justify-between items-center">
                <span class="text-base-content/60">{{ __('tickets.status') }}:</span>
                <x-modern-status-badge :status="$ticket->status" size="sm">
                    {{ $ticket->statusLabel }}
                </x-modern-status-badge>
            </li>
            <li class="flex justify-between items-center">
                <span class="text-base-content/60">{{ __('tickets.assigned_to') }}:</span>
                @if($ticket->assignedTo)
                    <div class="flex items-center gap-2">
                        <img class="w-5 h-5 rounded-full modern-avatar"
                             src="{{ $ticket->assignedTo->getAvatar(['extension' => 'webp', 'size' => 20]) }}"
                             alt="{{ $ticket->assignedTo->username }}">
                        <span class="font-medium">{{ $ticket->assignedTo->username }}</span>
                    </div>
                @else
                    <span class="text-base-content/50 italic">{{ __('tickets.unassigned') }}</span>
                @endif
            </li>
            <li class="flex justify-between items-center">
                <span class="text-base-content/60">{{ __('tickets.created_at') }}:</span>
                <span class="font-mono text-sm">{{ $ticket->created_at->format('d.m.Y H:i') }}</span>
            </li>
            <li class="flex justify-between items-center">
                <span class="text-base-content/60">{{ __('tickets.updated_at') }}:</span>
                <span class="font-mono text-sm">{{ $ticket->updated_at->format('d.m.Y H:i') }}</span>
            </li>
            <li class="flex justify-between items-center">
                <span class="text-base-content/60">{{ __('tickets.message_count') }}:</span>
                <x-modern-status-badge status="default" size="sm" variant="filled">
                    {{ $ticket->messages->count() }}
                </x-modern-status-badge>
            </li>
        </ul>
    </div>
</div>
