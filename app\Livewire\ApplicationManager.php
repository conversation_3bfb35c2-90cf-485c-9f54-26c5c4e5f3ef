<?php

namespace App\Livewire;

use App\Models\Application;
use App\Services\ResponseGeneratorService;
use Illuminate\Support\Facades\Auth;
use Illuminate\Pagination\LengthAwarePaginator;
use Livewire\Component;
use Livewire\WithPagination;

class ApplicationManager extends Component
{
    use WithPagination;

    public $viewMode = 'list'; // 'list', 'detail', 'edit', 'response'
    public $applicationId = null;

    // Properties for application management
    public $selectedApplication = null;
    public $applicationStatus = null;
    public $search = '';
    public $searchTerm = '';
    public $status = 'all';
    public $profession = 'all';
    public $comment = '';
    public $totalApplications = 0;
    public $pendingApplications = 0;
    public $approvedApplications = 0;
    public $rejectedApplications = 0;

    // Properties for response generator
    public $selectedReasons = [];
    public $suggestedReasons = [];
    public $customReasons = '';
    public $applicationQuality = 0;
    public $applicationStrengths = [];
    public $applicationWeaknesses = [];
    public $generatedResponse = null;
    public $smartMode = true;
    public $selectedApplicationId = null;

    // Response generator service
    protected $responseGenerator;

    protected $listeners = [
        'showApplicationDetail' => 'showDetail',
        'showApplicationEdit' => 'showEdit',
        'showResponseGenerator' => 'showResponseGenerator',
        'backToList' => 'backToList',
        'refreshApplications' => '$refresh'
    ];

    /**
     * Constructor to inject dependencies
     */
    public function boot(ResponseGeneratorService $responseGenerator)
    {
        $this->responseGenerator = $responseGenerator;
    }

    /**
     * Show the detail view for an application
     */
    public function showDetail($id)
    {
        $this->viewMode = 'detail';
        $this->applicationId = $id;
    }

    /**
     * Show the edit view for an application
     */
    public function showEdit($id)
    {
        $this->viewMode = 'edit';
        $this->applicationId = $id;
    }

    /**
     * Show the response generator for an application
     */
    public function showResponseGenerator($id)
    {
        $this->viewMode = 'response';
        $this->applicationId = $id;
    }

    /**
     * Go back to the list view
     */
    public function backToList()
    {
        $this->viewMode = 'list';
        $this->applicationId = null;
    }

    /**
     * Updatet den Suchfilter mit Debounce für bessere Performance
     */
    public function updatedSearchTerm()
    {
        // Die verzögerte Suche auf den tatsächlichen Suchfilter übertragen
        $this->search = $this->searchTerm;
        $this->resetPage();
    }

    /**
     * Wird ausgelöst, wenn sich die Filter ändern
     */
    public function updatedStatus()
    {
        $this->resetPage();
    }

    /**
     * Wird ausgelöst, wenn sich die Berufsfilter ändern
     */
    public function updatedProfession()
    {
        $this->resetPage();
    }

    /**
     * Lade Statistiken für das Dashboard
     */
    public function loadStatistics()
    {
        $this->totalApplications = Application::count();
        $this->pendingApplications = Application::where('status', 'pending')->count();
        $this->approvedApplications = Application::where('status', 'approved')->count();
        $this->rejectedApplications = Application::where('status', 'rejected')->count();
    }

    /**
     * Setze Sortierfeld
     */
    public function sortBy($field)
    {
        // Überprüfen ob das Feld sortierbar ist
        if (!array_key_exists($field, $this->sortableFields)) {
            return;
        }

        if ($this->sortField === $field) {
            $this->sortDirection = $this->sortDirection === 'asc' ? 'desc' : 'asc';
        } else {
            $this->sortField = $field;
            $this->sortDirection = 'asc';
        }

        // Seite zurücksetzen
        $this->resetPage();

        // Benachrichtigung
        session()->flash('message', "Liste sortiert nach {$this->sortableFields[$field]} ({$this->sortDirection})");
    }

    /**
     * Wähle eine Bewerbung für Detailansicht aus
     */
    public function viewApplication($id)
    {
        $this->selectedApplication = Application::select('*')->find($id);
        if (!$this->selectedApplication) {
            session()->flash('error', 'Bewerbung nicht gefunden.');
            return;
        }

        $this->applicationStatus = $this->selectedApplication->status ?? 'pending';
        $this->viewMode = 'detail';
    }

    // This method was removed to fix a duplicate method declaration

    /**
     * Zurück zur Detailansicht
     */
    public function backToDetail()
    {
        if ($this->selectedApplication) {
            $this->viewMode = 'detail';
        } else {
            $this->viewMode = 'list';
        }
    }

    /**
     * Bearbeitungsmodus für Bewerbung öffnen
     */
    public function editApplication($id)
    {
        $this->selectedApplication = Application::select('*')->find($id);
        if (!$this->selectedApplication) {
            session()->flash('error', __('admin.application_not_found'));
            return;
        }

        $this->applicationStatus = $this->selectedApplication->status ?? 'pending';
        $this->comment = $this->selectedApplication->team_comment ?? '';
        $this->viewMode = 'edit';
    }

    /**
     * Bewerbung aktualisieren
     */
    public function updateApplication()
    {
        if (!$this->selectedApplication) {
            session()->flash('error', 'Keine Bewerbung ausgewählt.');
            return;
        }

        try {
            // Status des Bewerbers vor der Aktualisierung speichern
            $oldStatus = $this->selectedApplication->status;

            $this->selectedApplication->status = $this->applicationStatus;
            $this->selectedApplication->team_comment = $this->comment;
            $this->selectedApplication->reviewer_id = Auth::user()->id;
            $this->selectedApplication->reviewed_at = now();
            $this->selectedApplication->save();

            $this->loadStatistics();

            if ($oldStatus !== $this->applicationStatus) {
                $statusLabels = [
                    'approved' => 'angenommen',
                    'rejected' => 'abgelehnt',
                    'pending' => 'auf ausstehend gesetzt'
                ];
                $statusLabel = $statusLabels[$this->applicationStatus] ?? $this->applicationStatus;

                session()->flash('message', "Die Bewerbung wurde erfolgreich {$statusLabel}.");
            } else {
                session()->flash('message', 'Die Bewerbung wurde erfolgreich aktualisiert.');
            }

            $this->viewMode = 'detail';
        } catch (\Exception $e) {
            session()->flash('error', 'Fehler beim Aktualisieren der Bewerbung: ' . $e->getMessage());
        }
    }

    /**
     * Bearbeitbarkeit einer Bewerbung umschalten
     */
    public function toggleEditability($id)
    {
        try {
            $application = Application::findOrFail($id);

            // Wenn die Bewerbung bereits bearbeitbar ist, deaktiviere die Bearbeitbarkeit
            if ($application->editable) {
                $application->editable = false;
                $application->save();

                session()->flash('message', __('admin.application_editing_disabled'));

                // Wenn wir uns in der Detailansicht befinden, aktualisieren wir die ausgewählte Bewerbung
                if ($this->selectedApplication && $this->selectedApplication->id === $id) {
                    $this->selectedApplication = $application;
                }

                return;
            }

            // Wenn die Bewerbung nicht bearbeitbar ist, zeige das Modal zur Auswahl der Bearbeitungsoption
            $this->selectedApplicationId = $id;
            $this->dispatchBrowserEvent('open-editability-modal');

        } catch (\Exception $e) {
            session()->flash('error', __('admin.error_changing_editability') . ': ' . $e->getMessage());
        }
    }

    /**
     * Bearbeitbarkeit einer Bewerbung aktivieren
     *
     * @param string $mode 'edit' für direkte Bearbeitung oder 'revision' für neue Revision
     */
    public function enableEditability($mode = 'edit')
    {
        if (!$this->selectedApplicationId) {
            session()->flash('error', __('admin.no_application_selected'));
            return;
        }

        $application = Application::find($this->selectedApplicationId);
        if (!$application) {
            session()->flash('error', __('admin.application_not_found'));
            return;
        }

        if ($mode === 'edit') {
            // Direkte Bearbeitung der bestehenden Bewerbung erlauben
            $application->editable = true;
            $application->save();

            session()->flash('message', __('admin.application_editing_enabled'));
        } else if ($mode === 'revision') {
            // Neue Revision erstellen
            // Die ursprüngliche Bewerbung bleibt nicht bearbeitbar
            // Stattdessen wird eine neue Revision erstellt, die bearbeitbar ist

            // Erstelle eine neue Bewerbung als Revision
            $revision = $application->replicate();
            $revision->parent_id = $application->id;
            $revision->status = 'revision';
            $revision->editable = true;
            $revision->created_at = now();
            $revision->updated_at = now();
            $revision->reviewer_id = null;
            $revision->reviewed_at = null;
            $revision->team_comment = null;
            $revision->save();

            session()->flash('message', __('admin.application_revision_created'));
        }

        // Aktualisiere die ausgewählte Bewerbung, wenn sie angezeigt wird
        if ($this->selectedApplication && $this->selectedApplication->id == $this->selectedApplicationId) {
            $this->selectedApplication = Application::find($this->selectedApplicationId);
        }

        // Schließe das Modal
        $this->dispatchBrowserEvent('close-editability-modal');
        $this->selectedApplicationId = null;
    }

    /**
     * Filter zurücksetzen
     */
    public function resetFilters()
    {
        $this->search = '';
        $this->searchTerm = '';
        $this->status = 'all';
        $this->profession = 'all';
        $this->resetPage();

        session()->flash('message', 'Filter wurden zurückgesetzt.');
    }

    /**
     * Öffne den Antwortgenerator
     */
    public function openResponseGenerator($id)
    {
        $this->selectedApplication = Application::select('*')->find($id);
        if (!$this->selectedApplication) {
            session()->flash('error', 'Bewerbung nicht gefunden.');
            return;
        }

        // Zurücksetzen der Werte
        $this->selectedReasons = [];
        $this->suggestedReasons = [];
        $this->customReasons = '';
        $this->applicationQuality = 0;
        $this->applicationStrengths = [];
        $this->applicationWeaknesses = [];

        // Smart-Modus ist immer aktiv
        $this->smartMode = true;

        // Analyse der Bewerbung durchführen
        $analysis = $this->responseGenerator->analyzeApplication($this->selectedApplication);

        // Status und Gründe basierend auf der Analyse setzen
        $this->applicationStatus = $analysis['recommended_status'];
        $this->selectedReasons = $analysis['rejection_reasons'];

        // Antwort generieren
        $this->generatedResponse = $this->responseGenerator->generateSmartResponse($this->selectedApplication);

        // Bewerbungsqualität und Stärken/Schwächen speichern
        $this->applicationQuality = $analysis['quality_score'];
        $this->applicationStrengths = $analysis['strengths'];
        $this->applicationWeaknesses = $analysis['weaknesses'];

        // Zur Antwortansicht wechseln
        $this->viewMode = 'response';

        // Benachrichtigung anzeigen
        $statusText = $this->applicationStatus === 'approved' ? 'Annahme' : 'Ablehnung';
        session()->flash('message', "Basierend auf der Analyse wird eine {$statusText} empfohlen. Qualitätsbewertung: {$this->applicationQuality}/100");
    }

    /**
     * Generiere eine Antwort basierend auf den ausgewählten Parametern
     */
    public function generateResponse()
    {
        if (!$this->selectedApplication) {
            session()->flash('error', 'Keine Bewerbung ausgewählt.');
            return;
        }

        // Wenn der Status geändert wurde, generieren wir eine neue Antwort
        // aber behalten die Analyse-Ergebnisse bei
        $this->generatedResponse = $this->responseGenerator->generateFullResponse(
            $this->selectedApplication,
            $this->applicationStatus,
            $this->selectedReasons,
            $this->customReasons,
            true // Smart-Modus ist immer aktiv
        );

        // Wenn keine manuellen Gründe ausgewählt wurden, zeige die vorgeschlagenen Gründe an
        if (empty($this->selectedReasons) && isset($this->generatedResponse['suggested_reasons'])) {
            $this->suggestedReasons = $this->generatedResponse['suggested_reasons'];
        }
    }

    /**
     * Generiere eine intelligente Antwort basierend auf den Bewerbungsdaten
     */
    public function generateSmartResponse()
    {
        if (!$this->selectedApplication) {
            session()->flash('error', 'Keine Bewerbung ausgewählt.');
            return;
        }

        // Analyse der Bewerbung durchführen
        $analysis = $this->responseGenerator->analyzeApplication($this->selectedApplication);

        // Status und Gründe basierend auf der Analyse setzen
        $this->applicationStatus = $analysis['recommended_status'];
        $this->selectedReasons = $analysis['rejection_reasons'];

        // Antwort generieren
        $this->generatedResponse = $this->responseGenerator->generateSmartResponse($this->selectedApplication);

        // Bewerbungsqualität und Stärken/Schwächen speichern
        $this->applicationQuality = $analysis['quality_score'];
        $this->applicationStrengths = $analysis['strengths'];
        $this->applicationWeaknesses = $analysis['weaknesses'];

        // Benachrichtigung anzeigen
        session()->flash('message', 'Intelligente Antwort wurde generiert. Qualitätsbewertung: ' . $analysis['quality_score'] . '/100');
    }

    /**
     * Grund zur Antwort hinzufügen oder entfernen
     */
    public function toggleReason($reason)
    {
        if (in_array($reason, $this->selectedReasons)) {
            $this->selectedReasons = array_values(array_diff($this->selectedReasons, [$reason]));
        } else {
            $this->selectedReasons[] = $reason;
        }

        $this->generateResponse();
    }

    /**
     * Fügt einen vorgeschlagenen Grund hinzu
     */
    public function addSuggestedReason($reason)
    {
        if (!in_array($reason, $this->selectedReasons)) {
            $this->selectedReasons[] = $reason;
            $this->suggestedReasons = array_values(array_diff($this->suggestedReasons, [$reason]));
            $this->generateResponse();
        }
    }



    /**
     * Kopieren der generierten Antwort in die Zwischenablage
     */
    public function copyToClipboard()
    {
        // Stelle sicher, dass wir den kopierbaren Text aus dem Response-Array nehmen
        if (is_array($this->generatedResponse) && isset($this->generatedResponse['copyable'])) {
            $textToCopy = $this->generatedResponse['copyable'];

        } else {
            // Fallback für ältere Implementierungen
            $textToCopy = is_string($this->generatedResponse) ? $this->generatedResponse : '';
        }

        // Dispatch des Events mit dem zu kopierenden Text
        $this->dispatch('copyToClipboard', text: $textToCopy);

        // Erfolgsmeldung anzeigen
        session()->flash('clipboard', 'Antwort wurde in die Zwischenablage kopiert!');
    }

    /**
     * Liefert CSS-Klassen für visuelle Darstellung des Status
     *
     * @param string $status Status der Bewerbung
     * @return array Array mit CSS-Klassen für verschiedene Elemente
     */
    public function getStatusVisualClasses($status)
    {
        switch ($status) {
            case 'approved':
                return [
                    'badge' => 'badge-success',
                    'icon' => 'check-circle',
                    'bg' => 'bg-success/10',
                    'border' => 'border-success/30',
                    'text' => 'text-success'
                ];
            case 'rejected':
                return [
                    'badge' => 'badge-error',
                    'icon' => 'x-circle',
                    'bg' => 'bg-error/10',
                    'border' => 'border-error/30',
                    'text' => 'text-error'
                ];
            default: // pending
                return [
                    'badge' => 'badge-warning',
                    'icon' => 'clock',
                    'bg' => 'bg-warning/10',
                    'border' => 'border-warning/30',
                    'text' => 'text-warning'
                ];
        }
    }

    /**
     * Render the component
     */
    public function render()
    {
        return view('livewire.application-manager');
    }

    /**
     * Erstellt ein leeres Pagination-Objekt
     *
     * @return LengthAwarePaginator
     */
    protected function createEmptyPaginator(): LengthAwarePaginator
    {
        return new LengthAwarePaginator(
            [], // leere Ergebnisse
            0,  // Gesamtzahl 0
            10, // Items pro Seite
            1,  // aktuelle Seite
            ['path' => request()->url()] // URL für Pagination-Links
        );
    }

    /**
     * Fügt JavaScript für Tabs und Modals hinzu
     */
    private function addJavaScript()
    {
        // Tab-Funktionalität
        $this->js("\n
            function showTab(tabName) {
                // Alle Tabs und Inhalte ausblenden/deaktivieren
                document.querySelectorAll('.tab').forEach(tab => {
                    tab.classList.remove('tab-active');
                });

                if (document.getElementById('preview-content')) {
                    document.getElementById('preview-content').classList.add('hidden');
                }

                if (document.getElementById('raw-content')) {
                    document.getElementById('raw-content').classList.add('hidden');
                }

                // Ausgewählten Tab und Inhalt aktivieren
                if (document.getElementById('tab-' + tabName)) {
                    document.getElementById('tab-' + tabName).classList.add('tab-active');
                }

                if (document.getElementById(tabName + '-content')) {
                    document.getElementById(tabName + '-content').classList.remove('hidden');
                }
            }

            // Funktion zum Initialisieren der Tabs
            function initTabs() {
                // Wenn ein Tab existiert, zeige den ersten Tab an (standardmäßig 'preview')
                if (document.getElementById('tab-preview')) {
                    showTab('preview');

                    // Event-Listener für Tab-Buttons hinzufügen
                    document.getElementById('tab-preview').addEventListener('click', function() {
                        showTab('preview');
                    });

                    document.getElementById('tab-raw').addEventListener('click', function() {
                        showTab('raw');
                    });
                }

                // Kopier-Button initialisieren, falls vorhanden
                const copyButton = document.getElementById('copy-button');
                if (copyButton) {
                    copyButton.addEventListener('click', function() {
                        const rawContent = document.getElementById('raw-content');
                        if (rawContent) {
                            const text = rawContent.textContent || rawContent.innerText;
                            navigator.clipboard.writeText(text).then(() => {
                                // Erfolgsbenachrichtigung anzeigen
                                const copyButton = document.getElementById('copy-button');
                                const originalText = copyButton.innerText;
                                copyButton.innerText = 'Kopiert!';
                                copyButton.classList.add('btn-success');
                                copyButton.classList.remove('btn-primary');

                                // Nach 2 Sekunden zurücksetzen
                                setTimeout(() => {
                                    copyButton.innerText = originalText;
                                    copyButton.classList.remove('btn-success');
                                    copyButton.classList.add('btn-primary');
                                }, 2000);
                            });
                        }
                    });
                }
            }

            // Modal-Funktionalität
            window.addEventListener('open-editability-modal', event => {
                document.getElementById('editability-modal').showModal();
            });

            window.addEventListener('close-editability-modal', event => {
                document.getElementById('editability-modal').close();
            });

            // Initialisierung beim Laden
            document.addEventListener('DOMContentLoaded', initTabs);
            initTabs(); // Sofort ausführen für schnelleres Rendering
        ");
    }

    /**
     * Ermittelt die verfügbaren Berufe für den Filter
     */
    protected function getAvailableProfessions()
    {
        try {
            // Bekannte Berufstypen
            $professionOptions = [
                'actor' => 'Schauspieler',
                'actor_no_voice' => 'Schauspieler (No voice)',
                'voice_actor' => 'Synchronsprecher',
                'builder' => 'Builder',
                'designer' => 'Designer',
                'cutter' => 'Cutter',
                'cameraman' => 'Kameramann',
                'developer' => 'Developer',
                'modeler' => 'Modellierer',
                'music_producer' => 'Musikproduzent',
                'other' => 'Andere'
            ];

            return $professionOptions;
        } catch (\Exception $e) {
            return [];
        }
    }
}
