<?php

namespace App\Enums;

enum Role: int
{
    case MINEWACHE_TEAM = 0b11111111111111111111111111111111;

    case SCHAUSPIELER = 1 << 0;
    case BUILDER = 1 << 1;
    case DESIGNER = 1 << 2;
    case SYNCHRONSPRECHER = 1 << 3;
    case MODELLIERER = 1 << 4;
    case DEVELOPER = 1 << 5;
    case KAMERAMANN = 1 << 6;
    case CUTTER = 1 << 7;
    case ANGENOMMEN = 1 << 8;
    case ANGENOMMENPLUS = 1 << 9;

    public function label(): string
    {
        return match ($this) {
            self::MINEWACHE_TEAM => 'Minewache-Team',
            self::SCHAUSPIELER => 'Schauspieler',
            self::BUILDER => 'Builder',
            self::DESIGNER => 'Designer',
            self::SYNCHRONSPRECHER => 'Synchronsprecher',
            self::MODELLIERER => 'Modellierer',
            self::DEVELOPER => 'Developer',
            self::KAMERAMANN => 'Kameramann',
            self::CUTTER => 'Cutter',
            self::ANGENOMMEN => 'Angenommen',
            self::<PERSON><PERSON><PERSON><PERSON><PERSON>NPLUS => 'Angenommen+',
        };
    }
}
