<?php

namespace App\Livewire;

use Livewire\Component;
use App\Models\Application;
use Livewire\WithPagination;
use Illuminate\Support\Facades\DB;
use Livewire\Attributes\Url;
use Illuminate\Pagination\LengthAwarePaginator;
use Illuminate\Support\Facades\Cache;

class ApplicationList extends Component
{
    use WithPagination;

    // Filter und Sortieroptionen mit URL-Synchronisierung für Bookmarking
    #[Url]
    public $search = '';

    #[Url]
    public $status = 'all';

    #[Url]
    public $profession = 'all';

    #[Url]
    public $sortField = 'created_at';

    #[Url]
    public $sortDirection = 'desc';

    // Variable für verzögerte Suche
    public $searchTerm = '';

    // Für Statistiken
    public $totalApplications = 0;
    public $pendingApplications = 0;
    public $approvedApplications = 0;
    public $rejectedApplications = 0;

    // Listener für Livewire-Events
    protected $listeners = [
        'refreshApplications' => '$refresh'
    ];

    /**
     * Wird ausgeführt, wenn die Komponente gemountet wird
     */
    public function mount()
    {
        // Suchterm mit URL synchronisieren
        $this->searchTerm = $this->search;
        $this->loadStatistics();
    }

    /**
     * Updatet den Suchfilter mit Debounce für bessere Performance
     */
    public function updatedSearchTerm()
    {
        // Die verzögerte Suche auf den tatsächlichen Suchfilter übertragen
        $this->search = $this->searchTerm;
        $this->resetPage();
    }

    /**
     * Wird ausgelöst, wenn sich die Filter ändern
     */
    public function updatedStatus()
    {
        $this->resetPage();
    }

    /**
     * Wird ausgelöst, wenn sich die Berufsfilter ändern
     */
    public function updatedProfession()
    {
        $this->resetPage();
    }

    /**
     * Lade Statistiken für das Dashboard mit Caching
     */
    public function loadStatistics()
    {
        $statistics = Cache::remember('application_statistics', now()->addMinutes(5), function () {
            $counts = Application::selectRaw('status, count(*) as count')
                ->groupBy('status')
                ->pluck('count', 'status')
                ->toArray();
                
            return [
                'total' => array_sum($counts),
                'pending' => $counts['pending'] ?? 0,
                'approved' => $counts['approved'] ?? 0,
                'rejected' => $counts['rejected'] ?? 0
            ];
        });
        
        $this->totalApplications = $statistics['total'];
        $this->pendingApplications = $statistics['pending'];
        $this->approvedApplications = $statistics['approved'];
        $this->rejectedApplications = $statistics['rejected'];
    }

    /**
     * Sortierung der Tabelle ändern
     */
    public function sortBy($field)
    {
        if ($this->sortField === $field) {
            $this->sortDirection = $this->sortDirection === 'asc' ? 'desc' : 'asc';
        } else {
            $this->sortField = $field;
            $this->sortDirection = 'asc';
        }
    }

    /**
     * Wähle eine Bewerbung für Detailansicht aus
     */
    public function viewApplication($id)
    {
        $this->dispatch('showApplicationDetail', id: $id);
    }

    /**
     * Bearbeitungsmodus für Bewerbung öffnen
     */
    public function editApplication($id)
    {
        $this->dispatch('showApplicationEdit', id: $id);
    }

    /**
     * Filter zurücksetzen
     */
    public function resetFilters()
    {
        $this->search = '';
        $this->searchTerm = '';
        $this->status = 'all';
        $this->profession = 'all';
        $this->resetPage();

        session()->flash('message', 'Filter wurden zurückgesetzt.');
    }

    /**
     * Erstellt ein leeres Paginator-Objekt für Fehlerfälle
     */
    protected function createEmptyPaginator()
    {
        return new LengthAwarePaginator(
            [], // Leeres Array
            0,  // Gesamtanzahl
            10, // Pro Seite
            1   // Aktuelle Seite
        );
    }

    /**
     * Ermittelt die verfügbaren Berufe für den Filter
     */
    protected function getAvailableProfessions()
    {
        try {
            // Bekannte Berufstypen
            $professionOptions = [
                'actor' => 'Schauspieler',
                'actor_no_voice' => 'Schauspieler (No voice)',
                'voice_actor' => 'Synchronsprecher',
                'builder' => 'Builder',
                'designer' => 'Designer',
                'cutter' => 'Cutter',
                'cameraman' => 'Kameramann',
                'developer' => 'Developer',
                'modeler' => 'Modellierer',
                'music_producer' => 'Musikproduzent',
                'other' => 'Andere'
            ];

            return $professionOptions;
        } catch (\Exception $e) {
            return [];
        }
    }

    /**
     * Definieren der verfügbaren Sortierfelder
     */
    protected function getSortableFields() {
        return [
            'created_at' => __('admin.created_at'),
            'name' => __('admin.name'),
            'status' => __('admin.status'),
            'reviewed_at' => __('admin.reviewed_at')
        ];
    }

    /**
     * Hauptrender-Methode
     */
    public function render()
    {
        try {
            $query = Application::query();

            // Suchfilter anwenden
            if (!empty($this->search)) {
                $query->where(function ($q) {
                    $q->where('name', 'like', '%' . $this->search . '%')
                      ->orWhere('discord_id', 'like', '%' . $this->search . '%');
                });
            }

            // Status-Filter anwenden
            if ($this->status !== 'all') {
                $query->where('status', $this->status);
            }

            // Berufs-Filter anwenden
            if ($this->profession !== 'all') {
                // JSON-Suche in professions Array
                $query->whereJsonContains('professions', $this->profession);
            }

            // Sortierung - universeller Ansatz
            if (in_array($this->sortField, ['created_at', 'reviewed_at', 'name', 'status'])) {
                // Für Standard-Felder kann orderBy verwendet werden
                $query->orderBy($this->sortField, $this->sortDirection);
            } else {
                // Fallback für unbekannte Felder
                $query->orderBy('created_at', $this->sortDirection);
            }

            // Pagination mit Exception-Handling
            try {
                $applications = $query->paginate(10);
            } catch (\Exception $e) {
                // Bei Fehlern leeres Pagination-Objekt zurückgeben
                $applications = $this->createEmptyPaginator();
                session()->flash('error', 'Fehler bei der Filterung: ' . $e->getMessage());
            }

            // Verfügbare Berufe für den Filter
            $availableProfessions = $this->getAvailableProfessions();

            // Ansicht rendern
            return view('livewire.application-list', [
                'applications' => $applications,
                'sortableFields' => $this->getSortableFields(),
                'availableProfessions' => $availableProfessions
            ]);

        } catch (\Exception $e) {
            // Master-Exception-Handler
            session()->flash('error', 'Fehler beim Laden der Bewerbungen: ' . $e->getMessage());

            // Leeres Pagination-Objekt erstellen
            $applications = $this->createEmptyPaginator();

            return view('livewire.application-list', [
                'applications' => $applications,
                'sortableFields' => $this->getSortableFields(),
                'availableProfessions' => $this->getAvailableProfessions()
            ]);
        }
    }
}
