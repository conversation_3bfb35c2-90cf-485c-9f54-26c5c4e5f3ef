<?php

namespace App\Services;

use App\Jobs\ProcessTicketMediaJob;
use App\Models\TicketAttachment;
use App\Models\TicketMessage;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage; // Although store() handles it, it's good practice for clarity

class TicketAttachmentService
{
    /**
     * Handle file uploads for a ticket message.
     *
     * @param Request $request
     * @param TicketMessage $message
     * @return void
     */
    public function handleUploads(Request $request, TicketMessage $message): void
    {
        if ($request->hasFile('attachments')) {
            foreach ($request->file('attachments') as $file) {
                $path = $file->store('ticket-attachments/' . $message->ticket_id);

                $attachment = new TicketAttachment([
                    'ticket_message_id' => $message->id,
                    'filename' => basename($path),
                    'original_filename' => $file->getClientOriginalName(),
                    'file_path' => $path,
                    'mime_type' => $file->getMimeType(),
                    'file_size' => $file->getSize(),
                    'processing_status' => 'pending',
                ]);

                $attachment->save();

                // Dispatch job to process the media file
                ProcessTicketMediaJob::dispatch($attachment);
            }
        }
    }
}
