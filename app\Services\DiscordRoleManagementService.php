<?php

namespace App\Services;

use Discord\Discord;
use Discord\Parts\Guild\Member;
use Discord\Parts\Guild\Role;
use Discord\Http\Exceptions\NotFoundException;
use Discord\Http\Exceptions\ForbiddenException;
use Discord\Http\Exceptions\RequestFailedException;
use Illuminate\Support\Facades\Log;
use Exception;

class DiscordRoleManagementService
{
    protected $discord;
    protected $botToken;
    protected $defaultGuildId;

    public function __construct()
    {
        $this->botToken = config('minewache_discord_bot.token');
        $this->defaultGuildId = config('minewache_discord_bot.guild_id'); // Store default guild ID

        // Initialize Discord client for REST operations
        try {
            $this->discord = new Discord([
                'token' => $this->botToken,
                'logger' => Log::getLogger(),
                // Intents are generally not required for pure REST-based role management,
                // but DiscordPHP might require some basic setup.
            ]);
        } catch (Exception $e) {
            Log::error('DiscordRoleManagementService: Failed to initialize Discord client for REST', ['exception' => $e]);
            $this->discord = null;
        }
    }

    /**
     * Adds a role to a user in a specific guild.
     *
     * @param string $userId The Discord User ID.
     * @param string $roleId The Discord Role ID.
     * @param string|null $guildId The Discord Guild ID. Uses default if null.
     * @return bool True on success, false on failure.
     */
    public function addRoleToUser(string $userId, string $roleId, ?string $guildId = null): bool
    {
        if (!$this->discord) {
            Log::error('DiscordRoleManagementService: Discord client not initialized.');
            return false;
        }

        $guildId = $guildId ?? $this->defaultGuildId;
        if (!$guildId) {
            Log::error('DiscordRoleManagementService: Guild ID not provided or configured.');
            return false;
        }

        try {
            // Fetch the guild. Note: DiscordPHP might require running the loop for some cache-dependent get methods.
            // For REST, direct fetch is preferred. ->guilds->fetch($guildId) might be better if available and blocking.
            // Let's assume we can get a guild object or operate directly with IDs for members.

            // Fetch the member
            // $guild = $this->discord->guilds->get('id', $guildId); // This might rely on cache
            // A more direct REST way if available:
            // $member = $this->discord->guild->getMember($guildId, $userId); -> This method might not exist.
            // Alternative: fetch member through REST endpoint if DiscordPHP allows easy construction of such requests.
            // For now, let's assume a common pattern using guild part:

            $guild = $this->discord->guilds->fetch($guildId, true)->wait(); // Fetch fresh, wait for promise
            if (!$guild) {
                Log::error("DiscordRoleManagementService: Guild {$guildId} not found.");
                return false;
            }

            $member = $guild->members->fetch($userId, true)->wait(); // Fetch fresh, wait for promise
            if (!$member) {
                Log::error("DiscordRoleManagementService: User {$userId} not found in guild {$guildId}.");
                return false;
            }

            // Add the role
            // $member->addRole($roleId) is the typical method.
            $member->addRole($roleId)->done(
                function () use ($userId, $roleId, $guildId) {
                    Log::info("DiscordRoleManagementService: Successfully added role {$roleId} to user {$userId} in guild {$guildId}.");
                    // Invalidate our own Laravel role cache for this user if we implement one later (see FR-ROLE-004)
                },
                function (Exception $e) use ($userId, $roleId, $guildId) {
                    Log::error("DiscordRoleManagementService: Failed to add role {$roleId} to user {$userId} in guild {$guildId}.", ['exception' => $e]);
                }
            );
            // Note: addRole returns a Promise. For a synchronous service method, we might need to ->wait().
            // However, for fire-and-forget from web, this is often fine.
            // Let's assume for now the logging in done/fail is sufficient and we return true optimistically
            // or would need to wrap this in a blocking wait if immediate confirmation is essential for the caller.
            // $member->addRole($roleId)->wait(); // This would make it blocking

            return true; // Optimistic return, or after ->wait() if used.

        } catch (NotFoundException $e) {
            Log::error("DiscordRoleManagementService: Resource not found (e.g., guild, user, or role). Guild: {$guildId}, User: {$userId}, Role: {$roleId}.", ['exception' => $e->getMessage()]);
        } catch (ForbiddenException $e) {
            Log::error("DiscordRoleManagementService: Bot lacks permissions to add role. Guild: {$guildId}, User: {$userId}, Role: {$roleId}.", ['exception' => $e->getMessage()]);
        } catch (RequestFailedException $e) {
            Log::error("DiscordRoleManagementService: Discord API request failed. Guild: {$guildId}, User: {$userId}, Role: {$roleId}.", ['exception' => $e->getMessage()]);
        } catch (Exception $e) {
            Log::error("DiscordRoleManagementService: Error adding role to user. Guild: {$guildId}, User: {$userId}, Role: {$roleId}.", ['exception' => $e]);
        }
        return false;
    }

    /**
     * Removes a role from a user in a specific guild.
     *
     * @param string $userId The Discord User ID.
     * @param string $roleId The Discord Role ID.
     * @param string|null $guildId The Discord Guild ID. Uses default if null.
     * @return bool True on success, false on failure.
     */
    public function removeRoleFromUser(string $userId, string $roleId, ?string $guildId = null): bool
    {
        if (!$this->discord) {
            Log::error('DiscordRoleManagementService: Discord client not initialized.');
            return false;
        }

        $guildId = $guildId ?? $this->defaultGuildId;
        if (!$guildId) {
            Log::error('DiscordRoleManagementService: Guild ID not provided or configured.');
            return false;
        }

        try {
            $guild = $this->discord->guilds->fetch($guildId, true)->wait();
            if (!$guild) {
                Log::error("DiscordRoleManagementService: Guild {$guildId} not found.");
                return false;
            }

            $member = $guild->members->fetch($userId, true)->wait();
            if (!$member) {
                Log::error("DiscordRoleManagementService: User {$userId} not found in guild {$guildId}.");
                return false;
            }

            // Remove the role
            // $member->removeRole($roleId) is the typical method.
            $member->removeRole($roleId)->done(
                function () use ($userId, $roleId, $guildId) {
                    Log::info("DiscordRoleManagementService: Successfully removed role {$roleId} from user {$userId} in guild {$guildId}.");
                    // Invalidate our own Laravel role cache for this user if we implement one later (see FR-ROLE-004)
                },
                function (Exception $e) use ($userId, $roleId, $guildId) {
                    Log::error("DiscordRoleManagementService: Failed to remove role {$roleId} from user {$userId} in guild {$guildId}.", ['exception' => $e]);
                }
            );
            // Similar note about ->wait() applies here if blocking behavior is needed.
            // $member->removeRole($roleId)->wait();

            return true; // Optimistic return

        } catch (NotFoundException $e) {
            Log::error("DiscordRoleManagementService: Resource not found (guild, user, or role) for removal. Guild: {$guildId}, User: {$userId}, Role: {$roleId}.", ['exception' => $e->getMessage()]);
        } catch (ForbiddenException $e) {
            Log::error("DiscordRoleManagementService: Bot lacks permissions to remove role. Guild: {$guildId}, User: {$userId}, Role: {$roleId}.", ['exception' => $e->getMessage()]);
        } catch (RequestFailedException $e) {
            Log::error("DiscordRoleManagementService: Discord API request failed for removal. Guild: {$guildId}, User: {$userId}, Role: {$roleId}.", ['exception' => $e->getMessage()]);
        } catch (Exception $e) {
            Log::error("DiscordRoleManagementService: Error removing role from user. Guild: {$guildId}, User: {$userId}, Role: {$roleId}.", ['exception' => $e]);
        }
        return false;
    }
}
