<?php

namespace App\Events;

use App\Models\TicketAttachment;
use Illuminate\Broadcasting\Channel;
use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Broadcasting\PrivateChannel;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

class AttachmentProcessingFinished
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    /**
     * The ticket attachment instance.
     *
     * @var TicketAttachment
     */
    public $attachment;

    /**
     * Create a new event instance.
     *
     * @param  TicketAttachment  $attachment
     * @return void
     */
    public function __construct(TicketAttachment $attachment)
    {
        $this->attachment = $attachment;
    }
}
