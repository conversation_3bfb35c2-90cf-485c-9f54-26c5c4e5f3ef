// resources/js/ticket-view-custom.js

// Alpine component for ticket view
document.addEventListener('alpine:init', () => {
    Alpine.data('ticketView', () => ({
        ticketId: null,
        messagesContainer: null,
        newMessageIndicator: null,
        scrollToBottomBtn: null,

        init() {
            // Use Livewire 3's component.initialized hook instead of polling
            if (window.Livewire && window.Livewire.hook) {
                Livewire.hook('component.initialized', (component) => {
                    if (component.name === 'ticket-view') {
                        this.initializeAfterLivewire();
                    }
                });
            } else {
                // Fallback: Wait for Livewire to be ready and DOM to be available
                this.$nextTick(() => {
                    this.initializeAfterLivewire();
                });
            }
        },

        initializeAfterLivewire() {
            const maxRetries = 10;
            let retryCount = 0;

            const attemptInitialization = () => {
                // Guard: ensure $wire is available
                if (!this.$wire || !this.$wire.ticket) {
                    retryCount++;
                    if (retryCount >= maxRetries) {
                        console.error('Failed to initialize ticket view: Livewire $wire not available after maximum retries');
                        return;
                    }
                    console.warn(`Livewire $wire not ready, retrying... (${retryCount}/${maxRetries})`);
                    setTimeout(attemptInitialization, 100);
                    return;
                }

                // Initialize references with safety checks
                this.ticketId = this.$wire.ticket.id;
                this.messagesContainer = document.getElementById('messages-container');
                this.newMessageIndicator = document.getElementById('new-message-indicator');
                this.scrollToBottomBtn = document.getElementById('scroll-to-bottom');

                // Guard: if critical elements are missing, retry after a short delay
                if (!this.messagesContainer) {
                    retryCount++;
                    if (retryCount >= maxRetries) {
                        console.error('Failed to initialize ticket view: Messages container not found after maximum retries');
                        return;
                    }
                    console.warn(`Messages container not found, retrying... (${retryCount}/${maxRetries})`);
                    setTimeout(attemptInitialization, 100);
                    return;
                }

                // Reset retry count on successful initialization
                retryCount = 0;
                this.setupEventListeners();
            };

            attemptInitialization();
        },

        setupEventListeners() {

            // Scroll-to-bottom button click
            if (this.scrollToBottomBtn) {
                this.scrollToBottomBtn.addEventListener('click', () => {
                    this.scrollToEnd();
                });
            }

            // Listen to scroll events to toggle indicators
            if (this.messagesContainer) {
                this.messagesContainer.addEventListener('scroll', () => {
                    const buffer = 200;
                    const atBottom = this.messagesContainer.scrollHeight - this.messagesContainer.scrollTop <= this.messagesContainer.clientHeight + buffer;
                    if (atBottom) {
                        this.newMessageIndicator?.classList.add('hidden');
                        this.scrollToBottomBtn?.classList.add('hidden');
                    } else {
                        this.scrollToBottomBtn?.classList.remove('hidden');
                    }
                });
            }

            // Laravel Echo real-time subscriptions
            if (window.Echo && this.ticketId) {
                window.Echo.private(`tickets.${this.ticketId}`)
                    .listen('MessageAdded', e => {
                        this.handleMessageAdded(e.message);
                    })
                    .listenForWhisper('typing', e => {
                        this.handleTypingUpdate(e.users);
                    });
            }
        },

        // Scroll container to bottom
        scrollToEnd() {
            if (!this.messagesContainer) return;
            this.messagesContainer.scrollTo({
                top: this.messagesContainer.scrollHeight,
                behavior: 'smooth'
            });
            this.newMessageIndicator?.classList.add('hidden');
            this.scrollToBottomBtn?.classList.add('hidden');
        },

        // Called when a new message is added via Livewire or Echo
        handleMessageAdded(message) {
            // Let Livewire update the DOM, then scroll
            setTimeout(() => {
                this.scrollToEnd();
                // Show "new message" indicator briefly if user is not at bottom
                if (this.newMessageIndicator) {
                    this.newMessageIndicator.classList.remove('hidden');
                    // auto-hide after a few seconds
                    setTimeout(() => this.newMessageIndicator?.classList.add('hidden'), 3000);
                }
            }, 50);
        },

        // Play notification sound
        playNotificationSound() {
            const audio = document.getElementById('notification-sound');
            if (audio && audio.play) {
                audio.play().catch(() => {});
            }
        },

        // Attach attachments to a message element
        handleAttachmentsReady(messageId, attachments) {
            if (!this.messagesContainer) return;
            const msgEl = this.messagesContainer.querySelector(`[data-message-id="${messageId}"]`);
            if (!msgEl || !Array.isArray(attachments)) return;

            let container = msgEl.querySelector('.attachments-container');
            if (!container) {
                container = document.createElement('div');
                container.classList.add('attachments-container', 'mt-3', 'pt-3', 'border-t');
                msgEl.querySelector('.prose')?.after(container);
            }
            attachments.forEach(file => {
                const a = document.createElement('a');
                a.href = file.url;
                a.textContent = file.name;
                a.target = '_blank';
                a.rel = 'noopener';
                a.className = 'block text-blue-400 underline mb-1';
                container.appendChild(a);
            });
        },

        // Show/hide typing indicator
        handleTypingUpdate(users) {
            const typingEl = document.getElementById('typing-indicator');
            if (!typingEl) return;

            const userNames = Array.isArray(users) ? users : Object.values(users || {});
            if (userNames.length === 0) {
                typingEl.classList.add('hidden');
            } else {
                const nameSpan = typingEl.querySelector('.typing-user-name');
                if (nameSpan) {
                    nameSpan.textContent = userNames.join(', ');
                }
                typingEl.classList.remove('hidden');
            }
        }
    }));
});

// Utility: copy text to clipboard
window.copyToClipboard = function(text) {
    if (!text) return;
    if (navigator.clipboard && navigator.clipboard.writeText) {
        navigator.clipboard.writeText(text).catch(() => {});
    } else {
        const ta = document.createElement('textarea');
        ta.value = text;
        ta.style.position = 'fixed';
        ta.style.opacity = '0';
        document.body.appendChild(ta);
        ta.select();
        document.execCommand('copy');
        document.body.removeChild(ta);
    }
};

// Utility: cancel current upload
window.handleCancelUpload = function() {
    // Clear Livewire attachments
    if (window.Livewire && Livewire.emit) {
        Livewire.emit('clearAttachments');
    }
    // Reset the file input
    const inp = document.getElementById('attachments');
    if (inp) inp.value = '';
};

// SPA-style navigation for internal links within the ticket view
document.addEventListener('click', function(e) {
    const link = e.target.closest('#ticket-view a[href]');
    if (!link) return;
    const href = link.getAttribute('href');
    if (!href.startsWith('/') || link.hasAttribute('target') || link.dataset.noSpa !== undefined) {
        return; // external or opted-out
    }
    e.preventDefault();
    history.pushState({}, '', href);
    // Let Livewire or full reload handle the route
    if (window.Livewire && Livewire.visit) {
        Livewire.visit(href);
    } else {
        window.location.href = href;
    }
});

// Handle browser navigation (back/forward)
window.addEventListener('popstate', () => {
    if (window.Livewire && Livewire.visit) {
        Livewire.visit(location.pathname);
    }
});
