<?php

namespace App\Events;

use App\Models\Ticket;
use App\Models\TicketMessage;
use App\Models\User;
use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

class TicketRepliedByUser
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    public Ticket $ticket;
    public TicketMessage $ticketMessage;
    public User $replyingUser; // The Laravel user who replied

    /**
     * Create a new event instance.
     *
     * @param Ticket $ticket The ticket being replied to.
     * @param TicketMessage $ticketMessage The new message from the user.
     * @param User $replyingUser The Laravel user who sent the reply.
     */
    public function __construct(Ticket $ticket, TicketMessage $ticketMessage, User $replyingUser)
    {
        $this->ticket = $ticket;
        $this->ticketMessage = $ticketMessage;
        $this->replyingUser = $replyingUser;
    }
}
