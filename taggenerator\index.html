<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Saros-Tag-Generator</title>
  <script src="https://cdn.tailwindcss.com"></script>
  <style>
    body {
      font-family: 'Inter', sans-serif; /* A modern sans-serif font */
    }
    /* Custom scrollbar for a warm orange theme */
    ::-webkit-scrollbar {
      width: 8px;
      height: 8px;
    }
    ::-webkit-scrollbar-track {
      background: #2d2d2d; /* Darker background for contrast */
    }
    ::-webkit-scrollbar-thumb {
      background: #f59e0b; /* amber-500 */
      border-radius: 4px;
    }
    ::-webkit-scrollbar-thumb:hover {
      background: #d97706; /* amber-600 */
    }

    .btn-primary {
      @apply bg-gradient-to-r from-amber-500 to-orange-600 hover:from-amber-600 hover:to-orange-700 text-white font-semibold py-3 px-6 rounded-lg shadow-md hover:shadow-lg transition-all duration-300 ease-in-out transform hover:scale-105;
    }
    .btn-secondary { /* Updated secondary button for consistency or alternative style */
      @apply bg-slate-700 hover:bg-orange-800 text-slate-200 font-semibold py-3 px-6 rounded-lg shadow-md hover:shadow-lg transition-all duration-300 ease-in-out transform hover:scale-105;
    }

    /* Ensure html, body, and #root take full height for natural flow */
    html, body, #root {
      height: 100%;
      margin: 0;
    }
    /* body will get its background color from App.tsx's main div (bg-slate-900) or its own class.
       Padding is removed here as App.tsx controls its internal padding. */
    /* #root will simply be a full-height container for the React app */

  </style>
<!--
  Import Map for ES Modules:
  This allows using "bare" module specifiers (like 'react') or aliased paths
  (like '@/components/common/Icons') in JavaScript import statements,
  mapping them to actual URLs or paths.
  The error "The requested module '@/components/common/Icons' does not provide an export named 'BoltIcon'"
  means the browser, after resolving '@components/common/Icons' to './components/common/Icons.js' via this map,
  could not find 'BoltIcon' exported from the *JavaScript* file './components/common/Icons.js'.
  This usually indicates the .js file is outdated or not correctly compiled from its .tsx source.
-->
<script type="importmap">
{
  "imports": {
    "react": "https://esm.sh/react@^18.2.0",
    "react-dom/client": "https://esm.sh/react-dom@^18.2.0/client",
    "react/jsx-runtime": "https://esm.sh/react@^18.2.0/jsx-runtime",
    "@google/generative-ai": "https://esm.sh/@google/generative-ai@^0.21.0",
    "react-dom/": "https://esm.sh/react-dom@^18.2.0/",
    "react/": "https://esm.sh/react@^18.2.0/",
    "@/components/common/Icons": "./components/common/Icons.js",
    "@/services/geminiService": "./services/geminiService.js",
    "path": "https://esm.sh/path@^0.12.7",
    "vite": "https://esm.sh/vite@^6.3.5"
  }
}
</script>
</head>
<body class="bg-slate-900 text-slate-200"> <!-- Removed p-4 from body -->
  <div id="root"></div>
  <script type="module" src="./index.js"></script>
</body>
</html><link rel="stylesheet" href="index.css">
<script src="index.tsx" type="module"></script><link rel="stylesheet" href="index.css">
<script src="index.tsx" type="module"></script>
