<?php

namespace App\Livewire;

use Livewire\Component;

class ImageGallery extends Component
{
    public $isOpen = false;
    public $images = [];
    public $currentIndex = 0;
    public $messageId = null;
    public $title = '';

    protected $listeners = [
        'open-image-gallery' => 'openGallery',
    ];

    public function render()
    {
        return view('livewire.image-gallery');
    }

    public function openGallery($messageId, $index)
    {
        $this->messageId = $messageId;
        $this->currentIndex = $index;
        $this->isOpen = true;

        // The actual images will be loaded via Alpine.js from the DOM
        // This is more efficient than passing all image data through Livewire
        $this->dispatch('build-gallery-data', messageId: $messageId);
    }

    public function closeGallery()
    {
        $this->isOpen = false;
        $this->images = [];
        $this->currentIndex = 0;
        $this->messageId = null;
        $this->title = '';
    }

    public function navigate($direction)
    {
        // This will be handled by Alpine.js since it has the gallery data
        $this->dispatch('navigate-gallery', direction: $direction);
    }
}
