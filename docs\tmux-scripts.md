# tmux Scripts Documentation

This document provides detailed information about the tmux scripts included in the Minewache project for managing development and production environments.

## Overview

The project includes several tmux scripts to simplify the management of multiple services required for the application to run properly. These scripts create organized tmux sessions with multiple windows, each running a specific service.

## Available Scripts

### 1. Development Scripts

#### `scripts/tmux-dev.sh`

A simple wrapper script that calls `tmux-complete.sh`. Use this for standard development environments.

```bash
./scripts/tmux-dev.sh
```

#### `scripts/tmux-complete.sh`

The main development script that:
- Checks if required ports are available
- Stops any running Discord bot instances
- Synchronizes Discord bot environment variables
- Creates a tmux session with windows for:
  - <PERSON>vel development server
  - Queue worker
  - Vite development server
  - Reverb WebSocket server
  - Discord bot
  - Command shell

```bash
./scripts/tmux-complete.sh
```

### 2. Production Scripts

#### `scripts/tmux-production.sh`

A script for running production services in a tmux session when systemd services are not available or for temporary testing. This script:
- Checks if required ports are available
- Stops any running Discord bot instances
- Synchronizes Discord bot environment variables
- Creates a tmux session with windows for:
  - Queue worker
  - Reverb WebSocket server
  - Discord bot
  - Command shell

```bash
./scripts/tmux-production.sh
```

#### `scripts/monitor-production.sh`

A monitoring script for production environments. This script creates a tmux session with windows for monitoring the status and logs of systemd services. It does NOT start or manage the services themselves.

```bash
./scripts/monitor-production.sh
```

## Usage Instructions

### Basic Usage

1. Make sure the script is executable:
   ```bash
   chmod +x scripts/tmux-dev.sh
   chmod +x scripts/tmux-production.sh
   chmod +x scripts/monitor-production.sh
   ```

2. Run the appropriate script:
   ```bash
   # For development
   ./scripts/tmux-dev.sh
   
   # For production
   ./scripts/tmux-production.sh
   
   # For monitoring production services
   ./scripts/monitor-production.sh
   ```

### tmux Navigation

Once inside a tmux session, you can navigate using the following key combinations:

- `Ctrl+B` followed by a number (0-6): Switch to the corresponding window
- `Ctrl+B` followed by `d`: Detach from the session (leaves it running in the background)
- `Ctrl+B` followed by `c`: Create a new window
- `Ctrl+B` followed by `n`: Go to the next window
- `Ctrl+B` followed by `p`: Go to the previous window
- `Ctrl+B` followed by `?`: Show help with all key bindings

### Reattaching to a Session

If you've detached from a session, you can reattach using:

```bash
# For development session
tmux attach -t minewache-dev

# For production session
tmux attach -t minewache-prod

# For monitoring session
tmux attach -t minewache-monitor
```

### Ending a Session

To completely end a session and all its processes:

```bash
# For development session
tmux kill-session -t minewache-dev

# For production session
tmux kill-session -t minewache-prod

# For monitoring session
tmux kill-session -t minewache-monitor
```

## Troubleshooting

### Port Conflicts

If the script reports that ports are already in use, you can either:
1. Allow the script to free the ports automatically (recommended)
2. Manually free the ports using commands like:
   ```bash
   lsof -i:8000 -sTCP:LISTEN -t | xargs kill
   ```

### Discord Bot Issues

If the Discord bot fails to start:
1. Check that environment variables are properly set in `.env`
2. Ensure the bot token is valid
3. Check Discord bot logs for specific errors

### Session Already Exists

If you get an error that the session already exists:
1. Reattach to the existing session: `tmux attach -t minewache-dev`
2. Or kill the existing session: `tmux kill-session -t minewache-dev`

## Production Deployment

For proper production deployment, it's recommended to:

1. Set up systemd services using `scripts/setup-production.sh` (requires root/sudo)
2. Use `scripts/monitor-production.sh` for monitoring the services
3. Only use `scripts/tmux-production.sh` as a fallback if systemd services are not available

## Additional Notes

- The scripts are designed to work on Ubuntu Linux and similar distributions
- For Windows environments, use WSL (Windows Subsystem for Linux) to run these scripts
- Make sure tmux is installed: `sudo apt-get install tmux`
