<?php

return [
    // Application Form Progress
    'step_of' => 'Schritt :current von :total',
    'completed' => ':percent% abgeschlossen',
    'personal_data' => 'Persönliche Daten',
    'role_specific' => 'Rollenspezifisch',
    'about_you' => 'Über Dich', // Corrected from 'Über dich' for consistency if titles are capitalized
    'review' => 'Überprüfung', // Corrected from 'Überprüfen' to be a noun
    'review_submit' => 'Überprüfen & Absenden',

    // General Headers/Titles
    'application_form' => 'Bewerbungsformular',
    'application_description' => 'Werde Teil unseres kreativen Teams und hilf dabei, fantastische Minecraft-Inhalte zum Leben zu erwecken.',
    'personal_data_description' => 'Erzähle uns ein wenig über dich, um zu beginnen.',

    // Form Fields - General
    'name' => 'Name',
    'name_placeholder' => 'Dein bevorzugter Name',
    'name_help' => 'Wie sollen wir dich nennen?',
    'name_description' => 'Dies kann dein Discord-Name, Spitzname oder echter Name sein - deine Wahl.',

    'age' => 'Alter',
    'age_placeholder' => 'Dein Alter',
    'age_help' => 'Wir benötigen dein Alter für die Rollenzuweisung.',

    'gender' => 'Geschlecht',
    'gender_select' => 'Bitte auswählen', // Changed from 'Option auswählen' to common 'Bitte auswählen'
    'gender_male' => 'Männlich',
    'gender_female' => 'Weiblich',
    'gender_diverse' => 'Divers',
    'gender_no_info' => 'Keine Angabe',

    'pronouns' => 'Pronomen',
    'pronouns_placeholder' => 'z.B. er/ihm, sie/ihr, dey/deren',
    'optional' => '(optional)',

    // Professions
    'professions' => 'Tätigkeiten', // Used in Review section
    'professions_question' => 'Welche Rolle(n) interessieren dich?',
    'professions_select_description' => 'Wähle alle Positionen aus, für die du dich bewerben möchtest.',
    'professions_help' => 'Wähle mindestens eine aus',
    'professions_select_at_least_one' => 'Wähle mindestens eine Tätigkeit aus', // More specific

    'profession_description' => [
        'actor' => 'Schauspieler/in',
        'actor_no_voice' => 'Schauspieler/in (ohne Stimme)',
        'voice_actor' => 'Synchronsprecher/in',
        'builder' => 'Builder/in',
        'designer' => 'Designer/in',
        'cutter' => 'Cutter/in (Videoschnitt)',
        'cameraman' => 'Kameramann/frau',
        'developer' => 'Entwickler/in',
        'modeler' => '3D-Modellierer/in',
        'music_producer' => 'Musikproduzent/in',
        'other' => 'Andere Tätigkeit'
    ],
    'profession_subtitle' => [
        'actor' => 'Stimme + Performance',
        'actor_no_voice' => 'Nur Performance',
        'voice_actor' => 'Nur Stimme',
        'builder' => 'Architektur & Design',
        'designer' => 'Grafik & Visuelles',
        'cutter' => 'Videoschnitt',
        'cameraman' => 'Aufnahme & Film',
        'developer' => 'Mods & Programmierung',
        'modeler' => '3D-Modelle & Assets',
        'music_producer' => 'Audio & Musik',
        'other' => 'Benutzerdefinierte Rolle'
    ],
    'profession_description_long' => [
        'actor' => 'Erwecke Charaktere mit Schauspiel und Sprachaufnahmen zum Leben.',
        'actor_no_voice' => 'Fokussiere dich auf Schauspiel und Charakterdarstellung ohne Spracharbeit.',
        'voice_actor' => 'Stelle Sprachaufnahmen für Charaktere ohne Schauspiel bereit.',
        'builder' => 'Erstelle beeindruckende Bauten und architektonische Designs für unsere Welt.',
        'designer' => 'Gestalte Grafiken, Thumbnails und visuellen Content für unsere Marke.',
        'cutter' => 'Schneide und produziere hochwertige Videoinhalte für unsere Kanäle.',
        'cameraman' => 'Nimm fantastische Aufnahmen auf und verwalte Aufnahmesitzungen.',
        'developer' => 'Entwickle Mods und technische Lösungen für unseren Minecraft-Server.',
        'modeler' => 'Erstelle 3D-Modelle, Texturen und Assets für unsere Projekte.',
        'music_producer' => 'Komponiere und produziere Musik und Soundeffekte für unsere Inhalte.',
        'other' => 'Bringe deine einzigartigen Fähigkeiten und Talente in unser kreatives Team ein.'
    ],
    'other_profession' => 'Andere Tätigkeit',
    'other_profession_placeholder' => 'Bitte gib deine andere Tätigkeit an',
    'other_profession_help' => 'Bitte genauer beschreiben',

    // Step 2: Role-Specific Information
    'please_select_at_least_one_profession' => 'Bitte gehe zurück und wähle mindestens eine Tätigkeit aus.',
    'acting_voice_acting' => 'Schauspiel & Synchronisation',
    'voice_type' => 'Stimmlage',
    'voice_type_deep' => 'Tief',
    'voice_type_medium' => 'Mittel',
    'voice_type_high' => 'Hoch',
    'microphone' => 'Mikrofon',
    'microphone_placeholder' => 'z.B. Rode NT-USB, Blue Yeti',

    'builder_cameraman' => 'PC & Technische Informationen',
    'has_pc_question' => 'Hast du einen PC?',
    'has_modrinth_question' => 'Benutzt du Modrinth?',
    'has_minecraft_java_question' => 'Besitzt du die Minecraft Java Edition?',

    'ram' => 'RAM (PC)',
    'ram_placeholder' => 'z.B. 16GB',
    'fps' => 'Durchschnittliche FPS in Minecraft',
    'fps_less_than_10' => '<10 FPS',
    'fps_10' => '10-19 FPS',
    'fps_20' => '20-29 FPS',
    'fps_30' => '30-39 FPS',
    'fps_40' => '40-49 FPS',
    'fps_50' => '50-59 FPS',
    'fps_greater_than_60' => '60+ FPS',

    'gpu' => 'Grafikkarte (GPU)',
    'gpu_placeholder' => 'z.B. NVIDIA GeForce RTX 3060, AMD Radeon RX 6700 XT',

    'designer' => 'Designer/in',
    'design_programs' => 'Design-Programme',
    'design_programs_placeholder' => 'z.B. Photoshop, Illustrator, Figma',
    'design_style' => 'Bevorzugter Design-Stil',
    'design_style_placeholder' => 'z.B. Minimalistisch, Cartoon, Realistisch',
    'favorite_design' => 'Lieblingsdesign (Beispiel)',
    'favorite_design_placeholder' => 'Link zu einem Design, das du bewunderst',

    'developer' => 'Entwickler/in',
    'programming_languages' => 'Programmiersprachen',
    'programming_languages_placeholder' => 'z.B. Java, JavaScript, Python',
    'preferred_ide' => 'Bevorzugte IDE',
    'preferred_ide_placeholder' => 'z.B. VS Code, IntelliJ IDEA',

    'modeler' => '3D-Modellierer/in',
    'modeler_software_label' => '3D-Modellierungssoftware',
    // Using the generic daw_placeholder as per blade file, consider specific if needed
    // 'modeler_software_placeholder' => 'z.B. Blender, Cinema 4D',

    'music_producer' => 'Musikproduzent/in',
    'daw' => 'DAW (Digital Audio Workstation)',
    'daw_placeholder' => 'z.B. FL Studio, Ableton Live, Logic Pro',

    'portfolio' => 'Portfolio / Referenzen',
    'portfolio_placeholder' => 'Link(s) zu deiner Arbeit (z.B. GitHub, ArtStation, YouTube)',
    'desired_role' => 'Wunschrolle Beschreibung',
    'desired_role_placeholder' => 'Beschreibe kurz deine ideale Rolle oder Beiträge',

    // Step 3: About You
    'experience' => 'Erfahrung',
    'experience_placeholder' => 'Deine Erfahrung in den ausgewählten Rollen',
    'motivation' => 'Motivation',
    'motivation_placeholder' => 'Warum interessierst du dich für eine Mitarbeit in unserem Team?',
    'about_you_text' => 'Über Dich', // Matches progress bar
    'about_you_placeholder' => 'Erzähle uns mehr über dich, deine Fähigkeiten und Interessen (mind. 50 Zeichen).',
    'strengths_weaknesses' => 'Stärken & Schwächen',
    'strengths_weaknesses_placeholder' => 'Was kannst du gut? In welchen Bereichen möchtest du dich verbessern? (mind. 50 Zeichen).',
    'final_words' => 'Abschließende Worte',
    'final_words_placeholder' => 'Möchtest du uns sonst noch etwas mitteilen?',
    'characters' => 'Zeichen',

    // Step 4: Review & Submit
    'confirm_correct_information' => 'Ich bestätige, dass alle angegebenen Informationen korrekt sind und ich die DSGVO-Hinweise gelesen habe.',
    'not_specified' => 'Keine Angabe',
    'checkbox_questions' => 'Checkbox-Fragen',
    'yes' => 'Ja',
    'no' => 'Nein',

    // Form Controls & Actions
    'next_step' => 'Nächster Schritt',
    'previous_step' => 'Vorheriger Schritt',
    'submit_application' => 'Bewerbung absenden',
    'edit_application' => 'Bewerbung bearbeiten',

    // Processing & Submission Status
    'processing' => 'Wird verarbeitet...',
    'application_submitted' => 'Bewerbung abgeschickt!',
    'application_updated' => 'Bewerbung aktualisiert!',
    'thank_you' => 'Vielen Dank für deine Bewerbung!',
    'we_will_contact' => 'Wir werden deine Bewerbung prüfen und dich per Discord kontaktieren, falls du in die engere Auswahl kommst.',

    // Errors & Notices
    'required_fields_notice' => 'Mit * markierte Felder sind Pflichtfelder.',
    'application_not_editable' => 'Diese Bewerbung kann derzeit nicht bearbeitet werden.',
    'application_not_found_or_no_access' => 'Bewerbung nicht gefunden oder du hast keinen Zugriff darauf.',
    'filtering_error' => 'Fehler beim Filtern: :error',

    // General Table/Admin terms
    'created_at' => 'Erstellt am',
    'updated_at' => 'Aktualisiert am',
    'status' => 'Status',
    'confirmation' => 'Bestätigung',
];
