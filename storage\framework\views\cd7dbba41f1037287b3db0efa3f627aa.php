<?php if (isset($component)) { $__componentOriginal9ac128a9029c0e4701924bd2d73d7f54 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54 = $attributes; } ?>
<?php $component = App\View\Components\AppLayout::resolve([] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('app-layout'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\App\View\Components\AppLayout::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
     <?php $__env->slot('heading', null, []); ?> 
        <?php echo e(__('tickets.support_tickets')); ?>

     <?php $__env->endSlot(); ?>

    <!-- Enhanced Theme Switcher -->
    <?php if (isset($component)) { $__componentOriginal5ae8d5ca1c67746ce4565d9cc8953813 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal5ae8d5ca1c67746ce4565d9cc8953813 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.enhanced-theme-switcher','data' => ['position' => 'bottom-left','type' => 'simple']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('enhanced-theme-switcher'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['position' => 'bottom-left','type' => 'simple']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal5ae8d5ca1c67746ce4565d9cc8953813)): ?>
<?php $attributes = $__attributesOriginal5ae8d5ca1c67746ce4565d9cc8953813; ?>
<?php unset($__attributesOriginal5ae8d5ca1c67746ce4565d9cc8953813); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal5ae8d5ca1c67746ce4565d9cc8953813)): ?>
<?php $component = $__componentOriginal5ae8d5ca1c67746ce4565d9cc8953813; ?>
<?php unset($__componentOriginal5ae8d5ca1c67746ce4565d9cc8953813); ?>
<?php endif; ?>

    <div class="py-8">
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
            <?php
$__split = function ($name, $params = []) {
    return [$name, $params];
};
[$__name, $__params] = $__split('ticket-list', []);

$__html = app('livewire')->mount($__name, $__params, 'lw-2813764637-0', $__slots ?? [], get_defined_vars());

echo $__html;

unset($__html);
unset($__name);
unset($__params);
unset($__split);
if (isset($__slots)) unset($__slots);
?>
        </div>
    </div>

    <!-- Floating Action Button for Create Ticket -->
    <?php if (isset($component)) { $__componentOriginaleba2316cb51babd45ec9bbb202810df6 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginaleba2316cb51babd45ec9bbb202810df6 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.modern-fab','data' => ['href' => ''.e(route('tickets.create')).'','position' => 'bottom-right','size' => 'lg','variant' => 'primary','extended' => 'true','label' => ''.e(__('tickets.new_ticket')).'','icon' => '<svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path></svg>','class' => 'modern-fab']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('modern-fab'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['href' => ''.e(route('tickets.create')).'','position' => 'bottom-right','size' => 'lg','variant' => 'primary','extended' => 'true','label' => ''.e(__('tickets.new_ticket')).'','icon' => '<svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path></svg>','class' => 'modern-fab']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginaleba2316cb51babd45ec9bbb202810df6)): ?>
<?php $attributes = $__attributesOriginaleba2316cb51babd45ec9bbb202810df6; ?>
<?php unset($__attributesOriginaleba2316cb51babd45ec9bbb202810df6); ?>
<?php endif; ?>
<?php if (isset($__componentOriginaleba2316cb51babd45ec9bbb202810df6)): ?>
<?php $component = $__componentOriginaleba2316cb51babd45ec9bbb202810df6; ?>
<?php unset($__componentOriginaleba2316cb51babd45ec9bbb202810df6); ?>
<?php endif; ?>
 <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54)): ?>
<?php $attributes = $__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54; ?>
<?php unset($__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal9ac128a9029c0e4701924bd2d73d7f54)): ?>
<?php $component = $__componentOriginal9ac128a9029c0e4701924bd2d73d7f54; ?>
<?php unset($__componentOriginal9ac128a9029c0e4701924bd2d73d7f54); ?>
<?php endif; ?>
<?php /**PATH C:\Users\<USER>\Documents\GitHub\minewache-website\resources\views/tickets/index.blade.php ENDPATH**/ ?>