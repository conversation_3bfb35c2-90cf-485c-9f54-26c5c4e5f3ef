<?php

namespace Tests;

use Illuminate\Foundation\Testing\TestCase as BaseTestCase;
use Illuminate\Support\Facades\Event;

abstract class TestCase extends BaseTestCase
{
    use CreatesApplication;

    protected function setUp(): void
    {
        parent::setUp();

        // Register our mock Pusher service provider
        $this->app->register(MockPusherServiceProvider::class);

        // Fake all events that should be broadcast
        Event::fake([
            \App\Events\TicketMessageCreated::class,
        ]);
    }
}
