<?php

namespace App\Console\Commands;

use App\Models\TicketMessage;
use App\Services\DiscordService;
use Illuminate\Console\Command;

class RetryFailedTicketSync extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'tickets:retry-sync {--all : Retry all messages, not just failed ones} {--limit=50 : Maximum number of messages to process}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Retry synchronization of failed ticket messages with Discord';

    /**
     * Execute the console command.
     */
    public function handle(DiscordService $discordService)
    {
        $query = TicketMessage::query()
            ->where('is_from_discord', false) // Only messages from web to Discord
            ->whereNull('discord_message_id'); // No Discord message ID yet
        
        if (!$this->option('all')) {
            $query->where('sync_failed', true); // Only failed messages
        }
        
        $limit = (int) $this->option('limit');
        $messages = $query->limit($limit)->get();
        
        $count = $messages->count();
        $this->info("Found {$count} messages to retry synchronization.");
        
        if ($count === 0) {
            return;
        }
        
        $bar = $this->output->createProgressBar($count);
        $bar->start();
        
        $success = 0;
        $failed = 0;
        
        foreach ($messages as $message) {
            $result = $discordService->sendTicketMessage($message);
            
            if ($result) {
                $success++;
            } else {
                $failed++;
            }
            
            $bar->advance();
        }
        
        $bar->finish();
        $this->newLine(2);
        
        $this->info("Synchronization completed:");
        $this->info("- Successfully synchronized: {$success}");
        $this->info("- Failed to synchronize: {$failed}");
    }
}
