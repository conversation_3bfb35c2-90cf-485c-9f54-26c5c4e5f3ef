<?php if (isset($component)) { $__componentOriginal9ac128a9029c0e4701924bd2d73d7f54 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54 = $attributes; } ?>
<?php $component = App\View\Components\AppLayout::resolve([] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('app-layout'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\App\View\Components\AppLayout::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
     <?php $__env->slot('title', null, []); ?> Bewerbung anzeigen - MineWache <?php $__env->endSlot(); ?>

    <div class="container mx-auto px-4 py-8">
        <div class="max-w-4xl mx-auto">
            <div class="flex justify-between items-center mb-6">
                <h1 class="text-2xl font-bold">Bewerbung #<?php echo e($application->id); ?></h1>
                <div class="flex flex-col md:flex-row gap-2">
                    <div>
                        <span class="mr-2">Status:</span>
                        <?php if($application->status === 'pending'): ?>
                            <span class="badge badge-warning">In Bearbeitung</span>
                        <?php elseif($application->status === 'approved'): ?>
                            <span class="badge badge-success">Angenommen</span>
                        <?php elseif($application->status === 'rejected'): ?>
                            <span class="badge badge-error">Abgelehnt</span>
                        <?php else: ?>
                            <span class="badge"><?php echo e($application->status); ?></span>
                        <?php endif; ?>
                    </div>
                    <div>
                        <?php if($application->editable): ?>
                            <span class="badge badge-success gap-1">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 11V7a4 4 0 118 0m-4 8v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2z" />
                                </svg>
                                Bearbeitbar
                            </span>
                        <?php else: ?>
                            <span class="badge badge-neutral gap-1">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
                                </svg>
                                Nicht bearbeitbar
                            </span>
                        <?php endif; ?>
                    </div>
                </div>
            </div>

            <div class="flex flex-col space-y-2 mb-6 text-sm text-gray-500">
                <div>Eingereicht am <?php echo e($application->created_at->format('d.m.Y')); ?> um <?php echo e($application->created_at->format('H:i')); ?> Uhr</div>
                <?php if($application->updated_at->gt($application->created_at)): ?>
                    <div>Zuletzt aktualisiert am <?php echo e($application->updated_at->format('d.m.Y')); ?> um <?php echo e($application->updated_at->format('H:i')); ?> Uhr</div>
                <?php endif; ?>
            </div>

            <div class="card bg-base-200 shadow mb-6">
                <div class="card-body">
                    <h2 class="card-title mb-4">Persönliche Informationen</h2>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                            <p class="text-sm font-medium mb-1">Name</p>
                            <p><?php echo e($application->name ?? 'Nicht angegeben'); ?></p>
                        </div>
                        <div>
                            <p class="text-sm font-medium mb-1">Alter</p>
                            <p><?php echo e($application->age ?? 'Nicht angegeben'); ?></p>
                        </div>
                        <div>
                            <p class="text-sm font-medium mb-1">Geschlecht</p>
                            <p><?php echo e($application->gender ?? 'Nicht angegeben'); ?></p>
                        </div>
                        <div>
                            <p class="text-sm font-medium mb-1">Pronomen</p>
                            <p><?php echo e($application->pronouns ?? 'Nicht angegeben'); ?></p>
                        </div>
                    </div>
                </div>
            </div>

            <div class="card bg-base-200 shadow mb-6">
                <div class="card-body">
                    <h2 class="card-title mb-4">Tätigkeitsbereiche</h2>

                    <?php if(!empty($application->professions)): ?>
                        <div class="flex flex-wrap gap-2">
                            <?php $__currentLoopData = $application->professions; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $profession): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <span class="badge badge-lg">
                                    <?php if($profession == 'actor'): ?>
                                        Schauspieler
                                    <?php elseif($profession == 'voice_actor'): ?>
                                        Synchronsprecher
                                    <?php elseif($profession == 'builder'): ?>
                                        Builder
                                    <?php elseif($profession == 'designer'): ?>
                                        Designer
                                    <?php elseif($profession == 'cutter'): ?>
                                        Cutter
                                    <?php elseif($profession == 'cameraman'): ?>
                                        Kameramann
                                    <?php elseif($profession == 'developer'): ?>
                                        Entwickler
                                    <?php elseif($profession == 'modeler'): ?>
                                        3D-Modellierer
                                    <?php elseif($profession == 'music_producer'): ?>
                                        Musikproduzent
                                    <?php elseif($profession == 'other'): ?>
                                        Andere
                                    <?php else: ?>
                                        <?php echo e($profession); ?>

                                    <?php endif; ?>
                                </span>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        </div>
                    <?php else: ?>
                        <p>Keine Tätigkeitsbereiche ausgewählt</p>
                    <?php endif; ?>
                </div>
            </div>

            <div class="card bg-base-200 shadow mb-6">
                <div class="card-body">
                    <h2 class="card-title mb-4">Über dich</h2>
                    <div class="mb-4">
                        <p class="text-sm font-medium mb-1">Erfahre mehr über dich</p>
                        <p class="whitespace-pre-wrap"><?php echo e($application->about_you ?? 'Nicht angegeben'); ?></p>
                    </div>
                    <div class="mb-4">
                        <p class="text-sm font-medium mb-1">Stärken und Schwächen</p>
                        <p class="whitespace-pre-wrap"><?php echo e($application->strengths_weaknesses ?? 'Nicht angegeben'); ?></p>
                    </div>
                    <div>
                        <p class="text-sm font-medium mb-1">Abschließende Worte</p>
                        <p class="whitespace-pre-wrap"><?php echo e($application->final_words ?? 'Nicht angegeben'); ?></p>
                    </div>
                </div>
            </div>

            <?php if(in_array('voice_actor', $application->professions ?? [])): ?>
                <div class="card bg-base-200 shadow mb-6">
                    <div class="card-body">
                        <h2 class="card-title mb-4">Synchronsprecher-Informationen</h2>
                        <div class="mb-4">
                            <p class="text-sm font-medium mb-1">Stimmtyp</p>
                            <p><?php echo e($application->voice_type ?? 'Nicht angegeben'); ?></p>
                        </div>
                        <div>
                            <p class="text-sm font-medium mb-1">Mikrofon</p>
                            <p><?php echo e($application->microphone ?? 'Nicht angegeben'); ?></p>
                        </div>
                    </div>
                </div>
            <?php endif; ?>

            <?php if(in_array('developer', $application->professions ?? [])): ?>
                <div class="card bg-base-200 shadow mb-6">
                    <div class="card-body">
                        <h2 class="card-title mb-4">Entwickler-Informationen</h2>
                        <div class="mb-4">
                            <p class="text-sm font-medium mb-1">Programmiersprachen</p>
                            <p><?php echo e($application->languages ?? 'Nicht angegeben'); ?></p>
                        </div>
                        <div>
                            <p class="text-sm font-medium mb-1">IDE</p>
                            <p><?php echo e($application->ide ?? 'Nicht angegeben'); ?></p>
                        </div>
                    </div>
                </div>
            <?php endif; ?>

            <?php if(in_array('designer', $application->professions ?? [])): ?>
                <div class="card bg-base-200 shadow mb-6">
                    <div class="card-body">
                        <h2 class="card-title mb-4">Designer-Informationen</h2>
                        <div class="mb-4">
                            <p class="text-sm font-medium mb-1">Programme</p>
                            <p><?php echo e($application->program ?? 'Nicht angegeben'); ?></p>
                        </div>
                        <div class="mb-4">
                            <p class="text-sm font-medium mb-1">Design-Stil</p>
                            <p><?php echo e($application->design_style ?? 'Nicht angegeben'); ?></p>
                        </div>
                        <div>
                            <p class="text-sm font-medium mb-1">Portfolio</p>
                            <p><?php echo e($application->portfolio ?? 'Nicht angegeben'); ?></p>
                        </div>
                    </div>
                </div>
            <?php endif; ?>

            <div class="flex justify-between mt-8">
                <?php if (isset($component)) { $__componentOriginal19987cd45c699eefead8b7a8ac222798 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal19987cd45c699eefead8b7a8ac222798 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.modern-button','data' => ['variant' => 'ghost','href' => ''.e(route('my.applications')).'']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('modern-button'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['variant' => 'ghost','href' => ''.e(route('my.applications')).'']); ?>Zurück zur Übersicht <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal19987cd45c699eefead8b7a8ac222798)): ?>
<?php $attributes = $__attributesOriginal19987cd45c699eefead8b7a8ac222798; ?>
<?php unset($__attributesOriginal19987cd45c699eefead8b7a8ac222798); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal19987cd45c699eefead8b7a8ac222798)): ?>
<?php $component = $__componentOriginal19987cd45c699eefead8b7a8ac222798; ?>
<?php unset($__componentOriginal19987cd45c699eefead8b7a8ac222798); ?>
<?php endif; ?>

                <div class="flex flex-col gap-4">
                    <?php if($application->editable): ?>
                        <a href="<?php echo e(route('my.applications.edit', $application->id)); ?>" class="btn btn-primary">
                            Bewerbung bearbeiten
                        </a>
                    <?php else: ?>
                        <div class="tooltip" data-tip="Ein Administrator muss die Bearbeitung freischalten">
                            <button class="btn btn-disabled">
                                Bewerbung bearbeiten
                            </button>
                        </div>
                    <?php endif; ?>

                    <div class="alert alert-info shadow-lg">
                        <div>
                            <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" class="stroke-current flex-shrink-0 w-6 h-6"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path></svg>
                            <div>
                                <p class="text-sm">Möchtest du dich für weitere Bereiche bewerben? <a href="<?php echo e(route('bewerben')); ?>" class="link link-primary">Reiche eine neue Bewerbung ein</a>.</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
 <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54)): ?>
<?php $attributes = $__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54; ?>
<?php unset($__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal9ac128a9029c0e4701924bd2d73d7f54)): ?>
<?php $component = $__componentOriginal9ac128a9029c0e4701924bd2d73d7f54; ?>
<?php unset($__componentOriginal9ac128a9029c0e4701924bd2d73d7f54); ?>
<?php endif; ?><?php /**PATH C:\Users\<USER>\Documents\GitHub\minewache-website\resources\views/applications/my-application-show.blade.php ENDPATH**/ ?>