<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Check if the misspelled column exists
        if (Schema::hasColumn('applications', 'staus')) {
            Schema::table('applications', function (Blueprint $table) {
                // Drop the misspelled column
                $table->dropColumn('staus');
            });
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // We don't want to recreate the misspelled column
        // So this is intentionally left empty
    }
};
