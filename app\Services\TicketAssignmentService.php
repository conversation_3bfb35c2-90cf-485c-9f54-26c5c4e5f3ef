<?php

namespace App\Services;

use App\Models\Ticket;
use App\Models\User;
use App\Models\TicketMessage;
use App\Events\TicketAssignmentUpdated;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Event;
use Exception;

class TicketAssignmentService
{
    /**
     * Assigns a ticket to a user or unassigns it.
     *
     * @param int $ticketDbId The ID of the ticket in the database.
     * @param int|null $assigneeUserId The Laravel User ID of the staff member to assign the ticket to. Null to unassign.
     * @param int $assignerUserId The Laravel User ID of the person performing the assignment.
     * @return array ['success' => bool, 'message' => string, 'ticket' => ?Ticket]
     */
    public function assignTicket(int $ticketDbId, ?int $assigneeUserId, int $assignerUserId): array
    {
        Log::info("Attempting to assign ticket #{$ticketDbId} to user ID {$assigneeUserId} by user ID {$assignerUserId}.");

        try {
            $ticket = Ticket::with('creator')->find($ticketDbId);
            if (!$ticket) {
                Log::error("TicketAssignmentService: Ticket #{$ticketDbId} not found.");
                return ['success' => false, 'message' => "Ticket #{$ticketDbId} not found.", 'ticket' => null];
            }

            $assignerUser = User::find($assignerUserId);
            if (!$assignerUser) {
                // This should ideally not happen if the assigner is an authenticated user
                Log::error("TicketAssignmentService: Assigner user #{$assignerUserId} not found.");
                return ['success' => false, 'message' => "Assigner user #{$assignerUserId} not found.", 'ticket' => $ticket];
            }

            $assigneeUser = null;
            if ($assigneeUserId !== null) {
                $assigneeUser = User::find($assigneeUserId);
                if (!$assigneeUser) {
                    Log::error("TicketAssignmentService: Assignee user #{$assigneeUserId} not found for ticket #{$ticketDbId}.");
                    return ['success' => false, 'message' => "Assignee user #{$assigneeUserId} not found.", 'ticket' => $ticket];
                }
            }

            $oldAssigneeId = $ticket->assigned_to_user_id;
            $ticket->assigned_to_user_id = $assigneeUserId;
            $ticket->save();

            // Create a system message for the assignment
            $systemMessageContent = '';
            if ($assigneeUser) {
                $systemMessageContent = "Ticket assigned to {$assigneeUser->name} by {$assignerUser->name}.";
                 // If assignee name is not readily available, use ID or a generic term
                if (empty($assigneeUser->name)) $systemMessageContent = "Ticket assigned to User ID: {$assigneeUser->id} by {$assignerUser->name}.";

            } else {
                $systemMessageContent = "Ticket unassigned by {$assignerUser->name}.";
            }
            
            TicketMessage::create([
                'ticket_id' => $ticket->id,
                'user_id' => $assignerUserId, // Or a dedicated system user ID
                'content' => $systemMessageContent,
                'is_system_message' => true,
            ]);

            Log::info("TicketAssignmentService: Ticket #{$ticketDbId} " . ($assigneeUser ? "assigned to {$assigneeUser->name} (ID: {$assigneeUserId})" : "unassigned") . " by {$assignerUser->name} (ID: {$assignerUserId}). Old assignee ID: {$oldAssigneeId}.");

            // Dispatch an event
            Event::dispatch(new TicketAssignmentUpdated($ticket, $assigneeUser, $assignerUser));
            
            $successMessage = $assigneeUser ? "Ticket #{$ticketDbId} successfully assigned to {$assigneeUser->name}." : "Ticket #{$ticketDbId} successfully unassigned.";
            return ['success' => true, 'message' => $successMessage, 'ticket' => $ticket->load('assignedTo')];

        } catch (Exception $e) {
            Log::error("TicketAssignmentService: Exception during assignment for ticket #{$ticketDbId}.", [
                'exception' => $e->getMessage(),
                'trace' => $e->getTraceAsString(), // Be careful with logging full trace in production
            ]);
            return ['success' => false, 'message' => 'An unexpected error occurred during ticket assignment.', 'ticket' => null];
        }
    }
}
