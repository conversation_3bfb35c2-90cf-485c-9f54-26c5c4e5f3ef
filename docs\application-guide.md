# Application Design Guide für Minewache-Website

Dieser Guide definiert die Design-Patterns für die Entwicklung von Benutzeroberflächen auf der Minewache-Website, mit besonderem Fokus auf Bewerbungsformulare und Landing Pages.

## Inhaltsverzeichnis

1. [Design-Philosophie](#design-philosophie)
2. [Layout-Struktur](#layout-struktur)
   - [Welcome Page](#welcome-page)
   - [Formular-Layouts](#formular-layouts)
3. [Komponenten](#komponenten)
   - [Progress-Indikatoren](#progress-indikatoren)
   - [Formularelemente](#formularelemente)
   - [Call-to-Action](#call-to-action)
4. [Animation und Interaktion](#animation-und-interaktion)
5. [Blade vs. Livewire Implementation](#blade-vs-livewire-implementation)
6. [Responsives Verhalten](#responsives-verhalten)
7. [Beispielimplementierungen](#beispielimplementierungen)

## Design-Philosophie

Die Minewache-Website folgt einer klaren, benutzerfreundlichen Design-Philosophie mit folgenden Grundprinzipien:

- **Klare Führung**: Nutzer werden durch intuitive Strukturen und visuelle Hierarchien geleitet
- **Schrittweise Erklärung**: Komplexe Prozesse werden in überschaubare Schritte aufgeteilt
- **Visuelle Rückmeldung**: Interaktionen werden durch visuelles Feedback bestätigt
- **Konsistente Erfahrung**: Einheitliches Erscheinungsbild über alle Seiten hinweg
- **Markenidentität**: Einbindung der Minewache Identität durch Schriften, Farben und Animationen

## Layout-Struktur

### Welcome Page

Die Welcome Page folgt einer Hero-Section Struktur:

```blade
<main class="flex flex-col min-h-dvh">
    <section class="flex-grow flex items-center justify-center bg-base-200">
        <div class="container mx-auto px-4 py-8">
            <div class="hero-content grid grid-cols-1 lg:grid-cols-2 gap-8 items-center">
                <!-- Linke Spalte: Text und CTA -->
                <div class="flex flex-col items-center lg:items-start order-2 lg:order-1">
                    <h1 class="font-display font-bold text-primary text-title tracking-tight mb-4">
                        Hauptüberschrift
                    </h1>
                    <p class="font-modern text-paragraph text-base-content mb-8 max-w-xl">
                        Beschreibungstext
                    </p>
                    <a href="{{ route('action') }}" class="btn btn-primary btn-lg gap-2">
                        Call-to-Action
                        <svg class="w-5 h-5"><!-- Icon --></svg>
                    </a>
                </div>
                
                <!-- Rechte Spalte: Logo oder Bild -->
                <div class="flex justify-center lg:justify-end order-1 lg:order-2">
                    <x-application-logo class="w-auto h-32 lg:h-48" />
                </div>
            </div>
        </div>
    </section>
</main>
```

### Formular-Layouts

Formulare verwenden ein Card-basiertes Layout:

```blade
<div class="card bg-base-200 shadow-xl w-full max-w-4xl mx-auto">
    <div class="card-body">
        <!-- Progress-Indikator bei mehrstufigen Formularen -->
        <div class="w-full mb-8">
            <!-- Progress-Komponente -->
        </div>

        <!-- Formular -->
        <form action="{{ route('action') }}" method="post" class="space-y-6">
            @csrf
            
            <!-- Fehleranzeige -->
            @if ($errors->any())
                <div class="alert alert-error">
                    <x-heroicon-o-exclamation-circle class="h-6 w-6" />
                    <span>Fehlermeldung</span>
                </div>
            @endif
            
            <!-- Formularfelder -->
            <div class="form-control w-full">
                <!-- Label -->
                <label class="label">
                    <span class="label-text font-medium">Feldname <span class="text-error">*</span></span>
                    <span class="label-text-alt opacity-60">Zusatzinfo</span>
                </label>
                
                <!-- Input mit Icon -->
                <div class="input-group">
                    <span class="btn btn-square btn-ghost">
                        <x-heroicon-o-icon class="h-5 w-5" />
                    </span>
                    <input type="text" class="input input-bordered w-full" />
                </div>
                
                <!-- Fehleranzeige für Feld -->
                @error('feldname') <span class="label-text-alt text-error mt-1">{{ $message }}</span> @enderror
                
                <!-- Hilfetext -->
                <div class="mt-1.5 text-xs text-base-content/70">
                    Hilfe und Erklärung zum Feld
                </div>
            </div>
            
            <!-- Aktionsbereich -->
            <div class="card-actions flex flex-col md:flex-row md:justify-between items-center gap-4 mt-8">
                <!-- Information -->
                <div class="text-sm opacity-75">
                    <!-- Hinweis -->
                </div>
                
                <!-- Buttons -->
                <div class="flex items-center gap-3">
                    <a href="#" class="btn btn-ghost">Abbrechen</a>
                    <button type="submit" class="btn btn-primary btn-lg">Weiter</button>
                </div>
            </div>
        </form>
    </div>
</div>
```

## Komponenten

### Progress-Indikatoren

Progress-Indikatoren für mehrstufige Formulare:

```blade
<div class="w-full mb-8">
    <div class="flex flex-col space-y-2">
        <!-- Textuelle Fortschrittsanzeige -->
        <div class="flex justify-between text-xs text-base-content/70">
            <span>Schritt X von Y</span>
            <span>N% abgeschlossen</span>
        </div>
        
        <!-- Steps-Komponente -->
        <ul class="steps steps-horizontal w-full">
            <li class="step step-primary font-medium">Abgeschlossener Schritt</li>
            <li class="step font-medium">Aktueller Schritt</li>
            <li class="step font-medium">Zukünftiger Schritt</li>
        </ul>
    </div>
    
    <!-- Beschreibung des aktuellen Schritts -->
    <div class="mt-6 px-2">
        <p class="text-sm text-base-content/80">
            Erklärung zum aktuellen Formularschritt
        </p>
    </div>
</div>
```

### Formularelemente

#### Texteingabefelder mit Icons

```blade
<div class="form-control w-full">
    <label class="label">
        <span class="label-text font-medium">Feldname <span class="text-error">*</span></span>
        <span class="label-text-alt opacity-60">Zusatzinfo</span>
    </label>
    <div class="input-group">
        <span class="btn btn-square btn-ghost">
            <x-heroicon-o-icon class="h-5 w-5" />
        </span>
        <input type="text" name="feldname" 
               placeholder="Platzhalter" 
               class="input input-bordered w-full @error('feldname') input-error @enderror focus:border-primary" />
    </div>
    @error('feldname') <span class="label-text-alt text-error mt-1">{{ $message }}</span> @enderror
    <div class="mt-1.5 text-xs text-base-content/70">
        Hilfetext zur Eingabe
    </div>
</div>
```

#### Auswahlelemente mit visuellen Karten

```blade
<div class="form-control w-full">
    <label class="label">
        <span class="label-text font-medium">Auswahloptionen <span class="text-error">*</span></span>
        <span class="label-text-alt opacity-60">Wähle aus</span>
    </label>
    <div class="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-3">
        <div class="relative tooltip-wrapper">
            <input type="checkbox" id="option-id" value="option-value" name="options[]" class="peer hidden">
            <label for="option-id"
                   class="flex flex-col items-center justify-center h-20 sm:h-24 p-2 border-2 rounded-lg cursor-pointer
                    transition-all duration-300 ease-in-out transform
                    hover:scale-105 hover:shadow-md
                    peer-checked:border-primary peer-checked:bg-primary/10
                    peer-checked:scale-[1.02] peer-checked:shadow-lg
                    border-base-300">
                <!-- Icon und Text -->
                <div class="w-8 h-8 flex items-center justify-center transition-transform duration-300">
                    <x-heroicon-o-icon class="h-6 w-6" />
                </div>
                <span class="mt-2 text-center text-sm transition-all duration-300">Optionstext</span>
                
                <!-- Selection Indicator -->
                <div class="absolute top-1 right-1 w-5 h-5 bg-primary rounded-full flex items-center justify-center
                    opacity-0 scale-0 peer-checked:opacity-100 peer-checked:scale-100
                    transition-all duration-300 ease-in-out">
                    <x-heroicon-s-check class="h-3 w-3 text-white" />
                </div>
            </label>
            
            <!-- Tooltip für zusätzliche Informationen -->
            <div class="custom-tooltip">
                Beschreibung der Option
            </div>
        </div>
    </div>
</div>
```

#### Datengruppierungen

```blade
<div class="mt-8">
    <h3 class="text-lg font-medium mb-4 pb-2 border-b border-base-300">Gruppenüberschrift</h3>
    <div class="grid md:grid-cols-3 gap-4">
        <!-- Elemente -->
    </div>
</div>
```

#### Bestätigungen und Hinweise

```blade
<div class="mt-8 p-4 bg-base-300/50 rounded-lg border border-base-300">
    <div class="form-control">
        <div class="flex items-start space-x-4">
            <input type="checkbox" name="confirmation" class="checkbox checkbox-primary mt-1" />
            <div class="space-y-2">
                <label class="label-text font-medium cursor-pointer">
                    Bestätigungstext
                </label>
                <div class="flex items-start space-x-2">
                    <x-heroicon-o-icon class="h-5 w-5 shrink-0 mt-0.5 text-primary" />
                    <p class="text-sm text-base-content/80">
                        Ausführliche Beschreibung der Bestätigung
                    </p>
                </div>
            </div>
        </div>
    </div>
</div>
```

### Call-to-Action

#### Primäre CTA mit Animation

```blade
<a href="{{ route('action') }}"
   class="btn btn-primary btn-lg gap-2 transition-all hover:translate-y-[-4px] hover:shadow-lg duration-300">
    CTA-Text
    <svg class="w-5 h-5 animate-bounce"><!-- Icon --></svg>
</a>
```

#### Formular-Aktionsbereich

```blade
<div class="card-actions flex flex-col md:flex-row md:justify-between items-center gap-4 mt-8">
    <div class="text-sm opacity-75">
        <div class="flex items-center gap-2">
            <x-heroicon-o-information-circle class="h-5 w-5 text-primary" />
            <span>Informationstext</span>
        </div>
    </div>
    
    <div class="flex items-center gap-3">
        <a href="#" class="btn btn-ghost">
            Abbrechen
        </a>
        <button type="submit" class="btn btn-primary btn-lg gap-2 min-w-[200px] relative group">
            <span class="hidden group-hover:inline-block absolute -left-2 -translate-x-full">
                <x-heroicon-s-sparkles class="h-5 w-5 text-primary-content animate-pulse" />
            </span>
            Primärer Button Text
            <x-heroicon-o-arrow-right class="h-5 w-5" />
        </button>
    </div>
</div>
```

## Animation und Interaktion

### Einblend-Animationen

```blade
<!-- Fade-In (von transparent zu sichtbar) -->
<div class="opacity-0 animate-fade-in">
    Inhalt
</div>

<!-- Slide-Right (von links nach rechts eingleiten) -->
<h1 class="opacity-0 animate-slide-right">
    Überschrift
</h1>

<!-- Delayed Fade (mit Verzögerung einblenden) -->
<p class="opacity-0 animate-delayed-fade">
    Paragraph
</p>

<!-- Slide-Up (von unten nach oben eingleiten) -->
<a class="opacity-0 animate-slide-up">
    Button
</a>
```

### Interaktive Effekte

```blade
<!-- Hover-Effekte für Buttons -->
<button class="transition-all hover:translate-y-[-4px] hover:shadow-lg duration-300">
    Button
</button>

<!-- Hover-Effekte mit Gruppen -->
<button class="relative group">
    <span class="hidden group-hover:inline-block absolute -left-2 -translate-x-full">
        <x-heroicon-s-sparkles class="h-5 w-5 animate-pulse" />
    </span>
    Button Text
</button>

<!-- Auswahlelemente mit visueller Rückmeldung -->
<input type="checkbox" id="option" class="peer hidden">
<label for="option"
       class="transition-all duration-300 ease-in-out transform
              hover:scale-105 hover:shadow-md
              peer-checked:border-primary peer-checked:bg-primary/10
              peer-checked:scale-[1.02] peer-checked:shadow-lg">
    Option
</label>
```

## Blade vs. Livewire Implementation

### Blade-Implementation

Für statische Formulare und Seiten:

```blade
<!-- Blade-Form -->
<form action="{{ route('action') }}" method="post" class="space-y-6">
    @csrf
    
    <!-- Formularfelder -->
    <div class="form-control w-full">
        <label class="label">
            <span class="label-text">Feldname</span>
        </label>
        <input type="text" name="field" value="{{ old('field') }}" class="input input-bordered" />
        @error('field') <span class="text-error text-sm mt-1">{{ $message }}</span> @enderror
    </div>
    
    <!-- Submit Button -->
    <button type="submit" class="btn btn-primary">Absenden</button>
</form>
```

### Livewire-Implementation

Für dynamische, interaktive Formulare:

```blade
<!-- Livewire-Komponente -->
<form wire:submit.prevent="submitForm" class="space-y-6">
    <!-- Dynamisches Formularfeld -->
    <div class="form-control w-full">
        <label class="label">
            <span class="label-text">Feldname</span>
        </label>
        <input type="text" wire:model="field" class="input input-bordered" />
        @error('field') <span class="text-error text-sm mt-1">{{ $message }}</span> @enderror
    </div>
    
    <!-- Dynamischer Inhalt basierend auf Benutzereingaben -->
    @if($showAdditionalFields)
        <div class="form-control w-full" 
             x-transition:enter="transition ease-out duration-300"
             x-transition:enter-start="opacity-0 transform -translate-y-4"
             x-transition:enter-end="opacity-100 transform translate-y-0">
            <!-- Zusätzliche Felder -->
        </div>
    @endif
    
    <!-- Submit Button mit Ladeindikator -->
    <button type="submit" class="btn btn-primary" wire:loading.attr="disabled">
        <div wire:loading wire:target="submitForm" class="animate-spin">
            <x-heroicon-o-arrow-path class="w-5 h-5" />
        </div>
        <span wire:loading.remove wire:target="submitForm">Absenden</span>
        <span wire:loading wire:target="submitForm">Wird gesendet...</span>
    </button>
</form>
```

## Responsives Verhalten

### Grundlegende Responsive Patterns

```blade
<!-- Spaltenbasiertes Layout -->
<div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
    <!-- Inhalte -->
</div>

<!-- Flex Direction Änderung -->
<div class="flex flex-col md:flex-row items-center gap-4">
    <!-- Inhalte -->
</div>

<!-- Reihenfolge Änderung -->
<div class="order-2 lg:order-1">
    <!-- Inhalt ändert Position basierend auf Viewport -->
</div>

<!-- Ausrichtungsänderung -->
<div class="flex flex-col items-center lg:items-start">
    <!-- Inhalte zentral auf Mobil, links auf Desktop -->
</div>

<!-- Größenänderung -->
<div class="w-auto h-32 lg:h-48">
    <!-- Element skaliert mit Viewport -->
</div>
```

## Beispielimplementierungen

### Welcome Page (Blade)

```blade
<x-app-layout>
    <x-slot name="heading">
        Die Minewache
    </x-slot>

    <main class="flex flex-col min-h-dvh">
        <section class="flex-grow flex items-center justify-center bg-base-200">
            <div class="container mx-auto px-4 py-8">
                <div class="hero-content grid grid-cols-1 lg:grid-cols-2 gap-8 items-center">
                    <div class="flex flex-col items-center lg:items-start order-2 lg:order-1">
                        <h1 class="font-display font-bold text-primary text-title tracking-tight mb-4 opacity-0 animate-slide-right">
                            Die Minewache
                        </h1>
                        <p class="font-modern text-paragraph text-base-content mb-8 max-w-xl opacity-0 animate-delayed-fade">
                            Bist du interessiert daran, bei uns mitzumachen? Dann bewirb dich jetzt bei unserem Team!
                        </p>
                        <a href="{{ route('bewerben') }}"
                           class="btn btn-primary btn-lg gap-2 transition-all opacity-0 hover:translate-y-[-4px] hover:shadow-lg duration-300"
                           x-data
                           x-init="setTimeout(() => $el.classList.add('animate-slide-up'), 500)">
                            Jetzt bewerben
                            <svg class="w-5 h-5 animate-bounce" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                                <path fill-rule="evenodd" d="M12.293 5.293a1 1 0 011.414 0l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414-1.414L14.586 11H3a1 1 0 110-2h11.586l-2.293-2.293a1 1 0 010-1.414z" clip-rule="evenodd" />
                            </svg>
                        </a>
                    </div>
                    <div class="flex justify-center lg:justify-end order-1 lg:order-2 opacity-0 animate-fade-in">
                        <x-application-logo class="w-auto h-32 lg:h-48 fill-current text-primary transition-transform hover:scale-105 duration-300" />
                    </div>
                </div>
            </div>
        </section>
    </main>
</x-app-layout>
```

### Application Form (Livewire)

```php
<?php

namespace App\Livewire;

use Livewire\Component;

class ApplicationForm extends Component
{
    public $name;
    public $age;
    public $gender;
    public $pronouns;
    public $professions = [];
    public $otherProfession;
    public $confirmation = false;
    
    protected $professionOptions = [
        'actor' => 'Schauspieler',
        'actor_no_voice' => 'Schauspieler (No Voice)',
        // Weitere Optionen...
    ];
    
    protected function rules()
    {
        return [
            'name' => 'required|string|max:255',
            'age' => 'required|integer|min:12|max:99',
            'gender' => 'required|string',
            'pronouns' => 'nullable|string|max:50',
            'professions' => 'required|array|min:1',
            'otherProfession' => 'nullable|required_if:professions,other|string|max:255',
            'confirmation' => 'required|accepted',
        ];
    }
    
    public function updatedProfessions()
    {
        if (in_array('other', $this->professions)) {
            $this->validateOnly('otherProfession');
        }
    }
    
    public function submit()
    {
        $this->validate();
        
        // Logik für das Speichern der Daten
        
        return redirect()->route('questions');
    }
    
    public function render()
    {
        return view('livewire.application-form', [
            'professionOptions' => $this->professionOptions
        ]);
    }
}
```

```blade
<div class="card bg-base-200 shadow-xl w-full max-w-4xl mx-auto">
    <div class="card-body">
        <!-- Progress Indicator -->
        <div class="w-full mb-8">
            <!-- Progress code -->
        </div>

        <form wire:submit.prevent="submit" class="space-y-6">
            <!-- Formularfelder mit Livewire Binding -->
            <div class="form-control w-full">
                <label class="label">
                    <span class="label-text font-medium">Ansprechname <span class="text-error">*</span></span>
                </label>
                <div class="input-group">
                    <span class="btn btn-square btn-ghost">
                        <x-heroicon-o-user class="h-5 w-5" />
                    </span>
                    <input type="text" wire:model="name" placeholder="Dein Name" 
                           class="input input-bordered w-full @error('name') input-error @enderror" />
                </div>
                @error('name') <span class="label-text-alt text-error mt-1">{{ $message }}</span> @enderror
            </div>
            
            <!-- Weitere Formularfelder -->
            
            <!-- Formular-Aktionen -->
            <div class="card-actions flex flex-col md:flex-row md:justify-between items-center gap-4 mt-8">
                <div class="text-sm opacity-75">
                    <!-- Hinweis -->
                </div>
                
                <div class="flex items-center gap-3">
                    <a href="{{ route('home') }}" class="btn btn-ghost">
                        Abbrechen
                    </a>
                    <button type="submit" class="btn btn-primary btn-lg gap-2" wire:loading.attr="disabled">
                        <div wire:loading wire:target="submit" class="animate-spin">
                            <x-heroicon-o-arrow-path class="w-5 h-5" />
                        </div>
                        <span wire:loading.remove wire:target="submit">
                            Weiter zum nächsten Schritt
                        </span>
                        <span wire:loading wire:target="submit">
                            Wird gesendet...
                        </span>
                        <x-heroicon-o-arrow-right class="h-5 w-5" />
                    </button>
                </div>
            </div>
        </form>
    </div>
</div>
```

Diese Beispielimplementierungen zeigen, wie sowohl Blade als auch Livewire verwendet werden können, um den gleichen Design-Prinzipien zu folgen und dabei die Stärken jeder Technologie zu nutzen.