<x-app-layout>
    <x-slot name="heading">
        {{ __('messages.minewache_mods') }}
    </x-slot>

    <main class="flex flex-col min-h-dvh bg-base-200"
          x-data="{
              search: '{{ $search }}',
              category: '{{ $category }}',
              sortBy: '{{ $sortBy }}',
              requiredOnly: {{ $requiredOnly ? 'true' : 'false' }},
              sizeFilter: '{{ $sizeFilter }}',
              showInstallGuide: false,

              // Function to update URL with all filters
              updateFilters() {
                  window.location.href = '{{ route('mods') }}?search=' + this.search +
                                         '&category=' + this.category +
                                         '&sort=' + this.sortBy +
                                         '&required=' + (this.requiredOnly ? '1' : '0') +
                                         '&size=' + this.sizeFilter;
              }
          }">

        <!-- Hero Section -->
        <section class="py-12 bg-base-100">
            <div class="container mx-auto px-4">
                <div class="text-center">
                    <h1 class="text-5xl font-bold text-primary mb-6 animate-fade-in">Minewache Modpack</h1>
                    <p class="text-xl text-base-content/80 mb-8 max-w-3xl mx-auto animate-delayed-fade">
                        Entdecke alle Mods, die wir für das ultimative Minewache-Erlebnis verwenden.
                    </p>

                    <!-- Suchfeld -->
                    <form class="relative max-w-2xl mx-auto mb-8" @submit.prevent="updateFilters()">
                        <input type="text"
                               name="search"
                               x-model="search"
                               @input.debounce.500ms="updateFilters()"
                               placeholder="Suche nach Mods..."
                               class="input input-bordered w-full pl-12 pr-4 bg-base-200 text-lg">
                        <svg class="absolute left-4 top-1/2 transform -translate-y-1/2 w-5 h-5 text-base-content/50"
                             xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                  d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                        </svg>
                    </form>

                    <!-- Statistiken -->
                    <div class="grid grid-cols-2 md:grid-cols-4 gap-4 max-w-4xl mx-auto">
                        <div class="stat bg-base-200 rounded-xl p-4">
                            <div class="stat-title">Mods</div>
                            <div class="stat-value text-primary">{{ $modCount }}</div>
                        </div>
                        <div class="stat bg-base-200 rounded-xl p-4">
                            <div class="stat-title">Version</div>
                            <div class="stat-value text-primary">1.12.2</div>
                        </div>
                        <div class="stat bg-base-200 rounded-xl p-4">
                            <div class="stat-title">Forge</div>
                            <div class="stat-value text-primary">14.23.5</div>
                        </div>
                        <div class="stat bg-base-200 rounded-xl p-4">
                            <div class="stat-title">Größe</div>
                            <div class="stat-value text-primary">{{ number_format($totalSize, 1) }}MB</div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Mods Grid -->
        <section class="py-12 bg-base-200">
            <div class="container mx-auto px-4">
                <!-- Filter & Sort -->
                <div class="flex flex-col md:flex-row gap-4 justify-between items-center mb-8">
                    <!-- Sortierung -->
                    <select x-model="sortBy"
                            @change="updateFilters()"
                            class="select select-bordered">
                        <option value="name">Name</option>
                        <option value="downloads">Downloads</option>
                        <option value="size">Größe</option>
                    </select>
                </div>

                <!-- Main Content with Left Filter and Mods Grid -->
                <div class="flex flex-col md:flex-row gap-4">
                    <!-- Left Filter Section -->
                    <div class="md:w-32 bg-base-100 p-2 rounded-lg shadow-md self-start sticky top-20">
                        <h3 class="text-base font-bold mb-3">Filter</h3>

                        <!-- Category Filter -->
                        <div class="mb-4">
                            <h4 class="text-sm font-medium mb-1">Kategorien</h4>
                            <div class="space-y-1">
                                <div class="form-control">
                                    <label class="label py-1 cursor-pointer justify-start gap-2">
                                        <input type="radio" name="category-radio" class="radio radio-sm radio-primary"
                                               :checked="category === ''"
                                               @click="category = ''; updateFilters()">
                                        <span class="label-text text-xs">Alle</span>
                                    </label>
                                </div>
                                @foreach(['Performance', 'Gameplay', 'Visuals', 'Utility', 'Library', 'Technology', 'Transportation', 'Roleplay', 'Customization'] as $cat)
                                <div class="form-control">
                                    <label class="label py-1 cursor-pointer justify-start gap-2">
                                        <input type="radio" name="category-radio" class="radio radio-sm radio-primary"
                                               :checked="category === '{{ $cat }}'"
                                               @click="category = '{{ $cat }}'; updateFilters()">
                                        <span class="label-text text-xs">{{ $cat }}</span>
                                    </label>
                                </div>
                                @endforeach
                            </div>
                        </div>

                        <!-- Size Filter -->
                        <div>
                            <h4 class="text-sm font-medium mb-1">Größe</h4>
                            <div class="form-control">
                                <label class="label py-1 cursor-pointer justify-start gap-2">
                                    <input type="radio" name="size-radio" class="radio radio-sm radio-primary"
                                           :checked="sizeFilter === 'small'"
                                           @click="sizeFilter = 'small'; updateFilters()">
                                    <span class="label-text text-xs">Unter 1MB</span>
                                </label>
                            </div>
                            <div class="form-control">
                                <label class="label py-1 cursor-pointer justify-start gap-2">
                                    <input type="radio" name="size-radio" class="radio radio-sm radio-primary"
                                           :checked="sizeFilter === 'medium'"
                                           @click="sizeFilter = 'medium'; updateFilters()">
                                    <span class="label-text text-xs">1MB - 5MB</span>
                                </label>
                            </div>
                            <div class="form-control">
                                <label class="label py-1 cursor-pointer justify-start gap-2">
                                    <input type="radio" name="size-radio" class="radio radio-sm radio-primary"
                                           :checked="sizeFilter === 'large'"
                                           @click="sizeFilter = 'large'; updateFilters()">
                                    <span class="label-text text-xs">Über 5MB</span>
                                </label>
                            </div>
                            <div class="form-control">
                                <label class="label py-1 cursor-pointer justify-start gap-2">
                                    <input type="radio" name="size-radio" class="radio radio-sm radio-primary"
                                           :checked="sizeFilter === ''"
                                           @click="sizeFilter = ''; updateFilters()">
                                    <span class="label-text text-xs">Alle Größen</span>
                                </label>
                            </div>
                        </div>
                    </div>

                    <!-- Mods Grid -->
                    <div class="flex-1 grid grid-cols-2 gap-3">
                    @foreach($mods as $mod)
                        <div class="card bg-base-100 hover:shadow-xl transition-all duration-300 group h-40">
                            <div class="card-body p-3">
                                <div class="flex items-start gap-3">
                                    <div class="w-12 h-12 rounded-lg bg-base-200 overflow-hidden">
                                        <img src="{{ $mod['icon_url'] }}"
                                             alt="{{ $mod['name'] }} icon"
                                             class="w-full h-full object-cover transform group-hover:scale-110 transition-transform duration-300">
                                    </div>
                                    <div class="flex-1 min-w-0">
                                        <h3 class="card-title text-base font-bold group-hover:text-primary transition-colors truncate">
                                            {{ $mod['name'] }}
                                        </h3>
                                        <p class="text-xs text-base-content/70">by {{ $mod['author'] }}</p>
                                        <p class="text-xs mt-1 line-clamp-2">{{ $mod['description'] }}</p>
                                        <div class="flex flex-wrap items-center gap-2 mt-2">
                                            <span class="badge badge-sm badge-outline">{{ $mod['category'] }}</span>
                                            <span class="text-xs text-base-content/60">
                                                <i class="fas fa-download mr-1"></i> {{ number_format($mod['downloads']) }}
                                            </span>
                                            <span class="text-xs text-base-content/60">
                                                <i class="fas fa-weight-hanging mr-1"></i> {{ $mod['size_mb'] }}MB
                                            </span>
                                            @if($mod['required'])
                                            <span class="badge badge-sm badge-primary">Required</span>
                                            @endif
                                        </div>
                                    </div>
                                </div>
                                <div class="card-actions justify-end mt-3">
                                    <a href="{{ $mod['curseforge_url'] }}"
                                       target="_blank"
                                       class="btn btn-xs btn-ghost gap-1">
                                        CurseForge
                                        <svg xmlns="http://www.w3.org/2000/svg" class="h-3 w-3" fill="none"
                                             viewBox="0 0 24 24" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                                  d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14" />
                                        </svg>
                                    </a>
                                </div>
                            </div>
                        </div>
                    @endforeach
                    </div>
                </div>

                <!-- Download Section -->
                <div class="text-center mt-20 pt-8 space-y-4">
                    <button @click="showInstallGuide = true"
                            class="btn btn-primary btn-lg gap-2">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none"
                             viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                  d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4" />
                        </svg>
                        Modpack Installieren
                    </button>
                </div>
            </div>
        </section>

        <!-- Installation Guide Modal -->
        <div x-show="showInstallGuide"
             class="fixed inset-0  backdrop-blur-sm z-50 flex items-center justify-center p-4">
            <div class="bg-base-100 rounded-lg max-w-2xl w-full p-6" @click.away="showInstallGuide = false">
                <h2 class="text-2xl font-bold mb-4">Installation Guide</h2>
                <div class="prose">
                    <ol class="list-decimal list-inside">
                        <li class="mb-4">Installiere den CurseForge Client
                            <a href="https://www.curseforge.com/download/app"
                               target="_blank"
                               class="link link-primary">hier</a>
                        </li>
                        <li class="mb-4">Öffne den CurseForge Client und wähle Minecraft aus</li>
                        <li class="mb-4">Suche Nach Die_Minewache</li>
                        <li class="mb-4">Installiere das Modpack</li>
                    </ol>
                </div>
                <div class="mt-6 flex justify-end">
                    <button @click="showInstallGuide = false" class="btn btn-primary">Verstanden</button>
                </div>
            </div>
        </div>
    </main>
</x-app-layout>
