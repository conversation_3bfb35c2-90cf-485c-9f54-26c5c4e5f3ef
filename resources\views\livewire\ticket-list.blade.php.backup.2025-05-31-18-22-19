<div>
    <div class="bg-gradient-modern min-h-screen py-8 px-4">
        <div class="glass rounded-3xl shadow-2xl p-6 max-w-6xl mx-auto">
            <div class="flex flex-col md:flex-row justify-between items-start md:items-center mb-6">
                <h1 class="text-2xl font-bold text-base-content mb-4 md:mb-0">
                    {{ $isSupporter ? __('tickets.all_tickets') : __('tickets.my_tickets') }}
                </h1>
                <div class="flex flex-col sm:flex-row gap-3">
                    <a href="{{ route('tickets.create') }}" class="bg-blue-500 hover:bg-blue-600 text-white rounded-full px-4 py-2 shadow-lg transition flex items-center gap-2">
                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                        </svg>
                        {{ __('tickets.new_ticket') }}
                    </a>
                </div>
            </div>

            <!-- Filters -->
            <div class="mb-6 bg-slate-800/50 p-4 rounded-2xl">
                <div class="flex flex-col md:flex-row gap-4">
                    <div class="flex-1">
                        <label for="search" class="block text-sm font-medium text-slate-300 mb-1">{{ __('admin.search') }}</label>
                        <input wire:model.live.debounce.300ms="search" type="text" id="search" class="bg-slate-800 text-slate-100 rounded-2xl px-4 py-2 w-full focus:outline-none focus:ring-2 focus:ring-blue-500" placeholder="{{ __('admin.search') }}...">
                    </div>

                    <div class="w-full md:w-48">
                        <label for="status" class="block text-sm font-medium text-slate-300 mb-1">{{ __('tickets.status') }}</label>
                        <select wire:model.live="status" id="status" class="bg-slate-800 text-slate-100 rounded-2xl px-4 py-2 w-full focus:outline-none focus:ring-2 focus:ring-blue-500">
                            <option value="">{{ __('admin.all_statuses') }}</option>
                            <option value="open">{{ __('tickets.status_open') }}</option>
                            <option value="in_progress">{{ __('tickets.status_in_progress') }}</option>
                            <option value="closed">{{ __('tickets.status_closed') }}</option>
                        </select>
                    </div>

                    @if($isSupporter)
                        <div class="w-full md:w-48">
                            <label for="assignedTo" class="block text-sm font-medium text-slate-300 mb-1">{{ __('tickets.assigned_to') }}</label>
                            <select wire:model.live="assignedTo" id="assignedTo" class="bg-slate-800 text-slate-100 rounded-2xl px-4 py-2 w-full focus:outline-none focus:ring-2 focus:ring-blue-500">
                                <option value="">{{ __('admin.all') }}</option>
                                <option value="unassigned">{{ __('tickets.unassigned') }}</option>
                                @foreach($supporters as $id => $name)
                                    <option value="{{ $id }}">{{ $name }}</option>
                                @endforeach
                            </select>
                        </div>
                    @endif

                    <div class="flex items-end">
                        <button wire:click="resetFilters" class="bg-slate-700 hover:bg-slate-600 text-white rounded-full px-4 py-2 shadow-lg transition flex items-center gap-2">
                            <svg class="w-5 h-5 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                            </svg>
                            {{ __('admin.reset_filters') }}
                        </button>
                    </div>
                </div>
            </div>

            <!-- Tickets List -->
            @if($tickets->isEmpty())
                <div class="text-center py-8 bg-slate-800/50 rounded-2xl">
                    <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                    </svg>
                    <h3 class="mt-2 text-sm font-medium text-white">{{ __('tickets.no_tickets') }}</h3>
                    <p class="mt-1 text-sm text-slate-300">{{ __('tickets.no_tickets_description') }}</p>
                    <div class="mt-6">
                        <a href="{{ route('tickets.create') }}" class="bg-blue-500 hover:bg-blue-600 text-white rounded-full px-4 py-2 shadow-lg transition flex items-center gap-2 inline-flex">
                            <svg class="w-5 h-5 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                            </svg>
                            {{ __('tickets.create_first_ticket') }}
                        </a>
                    </div>
                </div>
            @else
                <div class="overflow-x-auto rounded-2xl bg-slate-800/50">
                    <table class="w-full text-left text-slate-200">
                        <thead>
                            <tr class="bg-slate-700/50 text-slate-300 text-xs uppercase">
                                <th class="px-4 py-3">ID</th>
                                <th class="px-4 py-3">{{ __('tickets.title') }}</th>
                                @if($isSupporter)
                                    <th class="px-4 py-3">{{ __('tickets.created_by') }}</th>
                                @endif
                                <th class="px-4 py-3">{{ __('tickets.status') }}</th>
                                <th class="px-4 py-3">{{ __('tickets.created_at') }}</th>
                                <th class="px-4 py-3">{{ __('tickets.assigned_to') }}</th>
                                <th class="px-4 py-3">{{ __('tickets.actions') }}</th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach($tickets as $ticket)
                                <tr class="border-b border-slate-700 hover:bg-slate-700/30 cursor-pointer" onclick="window.location='{{ route('tickets.show', $ticket) }}'">
                                    <td class="px-4 py-3">{{ $ticket->id }}</td>
                                    <td class="px-4 py-3">
                                        <div class="flex flex-col">
                                            <a href="{{ route('tickets.show', $ticket) }}" class="font-medium text-blue-300 hover:text-blue-400">
                                                {{ $ticket->title }}
                                            </a>
                                            @if($ticket->messages->isNotEmpty())
                                                <span class="text-xs text-slate-400 mt-1 truncate max-w-xs">
                                                    {{ Str::limit($ticket->messages->last()->message, 50) }}
                                                </span>
                                            @endif
                                        </div>
                                    </td>
                                    @if($isSupporter)
                                        <td class="px-4 py-3">{{ $ticket->user->username }}</td>
                                    @endif
                                    <td class="px-4 py-3">
                                        <span class="px-2 py-1 text-xs rounded-full {{ $ticket->statusColor }}">
                                            {{ $ticket->statusLabel }}
                                        </span>
                                    </td>
                                    <td class="px-4 py-3">
                                        <div class="flex flex-col">
                                            <span>{{ $ticket->created_at->format('d.m.Y') }}</span>
                                            <span class="text-xs text-slate-400">{{ $ticket->created_at->format('H:i') }}</span>
                                        </div>
                                    </td>
                                    <td class="px-4 py-3">
                                        @if($ticket->assignedTo)
                                            <div class="flex items-center">
                                                <img class="w-6 h-6 rounded-full mr-2 border border-slate-600" src="{{ $ticket->assignedTo->getAvatar(['extension' => 'webp', 'size' => 24]) }}" alt="{{ $ticket->assignedTo->username }}">
                                                <span>{{ $ticket->assignedTo->username }}</span>
                                            </div>
                                        @else
                                            <span class="text-slate-400">{{ __('tickets.unassigned') }}</span>
                                        @endif
                                    </td>
                                    <td class="px-4 py-3">
                                        <div class="flex space-x-2">
                                            <a href="{{ route('tickets.show', $ticket) }}" class="bg-slate-700 hover:bg-slate-600 text-white rounded-full p-2 transition">
                                                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                                                </svg>
                                            </a>
                                            @if($ticket->messages->count() > 0)
                                                <span class="inline-flex items-center justify-center px-2 py-1 text-xs font-bold leading-none text-white bg-blue-600 rounded-full">
                                                    {{ $ticket->messages->count() }}
                                                </span>
                                            @endif
                                        </div>
                                    </td>
                                </tr>
                            @endforeach
                        </tbody>
                    </table>
                </div>

                <div class="mt-4">
                    {{ $tickets->links() }}
                </div>
            @endif
        </div>
    </div>

    <!-- CSS for glass effect -->
    <style>
        .glass {
            background: rgba(30, 41, 59, 0.6);
            backdrop-filter: blur(12px);
            border: 1px solid rgba(255, 255, 255, 0.1);
            box-shadow: 0 8px 32px 0 rgba(0, 0, 0, 0.37);
        }
    </style>
</div>
