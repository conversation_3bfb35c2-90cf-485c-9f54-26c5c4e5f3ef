<?php $attributes ??= new \Illuminate\View\ComponentAttributeBag;

$__newAttributes = [];
$__propNames = \Illuminate\View\ComponentAttributeBag::extractPropNames((['show' => false]));

foreach ($attributes->all() as $__key => $__value) {
    if (in_array($__key, $__propNames)) {
        $$__key = $$__key ?? $__value;
    } else {
        $__newAttributes[$__key] = $__value;
    }
}

$attributes = new \Illuminate\View\ComponentAttributeBag($__newAttributes);

unset($__propNames);
unset($__newAttributes);

foreach (array_filter((['show' => false]), 'is_string', ARRAY_FILTER_USE_KEY) as $__key => $__value) {
    $$__key = $$__key ?? $__value;
}

$__defined_vars = get_defined_vars();

foreach ($attributes->all() as $__key => $__value) {
    if (array_key_exists($__key, $__defined_vars)) unset($$__key);
}

unset($__defined_vars); ?>

<div
    x-data="{
        show: <?php echo e($show ? 'true' : 'false'); ?>,
        consent: false,
        consentError: '',
        isSubmitting: false,
        showDetails: false,

        init() {
            // Check if user has already given consent
            const hasCookie = document.cookie.split(';').some(item => item.trim().startsWith('gdpr_consent='));

            if (hasCookie) {
                // User has already given consent, don't show the modal
                this.show = false;
                return;
            }

            // Listen for the gdpr-consent-modal event
            window.addEventListener('gdpr-consent-modal', () => {
                this.show = true;
            });

            // Show modal if show prop is true
            if (<?php echo e($show ? 'true' : 'false'); ?>) {
                this.show = true;
            }
        },

        submitConsent() {
            if (!this.consent) return;

            this.isSubmitting = true;
            this.consentError = '';

            // Submit consent via AJAX
            fetch('<?php echo e(route('auth.consent.store')); ?>', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': '<?php echo e(csrf_token()); ?>',
                    'Accept': 'application/json'
                },
                body: JSON.stringify({
                    consent: this.consent ? 1 : 0
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // Redirect to login
                    window.location.href = '<?php echo e(route('login')); ?>';
                } else if (data.errors && data.errors.consent) {
                    this.consentError = data.errors.consent[0];
                    this.isSubmitting = false;
                }
            })
            .catch(error => {
                console.error('Error:', error);
                this.consentError = 'Ein Fehler ist aufgetreten. Bitte versuche es erneut.';
                this.isSubmitting = false;
            });
        }
    }"
    x-show="show"
    x-on:gdpr-consent-modal.window="show = true"
    x-on:close-gdpr-consent.window="show = false"
    class="fixed inset-0 z-50 overflow-hidden"
    x-transition:enter="transition ease-out duration-300"
    x-transition:enter-start="opacity-0"
    x-transition:enter-end="opacity-100"
    x-transition:leave="transition ease-in duration-200"
    x-transition:leave-start="opacity-100"
    x-transition:leave-end="opacity-0"
    style="display: none;"
>
    <div class="flex items-center justify-center min-h-screen p-4">
        <div class="fixed inset-0 bg-black/50" x-on:click="show = false"></div>

        <div
            class="relative bg-base-100 rounded-lg shadow-xl w-full max-w-md mx-auto overflow-hidden"
            x-transition:enter="transition ease-out duration-300"
            x-transition:enter-start="opacity-0 transform scale-95"
            x-transition:enter-end="opacity-100 transform scale-100"
            x-transition:leave="transition ease-in duration-200"
            x-transition:leave-start="opacity-100 transform scale-100"
            x-transition:leave-end="opacity-0 transform scale-95"
        >
            <div class="p-5">
                <div class="flex items-center gap-2 mb-4">
                    <div class="bg-primary/10 p-2 rounded-full">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-primary" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" />
                        </svg>
                    </div>
                    <h2 class="text-lg font-medium">Datenschutz-Einwilligung</h2>
                </div>

                <?php if(config('app.debug')): ?>
                <div class="alert alert-info mb-4 text-xs">
                    <div>
                        <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" class="stroke-current shrink-0 w-4 h-4"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path></svg>
                        <span>Debug: GDPR Consent Cookie Status: <?php echo e(\Illuminate\Support\Facades\Cookie::has('gdpr_consent') ? 'Present' : 'Not Present'); ?></span>
                    </div>
                </div>
                <?php endif; ?>

                <p class="text-sm mb-4">
                    Bevor du dich mit Discord anmeldest, benötigen wir deine Einwilligung zur Verarbeitung deiner Daten.
                </p>

                <!-- Zusammenfassung (immer sichtbar) -->
                <div class="bg-base-200 rounded-lg p-3 mb-3">
                    <div class="flex gap-2">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-primary shrink-0 mt-0.5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                        </svg>
                        <div>
                            <p class="text-sm">Wir erheben deine <strong>Discord-ID, Benutzername, Avatar</strong> und weitere öffentliche Profilinformationen zur Kontoverwaltung und Authentifizierung.</p>
                            <button
                                @click="showDetails = !showDetails"
                                class="text-xs text-primary mt-1 flex items-center"
                            >
                                <span x-text="showDetails ? 'Weniger Details' : 'Mehr Details'"></span>
                                <svg
                                    xmlns="http://www.w3.org/2000/svg"
                                    class="h-3 w-3 ml-1 transition-transform"
                                    :class="showDetails ? 'rotate-180' : ''"
                                    fill="none"
                                    viewBox="0 0 24 24"
                                    stroke="currentColor"
                                >
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
                                </svg>
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Details (nur sichtbar, wenn showDetails = true) -->
                <div x-show="showDetails" x-transition class="space-y-2 mb-4">
                    <div class="bg-base-200 rounded-lg p-3 flex gap-2">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 text-primary shrink-0 mt-0.5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 7h6m0 10v-3m-3 3h.01M9 17h.01M9 14h.01M12 14h.01M15 11h.01M12 11h.01M9 11h.01M7 21h10a2 2 0 002-2V5a2 2 0 00-2-2H7a2 2 0 00-2 2v14a2 2 0 002 2z" />
                        </svg>
                        <div>
                            <h3 class="text-sm font-medium">Zweck</h3>
                            <p class="text-xs text-base-content/80">
                                Zur Erstellung und Verwaltung deines Kontos, zur Authentifizierung und zur Kommunikation.
                            </p>
                        </div>
                    </div>

                    <div class="bg-base-200 rounded-lg p-3 flex gap-2">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 text-primary shrink-0 mt-0.5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 6l3 1m0 0l-3 9a5.002 5.002 0 006.001 0M6 7l3 9M6 7l6-2m6 2l3-1m-3 1l-3 9a5.002 5.002 0 006.001 0M18 7l3 9m-3-9l-6-2m0-2v2m0 16V5m0 16H9m3 0h3" />
                        </svg>
                        <div>
                            <h3 class="text-sm font-medium">Rechtsgrundlage</h3>
                            <p class="text-xs text-base-content/80">
                                Art. 6 Abs. 1 lit. a DSGVO (Einwilligung) und Art. 6 Abs. 1 lit. b DSGVO (Erfüllung des Nutzungsvertrags).
                            </p>
                        </div>
                    </div>

                    <div class="bg-base-200 rounded-lg p-3 flex gap-2">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 text-primary shrink-0 mt-0.5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                        </svg>
                        <div>
                            <h3 class="text-sm font-medium">Speicherdauer</h3>
                            <p class="text-xs text-base-content/80">
                                Für die Dauer deiner Mitgliedschaft. Löschung jederzeit über die Datenschutzeinstellungen möglich.
                            </p>
                        </div>
                    </div>
                </div>

                <div class="bg-primary/5 border border-primary/20 rounded-lg p-3 mb-4">
                    <label class="flex items-start gap-2 cursor-pointer">
                        <input type="checkbox" name="consent" value="1" x-model="consent" class="checkbox checkbox-primary checkbox-sm mt-1" required />
                        <span class="text-sm">
                            Ich stimme der Verarbeitung meiner Daten gemäß der <a href="<?php echo e(route('datenschutz')); ?>" class="text-primary underline" target="_blank">Datenschutzerklärung</a> zu.
                        </span>
                    </label>
                    <span class="text-error text-xs mt-1 block pl-7" x-show="consentError" x-text="consentError" style="display: none;"></span>
                </div>

                <div class="flex justify-between items-center">
                    <button type="button" x-on:click="show = false" class="btn btn-ghost btn-sm">
                        Abbrechen
                    </button>
                    <?php if (isset($component)) { $__componentOriginal19987cd45c699eefead8b7a8ac222798 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal19987cd45c699eefead8b7a8ac222798 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.modern-button','data' => ['variant' => 'primary','size' => 'sm','type' => 'button','xOn:click' => 'submitConsent()','xBind:disabled' => '!consent || isSubmitting']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('modern-button'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['variant' => 'primary','size' => 'sm','type' => 'button','x-on:click' => 'submitConsent()','x-bind:disabled' => '!consent || isSubmitting']); ?><span x-show="!isSubmitting">Fortfahren</span>
                        <span x-show="isSubmitting" class="loading loading-spinner loading-xs"></span> <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal19987cd45c699eefead8b7a8ac222798)): ?>
<?php $attributes = $__attributesOriginal19987cd45c699eefead8b7a8ac222798; ?>
<?php unset($__attributesOriginal19987cd45c699eefead8b7a8ac222798); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal19987cd45c699eefead8b7a8ac222798)): ?>
<?php $component = $__componentOriginal19987cd45c699eefead8b7a8ac222798; ?>
<?php unset($__componentOriginal19987cd45c699eefead8b7a8ac222798); ?>
<?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>
<?php /**PATH C:\Users\<USER>\Documents\GitHub\minewache-website\resources\views/components/gdpr-consent-modal.blade.php ENDPATH**/ ?>