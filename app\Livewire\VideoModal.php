<?php

namespace App\Livewire;

use Livewire\Component;

class VideoModal extends Component
{
    public $isOpen = false;
    public $videoUrl = '';
    public $title = '';

    protected $listeners = [
        'open-video-modal' => 'openModal',
    ];

    public function render()
    {
        return view('livewire.video-modal');
    }

    public function openModal($url, $title)
    {
        $this->videoUrl = $url;
        $this->title = $title;
        $this->isOpen = true;
    }

    public function closeModal()
    {
        $this->isOpen = false;
        $this->videoUrl = '';
        $this->title = '';
    }
}
