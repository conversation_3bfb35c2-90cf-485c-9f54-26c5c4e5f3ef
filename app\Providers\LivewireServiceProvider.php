<?php

namespace App\Providers;

use Illuminate\Support\ServiceProvider;
use Livewire\Livewire;
use App\Livewire\ApplicationManager;
use App\Livewire\ApplicationList;
use App\Livewire\ApplicationDetail;
use App\Livewire\ApplicationEdit;
use App\Livewire\ResponseGenerator;
use App\Livewire\GeminiConsentModal;
use App\Livewire\UserDataConsentModal;
use App\Livewire\UserDataConsentSettings;

class LivewireServiceProvider extends ServiceProvider
{
    /**
     * Register services.
     */
    public function register(): void
    {
        //
    }

    /**
     * Bootstrap services.
     */
    public function boot(): void
    {
        Livewire::component('application-manager', ApplicationManager::class);
        Livewire::component('application-list', ApplicationList::class);
        Livewire::component('application-detail', ApplicationDetail::class);
        Livewire::component('application-edit', ApplicationEdit::class);
        Livewire::component('response-generator', ResponseGenerator::class);
        Livewire::component('gemini-consent-modal', GeminiConsentModal::class);
        Livewire::component('user-data-consent-modal', UserDataConsentModal::class);
        Livewire::component('user-data-consent-settings', UserDataConsentSettings::class);
    }
}
