<x-app-layout>
    <x-slot name="heading">
        {{ __('series.about_series') }}
    </x-slot>

    <main class="bg-base-200 py-8 md:py-12">
        <div class="container mx-auto px-4">
            <!-- Hero Section -->
            <div class="bg-base-100 rounded-xl shadow-lg overflow-hidden mb-12">
                <div class="p-6 md:p-8 lg:p-10">
                    <h1 class="text-3xl md:text-4xl font-bold text-primary mb-6">{{ __('series.hero_title') }}</h1>
                    
                    <div class="prose prose-lg max-w-none dark:prose-invert">
                        <p class="lead text-xl">
                            {{ __('series.hero_description') }}
                        </p>
                    </div>
                </div>
            </div>

            <!-- Series Structure -->
            <div class="bg-base-100 rounded-xl shadow-lg overflow-hidden mb-12">
                <div class="p-6 md:p-8 lg:p-10">
                    <h2 class="text-2xl md:text-3xl font-bold text-primary mb-6">{{ __('series.structure_title') }}</h2>
                    
                    <div class="prose prose-lg max-w-none dark:prose-invert">
                        <p>{{ __('series.structure_intro') }}</p>
                        <ul>
                            <li>{{ __('series.structure_point_1') }}</li>
                            <li>{{ __('series.structure_point_2') }}</li>
                            <li>{{ __('series.structure_point_3') }}</li>
                            <li>{{ __('series.structure_point_4') }}</li>
                        </ul>
                        
                        <p>{{ __('series.episode_structure_intro') }}</p>
                        <ul>
                            <li>00:00 {{ __('series.action_story') }}</li>
                            <li>06:40 {{ __('series.story') }}</li>
                            <li>14:37 {{ __('series.action_story') }}</li>
                            <li>19:00 {{ __('series.action') }}</li>
                            <li>39:05 {{ __('series.action_story') }}</li>
                        </ul>
                    </div>
                </div>
            </div>

            <!-- Content Analysis -->
            <div class="bg-base-100 rounded-xl shadow-lg overflow-hidden mb-12">
                <div class="p-6 md:p-8 lg:p-10">
                    <h2 class="text-2xl md:text-3xl font-bold text-primary mb-6">{{ __('series.content_title') }}</h2>
                    
                    <div class="prose prose-lg max-w-none dark:prose-invert">
                        <p>{{ __('series.content_description') }}</p>
                        
                        <p>{{ __('series.episode_elements_intro') }}</p>
                        <ul>
                            <li>{{ __('series.element_1') }}</li>
                            <li>{{ __('series.element_2') }}</li>
                            <li>{{ __('series.element_3') }}</li>
                            <li>{{ __('series.element_4') }}</li>
                        </ul>
                    </div>
                </div>
            </div>

            <!-- Production Details -->
            <div class="bg-base-100 rounded-xl shadow-lg overflow-hidden mb-12">
                <div class="p-6 md:p-8 lg:p-10">
                    <h2 class="text-2xl md:text-3xl font-bold text-primary mb-6">{{ __('series.production_title') }}</h2>
                    
                    <div class="prose prose-lg max-w-none dark:prose-invert">
                        <p>{{ __('series.production_intro') }}</p>
                        <ul>
                            <li>{{ __('series.production_point_1') }}</li>
                            <li>{{ __('series.production_point_2') }}</li>
                            <li>{{ __('series.production_point_3') }}</li>
                            <li>{{ __('series.production_point_4') }}</li>
                            <li>{{ __('series.production_point_5') }}</li>
                        </ul>
                    </div>
                </div>
            </div>

            <!-- Distribution and Platforms -->
            <div class="bg-base-100 rounded-xl shadow-lg overflow-hidden mb-12">
                <div class="p-6 md:p-8 lg:p-10">
                    <h2 class="text-2xl md:text-3xl font-bold text-primary mb-6">{{ __('series.distribution_title') }}</h2>
                    
                    <div class="prose prose-lg max-w-none dark:prose-invert">
                        <p>{{ __('series.distribution_intro') }}</p>
                        <ul>
                            <li>{{ __('series.distribution_point_1') }}</li>
                            <li>{{ __('series.distribution_point_2') }} <a href="https://minewache.de" class="text-primary hover:underline">https://minewache.de</a></li>
                            <li>{{ __('series.distribution_point_3') }} <a href="https://discord.gg/wwrK2csZnX" class="text-primary hover:underline">https://discord.gg/wwrK2csZnX</a></li>
                        </ul>
                    </div>
                </div>
            </div>

            <!-- CTA Section -->
            <div class="text-center mb-12">
                <a href="{{ route('youtube') }}" class="btn btn-primary btn-lg gap-2">
                    {{ __('messages.view_all_episodes') }}
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M14.752 11.168l-3.197-2.132A1 1 0 0010 9.87v4.263a1 1 0 001.555.832l3.197-2.132a1 1 0 000-1.664z" />
                    </svg>
                </a>
            </div>
        </div>
    </main>
</x-app-layout>
