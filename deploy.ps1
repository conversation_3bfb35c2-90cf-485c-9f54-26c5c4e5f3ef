# MineWache Website - Production Deployment Script for Windows
# Place this file in the root directory of your Laravel project

# Function to display colored messages
function Write-ColorMessage {
    param (
        [string]$color,
        [string]$message
    )

    switch ($color) {
        "green" { Write-Host $message -ForegroundColor Green }
        "yellow" { Write-Host $message -ForegroundColor Yellow }
        "red" { Write-Host $message -ForegroundColor Red }
        "blue" { Write-Host $message -ForegroundColor Blue }
        default { Write-Host $message }
    }
}

# Function to log messages
function Log-Message {
    param (
        [string]$level,
        [string]$message
    )

    $logFile = ".\deploy.log"

    # Write to log file
    "[$(Get-Date -Format 'yyyy-MM-dd HH:mm:ss')] [$level] $message" | Out-File -Append -FilePath $logFile

    # Display to console
    Write-ColorMessage -color $level -message $message
}

# Function to check system requirements
function Test-SystemRequirements {
    Log-Message -level "blue" -message "Checking system requirements..."

    # Check disk space
    $drive = (Get-Location).Drive.Name + ":"
    $freeSpace = (Get-PSDrive -Name (Get-Location).Drive.Name).Free / 1MB
    if ($freeSpace -lt 1000) {
        Log-Message -level "red" -message "Insufficient disk space. At least 1GB required."
        exit 1
    }

    # Check PHP memory limit
    $phpMemoryLimit = php -r "echo ini_get('memory_limit');"
    if ($phpMemoryLimit -ne "-1" -and [int]($phpMemoryLimit -replace "M", "") -lt 256) {
        Log-Message -level "yellow" -message "Warning: PHP memory limit might be too low ($phpMemoryLimit)"
    }
}

# Function to check database connection
function Test-DatabaseConnection {
    Log-Message -level "yellow" -message "Checking database configuration and connection..."

    # Get default connection from Laravel config
    try {
        $defaultConnection = php artisan config:show --json | ConvertFrom-Json | Select-Object -ExpandProperty database | Select-Object -ExpandProperty default
    } catch {
        $defaultConnection = "unknown"
        Log-Message -level "yellow" -message "Could not determine database connection type: $_"
    }

    if ($defaultConnection -eq "sqlite") {
        try {
            $sqliteDbPath = php artisan config:show --json | ConvertFrom-Json | Select-Object -ExpandProperty database | Select-Object -ExpandProperty connections | Select-Object -ExpandProperty sqlite | Select-Object -ExpandProperty database
            Log-Message -level "blue" -message "Default connection is SQLite. Checking database file: $sqliteDbPath"
        } catch {
            $sqliteDbPath = "database/database.sqlite"
            Log-Message -level "yellow" -message "Could not determine SQLite database path: $_"
            Log-Message -level "yellow" -message "Using default path: $sqliteDbPath"
        }

        # Ensure the path is absolute or relative to the project root
        if (-not $sqliteDbPath.StartsWith("/")) {
            $sqliteDbPath = "$(Get-Location)\$sqliteDbPath"
        }

        $dbDir = Split-Path -Parent $sqliteDbPath

        # Check if the directory exists, create if not
        if (-not (Test-Path $dbDir)) {
            Log-Message -level "yellow" -message "Database directory does not exist. Creating: $dbDir"
            New-Item -ItemType Directory -Path $dbDir -Force | Out-Null
        }

        # Check if the database file exists, create if not
        if (-not (Test-Path $sqliteDbPath)) {
            Log-Message -level "yellow" -message "SQLite database file does not exist. Creating: $sqliteDbPath"
            New-Item -ItemType File -Path $sqliteDbPath -Force | Out-Null
            Log-Message -level "green" -message "SQLite database file created."
        } else {
            Log-Message -level "green" -message "SQLite database file found."
        }
    } else {
        Log-Message -level "blue" -message "Default connection is '$defaultConnection'. Assuming database exists and permissions are correct."
    }

    # Now, test the actual connection using Laravel
    Log-Message -level "yellow" -message "Testing database connection via Laravel..."
    try {
        php artisan db:show --json | Out-Null
        Log-Message -level "green" -message "Database connection successful."
    } catch {
        Log-Message -level "red" -message "Database connection failed using Laravel configuration."
        Log-Message -level "yellow" -message "Possible solutions:"
        Write-Host "1. Check database credentials in .env"
        Write-Host "2. Ensure database server is running (for MySQL/MariaDB etc.)"
        Write-Host "3. Check network connectivity to database server"
        Write-Host "4. Ensure the database user has correct permissions"
        Write-Host "5. For SQLite, check file permissions for '$sqliteDbPath'"
        exit 1
    }
}

# Function to check and fix common Laravel issues
function Repair-LaravelHealth {
    Log-Message -level "blue" -message "Checking Laravel health..."

    # Check storage link
    if (-not (Test-Path "public\storage")) {
        Log-Message -level "yellow" -message "Storage symlink missing. Creating..."
        php artisan storage:link
    }

    # Check Laravel logs for errors
    if (Test-Path "storage\logs\laravel.log") {
        $errorCount = (Select-String -Path "storage\logs\laravel.log" -Pattern "ERROR" | Measure-Object).Count
        if ($errorCount -gt 0) {
            Log-Message -level "yellow" -message "Warning: $errorCount errors found in Laravel log"
        }
    }

    # Check Reverb configuration
    Log-Message -level "yellow" -message "Checking Reverb configuration..."
    $envContent = Get-Content .env -Raw

    if (-not ($envContent -match "BROADCAST_CONNECTION=reverb")) {
        Log-Message -level "yellow" -message "Setting BROADCAST_CONNECTION to reverb in .env"
        $envContent = $envContent -replace "BROADCAST_CONNECTION=.*", "BROADCAST_CONNECTION=reverb"
        $envContent | Set-Content .env
    }

    # Check if Reverb environment variables exist
    if (-not ($envContent -match "REVERB_APP_ID")) {
        Log-Message -level "yellow" -message "Adding Reverb configuration to .env"
        $reverbConfig = @"

# Reverb-Konfiguration
REVERB_APP_ID="minewache_app"
REVERB_APP_KEY="minewache_key"
REVERB_APP_SECRET="minewache_secret"
REVERB_HOST="localhost"
REVERB_PORT=6001
REVERB_SCHEME="http"

# Reverb Server Konfiguration
REVERB_SERVER_HOST="127.0.0.1"
REVERB_SERVER_PORT=6001
"@
        Add-Content -Path .env -Value $reverbConfig
    }

    # Check if Vite Reverb environment variables exist
    if (-not ($envContent -match "VITE_REVERB_APP_KEY")) {
        Log-Message -level "yellow" -message "Adding Vite Reverb configuration to .env"
        $viteReverbConfig = @"

# Vite Reverb Konfiguration
VITE_REVERB_APP_KEY="`${REVERB_APP_KEY}"
VITE_REVERB_HOST="`${REVERB_HOST}"
VITE_REVERB_PORT="`${REVERB_PORT}"
VITE_REVERB_SCHEME="`${REVERB_SCHEME}"
"@
        Add-Content -Path .env -Value $viteReverbConfig
    }
}

# Function to handle backup
function New-ProjectBackup {
    Log-Message -level "blue" -message "Creating backup..."
    $backupDir = ".\backups"
    $timestamp = Get-Date -Format "yyyyMMdd_HHmmss"

    # Create backup directory if it doesn't exist
    if (-not (Test-Path $backupDir)) {
        New-Item -ItemType Directory -Path $backupDir -Force | Out-Null
    }

    # Database backup
    Log-Message -level "yellow" -message "Creating database backup..."
    # Check if sqlite3 is available
    $sqlite3Available = $null -ne (Get-Command sqlite3 -ErrorAction SilentlyContinue)

    if ($sqlite3Available) {
        # Use Laravel's built-in database dump command
        try {
            php artisan schema:dump --path="$backupDir" --prune
        } catch {
            Log-Message -level "yellow" -message "Schema dump failed: $_"
            Log-Message -level "yellow" -message "Skipping database backup. Consider installing sqlite3 command-line tool."
        }
    } else {
        Log-Message -level "yellow" -message "sqlite3 command-line tool not found. Skipping database backup."
        Log-Message -level "yellow" -message "Consider installing sqlite3 command-line tool for database backups."
    }

    # Files backup
    Log-Message -level "yellow" -message "Creating files backup..."
    try {
        # Create a temporary list of files to exclude
        $excludeList = @("node_modules", "vendor", ".git", "storage\logs")

        # Get all items except those in the exclude list
        $filesToBackup = Get-ChildItem -Path "." -Recurse |
            Where-Object { $item = $_; -not ($excludeList | Where-Object { $item.FullName -like "*\$_*" }) }

        # Create the archive
        Compress-Archive -Path $filesToBackup.FullName -DestinationPath "$backupDir\files_backup_$timestamp.zip" -Force
        Log-Message -level "green" -message "Files backup created successfully."
    } catch {
        Log-Message -level "yellow" -message "Files backup failed: $_"
        Log-Message -level "yellow" -message "Continuing deployment without files backup."
    }

    Log-Message -level "green" -message "Backup created successfully in $backupDir"
}

# Function to handle Discord bot deployment
function Start-DiscordBotDeployment {
    Log-Message -level "blue" -message "Deploying Discord bot..."
    $botDir = "$(Get-Location)\discord-bot"

    if (-not (Test-Path $botDir)) {
        Log-Message -level "red" -message "Discord bot directory not found at $botDir"
        return $false
    }

    # Check Node.js version compatibility
    if (-not (Get-Command node -ErrorAction SilentlyContinue)) {
        Log-Message -level "red" -message "Node.js is required but not found."
        return $false
    }

    $requiredNodeVersion = "18.0.0"
    $currentNodeVersion = (node -v).Substring(1)  # Remove the 'v' prefix

    # Simple version comparison (not perfect but works for most cases)
    if ([version]$currentNodeVersion -lt [version]$requiredNodeVersion) {
        Log-Message -level "red" -message "Node.js $requiredNodeVersion or higher required (found v$currentNodeVersion)"
        return $false
    }

    # Backup existing Discord bot configuration
    if (Test-Path "$botDir\.env") {
        Copy-Item "$botDir\.env" "$botDir\.env.backup.$(Get-Date -Format 'yyyyMMdd_HHmmss')"
        Log-Message -level "green" -message "Backed up existing discord-bot/.env"
    }

    # Install dependencies
    Log-Message -level "yellow" -message "Installing Discord bot dependencies..."
    Push-Location $botDir
    try {
        npm ci --only=production
        if ($LASTEXITCODE -ne 0) {
            Log-Message -level "red" -message "Failed to install Discord bot dependencies"
            Pop-Location
            return $false
        }
    } catch {
        Log-Message -level "red" -message "Failed to install Discord bot dependencies: $_"
        Pop-Location
        return $false
    }

    # Sync environment variables
    Log-Message -level "yellow" -message "Syncing Discord bot environment..."
    try {
        node sync-env.js
        if ($LASTEXITCODE -ne 0) {
            Log-Message -level "red" -message "Failed to sync Discord bot environment"
            Pop-Location
            return $false
        }
    } catch {
        Log-Message -level "red" -message "Failed to sync Discord bot environment: $_"
        Pop-Location
        return $false
    }

    Pop-Location
    return $true
}

# Function to start services
function Start-Services {
    Log-Message -level "blue" -message "Starting services..."

    # Start Reverb service
    Log-Message -level "yellow" -message "Starting Reverb service..."
    Start-Process -FilePath "php" -ArgumentList "artisan reverb:start --host=127.0.0.1 --port=6001" -WindowStyle Minimized

    # Start Queue Worker service
    Log-Message -level "yellow" -message "Starting Queue Worker service..."
    Start-Process -FilePath "php" -ArgumentList "artisan queue:work --queue=default,media --sleep=3 --tries=3 --timeout=3600" -WindowStyle Minimized

    # Start Discord bot
    Log-Message -level "yellow" -message "Starting Discord bot..."
    Start-Process -FilePath "node" -ArgumentList "discord-bot/index.js" -WindowStyle Minimized

    Log-Message -level "green" -message "Services started successfully!"
    Log-Message -level "yellow" -message "Note: These services are running in minimized windows. To stop them, you'll need to close those windows manually."
}

# Main deployment logic
function Main {
    Log-Message -level "blue" -message "Starting deployment process..."

    # Create backup
    New-ProjectBackup

    # Run checks
    Test-SystemRequirements
    Test-DatabaseConnection
    Repair-LaravelHealth

    # Pull latest changes
    Log-Message -level "yellow" -message "Pulling latest changes from git..."
    git pull

    # Install/update Composer dependencies
    Log-Message -level "yellow" -message "Installing Composer dependencies..."
    composer install --no-interaction --prefer-dist --optimize-autoloader --no-dev

    # Install/update NPM dependencies
    Log-Message -level "yellow" -message "Installing NPM dependencies..."
    npm ci

    # Build frontend assets
    Log-Message -level "yellow" -message "Building frontend assets..."
    npm run build

    # Run database migrations
    Log-Message -level "yellow" -message "Running database migrations..."
    php artisan migrate --force

    # Clear caches
    Log-Message -level "yellow" -message "Clearing application caches..."
    php artisan optimize:clear

    # Cache config and routes for production performance
    Log-Message -level "yellow" -message "Caching configuration and routes..."
    php artisan config:cache
    php artisan route:cache
    php artisan view:cache

    # Deploy Discord bot
    if (-not (Start-DiscordBotDeployment)) {
        Log-Message -level "red" -message "Discord bot deployment failed"
        exit 1
    }

    # Start services
    Start-Services

    Log-Message -level "green" -message "Deployment completed successfully!"
}

# Start deployment
Main
