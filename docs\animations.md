# Animationen im Minewache-Website-Projekt

Dieses Dokument beschreibt alle verfügbaren Animationen und Übergänge, die im Minewache-Website-Projekt verwendet werden. Animationen werden hauptsächlich durch die Kombination von Tailwind CSS, DaisyUI und benutzerdefinierten CSS-Klassen implementiert.

## Inhaltsverzeichnis

1. [Grundlegende Animationen](#grundlegende-animationen)
2. [Slide-Animationen](#slide-animationen)
3. [Fade-Animationen](#fade-animationen) 
4. [Interaktive Animationen](#interaktive-animationen)
5. [Füllstand-Animationen](#füllstand-animationen)
6. [Verzögerte Animationen](#verzögerte-animationen)
7. [Navigation & UI Animationen](#navigation--ui-animationen)
8. [Verwendung in Komponenten](#verwendung-in-komponenten)
9. [DaisyUI Animationseinstellungen](#daisyui-animationseinstellungen)

## Grundlegende Animationen

Diese Animationen bilden die Grundlage für viele UI-Komponenten und Übergänge.

### Pulse

Eine sanfte Pulsation, die Aufmerksamkeit erzeugt ohne zu stören.

```css
@keyframes pulse {
  '0%, 100%': { opacity: '1' },
  '50%': { opacity: '0.7' }
}

.animate-pulse {
  animation: pulse 2.5s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}
```

### Bounce

Eine leichte Hüpfbewegung, ideal für interaktive Elemente oder Call-to-Actions.

```css
@keyframes bounce {
  '0%, 100%': { transform: 'translateY(0)' },
  '50%': { transform: 'translateY(-5px)' }
}

.animate-bounce {
  animation: bounce 2s infinite;
}
```

## Slide-Animationen

Sanfte Bewegungsanimationen für Elemente, die ins Sichtfeld kommen oder verschwinden.

### Slide Up

Elemente gleiten von unten nach oben ins Sichtfeld.

```css
@keyframes slideUp {
  '0%': {
    transform: 'translateY(20px)',
    opacity: '0'
  },
  '100%': {
    transform: 'translateY(0)',
    opacity: '1'
  }
}

.animate-slide-up {
  animation: slideUp 0.7s ease-out forwards;
}
```

### Slide Down

Elemente gleiten von oben nach unten ins Sichtfeld.

```css
@keyframes slideDown {
  '0%': {
    transform: 'translateY(-20px)',
    opacity: '0'
  },
  '100%': {
    transform: 'translateY(0)',
    opacity: '1'
  }
}

.animate-slide-down {
  animation: slideDown 0.7s ease-out forwards;
}
```

### Slide Left

Elemente gleiten von rechts nach links ins Sichtfeld.

```css
@keyframes slideLeft {
  '0%': {
    transform: 'translateX(20px)',
    opacity: '0'
  },
  '100%': {
    transform: 'translateX(0)',
    opacity: '1'
  }
}

.animate-slide-left {
  animation: slideLeft 0.7s ease-out forwards;
}
```

### Slide Right

Elemente gleiten von links nach rechts ins Sichtfeld.

```css
@keyframes slideRight {
  '0%': {
    transform: 'translateX(-20px)',
    opacity: '0'
  },
  '100%': {
    transform: 'translateX(0)',
    opacity: '1'
  }
}

.animate-slide-right {
  animation: slideRight 0.7s ease-out forwards;
}
```

## Fade-Animationen

Transparenz-basierte Übergänge für sanftes Ein- und Ausblenden.

### Fade In

Elemente werden sanft eingeblendet.

```css
@keyframes fadeIn {
  '0%': { opacity: '0' },
  '100%': { opacity: '1' }
}

.animate-fade-in {
  animation: fadeIn 0.7s ease-in-out forwards;
}
```

## Interaktive Animationen

Animationen, die auf Benutzerinteraktionen reagieren.

### Link-Hover

Sanfter Farbübergang bei Hover über Links.

```css
a {
  @apply text-info transition-colors;
}

a:hover {
  @apply text-primary;
}
```

### Button-Hover

Farbwechsel für Buttons bei Hover.

```css
.btn-hover:hover {
  background-color: var(--color-primary);
  color: var(--color-primary-content);
}
```

### Breeze-Input Focus

Hervorhebung von Formularfeldern bei Fokus.

```css
.breeze-input:focus {
  border-color: var(--color-primary);
  box-shadow: 0 0 0 3px rgba(40, 100, 200, 0.25);
  outline: none;
}
```

## Füllstand-Animationen

Animationen für Fortschrittsbalken und ähnliche Anzeigen.

### Fill Animation (33%)

```css
@keyframes fillWidth {
  from { width: 0%; }
  to { width: 33%; }
}

.fill-animation {
  animation: fillWidth 1.5s ease-out forwards;
}
```

### Fill Animation (66%)

```css
@keyframes fillWidth2-3 {
  from { width: 0%; }
  to { width: 66%; }
}

.fill-animation2-3 {
  animation: fillWidth2-3 2.5s ease-out forwards;
}
```

### Fill Animation (100%)

```css
@keyframes fillWidth3-3 {
  from { width: 0%; }
  to { width: 100%; }
}

.fill-animation3-3 {
  animation: fillWidth3-3 3.5s ease-out forwards;
}
```

## Verzögerte Animationen

Animationen mit absichtlicher Verzögerung für gestaffelte Effekte.

### Delayed Fade

```css
.animate-delayed-fade {
  animation: fadeIn 0.7s ease-in-out 0.3s forwards;
}
```

### Delayed Slide

```css
.animate-delayed-slide {
  animation: slideUp 0.7s ease-out 0.3s forwards;
}
```

## Navigation & UI Animationen

Spezielle Animationen für Navigations- und UI-Elemente.

### Navigation Item Slide Down/Up

```css
.nav-item-slide-down {
  animation: slideDown 0.5s ease-out forwards;
}

.nav-item-slide-up {
  animation: slideUp 0.5s ease-out forwards;
}
```

### Burger Menu Animation

Eine besondere Animation für das Burger-Menü-Icon.

```css
@keyframes spin-grow {
  0% {
    transform: scale(0.8) rotate(0deg);
    opacity: 0;
  }
  100% {
    transform: scale(1) rotate(180deg);
    opacity: 1;
  }
}

.nav-burger-appear {
  animation: spin-grow 0.5s ease-out forwards;
}
```

## Verwendung in Komponenten

Beispiele für die Verwendung der Animationen in verschiedenen UI-Komponenten.

### Cards

```html
<div class="card-themed animate-fade-in">
  <!-- Card Content -->
</div>
```

### Alerts

```html
<div class="alert-themed success animate-slide-up">
  Deine Änderungen wurden erfolgreich gespeichert!
</div>
```

### Fortschrittsbalken

```html
<div class="bg-base-300 rounded-full h-2.5">
  <div class="bg-primary h-2.5 rounded-full fill-animation3-3"></div>
</div>
```

## DaisyUI Animationseinstellungen

Die folgenden DaisyUI Variablen steuern die Animationszeiten für UI-Komponenten:

```js
"--animation-btn": "0.25s",     // Button-Animationszeit
"--animation-input": "0.2s",    // Input-Animationszeit
"--btn-focus-scale": "0.95",    // Skalierungsfaktor für fokussierte Buttons
```

Diese Werte können in der `tailwind.config.js` Datei im DaisyUI-Theme-Bereich angepasst werden.

## Best Practices

1. **Performance**: Bevorzuge `transform` und `opacity` für Animationen statt Layout-Eigenschaften wie `width` oder `height`, um die Performance zu optimieren.
2. **Zugänglichkeit**: Halte Animationen subtil und biete Benutzern mit der Präferenz für reduzierte Bewegung alternative Stile an.
3. **Konsistenz**: Verwende die definierten Animationsklassen konsequent für ein einheitliches Erscheinungsbild.
4. **Zweck**: Setze Animationen gezielt ein, um die Benutzerführung zu verbessern oder Statusänderungen zu verdeutlichen.

## Anpassen von Animationen

Um eine bestehende Animation zu ändern oder eine neue hinzuzufügen:

1. Definiere neue Keyframes in `resources/css/app.css` oder bearbeite bestehende
2. Füge eine neue Animationsklasse mit Verweis auf diese Keyframes hinzu
3. Bei komplexeren Animationen, füge die Keyframe-Definition in der `tailwind.config.js` unter dem `keyframes`-Abschnitt hinzu

## Responsive Animationen

Für bessere Performance auf mobilen Geräten können Animationen mit Tailwind responsive gemacht werden:

```html
<div class="md:animate-fade-in">
  <!-- Animiert nur auf mittleren und größeren Bildschirmen -->
</div>
```