
import React from 'react';

// FIX: Extend React.HTMLAttributes<HTMLDivElement> to accept standard HTML attributes like 'role'
interface GlassCardProps extends React.HTMLAttributes<HTMLDivElement> {
  children: React.ReactNode;
  className?: string;
}

// FIX: Destructure className and children, then spread the rest of the props
export const GlassCard: React.FC<GlassCardProps> = ({ children, className = '', ...rest }) => {
  return (
    <div
      className={`
        bg-slate-800/60 backdrop-blur-xl 
        border border-slate-700/50 
        rounded-2xl shadow-2xl 
        transition-all duration-300 ease-in-out
        ${className}
      `}
      // FIX: Spread the rest of the props onto the div
      {...rest}
    >
      {children}
    </div>
  );
};