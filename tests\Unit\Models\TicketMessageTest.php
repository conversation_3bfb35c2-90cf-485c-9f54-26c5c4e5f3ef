<?php

namespace Tests\Unit\Models;

use App\Models\Ticket;
use App\Models\TicketAttachment;
use App\Models\TicketMessage;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class TicketMessageTest extends TestCase
{
    use RefreshDatabase;

    /** @test */
    public function it_has_fillable_attributes()
    {
        $fillable = [
            'ticket_id',
            'user_id',
            'message',
            'is_from_discord',
            'discord_message_id',
            'sync_failed',
            'is_system_message',
            'message_source',
        ];

        $message = new TicketMessage();
        $this->assertEquals($fillable, $message->getFillable());
    }

    /** @test */
    public function it_casts_attributes_correctly()
    {
        $message = new TicketMessage();
        $casts = $message->getCasts();

        $this->assertArrayHasKey('is_from_discord', $casts);
        $this->assertEquals('boolean', $casts['is_from_discord']);

        $this->assertArrayHasKey('sync_failed', $casts);
        $this->assertEquals('boolean', $casts['sync_failed']);

        $this->assertArrayHasKey('is_system_message', $casts);
        $this->assertEquals('boolean', $casts['is_system_message']);

        $this->assertArrayHasKey('message_source', $casts);
        $this->assertEquals('string', $casts['message_source']);

        $this->assertArrayHasKey('created_at', $casts);
        $this->assertEquals('datetime', $casts['created_at']);

        $this->assertArrayHasKey('updated_at', $casts);
        $this->assertEquals('datetime', $casts['updated_at']);
    }

    /** @test */
    public function it_belongs_to_a_ticket()
    {
        $ticket = Ticket::factory()->create();
        $message = TicketMessage::factory()->create(['ticket_id' => $ticket->id]);

        $this->assertInstanceOf(Ticket::class, $message->ticket);
        $this->assertEquals($ticket->id, $message->ticket->id);
    }

    /** @test */
    public function it_belongs_to_a_user()
    {
        $user = User::factory()->create();
        $message = TicketMessage::factory()->create(['user_id' => $user->id]);

        $this->assertInstanceOf(User::class, $message->user);
        $this->assertEquals($user->id, $message->user->id);
    }

    /** @test */
    public function it_has_many_attachments()
    {
        $message = TicketMessage::factory()->create();
        $attachment = TicketAttachment::factory()->create(['ticket_message_id' => $message->id]);

        $this->assertInstanceOf(TicketAttachment::class, $message->attachments->first());
        $this->assertEquals($attachment->id, $message->attachments->first()->id);
    }

    /** @test */
    public function it_can_be_from_discord()
    {
        $discordMessage = TicketMessage::factory()->create([
            'is_from_discord' => true,
            'discord_message_id' => '123456789'
        ]);

        $webMessage = TicketMessage::factory()->create([
            'is_from_discord' => false,
            'discord_message_id' => null
        ]);

        $this->assertTrue($discordMessage->is_from_discord);
        $this->assertFalse($webMessage->is_from_discord);
        $this->assertEquals('123456789', $discordMessage->discord_message_id);
        $this->assertNull($webMessage->discord_message_id);
    }
}
