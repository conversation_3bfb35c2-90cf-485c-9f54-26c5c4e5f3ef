<?php

namespace App\Http\Controllers;

use App\Jobs\ProcessTicketMediaJob;
use App\Models\Ticket;
use App\Models\TicketAttachment;
use App\Models\TicketMessage;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;

class TicketApiController extends Controller
{
    /**
     * Create a new ticket from Discord.
     */
    public function create(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'user_id' => 'required|string|exists:users,id',
            'title' => 'required|string|max:255',
            'description' => 'required|string',
            'discord_channel_id' => 'required|string',
        ]);

        if ($validator->fails()) {
            return response()->json(['error' => $validator->errors()], 422);
        }

        $ticket = new Ticket([
            'user_id' => $request->user_id,
            'title' => $request->title,
            'description' => $request->description,
            'status' => 'open',
            'discord_channel_id' => $request->discord_channel_id,
        ]);

        $ticket->save();

        return response()->json([
            'success' => true,
            'ticket' => $ticket,
        ]);
    }

    /**
     * Reconstruct a ticket from Discord channel information.
     */
    public function reconstruct(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'user_id' => 'required|string',
            'title' => 'required|string|max:255',
            'description' => 'required|string',
            'discord_channel_id' => 'required|string',
            'is_reconstructed' => 'nullable|boolean',
            'suggested_id' => 'nullable|integer',
        ]);

        if ($validator->fails()) {
            return response()->json(['error' => $validator->errors()], 422);
        }

        // Check if a ticket with this Discord channel ID already exists
        $existingTicket = Ticket::where('discord_channel_id', $request->discord_channel_id)->first();

        if ($existingTicket) {
            // If the ticket already exists, return it
            Log::info('Ticket already exists for Discord channel, returning existing ticket', [
                'discord_channel_id' => $request->discord_channel_id,
                'ticket_id' => $existingTicket->id
            ]);

            return response()->json([
                'success' => true,
                'ticket' => $existingTicket,
                'message' => 'Existing ticket found for this Discord channel'
            ]);
        }

        // Check if the user exists, if not create a placeholder user
        $userId = $request->user_id;
        $user = User::find($userId);

        if (!$user) {
            try {
                // Try to fetch user info from Discord API via the bot
                $botApiUrl = config('services.discord.bot_url', env('DISCORD_BOT_API_URL', 'http://localhost:3001'));
                $endpoint = "{$botApiUrl}/api/discord/user/{$userId}";
                $apiKey = config('services.discord.api_key');

                $response = Http::withHeaders([
                    'Authorization' => "Bearer {$apiKey}",
                    'Accept' => 'application/json'
                ])->get($endpoint);

                if ($response->successful() && isset($response['user'])) {
                    $discordUser = $response['user'];

                    // Create a new user record
                    $user = new User([
                        'id' => $userId,
                        'username' => $discordUser['username'] ?? "Discord User {$userId}",
                        'global_name' => $discordUser['global_name'] ?? null,
                        'discriminator' => $discordUser['discriminator'] ?? '0000',
                        'avatar' => $discordUser['avatar'] ?? null,
                        'locale' => 'de',  // Default locale
                        'verified' => false,
                        'mfa_enabled' => false,
                        'premium_type' => 0,
                        'public_flags' => 0,
                        'permissions' => 0, // No special permissions
                    ]);

                    $user->save();

                    Log::info('Created new user from Discord API for ticket reconstruction', [
                        'user_id' => $userId,
                        'username' => $user->username
                    ]);
                } else {
                    // Create a minimal user record as fallback
                    $user = new User([
                        'id' => $userId,
                        'username' => "Discord User {$userId}",
                        'discriminator' => '0000',
                        'locale' => 'de',  // Default locale
                        'verified' => false,
                        'mfa_enabled' => false,
                        'premium_type' => 0,
                        'public_flags' => 0,
                        'permissions' => 0, // No special permissions
                    ]);

                    $user->save();

                    Log::info('Created fallback user for ticket reconstruction', [
                        'user_id' => $userId
                    ]);
                }
            } catch (\Exception $e) {
                // If we can't create a user, we can't create a ticket
                Log::error('Failed to create user for ticket reconstruction', [
                    'user_id' => $userId,
                    'error' => $e->getMessage()
                ]);

                return response()->json([
                    'success' => false,
                    'error' => 'Failed to create user for ticket reconstruction'
                ], 500);
            }
        }

        // Create the ticket
        $ticket = new Ticket([
            'user_id' => $userId,
            'title' => $request->title,
            'description' => $request->description,
            'status' => 'open',
            'discord_channel_id' => $request->discord_channel_id,
        ]);

        // Add a note that this ticket was reconstructed
        $ticket->is_reconstructed = true;

        $ticket->save();

        // Log the reconstruction
        Log::info('Ticket reconstructed from Discord channel', [
            'ticket_id' => $ticket->id,
            'discord_channel_id' => $request->discord_channel_id,
            'user_id' => $userId,
            'suggested_id' => $request->suggested_id ?? null
        ]);

        // Create a system message to indicate the ticket was reconstructed
        $systemMessage = new TicketMessage([
            'ticket_id' => $ticket->id,
            'user_id' => $userId, // Use the same user ID as the ticket creator
            'message' => 'This ticket was automatically reconstructed from an existing Discord channel. Some information may be missing.',
            'is_system_message' => true,
        ]);

        $systemMessage->save();

        return response()->json([
            'success' => true,
            'ticket' => $ticket,
            'message' => 'Ticket successfully reconstructed'
        ]);
    }

    /**
     * Add a message to a ticket from Discord.
     */
    public function addMessage(Request $request, Ticket $ticket)
    {
        // Prepare validation rules
        $validationRules = [
            'user_id' => 'required|string',
            'message' => 'required|string',
            'discord_message_id' => 'nullable|string',
            'is_from_discord' => 'nullable|boolean',
            'attachments' => 'nullable|array',
            'attachments.*.id' => 'required|string',
            'attachments.*.filename' => 'required|string',
            'attachments.*.url' => 'required|url',
            'attachments.*.size' => 'required|integer',
            'attachments.*.content_type' => 'required|string',
        ];

        // Only check if user exists in database if the message is not from Discord
        if (!$request->is_from_discord) {
            $validationRules['user_id'] .= '|exists:users,id';
        }

        $validator = Validator::make($request->all(), $validationRules);

        // Log the request data for debugging
        Log::info('Adding message to ticket', [
            'ticket_id' => $ticket->id,
            'user_id' => $request->user_id,
            'is_from_discord' => $request->is_from_discord ?? false,
            'has_discord_message_id' => isset($request->discord_message_id),
            'attachments_count' => count($request->attachments ?? []),
        ]);

        if ($validator->fails()) {
            return response()->json(['error' => $validator->errors()], 422);
        }

        // Check if the user exists in the database
        $userId = $request->user_id;
        $user = User::find($userId);

        // If the message is from Discord and the user doesn't exist, create a placeholder user
        if ($request->is_from_discord && !$user) {
            try {
                // Try to fetch user info from Discord API via the bot
                $botApiUrl = config('services.discord.bot_url', env('DISCORD_BOT_API_URL', 'http://localhost:3001'));
                $endpoint = "{$botApiUrl}/api/discord/user/{$userId}";
                $apiKey = config('services.discord.api_key');

                $response = Http::withHeaders([
                    'Authorization' => "Bearer {$apiKey}",
                    'Accept' => 'application/json'
                ])->get($endpoint);

                if ($response->successful() && isset($response['user'])) {
                    $discordUser = $response['user'];

                    // Create a new user with Discord data
                    $user = new User([
                        'id' => $userId,
                        'username' => $discordUser['username'] ?? "Discord User {$userId}",
                        'global_name' => $discordUser['global_name'] ?? null,
                        'discriminator' => $discordUser['discriminator'] ?? '0000',
                        'avatar' => $discordUser['avatar'] ?? null,
                        'locale' => 'de',  // Default locale
                        'verified' => false,
                        'mfa_enabled' => false,
                        'premium_type' => 0,
                        'public_flags' => 0,
                        'permissions' => 0, // No special permissions
                    ]);
                    $user->save();

                    Log::info("Created new user from Discord", [
                        'user_id' => $userId,
                        'username' => $user->username
                    ]);
                } else {
                    // Create a minimal user record if Discord API call fails
                    $user = new User([
                        'id' => $userId,
                        'username' => "Discord User {$userId}",
                        'discriminator' => '0000',
                        'locale' => 'de',  // Default locale
                        'verified' => false,
                        'mfa_enabled' => false,
                        'premium_type' => 0,
                        'public_flags' => 0,
                        'permissions' => 0, // No special permissions
                    ]);
                    $user->save();

                    Log::info("Created minimal user record for Discord user", [
                        'user_id' => $userId
                    ]);
                }
            } catch (\Exception $e) {
                Log::error("Failed to create user from Discord", [
                    'user_id' => $userId,
                    'error' => $e->getMessage()
                ]);

                // Create a minimal user record as fallback
                $user = new User([
                    'id' => $userId,
                    'username' => "Discord User {$userId}",
                    'discriminator' => '0000',
                    'locale' => 'de',  // Default locale
                    'verified' => false,
                    'mfa_enabled' => false,
                    'premium_type' => 0,
                    'public_flags' => 0,
                    'permissions' => 0, // No special permissions
                ]);
                $user->save();
            }
        }

        $messageData = [
            'ticket_id' => $ticket->id,
            'user_id' => $userId,
            'message' => $request->message,
            'is_from_discord' => $request->is_from_discord ?? false,
        ];

        // Add discord_message_id if provided
        if ($request->has('discord_message_id')) {
            $messageData['discord_message_id'] = $request->discord_message_id;
        }

        $message = new TicketMessage($messageData);

        $message->save();

        // Associate any pending attachments with this message
        if ($request->is_from_discord) {
            $pendingAttachments = TicketAttachment::whereNull('ticket_message_id')
                ->where('is_from_discord', true)
                ->whereNotNull('metadata')
                ->get();

            foreach ($pendingAttachments as $pendingAttachment) {
                $metadata = json_decode($pendingAttachment->metadata, true);

                // Check if this attachment belongs to the current ticket and is pending association
                if (isset($metadata['ticket_id']) && $metadata['ticket_id'] == $ticket->id &&
                    isset($metadata['pending_association']) && $metadata['pending_association']) {

                    // Associate the attachment with this message
                    $pendingAttachment->ticket_message_id = $message->id;

                    // Update metadata to indicate it's no longer pending
                    $metadata['pending_association'] = false;
                    $pendingAttachment->metadata = json_encode($metadata);

                    $pendingAttachment->save();

                    Log::info('Associated pending attachment with message', [
                        'attachment_id' => $pendingAttachment->id,
                        'message_id' => $message->id,
                        'ticket_id' => $ticket->id
                    ]);
                }
            }
        }

        // Handle attachments if present
        $attachments = [];
        if ($request->has('attachments')) {
            foreach ($request->attachments as $attachmentData) {
                try {
                    // Download the file from Discord
                    $tempFile = tempnam(sys_get_temp_dir(), 'discord_');
                    file_put_contents($tempFile, file_get_contents($attachmentData['url']));

                    // Store the file
                    $path = Storage::putFile(
                        'ticket-attachments/' . $ticket->id,
                        $tempFile
                    );

                    // Create the attachment record
                    $attachment = new TicketAttachment([
                        'ticket_message_id' => $message->id,
                        'filename' => basename($path),
                        'original_filename' => $attachmentData['filename'],
                        'file_path' => $path,
                        'mime_type' => $attachmentData['content_type'],
                        'file_size' => $attachmentData['size'],
                        'discord_attachment_id' => $attachmentData['id'],
                        'is_from_discord' => true,
                        'processing_status' => 'pending',
                    ]);

                    $attachment->save();
                    $attachments[] = $attachment;

                    // Dispatch job to process the media file
                    ProcessTicketMediaJob::dispatch($attachment);

                    // Clean up temp file
                    @unlink($tempFile);
                } catch (\Exception $e) {
                    // Log the error but continue with other attachments
                    logger()->error('Failed to process Discord attachment: ' . $e->getMessage(), [
                        'attachment' => $attachmentData,
                        'exception' => $e,
                    ]);
                }
            }
        }

        return response()->json([
            'success' => true,
            'message' => $message,
            'attachments' => $attachments,
        ]);
    }

    /**
     * Update ticket status from Discord.
     */
    public function updateStatus(Request $request, Ticket $ticket)
    {
        $validator = Validator::make($request->all(), [
            'status' => 'required|in:open,in_progress,closed',
        ]);

        if ($validator->fails()) {
            return response()->json(['error' => $validator->errors()], 422);
        }

        $ticket->status = $request->status;
        $ticket->save();

        return response()->json([
            'success' => true,
            'ticket' => $ticket,
        ]);
    }

    /**
     * Get ticket information.
     */
    public function show(Ticket $ticket)
    {
        $ticket->load(['user', 'assignedTo', 'messages.user', 'messages.attachments']);

        return response()->json([
            'ticket' => $ticket,
        ]);
    }

    /**
     * Get all tickets for a user.
     */
    public function userTickets(Request $request, $userId)
    {
        $user = User::findOrFail($userId);

        $tickets = $user->tickets()
            ->with(['assignedTo'])
            ->latest()
            ->get();

        return response()->json([
            'tickets' => $tickets,
        ]);
    }

    /**
     * Download a ticket attachment.
     */
    public function downloadAttachment(Request $request, TicketAttachment $attachment)
    {
        // Verify the signature for signed URLs
        if (!$request->hasValidSignature()) {
            return response()->json(['error' => 'Invalid or expired URL'], 403);
        }

        if (!Storage::exists($attachment->file_path)) {
            return response()->json(['error' => 'Attachment not found'], 404);
        }

        return response()->download(
            Storage::path($attachment->file_path),
            $attachment->original_filename,
            ['Content-Type' => $attachment->mime_type]
        );
    }

    /**
     * Download a ticket attachment for the Discord bot.
     * This endpoint is authenticated with the API token instead of a signature.
     */
    public function botDownloadAttachment(Request $request, TicketAttachment $attachment)
    {
        // Log the request for debugging
        Log::info('Bot download attachment request', [
            'attachment_id' => $attachment->id,
            'file_path' => $attachment->file_path,
            'processed_file_path' => $attachment->processed_file_path,
            'processing_status' => $attachment->processing_status,
            'original_filename' => $attachment->original_filename,
            'mime_type' => $attachment->mime_type,
            'token' => substr($request->bearerToken() ?? 'none', 0, 3) . '...',
            'ip' => $request->ip()
        ]);

        // Determine which file to serve - use processed file if available and completed
        $filePath = $attachment->file_path;
        $mimeType = $attachment->mime_type;

        // If the attachment has been processed successfully and has a processed file, use that instead
        if ($attachment->processing_status === 'completed' && $attachment->processed_file_path && Storage::exists($attachment->processed_file_path)) {
            $filePath = $attachment->processed_file_path;

            // Update mime type based on the processed file extension
            $extension = pathinfo($filePath, PATHINFO_EXTENSION);
            if ($extension === 'mp4') {
                $mimeType = 'video/mp4';
            } elseif ($extension === 'mp3') {
                $mimeType = 'audio/mpeg';
            } elseif ($extension === 'jpg' || $extension === 'jpeg') {
                $mimeType = 'image/jpeg';
            }

            Log::info('Using processed file for Discord bot', [
                'attachment_id' => $attachment->id,
                'processed_file_path' => $filePath,
                'mime_type' => $mimeType
            ]);
        }

        if (!Storage::exists($filePath)) {
            Log::error('Attachment file not found', [
                'attachment_id' => $attachment->id,
                'file_path' => $filePath
            ]);
            return response()->json(['error' => 'Attachment not found'], 404);
        }

        try {
            $fullPath = Storage::path($filePath);
            $fileContents = file_get_contents($fullPath);

            if ($fileContents === false) {
                Log::error('Failed to read attachment file', [
                    'attachment_id' => $attachment->id,
                    'file_path' => $fullPath
                ]);
                return response()->json(['error' => 'Failed to read attachment file'], 500);
            }

            // Ensure the filename has the correct extension based on the mime type
            $originalFilename = $attachment->original_filename;
            $extension = pathinfo($filePath, PATHINFO_EXTENSION);
            $originalExtension = pathinfo($originalFilename, PATHINFO_EXTENSION);

            // If the extensions don't match, append the new extension
            if (strtolower($originalExtension) !== strtolower($extension) && !empty($extension)) {
                $originalFilename = pathinfo($originalFilename, PATHINFO_FILENAME) . '.' . $extension;
            }

            return response($fileContents, 200, [
                'Content-Type' => $mimeType,
                'Content-Disposition' => 'attachment; filename="' . $originalFilename . '"',
                'Content-Length' => strlen($fileContents)
            ]);
        } catch (\Exception $e) {
            Log::error('Exception in botDownloadAttachment', [
                'attachment_id' => $attachment->id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return response()->json(['error' => 'Internal server error: ' . $e->getMessage()], 500);
        }
    }

    /**
     * Upload an attachment from Discord.
     */
    public function uploadAttachment(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'file' => 'required|file|max:10240', // 10MB max
            'ticket_message_id' => 'required',
            'original_filename' => 'required|string',
            'mime_type' => 'required|string',
            'file_size' => 'required|integer',
            'discord_attachment_id' => 'required|string',
        ]);

        if ($validator->fails()) {
            return response()->json(['error' => $validator->errors()], 422);
        }

        // Handle case where ticket_message_id is 0 (message not created yet)
        $ticket = null;
        $messageId = $request->ticket_message_id;

        if ($messageId == 0) {
            // If message ID is 0, we need to find the ticket from the discord_channel_id
            // This is a temporary solution until the message is created
            $channelId = $request->header('X-Discord-Channel-Id');

            if (!$channelId) {
                return response()->json(['error' => 'Channel ID is required when message ID is 0'], 422);
            }

            $ticket = Ticket::where('discord_channel_id', $channelId)->first();

            if (!$ticket) {
                return response()->json(['error' => 'Ticket not found for channel ID: ' . $channelId], 404);
            }

            Log::info('Uploading attachment for future message in channel', [
                'channel_id' => $channelId,
                'ticket_id' => $ticket->id,
                'filename' => $request->original_filename
            ]);
        } else {
            // Normal case - message already exists
            $message = TicketMessage::find($messageId);

            if (!$message) {
                return response()->json(['error' => 'Message not found: ' . $messageId], 404);
            }

            $ticket = $message->ticket;
        }

        // Store the file
        $path = $request->file('file')->store('ticket-attachments/' . $ticket->id);

        // Create the attachment record
        $attachmentData = [
            'filename' => basename($path),
            'original_filename' => $request->original_filename,
            'file_path' => $path,
            'mime_type' => $request->mime_type,
            'file_size' => $request->file_size,
            'discord_attachment_id' => $request->discord_attachment_id,
            'is_from_discord' => true,
            'processing_status' => 'pending',
        ];

        // If message ID is 0, we'll set ticket_message_id later when the message is created
        if ($messageId != 0) {
            $attachmentData['ticket_message_id'] = $messageId;
        }

        $attachment = new TicketAttachment($attachmentData);
        $attachment->save();

        // Store the ticket ID in the attachment's metadata for later association
        if ($messageId == 0) {
            $attachment->metadata = json_encode([
                'ticket_id' => $ticket->id,
                'pending_association' => true
            ]);
            $attachment->save();

            Log::info('Created attachment without message association', [
                'attachment_id' => $attachment->id,
                'ticket_id' => $ticket->id,
                'discord_attachment_id' => $request->discord_attachment_id
            ]);
        }

        // Dispatch job to process the media file
        ProcessTicketMediaJob::dispatch($attachment);

        return response()->json([
            'success' => true,
            'attachment_id' => $attachment->id,
            'attachment' => $attachment,
        ]);
    }
}
