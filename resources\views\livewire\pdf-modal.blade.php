<div>
    <!-- PDF Modal -->
    <div id="pdfModal" class="fixed inset-0 bg-black bg-opacity-80 flex items-center justify-center z-50 @if(!$isOpen) hidden @endif">
        <div class="relative w-full max-w-4xl h-[90vh] p-4 flex flex-col">
            <div class="flex justify-between items-center mb-2">
                <p id="pdfModalTitle" class="text-white text-sm">{{ $title }}</p>
                <button wire:click="closeModal" class="text-white bg-gray-700 hover:bg-gray-600 rounded-full p-2">
                    <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd"></path>
                    </svg>
                </button>
            </div>
            <iframe id="pdfModalContent" src="{{ $pdfUrl }}" class="flex-1 w-full h-full rounded-lg shadow-lg border-0"></iframe>
        </div>
    </div>

    <script>
        document.addEventListener('livewire:initialized', function() {
            // Reset iframe when modal is closed
            Livewire.hook('element.updated', ({ el, component }) => {
                if (component.name === 'pdf-modal') {
                    const iframeElement = document.getElementById('pdfModalContent');
                    if (iframeElement && !@js($isOpen)) {
                        iframeElement.src = 'about:blank';
                    }
                }
            });
        });
    </script>
</div>
