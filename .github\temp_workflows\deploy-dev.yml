name: Deploy to Development Server

on:
  push:
    branches: [ dev ]

jobs:
  deploy:
    runs-on: ubuntu-latest
    
    steps:
    - uses: actions/checkout@v3
      with:
        fetch-depth: 0

    - name: Setup SSH
      uses: webfactory/ssh-agent@v0.7.0
      with:
        ssh-private-key: ${{ secrets.DEV_SSH_PRIVATE_KEY }}

    - name: Add SSH Known Hosts
      run: |
        mkdir -p ~/.ssh
        ssh-keyscan -H ${{ secrets.DEV_SERVER_IP }} >> ~/.ssh/known_hosts

    - name: Deploy to Development Server
      env:
        SERVER_IP: ${{ secrets.DEV_SERVER_IP }}
        SERVER_USER: ${{ secrets.DEV_SERVER_USER }}
        SERVER_PATH: ${{ secrets.DEV_SERVER_PATH }}
      run: |
        # Create a deployment log
        echo "Starting deployment to development server at $(date)" > deploy.log
        
        # SSH into the server and run deployment commands
        ssh $SERVER_USER@$SERVER_IP << 'EOF'
          cd ${{ secrets.DEV_SERVER_PATH }}
          
          # Create a backup before deployment
          timestamp=$(date +%Y%m%d_%H%M%S)
          mkdir -p backups
          tar -czf backups/backup_$timestamp.tar.gz --exclude=backups --exclude=vendor --exclude=node_modules .
          
          # Pull the latest changes
          git fetch --all
          git checkout dev
          git pull origin dev
          
          # Install dependencies
          composer install --no-interaction --prefer-dist --optimize-autoloader
          npm ci
          npm run build
          
          # Run migrations
          php artisan migrate --force
          
          # Clear caches
          php artisan optimize:clear
          php artisan config:cache
          php artisan route:cache
          php artisan view:cache
          
          # Set proper permissions
          chown -R www-data:www-data .
          chmod -R 755 .
          chmod -R 775 storage bootstrap/cache
          
          # Restart services
          sudo systemctl restart minewache-reverb
          sudo systemctl restart minewache-queue
          sudo systemctl restart minewache-discord
        EOF
        
        # Log the result
        if [ $? -eq 0 ]; then
          echo "Deployment to development server completed successfully at $(date)" >> deploy.log
          exit 0
        else
          echo "Deployment to development server failed at $(date)" >> deploy.log
          exit 1
        fi

    - name: Upload Deployment Log
      if: always()
      uses: actions/upload-artifact@v4
      with:
        name: deployment-log
        path: ./deploy.log
