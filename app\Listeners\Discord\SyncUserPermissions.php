<?php

namespace App\Listeners\Discord;

use App\Events\Discord\UserRolesChanged;
use App\Services\PermissionSyncService; // Import the new service
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Support\Facades\Log;

class SyncUserPermissions // Optional: implements ShouldQueue
{
    // use InteractsWithQueue; // Optional

    protected PermissionSyncService $permissionSyncService;

    /**
     * Create the event listener.
     *
     * @param PermissionSyncService $permissionSyncService
     * @return void
     */
    public function __construct(PermissionSyncService $permissionSyncService)
    {
        $this->permissionSyncService = $permissionSyncService;
    }

    /**
     * Handle the event.
     *
     * @param  \App\Events\Discord\UserRolesChanged  $event
     * @return void
     */
    public function handle(UserRolesChanged $event)
    {
        Log::info("SyncUserPermissions Listener: Handling UserRolesChanged event for Discord user {$event->discordUserId} in guild {$event->guildId}.");

        // Extract just the role IDs from the event data
        $discordRoleIds = array_map(function($role) {
            return $role['id'];
        }, $event->newRoles);

        $success = $this->permissionSyncService->syncUserPermissionsFromDiscordRoles(
            $event->discordUserId,
            $discordRoleIds
        );

        if ($success) {
            Log::info("SyncUserPermissions Listener: Successfully processed role update for Discord user {$event->discordUserId}.");
        } else {
            Log::error("SyncUserPermissions Listener: Failed to process role update for Discord user {$event->discordUserId}.");
        }
    }
}
