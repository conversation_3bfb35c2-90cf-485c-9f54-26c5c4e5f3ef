<?php

namespace Database\Factories;

use App\Models\RegisterYoutubeLink;
use Illuminate\Database\Eloquent\Factories\Factory;

class RegisterYoutubeLinkFactory extends Factory
{
    protected $model = RegisterYoutubeLink::class;

    public function definition(): array
    {
        return [
            'season' => fake()->numberBetween(1, 5),
            'episode' => fake()->numberBetween(1, 20),
            'link' => fake()->regexify('[A-Za-z0-9_-]{11}'), // YouTube video ID format
            'created_at' => fake()->dateTimeBetween('-1 year', 'now'),
            'updated_at' => fake()->dateTimeBetween('-1 month', 'now'),
        ];
    }

    /**
     * Configure the model for a specific season.
     */
    public function forSeason(int $season): self
    {
        return $this->state(function (array $attributes) use ($season) {
            return [
                'season' => $season,
            ];
        });
    }

    /**
     * Configure the model for a specific episode.
     */
    public function forEpisode(int $episode): self
    {
        return $this->state(function (array $attributes) use ($episode) {
            return [
                'episode' => $episode,
            ];
        });
    }
}
