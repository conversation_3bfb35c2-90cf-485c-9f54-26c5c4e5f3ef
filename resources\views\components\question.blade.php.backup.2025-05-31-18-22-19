<div class="form-control w-full mb-6">
    <label class="label">
        <span class="label-text font-medium">{{ $label }} @if(!$optional) <span class="text-error">*</span> @else <span class="text-xs opacity-60">(Optional)</span> @endif</span>
        
        @if ($type === 'textarea')
        <span class="label-text-alt text-base-content/60">Max. {{ $maxlength ?? 1000 }} <PERSON><PERSON><PERSON></span>
        @endif
    </label>

    @if ($type === 'select')
        <div class="relative">
            <select id="{{ $id }}" name="{{ $id }}" @if(!$optional) required @endif
                   class="select select-bordered w-full pr-10 @error($id) select-error @enderror"
                   onchange="handleOtherOption(this, '{{ $id }}')">
                <option value="" disabled selected>Bitte auswählen...</option>
                @foreach ($options as $value => $text)
                    <option value="{{ $value }}">{{ $text }}</option>
                @endforeach
            </select>
            <div class="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none">
                <x-heroicon-o-chevron-down class="h-5 w-5 text-gray-400" />
            </div>
        </div>
        <div id="{{ $id }}-other" class="mt-3 animate-fade-in" style="display: none;">
            <div class="flex items-center space-x-2 opacity-80 mb-2">
                <x-heroicon-o-pencil class="h-4 w-4" />
                <span class="text-sm">Bitte spezifizieren:</span>
            </div>
            <input type="text" id="{{ $id }}_other" name="{{ $id }}_other" 
                   class="input input-bordered w-full" placeholder="Deine Antwort...">
        </div>
        @error($id) <span class="label-text-alt text-error mt-1">{{ $message }}</span> @enderror

    @elseif ($type === 'text')
        <div class="input-group">
            @if(str_contains($label, 'RAM'))
                <span class="btn btn-square btn-ghost">
                    <x-heroicon-o-cpu-chip class="h-5 w-5" />
                </span>
            @elseif(str_contains($label, 'GPU'))
                <span class="btn btn-square btn-ghost">
                    <x-heroicon-o-rectangle-group class="h-5 w-5" />
                </span>
            @elseif(str_contains($label, 'Mikrofon'))
                <span class="btn btn-square btn-ghost">
                    <x-heroicon-o-microphone class="h-5 w-5" />
                </span>
            @elseif(str_contains($label, 'DAW'))
                <span class="btn btn-square btn-ghost">
                    <x-heroicon-o-adjustments-horizontal class="h-5 w-5" />
                </span>
            @elseif(str_contains($label, 'Wunschrolle'))
                <span class="btn btn-square btn-ghost">
                    <x-heroicon-o-user-circle class="h-5 w-5" />
                </span>
            @else
                <span class="btn btn-square btn-ghost">
                    <x-heroicon-o-document-text class="h-5 w-5" />
                </span>
            @endif
            <input type="text" id="{{ $id }}" name="{{ $id }}" 
                   @if(!$optional) required @endif
                   placeholder="Deine Antwort..."
                   maxlength="{{ $maxlength ?? 255 }}"
                   class="input input-bordered w-full @error($id) input-error @enderror">
        </div>
        @error($id) <span class="label-text-alt text-error mt-1">{{ $message }}</span> @enderror

    @elseif ($type === 'textarea')
        <div class="relative">
            <textarea id="{{ $id }}" name="{{ $id }}" 
                     @if(!$optional) required @endif
                     rows="4" maxlength="{{ $maxlength ?? 1000 }}"
                     placeholder="Beschreibe hier deine Erfahrungen, Links oder Beispiele..."
                     class="textarea textarea-bordered w-full h-32 @error($id) textarea-error @enderror"></textarea>
            <div class="absolute bottom-2 right-2 text-xs opacity-60">
                <span id="{{ $id }}-counter">0</span>/{{ $maxlength ?? 1000 }}
            </div>
        </div>
        @error($id) <span class="label-text-alt text-error mt-1">{{ $message }}</span> @enderror
        <script>
            document.getElementById('{{ $id }}').addEventListener('input', function() {
                document.getElementById('{{ $id }}-counter').textContent = this.value.length;
            });
        </script>

    @elseif ($type === 'number')
        <div class="input-group">
            @if(str_contains($label, 'RAM'))
                <span class="btn btn-square btn-ghost">
                    <x-heroicon-o-cpu-chip class="h-5 w-5" />
                </span>
            @else
                <span class="btn btn-square btn-ghost">
                    <x-heroicon-o-calculator class="h-5 w-5" />
                </span>
            @endif
            <input type="number" id="{{ $id }}" name="{{ $id }}" 
                   @if(!$optional) required @endif
                   min="{{ $min ?? 0 }}" max="{{ $max ?? 256 }}"
                   placeholder="z.B. {{ str_contains($label, 'RAM') ? '16' : '0' }}"
                   class="input input-bordered w-full @error($id) input-error @enderror">
            @if(str_contains($label, 'RAM'))
                <span class="btn btn-ghost">GB</span>
            @endif
        </div>
        @if(str_contains($label, 'RAM'))
            <div class="mt-1 text-xs text-base-content/70">
                Empfohlen: mindestens 4 GB für grundlegende Funktionen, 8 GB oder mehr für flüssiges Arbeiten
            </div>
        @endif
        @error($id) <span class="label-text-alt text-error mt-1">{{ $message }}</span> @enderror
    @endif
</div>

<script>
    function handleOtherOption(selectElement, id) {
        var otherInput = document.getElementById(id + '-other');
        if (selectElement.value === 'other') {
            if (otherInput.style.display === 'none') {
                otherInput.style.display = 'block';
                // Sanfte Einblendanimation
                otherInput.classList.add('animate-fade-in');
                // Fokus auf das Eingabefeld setzen
                setTimeout(() => {
                    document.getElementById(id + '_other').focus();
                }, 100);
            }
        } else {
            otherInput.style.display = 'none';
        }
    }
</script>

<style>
    .animate-fade-in {
        animation: fadeIn 0.3s ease-in-out;
    }
    
    @keyframes fadeIn {
        from { opacity: 0; transform: translateY(-10px); }
        to { opacity: 1; transform: translateY(0); }
    }
</style>
