@props([
    'status' => 'default',
    'size' => 'md', // xs, sm, md, lg
    'variant' => 'default', // default, outlined, filled
    'icon' => null,
    'pulse' => false
])

@php
$baseClasses = 'inline-flex items-center justify-center font-medium rounded-full transition-all duration-300 ease-out';

$sizeClasses = match($size) {
    'xs' => 'px-2 py-0.5 text-xs gap-1',
    'sm' => 'px-2.5 py-1 text-xs gap-1.5',
    'md' => 'px-3 py-1.5 text-sm gap-2',
    'lg' => 'px-4 py-2 text-base gap-2.5',
    default => 'px-3 py-1.5 text-sm gap-2'
};

$statusConfig = match($status) {
    'open' => [
        'bg' => 'bg-amber-500/20 text-amber-300 border-amber-500/30',
        'outlined' => 'border-2 border-amber-500 text-amber-500 bg-transparent',
        'filled' => 'bg-amber-500 text-white',
        'icon' => '<svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path></svg>'
    ],
    'in_progress' => [
        'bg' => 'bg-blue-500/20 text-blue-300 border-blue-500/30',
        'outlined' => 'border-2 border-blue-500 text-blue-500 bg-transparent',
        'filled' => 'bg-blue-500 text-white',
        'icon' => '<svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path></svg>'
    ],
    'closed' => [
        'bg' => 'bg-green-500/20 text-green-300 border-green-500/30',
        'outlined' => 'border-2 border-green-500 text-green-500 bg-transparent',
        'filled' => 'bg-green-500 text-white',
        'icon' => '<svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path></svg>'
    ],
    'pending' => [
        'bg' => 'bg-gray-500/20 text-gray-300 border-gray-500/30',
        'outlined' => 'border-2 border-gray-500 text-gray-500 bg-transparent',
        'filled' => 'bg-gray-500 text-white',
        'icon' => '<svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path></svg>'
    ],
    'urgent' => [
        'bg' => 'bg-red-500/20 text-red-300 border-red-500/30',
        'outlined' => 'border-2 border-red-500 text-red-500 bg-transparent',
        'filled' => 'bg-red-500 text-white',
        'icon' => '<svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"></path></svg>'
    ],
    default => [
        'bg' => 'bg-base-300/20 text-base-content border-base-300/30',
        'outlined' => 'border-2 border-base-300 text-base-content bg-transparent',
        'filled' => 'bg-base-300 text-base-content',
        'icon' => '<svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path></svg>'
    ]
};

$variantClasses = match($variant) {
    'outlined' => $statusConfig['outlined'],
    'filled' => $statusConfig['filled'],
    default => $statusConfig['bg']
};

$pulseClasses = $pulse ? 'animate-pulse' : '';

$classes = implode(' ', array_filter([
    $baseClasses,
    $sizeClasses,
    $variantClasses,
    $pulseClasses
]));

$displayIcon = $icon ?: $statusConfig['icon'];
@endphp

<span {{ $attributes->merge(['class' => $classes]) }}>
    @if($displayIcon)
        {!! $displayIcon !!}
    @endif
    {{ $slot }}
</span>

<style>
    /* Theme-specific adjustments */
    [data-theme="minewache-light"] .modern-status-badge {
        filter: brightness(0.9);
    }
    
    [data-theme="minewache-high-contrast"] .modern-status-badge {
        border-width: 2px !important;
        font-weight: bold;
    }
    
    [data-theme="minewache-colorful"] .modern-status-badge {
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    }
</style>
