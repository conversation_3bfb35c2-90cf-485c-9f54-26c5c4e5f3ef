<?php

namespace App\Events;

use App\Models\Ticket;
use App\Models\User; // User who changed the status
use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

class TicketStatusChangedByWebApp
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    public Ticket $ticket;
    public string $oldStatus;
    public string $newStatus;
    public ?User $actingUser; // User who made the change in the web app

    /**
     * Create a new event instance.
     *
     * @param Ticket $ticket The ticket whose status changed.
     * @param string $oldStatus The previous status.
     * @param string $newStatus The new status.
     * @param User|null $actingUser The user who initiated the change.
     */
    public function __construct(Ticket $ticket, string $oldStatus, string $newStatus, ?User $actingUser)
    {
        $this->ticket = $ticket;
        $this->oldStatus = $oldStatus;
        $this->newStatus = $newStatus;
        $this->actingUser = $actingUser;
    }
}
