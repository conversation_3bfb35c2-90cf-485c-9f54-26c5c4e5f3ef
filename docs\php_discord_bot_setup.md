# PHP Discord Bot Setup Guide

This document provides instructions for setting up and running the PHP Discord Bot for the Minewache website.

## Overview

The Minewache Discord Bot has been rewritten in PHP and integrated directly into the Laravel application. This integration eliminates the need for a separate Node.js bot and simplifies the architecture by allowing direct access to Laravel services, models, and configurations.

## Prerequisites

- PHP 8.2 or higher
- Laravel 10+
- Composer
- A valid Discord Bot Token with appropriate permissions
- For production: Supervisor or similar process manager

## Configuration

1. Set the following environment variables in your `.env` file:

```
# Discord Bot Configuration
DISCORD_BOT_TOKEN=your_discord_bot_token_here
LARASCORD_GUILD_ID=your_discord_guild_id_here
DISCORD_TICKET_CATEGORY_ID=your_discord_ticket_category_id_here
DISCORD_SUPPORT_ROLE_IDS=role_id_1,role_id_2,role_id_3
DISCORD_BOT_ACTIVITY_NAME="Minewache"
DISCORD_BOT_ACTIVITY_TYPE="PLAYING"
DISCORD_ENABLED=true

# Gemini AI Integration (optional, for ticket AI responses)
GEMINI_API_KEY=your_gemini_api_key_here
GEMINI_ENABLED=true
GEMINI_SYSTEM_USER_ID=ai_system_user_id
```

2. Make sure the Discord bot has the following permissions in your Discord server:
   - Read Messages/View Channels
   - Send Messages
   - Manage Channels
   - Manage Roles
   - Read Message History
   - Embed Links
   - Attach Files

## Running the Bot

### Development Environment

To start the bot in development:

```bash
# Windows
start-discord.bat

# Linux/macOS
php artisan minewache:run-discord-bot
```

To stop the bot in development:

```bash
# Windows
stop-discord.bat

# Linux/macOS
# Use Ctrl+C in the terminal where the bot is running
```

### Production Environment

For production, it's recommended to use Supervisor to manage the bot process:

1. Install Supervisor if not already installed:
   ```bash
   sudo apt install supervisor
   ```

2. Copy the provided Supervisor configuration file:
   ```bash
   sudo cp minewache-discord-bot.supervisor.conf /etc/supervisor/conf.d/
   ```

3. Edit the configuration file to update paths and user:
   ```bash
   sudo nano /etc/supervisor/conf.d/minewache-discord-bot.supervisor.conf
   ```

4. Update the following lines with your actual paths and user:
   ```
   command=php /path/to/your/laravel/artisan minewache:run-discord-bot
   user=www-data
   stdout_logfile=/path/to/your/laravel/storage/logs/discord-bot-supervisor.log
   stderr_logfile=/path/to/your/laravel/storage/logs/discord-bot-supervisor.error.log
   ```

5. Reload Supervisor and start the bot:
   ```bash
   sudo supervisorctl reread
   sudo supervisorctl update
   sudo supervisorctl start minewache-discord-bot
   ```

6. To check the status of the bot:
   ```bash
   sudo supervisorctl status minewache-discord-bot
   ```

7. To stop the bot:
   ```bash
   sudo supervisorctl stop minewache-discord-bot
   ```

8. To restart the bot:
   ```bash
   sudo supervisorctl restart minewache-discord-bot
   ```

## Troubleshooting

### Bot Not Connecting

1. Check that your Discord Bot Token is correct
2. Ensure the bot has been added to your Discord server with the correct permissions
3. Check the logs for any error messages:
   - Development: Check the console output
   - Production: Check the Supervisor logs

### Permission Issues

1. Ensure the bot has the necessary permissions in your Discord server
2. Check that the role mappings in `PermissionSyncService` match your Discord server's roles

### Ticket System Issues

1. Ensure the ticket category ID is correctly set in your `.env` file
2. Check that the support role IDs are correctly set in your `.env` file
3. Verify that the bot has permission to create and manage channels in the ticket category

## AI Integration

The PHP Discord Bot includes integration with Google's Gemini AI for ticket responses:

1. When a user sends a message in a ticket channel, the bot can generate an AI response if the user has given consent
2. The AI response is generated using the `AITicketService` and sent back to the Discord channel
3. Users can give consent to AI responses using the `/ai-consent` command in Discord

To enable AI integration:

1. Set up a Gemini API key at [Google AI Studio](https://ai.google.dev/)
2. Configure the Gemini environment variables in your `.env` file:
   ```
   GEMINI_API_KEY=your_gemini_api_key_here
   GEMINI_ENABLED=true
   GEMINI_SYSTEM_USER_ID=ai_system_user_id
   ```
3. Create a system user in your Laravel application to represent the AI and set its ID in `GEMINI_SYSTEM_USER_ID`

## Architecture

The PHP Discord Bot is implemented using the following components:

1. `RunMinewacheDiscordBot` - The main Artisan command that runs the bot
2. `DiscordRoleManagementService` - Handles role management and synchronization
3. `DiscordTicketChannelService` - Manages ticket channels
4. `DiscordMessagingService` - Handles sending messages to Discord
5. `PermissionSyncService` - Synchronizes Discord roles with Laravel permissions
6. `SyncUserPermissions` - Listener for the `UserRolesChanged` event
7. `AITicketService` - Generates AI responses for tickets using Gemini

The bot uses the `team-reflex/discord-php` library to interact with the Discord API.

## Event-Driven Architecture

The bot uses an event-driven architecture to communicate with the Laravel application:

1. When a user's roles change in Discord, the bot dispatches a `UserRolesChanged` event
2. The `SyncUserPermissions` listener handles this event and updates the user's permissions in Laravel
3. When a ticket is replied to in Laravel, a `TicketRepliedByUser` event is dispatched
4. The `SendTicketReplyToDiscord` listener handles this event and sends the reply to Discord

This event-driven approach eliminates the need for HTTP API calls between the bot and the Laravel application, making the integration more efficient and reliable.
