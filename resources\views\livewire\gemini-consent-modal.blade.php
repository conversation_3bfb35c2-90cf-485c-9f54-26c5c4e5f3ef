<div>
    <!-- Gemini AI Consent Modal -->
    <div x-data="{ show: @entangle('showModal') }"
         x-show="show"
         x-cloak
         class="fixed inset-0 z-50 overflow-y-auto"
         x-transition:enter="transition ease-out duration-300"
         x-transition:enter-start="opacity-0"
         x-transition:enter-end="opacity-100"
         x-transition:leave="transition ease-in duration-200"
         x-transition:leave-start="opacity-100"
         x-transition:leave-end="opacity-0">

        <!-- Backdrop -->
        <div class="fixed inset-0 bg-black bg-opacity-50 transition-opacity"></div>

        <!-- Modal Content -->
        <div class="flex items-center justify-center min-h-screen p-4">
            <div class="relative bg-slate-800 rounded-2xl max-w-md w-full mx-auto shadow-xl overflow-hidden"
                 x-transition:enter="transition ease-out duration-300"
                 x-transition:enter-start="opacity-0 transform scale-95"
                 x-transition:enter-end="opacity-100 transform scale-100"
                 x-transition:leave="transition ease-in duration-200"
                 x-transition:leave-start="opacity-100 transform scale-100"
                 x-transition:leave-end="opacity-0 transform scale-95">

                <!-- Modal Header -->
                <div class="bg-slate-700 px-6 py-4 flex items-center justify-between">
                    <h3 class="text-lg font-medium text-white flex items-center gap-2">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-blue-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z" />
                        </svg>
                        {{ __('tickets.gemini_consent_title') }}
                    </h3>
                    <button @click="show = false" class="text-gray-400 hover:text-white">
                        <svg class="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                        </svg>
                    </button>
                </div>

                <!-- Modal Body -->
                <div class="px-6 py-4">
                    <div class="flex items-start mb-4">
                        <div class="flex-shrink-0 mr-4">
                            <div class="h-12 w-12 rounded-full bg-blue-100 flex items-center justify-center">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-8 w-8 text-blue-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z" />
                                </svg>
                            </div>
                        </div>
                        <div>
                            <h4 class="text-lg font-semibold text-white mb-1">{{ __('tickets.gemini_consent_title') }}</h4>
                            <p class="text-gray-300 text-sm">
                                {{ __('tickets.gemini_consent_description') }}
                            </p>
                        </div>
                    </div>

                    <div class="bg-slate-700/50 rounded-lg p-4 mb-4 text-sm text-gray-300">
                        <p class="mb-2">{{ __('tickets.gemini_consent_explanation') }}</p>
                        <p class="mt-4 text-xs text-gray-400">
                            {{ __('tickets.gemini_consent_data_usage') }}
                        </p>
                    </div>
                </div>

                <!-- Modal Footer -->
                <div class="bg-slate-700/30 px-6 py-4 flex justify-between">
                    <button wire:click="denyConsent" class="px-4 py-2 bg-slate-600 text-white rounded-lg hover:bg-slate-500 transition-colors">
                        {{ __('tickets.gemini_consent_decline') }}
                    </button>
                    <button wire:click="giveConsent" class="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-500 transition-colors">
                        {{ __('tickets.gemini_consent_accept') }}
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>
