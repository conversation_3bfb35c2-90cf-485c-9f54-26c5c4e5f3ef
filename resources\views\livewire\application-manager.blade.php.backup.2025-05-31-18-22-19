<div>
    <div class="container mx-auto px-4 py-6 bg-base-200">

        <!-- Erfolgsmeldung oder Fehlermeldung anzeigen -->
        @if(session()->has('message'))
            <div class="alert alert-success mb-6 shadow-lg">
                <div>
                    <x-heroicon-o-check-circle class="w-6 h-6"/>
                    <span>{{ session('message') }}</span>
                </div>
            </div>
        @endif

        @if(session()->has('error'))
            <div class="alert alert-error mb-6 shadow-lg">
                <div>
                    <x-heroicon-o-exclamation-triangle class="w-6 h-6"/>
                    <span>{{ session('error') }}</span>
                </div>
            </div>
        @endif

        <!-- Dynamischer Inhalt: Liste oder Detail/Edit/Response Ansicht mit Lazy Loading -->
        @if($viewMode === 'list')
            @livewire('application-list', [], ['lazy' => true])
        @elseif($viewMode === 'detail')
            @livewire('application-detail', ['applicationId' => $applicationId], ['lazy' => true])
        @elseif($viewMode === 'edit')
            @livewire('application-edit', ['applicationId' => $applicationId], ['lazy' => true])
        @elseif($viewMode === 'response')
            @livewire('response-generator', ['applicationId' => $applicationId], ['lazy' => true])
        @endif
    </div>

    <!-- JavaScript wird über Livewire-Hooks geladen -->

    <!-- Modal für die Auswahl der Bearbeitungsoption -->
    <dialog id="editability-modal" class="modal">
        <div class="modal-box">
            <h3 class="font-bold text-lg mb-4">{{ __('admin.enable_editing') }}</h3>
            <p class="mb-4">{{ __('admin.select_editing_mode') }}</p>

            <div class="flex flex-col gap-4">
                <button wire:click="enableEditability('edit')" class="btn btn-primary">
                    <x-heroicon-o-pencil-square class="w-5 h-5 mr-2"/>
                    {{ __('admin.edit_directly') }}
                </button>
                <button wire:click="enableEditability('revision')" class="btn btn-secondary">
                    <x-heroicon-o-document-duplicate class="w-5 h-5 mr-2"/>
                    {{ __('admin.create_revision') }}
                </button>
            </div>

            <div class="modal-action">
                <form method="dialog">
                    <button class="btn">{{ __('admin.cancel') }}</button>
                </form>
            </div>
        </div>
        <form method="dialog" class="modal-backdrop">
            <button>{{ __('admin.cancel') }}</button>
        </form>
    </dialog>

    <!-- Modal events are handled via Livewire hooks -->
</div>
