<div class="max-w-4xl mx-auto py-8 px-4 sm:px-6 lg:px-8">
    <div class="bg-base-200 shadow-lg rounded-lg overflow-hidden mb-6">
        <!-- Progress header -->
        <div class="bg-base-300 px-4 py-5 border-b border-base-content/10 sm:px-6">
            <div class="flex items-center justify-between">
                <h3 class="text-lg leading-6 font-medium">
                    {{ __('application.application_form') }}
                </h3>
                <div class="flex items-center space-x-2">
                    @foreach(range(1, 4) as $step)
                        <div @class([
                            'w-12 h-12 rounded-full flex items-center justify-center text-sm font-medium',
                            'bg-primary text-primary-content' => $currentStep >= $step,
                            'bg-base-100 text-base-content/80' => $currentStep < $step
                        ])>
                            @if($currentStep > $step)
                                <x-heroicon-o-check class="h-6 w-6" />
                            @else
                                {{ $step }}
                            @endif
                        </div>
                        @if($step < 4)
                            <div class="w-8 h-0.5 @if($currentStep > $step) bg-primary @else bg-base-100 @endif"></div>
                        @endif
                    @endforeach
                </div>
            </div>
            <div class="mt-4 flex justify-between text-sm">
                <div @class(['font-medium', 'text-primary' => $currentStep == 1])>{{ __('application.personal_data') }}</div>
                <div @class(['font-medium', 'text-primary' => $currentStep == 2])>{{ __('application.role_specific') }}</div>
                <div @class(['font-medium', 'text-primary' => $currentStep == 3])>{{ __('application.about_you') }}</div>
                <div @class(['font-medium', 'text-primary' => $currentStep == 4])>{{ __('application.review') }}</div>
            </div>
        </div>

        <!-- Step content -->
        <div class="px-4 py-5 sm:p-6 animate-fade-in">
            <!-- Step 1: Personal Information -->
            @if($currentStep == 1)
                <div class="space-y-6 animate-slide-up">
                    <h2 class="text-xl font-semibold mb-4">{{ __('application.personal_data') }}</h2>

                    <!-- GDPR Data Notice -->
                    <x-gdpr-data-notice type="application" class="mb-6" />

                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <!-- Name -->
                        <div>
                            <label for="name" class="block text-sm font-medium">{{ __('application.name') }}</label>
                            <input type="text" id="name" wire:model.live="name" class="mt-1 input input-bordered w-full" placeholder="{{ __('application.name_placeholder') }}">
                            @error('name') <span class="text-error text-sm">{{ $message }}</span> @enderror
                            <x-gdpr-field-explanation field="name" />
                        </div>

                        <!-- Age -->
                        <div>
                            <label for="age" class="block text-sm font-medium">{{ __('application.age') }}</label>
                            <input type="number" id="age" wire:model.live="age" class="mt-1 input input-bordered w-full" min="12" max="120" placeholder="{{ __('application.age_placeholder') }}">
                            @error('age') <span class="text-error text-sm">{{ $message }}</span> @enderror
                            <x-gdpr-field-explanation field="age" />
                        </div>
                    </div>

                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <!-- Gender -->
                        <div>
                            <label for="gender" class="block text-sm font-medium">{{ __('application.gender') }}</label>
                            <select id="gender" wire:model.live="gender" class="mt-1 select select-bordered w-full">
                                <option value="">{{ __('application.gender_select') }}</option>
                                <option value="male">{{ __('application.gender_male') }}</option>
                                <option value="female">{{ __('application.gender_female') }}</option>
                                <option value="diverse">{{ __('application.gender_diverse') }}</option>
                                <option value="prefer_not_to_say">{{ __('application.gender_no_info') }}</option>
                            </select>
                            @error('gender') <span class="text-error text-sm">{{ $message }}</span> @enderror
                        </div>

                        <!-- Pronouns -->
                        <div>
                            <label for="pronouns" class="block text-sm font-medium">{{ __('application.pronouns') }} ({{ __('application.optional') }})</label>
                            <input type="text" id="pronouns" wire:model.live="pronouns" class="mt-1 input input-bordered w-full" placeholder="{{ __('application.pronouns_placeholder') }}">
                        </div>
                    </div>

                    <!-- Professions -->
                    <div>
                        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                            {{ __('application.professions') }} ({{ __('application.professions_select_at_least_one') }})
                        </label>
                        <div class="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-3">
                            @foreach($professionOptions as $value => $label)
                                <div class="relative">
                                    <input type="checkbox" id="prof-{{ $value }}"
                                           wire:model="professions"
                                           value="{{ $value }}"
                                           class="peer sr-only">
                                    <label for="prof-{{ $value }}"
                                           class="flex items-center p-3 w-full text-gray-500 bg-white border-2 border-gray-200 rounded-lg cursor-pointer
                       peer-checked:border-blue-600 peer-checked:text-blue-600 hover:text-gray-600 hover:bg-gray-50
                       dark:hover:text-gray-300 dark:border-gray-700 dark:peer-checked:text-blue-500
                       dark:text-gray-400 dark:bg-gray-800 dark:hover:bg-gray-700">
                                        <div class="block">
                                            <div class="w-full text-sm font-semibold">{{ $label }}</div>
                                        </div>
                                        <svg class="w-5 h-5 ms-auto hidden peer-checked:block" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="currentColor" viewBox="0 0 20 20">
                                            <path fill="currentColor" d="M10 .5a9.5 9.5 0 1 0 9.5 9.5A9.51 9.51 0 0 0 10 .5Zm3.707 8.207-4 4a1 1 0 0 1-1.414 0l-2-2a1 1 0 0 1 1.414-1.414L9 10.586l3.293-3.293a1 1 0 0 1 1.414 1.414Z"/>
                                        </svg>
                                    </label>
                                </div>
                            @endforeach
                        </div>
                        @error('professions')
                        <span class="text-red-500 text-sm block mt-1">{{ $message }}</span>
                        @enderror
                        @if(session()->has('professions_error'))
                            <span class="text-red-500 text-sm block mt-1">{{ session('professions_error') }}</span>
                        @endif
                    </div>

                    <!-- Other Profession -->
                    @if(in_array('other', $professions ?? []))
                        <div class="animate-fade-in">
                            <label for="otherProfession" class="block text-sm font-medium">{{ __('application.other_profession') }}</label>
                            <input type="text" id="otherProfession" wire:model.live="otherProfession" class="mt-1 input input-bordered w-full" placeholder="{{ __('application.other_profession_placeholder') }}">
                            @error('otherProfession') <span class="text-error text-sm">{{ $message }}</span> @enderror
                        </div>
                    @endif
                </div>
            @endif

            <!-- Step 2: Role-specific Information -->
            @if($currentStep == 2)
                <div class="space-y-6 animate-slide-up">
                    <h2 class="text-xl font-semibold mb-4">{{ __('application.role_specific') }}</h2>

                    <!-- Notice if no professions selected -->
                    @if(empty($professions))
                        <div class="alert alert-warning">
                            <x-heroicon-o-exclamation-triangle class="h-6 w-6" />
                            <span>{{ __('application.please_select_at_least_one_profession') }}</span>
                        </div>
                    @endif

                    <!-- Different fields based on selected professions -->
                    @if(in_array('actor', $professions ?? []) || in_array('voice_actor', $professions ?? []))
                        <div class="card bg-base-300/50 p-4 mb-6 animate-fade-in">
                            <h3 class="font-medium mb-3">{{ __('application.acting_voice_acting') }}:</h3>
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                <div>
                                    <label for="voice_type" class="block text-sm font-medium">{{ __('application.voice_type') }}</label>
                                    <select id="voice_type" wire:model.live="voice_type" class="mt-1 select select-bordered w-full">
                                        <option value="">{{ __('application.gender_select') }}</option>
                                        <option value="deep">{{ __('application.voice_type_deep') }}</option>
                                        <option value="medium">{{ __('application.voice_type_medium') }}</option>
                                        <option value="high">{{ __('application.voice_type_high') }}</option>
                                    </select>
                                </div>
                                <div>
                                    <label for="microphone" class="block text-sm font-medium">{{ __('application.microphone') }}</label>
                                    <input type="text" id="microphone" wire:model.live="microphone" class="mt-1 input input-bordered w-full" placeholder="{{ __('application.microphone_placeholder') }}">
                                </div>
                            </div>
                        </div>
                    @endif

                    @php
                        $pcRelevantProfessions = ['builder', 'cameraman', 'actor', 'actor_no_voice', 'designer', 'voice_actor', 'cutter', 'developer'];
                        $minecraftRelevantProfessions = ['builder', 'cameraman', 'actor', 'actor_no_voice'];
                        $gpuRelevantProfessions = ['builder', 'cameraman', 'actor', 'actor_no_voice', 'cutter'];
                    @endphp

                    @if(array_intersect($pcRelevantProfessions, $professions ?? []))
                        <div class="card bg-base-300/50 p-4 mb-6 animate-fade-in">
                            <h3 class="font-medium mb-3">{{ __('application.builder_cameraman') }}:</h3>

                            <!-- PC Checkbox -->
                            <div class="mb-3">
                                <div class="relative">
                                    <input type="checkbox" id="has_pc" wire:model="has_pc" class="peer sr-only">
                                    <label for="has_pc" class="flex items-center p-3 bg-white border-2 border-gray-200 rounded-lg cursor-pointer
               peer-checked:border-blue-600 peer-checked:text-blue-600 peer-checked:bg-blue-50 hover:text-gray-600 hover:bg-gray-50
               dark:hover:text-gray-300 dark:border-gray-700 dark:peer-checked:text-blue-500 dark:peer-checked:bg-blue-900/20
               dark:text-gray-400 dark:bg-gray-800 dark:hover:bg-gray-700">
                                        <span class="text-sm font-medium">{{ __('application.Hast du einen PC?') }}</span>
                                    </label>
                                </div>
                            </div>

                            <!-- Minecraft-related fields -->
                            @if(array_intersect($minecraftRelevantProfessions, $professions ?? []))
                                <!-- Modrinth Checkbox -->
                                <div class="mb-3">
                                    <div class="relative">
                                        <input type="checkbox" id="has_curseforge" wire:model="has_curseforge" class="peer sr-only">
                                        <label for="has_curseforge" class="flex items-center p-3 bg-white border-2 border-gray-200 rounded-lg cursor-pointer
                           peer-checked:border-blue-600 peer-checked:text-blue-600 hover:text-gray-600 hover:bg-gray-50
                           dark:hover:text-gray-300 dark:border-gray-700 dark:peer-checked:text-blue-500
                           dark:text-gray-400 dark:bg-gray-800 dark:hover:bg-gray-700">
                                            <span class="text-sm font-medium">{{ __('application.Hast du Modrinth?') }}</span>
                                        </label>
                                    </div>
                                </div>

                                <!-- Minecraft Java Checkbox -->
                                <div class="mb-3">
                                    <div class="relative">
                                        <input type="checkbox" id="has_minecraft_java" wire:model="has_minecraft_java" class="peer sr-only">
                                        <label for="has_minecraft_java" class="flex items-center p-3 bg-white border-2 border-gray-200 rounded-lg cursor-pointer
                           peer-checked:border-blue-600 peer-checked:text-blue-600 hover:text-gray-600 hover:bg-gray-50
                           dark:hover:text-gray-300 dark:border-gray-700 dark:peer-checked:text-blue-500
                           dark:text-gray-400 dark:bg-gray-800 dark:hover:bg-gray-700">
                                            <span class="text-sm font-medium">{{ __('application.Hast du Minecraft Java?') }}</span>
                                        </label>
                                    </div>
                                </div>

                                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                    <div>
                                        <label for="ram" class="block text-sm font-medium">{{ __('application.ram') }}</label>
                                        <input type="text" id="ram" wire:model.live="ram" class="mt-1 input input-bordered w-full" placeholder="{{ __('application.ram_placeholder') }}">
                                        <x-gdpr-field-explanation field="ram" />
                                    </div>
                                    <div>
                                        <label for="fps" class="block text-sm font-medium">{{ __('application.fps') }}</label>
                                        <select id="fps" wire:model.live="fps" class="mt-1 select select-bordered w-full">
                                            <option value="">{{ __('application.gender_select') }}</option>
                                            <option value="<10">{{ __('application.<10') }}</option>
                                            <option value="10-">{{ __('application.10') }}</option>
                                            <option value="20">{{ __('application.20') }}</option>
                                            <option value="30">{{ __('application.30') }}</option>
                                            <option value="40">{{ __('application.40') }}</option>
                                            <option value="50">{{ __('application.50') }}</option>
                                            <option value="60<">{{ __('application.>60') }}</option>
                                        </select>
                                    </div>
                                </div>
                            @endif

                            <!-- GPU-related field -->
                            @if(array_intersect($gpuRelevantProfessions, $professions ?? []))
                                <div>
                                    <label for="gpu" class="block text-sm font-medium">{{ __('application.gpu') }}</label>
                                    <input type="text" id="gpu" wire:model.live="gpu" class="mt-1 input input-bordered w-full" placeholder="{{ __('application.gpu_placeholder') }}">
                                    <x-gdpr-field-explanation field="gpu" />
                                </div>
                            @endif
                        </div>
                    @endif

                    @if(in_array('designer', $professions ?? []))
                        <div class="card bg-base-300/50 p-4 mb-6 animate-fade-in">
                            <h3 class="font-medium mb-3">{{ __('application.designer') }}:</h3>
                            <div class="grid grid-cols-1 gap-4">
                                <div>
                                    <label for="program" class="block text-sm font-medium">{{ __('application.design_programs') }}</label>
                                    <input type="text" id="program" wire:model.live="program" class="mt-1 input input-bordered w-full" placeholder="{{ __('application.design_programs_placeholder') }}">
                                </div>
                                <div>
                                    <label for="design_style" class="block text-sm font-medium">{{ __('application.design_style') }}</label>
                                    <input type="text" id="design_style" wire:model.live="design_style" class="mt-1 input input-bordered w-full" placeholder="{{ __('application.design_style_placeholder') }}">
                                </div>
                                <div>
                                    <label for="favorite_design" class="block text-sm font-medium">{{ __('application.favorite_design') }}</label>
                                    <input type="text" id="favorite_design" wire:model.live="favorite_design" class="mt-1 input input-bordered w-full" placeholder="{{ __('application.favorite_design_placeholder') }}">
                                </div>
                            </div>
                        </div>
                    @endif

                    @if(in_array('developer', $professions ?? []))
                        <div class="card bg-base-300/50 p-4 mb-6 animate-fade-in">
                            <h3 class="font-medium mb-3">{{ __('application.developer') }}:</h3>
                            <div class="grid grid-cols-1 gap-4">
                                <div>
                                    <label for="languages" class="block text-sm font-medium">{{ __('application.programming_languages') }}</label>
                                    <input type="text" id="languages" wire:model.live="languages" class="mt-1 input input-bordered w-full" placeholder="{{ __('application.programming_languages_placeholder') }}">
                                </div>
                                <div>
                                    <label for="ide" class="block text-sm font-medium">{{ __('application.preferred_ide') }}</label>
                                    <input type="text" id="ide" wire:model.live="ide" class="mt-1 input input-bordered w-full" placeholder="{{ __('application.preferred_ide_placeholder') }}">
                                </div>
                            </div>
                        </div>
                    @endif

                    @if(in_array('modeler', $professions ?? []))
                        <div class="card bg-base-300/50 p-4 mb-6 animate-fade-in">
                            <h3 class="font-medium mb-3">{{ __('application.modeler') }}:</h3>
                            <div class="grid grid-cols-1 gap-4">
                                <div>
                                    <label for="design_program" class="block text-sm font-medium">{{ __('application.3D Model Software') }}</label>
                                    <input type="text" id="daw" wire:model.live="daw" class="mt-1 input input-bordered w-full" placeholder="{{ __('application.daw_placeholder') }}">
                                </div>
                            </div>
                        </div>
                    @endif

                    @if(in_array('music_producer', $professions ?? []))
                        <div class="card bg-base-300/50 p-4 mb-6 animate-fade-in">
                            <h3 class="font-medium mb-3">{{ __('application.music_producer') }}:</h3>
                            <div class="grid grid-cols-1 gap-4">
                                <div>
                                    <label for="daw" class="block text-sm font-medium">{{ __('application.daw') }}</label>
                                    <input type="text" id="daw" wire:model.live="daw" class="mt-1 input input-bordered w-full" placeholder="{{ __('application.daw_placeholder') }}">
                                </div>
                            </div>
                        </div>
                    @endif

                    @if(in_array('other', $professions ?? []))
                        <div class="card bg-base-300/50 p-4 mb-6 animate-fade-in">
                            <h3 class="font-medium mb-3">{{ __('application.other_profession') }}:</h3>
                            <div class="grid grid-cols-1 gap-4">
                                <div>
                                    <label for="otherProfession" class="block text-sm font-medium">{{ __('application.other_profession') }}</label>
                                    <input type="text" id="otherProfession" wire:model.live="otherProfession" class="mt-1 input input-bordered w-full" placeholder="{{ __('application.other_profession_placeholder') }}">
                                </div>
                            </div>
                        </div>
                    @endif

                    <div>
                        <label for="portfolio" class="block text-sm font-medium">{{ __('application.portfolio') }}</label>
                        <textarea id="portfolio" wire:model.live="portfolio" rows="3" class="mt-1 textarea textarea-bordered w-full" placeholder="{{ __('application.portfolio_placeholder') }}"></textarea>
                    </div>
                    <div>
                        <label for="desired_role" class="block text-sm font-medium">{{ __('application.desired_role') }}</label>
                        <textarea id="desired_role" wire:model.live="desired_role" rows="3" class="mt-1 textarea textarea-bordered w-full" placeholder="{{ __('application.desired_role_placeholder') }}"></textarea>
                    </div>
                </div>
            @endif

            <!-- Step 3: About You -->
            @if($currentStep == 3)
                <div class="space-y-6 animate-slide-up">
                    <h2 class="text-xl font-semibold mb-4">{{ __('application.about_you') }}</h2>

                    <div>
                        <label for="about_you" class="block text-sm font-medium">{{ __('application.about_you') }}</label>
                        <textarea id="about_you" wire:model.live="about_you" rows="5" class="mt-1 textarea textarea-bordered w-full" placeholder="{{ __('application.about_you_placeholder') }}"></textarea>
                        @error('about_you') <span class="text-error text-sm">{{ $message }}</span> @enderror
                        <div class="text-xs text-right mt-1">{{ strlen($about_you ?? '') }}/50+ {{ __('application.characters') }}</div>
                    </div>

                    <div>
                        <label for="strengths_weaknesses" class="block text-sm font-medium">{{ __('application.strengths_weaknesses') }}</label>
                        <textarea id="strengths_weaknesses" wire:model.live="strengths_weaknesses" rows="5" class="mt-1 textarea textarea-bordered w-full" placeholder="{{ __('application.strengths_weaknesses_placeholder') }}"></textarea>
                        @error('strengths_weaknesses') <span class="text-error text-sm">{{ $message }}</span> @enderror
                        <div class="text-xs text-right mt-1">{{ strlen($strengths_weaknesses ?? '') }}/50+ {{ __('application.characters') }}</div>
                    </div>

                    <div>
                        <label for="final_words" class="block text-sm font-medium">{{ __('application.final_words') }} ({{ __('application.optional') }})</label>
                        <textarea id="final_words" wire:model.live="final_words" rows="3" class="mt-1 textarea textarea-bordered w-full" placeholder="{{ __('application.final_words_placeholder') }}"></textarea>
                    </div>
                </div>
            @endif

            <!-- Step 4: Review -->
            @if($currentStep == 4)
                <div class="space-y-6 animate-slide-up">
                    <h2 class="text-xl font-semibold mb-4">{{ __('application.review_submit') }}</h2>

                    <div class="space-y-4">
                        <!-- Personal Information Review -->
                        <div class="collapse collapse-arrow bg-base-300/50">
                            <input type="checkbox" checked />
                            <div class="collapse-title font-medium">
                                {{ __('application.personal_data') }}
                            </div>
                            <div class="collapse-content">
                                <dl class="grid grid-cols-1 sm:grid-cols-2 gap-x-4 gap-y-2 text-sm">
                                    <div>
                                        <dt class="font-medium">{{ __('application.name') }}:</dt>
                                        <dd>{{ $name }}</dd>
                                    </div>
                                    <div>
                                        <dt class="font-medium">{{ __('application.age') }}:</dt>
                                        <dd>{{ $age }}</dd>
                                    </div>
                                    <div>
                                        <dt class="font-medium">{{ __('application.gender') }}:</dt>
                                        <dd>{{ match($gender) {
                                        'male' => __('application.gender_male'),
                                        'female' => __('application.gender_female'),
                                        'diverse' => __('application.gender_diverse'),
                                        'prefer_not_to_say' => __('application.gender_no_info'),
                                        default => $gender
                                    } }}</dd>
                                    </div>
                                    <div>
                                        <dt class="font-medium">{{ __('application.pronouns') }}:</dt>
                                        <dd>{{ $pronouns ?: __('application.not_specified') }}</dd>
                                    </div>
                                    <div class="sm:col-span-2">
                                        <dt class="font-medium">{{ __('application.professions') }}:</dt>
                                        <dd>
                                            <div class="flex flex-wrap gap-1 mt-1">
                                                @foreach($professions ?? [] as $profession)
                                                    @if($profession !== 'other')
                                                        <span class="badge badge-primary">{{ $professionOptions[$profession] ?? $profession }}</span>
                                                    @endif
                                                @endforeach

                                                @if(in_array('other', $professions ?? []) && $otherProfession)
                                                    <span class="badge badge-secondary">{{ $otherProfession }}</span>
                                                @endif
                                            </div>
                                        </dd>
                                    </div>
                                </dl>
                            </div>
                        </div>


                        <!-- Role-specific Information Review -->
                        <div class="collapse collapse-arrow bg-base-300/50">
                            <input type="checkbox" checked />
                            <div class="collapse-title font-medium">
                                {{ __('application.role_specific') }}
                            </div>
                            <div class="collapse-content">
                                <dl class="grid grid-cols-1 sm:grid-cols-2 gap-x-4 gap-y-2 text-sm">
                                    @if(!empty($checkboxQuestions))
                                        <div class="collapse collapse-arrow bg-base-300/50">
                                            <input type="checkbox" checked />
                                            <div class="collapse-title font-medium">
                                                {{ __('application.checkbox_questions') }}
                                            </div>
                                            <div class="collapse-content">
                                                <ul class="list-disc list-inside">
                                                    @foreach($checkboxQuestions as $question => $answer)
                                                        <li>
                                                            <strong>{{ $question }}:</strong>
                                                            {{ $answer ? __('application.yes') : __('application.no') }}
                                                        </li>
                                                    @endforeach
                                                </ul>
                                            </div>
                                        </div>
                                    @endif

                                    @if($voice_type)
                                        <div>
                                            <dt class="font-medium">{{ __('application.voice_type') }}:</dt>
                                            <dd>{{ match($voice_type) {
                                        'deep' => __('application.voice_type_deep'),
                                        'medium' => __('application.voice_type_medium'),
                                        'high' => __('application.voice_type_high'),
                                        default => $voice_type
                                    } }}</dd>
                                        </div>
                                    @endif

                                    @if($microphone)
                                        <div>
                                            <dt class="font-medium">{{ __('application.microphone') }}:</dt>
                                            <dd>{{ $microphone }}</dd>
                                        </div>
                                    @endif

                                    @if($ram)
                                        <div>
                                            <dt class="font-medium">{{ __('application.ram') }}:</dt>
                                            <dd>{{ $ram }}</dd>
                                        </div>
                                    @endif

                                    @if($fps)
                                        <div>
                                            <dt class="font-medium">{{ __('application.fps') }}:</dt>
                                            <dd>{{ $fps }}</dd>
                                        </div>
                                    @endif

                                    @if($gpu)
                                        <div>
                                            <dt class="font-medium">{{ __('application.gpu') }}:</dt>
                                            <dd>{{ $gpu }}</dd>
                                        </div>
                                    @endif

                                    @if($program)
                                        <div>
                                            <dt class="font-medium">{{ __('application.design_programs') }}:</dt>
                                            <dd>{{ $program }}</dd>
                                        </div>
                                    @endif

                                    @if($design_style)
                                        <div>
                                            <dt class="font-medium">{{ __('application.design_style') }}:</dt>
                                            <dd>{{ $design_style }}</dd>
                                        </div>
                                    @endif

                                    @if($favorite_design)
                                        <div>
                                            <dt class="font-medium">{{ __('application.favorite_design') }}:</dt>
                                            <dd>{{ $favorite_design }}</dd>
                                        </div>
                                    @endif

                                    @if($portfolio)
                                        <div>
                                            <dt class="font-medium">{{ __('application.portfolio') }}:</dt>
                                            <dd><a href="{{ $portfolio }}" class="link link-primary" target="_blank">{{ $portfolio }}</a></dd>
                                        </div>
                                    @endif

                                    @if($languages)
                                        <div>
                                            <dt class="font-medium">{{ __('application.programming_languages') }}:</dt>
                                            <dd>{{ $languages }}</dd>
                                        </div>
                                    @endif

                                    @if($ide)
                                        <div>
                                            <dt class="font-medium">{{ __('application.preferred_ide') }}:</dt>
                                            <dd>{{ $ide }}</dd>
                                        </div>
                                    @endif

                                    @if($daw)
                                        <div>
                                            <dt class="font-medium">{{ __('application.daw') }}:</dt>
                                            <dd>{{ $daw }}</dd>
                                        </div>
                                    @endif

                                    @if($desired_role)
                                        <div class="sm:col-span-2">
                                            <dt class="font-medium">{{ __('application.desired_role') }}:</dt>
                                            <dd class="whitespace-pre-line">{{ $desired_role }}</dd>
                                        </div>
                                    @endif
                                </dl>
                            </div>
                        </div>

                        <!-- About You Review -->
                        <div class="collapse collapse-arrow bg-base-300/50">
                            <input type="checkbox" checked />
                            <div class="collapse-title font-medium">
                                {{ __('application.about_you') }}
                            </div>
                            <div class="collapse-content">
                                <dl class="grid grid-cols-1 gap-y-4 text-sm">
                                    <div>
                                        <dt class="font-medium">{{ __('application.about_you') }}:</dt>
                                        <dd class="mt-1 whitespace-pre-line">{{ $about_you }}</dd>
                                    </div>

                                    <div>
                                        <dt class="font-medium">{{ __('application.strengths_weaknesses') }}:</dt>
                                        <dd class="mt-1 whitespace-pre-line">{{ $strengths_weaknesses }}</dd>
                                    </div>

                                    @if($final_words)
                                        <div>
                                            <dt class="font-medium">{{ __('application.final_words') }}:</dt>
                                            <dd class="mt-1 whitespace-pre-line">{{ $final_words }}</dd>
                                        </div>
                                    @endif
                                </dl>
                            </div>
                        </div>

                        <!-- Confirmation -->
                        <div class="mt-6 flex items-start">
                            <div class="flex h-5 items-center">
                                <input id="confirmation" wire:model.live="confirmation" type="checkbox" class="checkbox checkbox-primary">
                            </div>
                            <div class="ml-3 text-sm">
                                <label for="confirmation" class="font-medium">
                                    {{ __('application.confirm_correct_information') }}
                                </label>
                                @error('confirmation') <span class="text-error text-sm block">{{ $message }}</span> @enderror
                            </div>
                        </div>
                    </div>
                </div>
            @endif
        </div>

        <!-- Navigation buttons -->
        <div class="px-4 py-3 bg-base-300 sm:px-6 flex @if($currentStep > 1) justify-between @else justify-end @endif">
            @if($currentStep > 1)
                <button type="button" wire:click="previousStep" class="btn btn-outline">
                    <x-heroicon-o-arrow-left class="h-5 w-5 mr-1" />
                    {{ __('application.previous_step') }}
                </button>
            @endif

            @if($currentStep < 4)
                <button type="button" wire:click="nextStep" class="btn btn-primary">
                    {{ __('application.next_step') }}
                    <x-heroicon-o-arrow-right class="h-5 w-5 ml-1" />
                </button>
            @endif

            @if($currentStep == 4)
                <button type="button" wire:click="submitApplication" class="btn btn-primary">
                    {{ __('application.submit_application') }}
                    <x-heroicon-o-check-circle class="h-5 w-5 ml-1" />
                </button>
            @endif
        </div>
    </div>

    <!-- Loading indicator -->
    <div wire:loading wire:target="nextStep, previousStep, submitApplication" class="fixed inset-0 flex items-center justify-center bg-base-100/75 z-50">
        <div class="text-center">
            <span class="loading loading-spinner loading-lg text-primary"></span>
            <p class="mt-2 font-medium">{{ __('application.processing') }}</p>
        </div>
    </div>
</div>
