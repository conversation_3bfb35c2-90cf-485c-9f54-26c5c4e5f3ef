<?php

namespace App\Livewire;

use App\Models\User;
use App\Models\UserDataConsent;
use Illuminate\Support\Facades\Auth;
use Livewire\Component;

class UserDataConsentSettings extends Component
{
    public bool $shareUsername = true;
    public bool $shareApplications = false;
    public bool $shareTickets = false;
    public bool $shareDiscordInfo = false;
    
    public bool $showSuccessMessage = false;
    
    /**
     * Mount the component
     */
    public function mount()
    {
        /** @var User $user */
        $user = Auth::user();
        
        if (!$user) {
            return;
        }
        
        // Erstelle einen Datensatz, wenn keiner existiert
        if (!$user->dataConsent) {
            $user->dataConsent()->create([
                'share_username' => true,
                'share_applications' => false,
                'share_tickets' => false,
                'share_discord_info' => false,
            ]);
            $user->refresh();
        }
        
        // Lade die aktuellen Einstellungen
        $this->shareUsername = $user->dataConsent->share_username;
        $this->shareApplications = $user->dataConsent->share_applications;
        $this->shareTickets = $user->dataConsent->share_tickets;
        $this->shareDiscordInfo = $user->dataConsent->share_discord_info;
    }
    
    /**
     * Speichere die Einstellungen
     */
    public function saveSettings()
    {
        /** @var User $user */
        $user = Auth::user();
        
        if (!$user || !$user->dataConsent) {
            return;
        }
        
        $user->dataConsent->update([
            'share_username' => $this->shareUsername,
            'share_applications' => $this->shareApplications,
            'share_tickets' => $this->shareTickets,
            'share_discord_info' => $this->shareDiscordInfo,
        ]);
        
        $this->showSuccessMessage = true;
        
        // Blende die Erfolgsmeldung nach 3 Sekunden aus
        $this->dispatch('setTimeout', [
            'callback' => '$wire.showSuccessMessage = false',
            'time' => 3000
        ]);
    }
    
    /**
     * Render the component
     */
    public function render()
    {
        return view('livewire.user-data-consent-settings');
    }
}
