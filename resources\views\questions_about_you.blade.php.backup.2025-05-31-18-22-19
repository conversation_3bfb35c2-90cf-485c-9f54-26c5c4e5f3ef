<x-app-layout>
    <x-slot name="header">
        <h2 class="font-display text-xl md:text-2xl text-gray-900 dark:text-white leading-tight animate-slide-down">
            {{ __('Über dich') }}
        </h2>
    </x-slot>

    <div class="py-8 md:py-12 bg-base-200">
        <div class="max-w-2xl mx-auto px-4 sm:px-6 lg:px-8">
            <!-- Verbesserte Fortschrittsanzeige -->
            <div class="mb-8 animate-fade-in-up">
                <div class="w-full mb-4">
                    <div class="flex flex-col space-y-2">
                        <div class="flex justify-between text-xs text-base-content/70">
                            <span>Schritt 3 von 3</span>
                            <span>Fast geschafft!</span>
                        </div>
                        <div class="relative pt-1">
                            <div class="h-2 bg-base-300 rounded-full overflow-hidden">
                                <div class="h-full bg-primary rounded-full w-full transition-all duration-1000"></div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="flex items-center justify-between mt-4 flex-wrap gap-2">
                    <div class="flex items-center gap-2">
                        <div class="w-8 h-8 rounded-full bg-primary flex items-center justify-center">
                            <span class="text-primary-content text-sm font-bold">1</span>
                        </div>
                        <span class="text-sm font-medium opacity-70">Persönliche Daten</span>
                    </div>
                    <div class="flex-grow hidden md:block border-t-2 border-dashed border-primary/30 mx-2"></div>
                    <div class="flex items-center gap-2">
                        <div class="w-8 h-8 rounded-full bg-primary flex items-center justify-center">
                            <span class="text-primary-content text-sm font-bold">2</span>
                        </div>
                        <span class="text-sm font-medium opacity-70">Rollenspezifisch</span>
                    </div>
                    <div class="flex-grow hidden md:block border-t-2 border-dashed border-primary/30 mx-2"></div>
                    <div class="flex items-center gap-2">
                        <div class="w-8 h-8 rounded-full bg-primary flex items-center justify-center">
                            <span class="text-primary-content text-sm font-bold">3</span>
                        </div>
                        <span class="text-sm font-medium">Über Dich</span>
                    </div>
                </div>
    
                <div class="mt-6 px-2">
                    <p class="text-sm text-base-content/80">
                        Im letzten Schritt möchten wir dich besser kennenlernen. Erzähle uns etwas über dich und deine Motivation.
                    </p>
                </div>
            </div>

            <!-- Hauptfragenkarte -->
            <div class="bg-white dark:bg-base-100 rounded-xl shadow-lg overflow-hidden animate-fade-in-up">
                <div class="p-6 md:p-8 text-gray-900 dark:text-gray-100">
                    <div class="flex flex-col sm:flex-row sm:items-start sm:space-x-4 mb-8">
                        <div class="rounded-full bg-primary/10 p-3 flex-shrink-0 mb-4 sm:mb-0">
                            <x-heroicon-o-user class="w-7 h-6 text-primary" />
                        </div>
                        <div>
                            <h3 class="font-geometric text-xl md:text-2xl font-medium text-gray-900 dark:text-white mb-2">Erzähl uns von dir</h3>
                            <p class="text-sm text-base-content/70">Diese Informationen helfen uns, deine Bewerbung besser einzuschätzen.</p>
                        </div>
                    </div>
                    <div class="card bg-base-200 shadow-xl w-full max-w-4xl mx-auto">
                        <div class="card-body">
                            <div class="w-full mb-6">
                                <ul class="steps steps-horizontal w-full">
                                    <li class="step step-primary">Persönliche Daten</li>
                                    <li class="step step-primary">Rollenspezifisch</li>
                                    <li class="step step-primary">Über Dich</li>
                                </ul>
                            </div>
                            <form action="{{ route('questions.verify') }}" method="get" class="space-y-6">
                                @csrf

                                <!-- Über dich -->
                                <div class="form-control w-full">
                                    <label class="label">
                                        <span class="label-text font-medium">Über dich <span class="text-error">*</span></span>
                                        <span class="label-text-alt text-base-content/60">Max. 1000 Zeichen</span>
                                    </label>
                                    <div class="relative">
                                        <textarea id="about_you" name="about_you" required
                                            rows="4" maxlength="1000"
                                            placeholder="Erzähle uns etwas über dich, deine Interessen und warum du bei unserem Projekt mitmachen möchtest..."
                                            class="textarea textarea-bordered w-full h-32 @error('about_you') textarea-error @enderror"></textarea>
                                        <div class="absolute bottom-2 right-2 text-xs opacity-60">
                                            <span id="about-you-counter">0</span>/1000
                                        </div>
                                    </div>
                                    @error('about_you') <span class="label-text-alt text-error mt-1">{{ $message }}</span> @enderror
                                    <div class="mt-2 text-xs text-base-content/70 flex items-start gap-2">
                                        <x-heroicon-o-light-bulb class="h-4 w-4 shrink-0 mt-0.5 text-primary" />
                                        <span>Gute Themen: Deine Motivation, relevante Erfahrungen und was dich an diesem Projekt begeistert.</span>
                                    </div>
                                </div>

                                <!-- Stärken und Schwächen -->
                                <div class="form-control w-full mt-6">
                                    <label class="label">
                                        <span class="label-text font-medium">Stärken und Schwächen <span class="text-xs opacity-60">(Optional)</span></span>
                                        <span class="label-text-alt text-base-content/60">Max. 500 Zeichen</span>
                                    </label>
                                    <div class="relative">
                                        <textarea id="strengths_weaknesses" name="strengths_weaknesses"
                                            rows="3" maxlength="500"
                                            placeholder="Was sind deine Stärken und wo siehst du Verbesserungspotenzial?"
                                            class="textarea textarea-bordered w-full @error('strengths_weaknesses') textarea-error @enderror"></textarea>
                                        <div class="absolute bottom-2 right-2 text-xs opacity-60">
                                            <span id="strengths-counter">0</span>/500
                                        </div>
                                    </div>
                                    @error('strengths_weaknesses') <span class="label-text-alt text-error mt-1">{{ $message }}</span> @enderror
                                </div>

                                <!-- Abschlussworte -->
                                <div class="form-control w-full mt-6">
                                    <label class="label">
                                        <span class="label-text font-medium">Abschlussworte <span class="text-xs opacity-60">(Optional)</span></span>
                                        <span class="label-text-alt text-base-content/60">Max. 300 Zeichen</span>
                                    </label>
                                    <div class="relative">
                                        <textarea id="final_words" name="final_words"
                                            rows="2" maxlength="300"
                                            placeholder="Möchtest du uns noch etwas mitteilen?"
                                            class="textarea textarea-bordered w-full @error('final_words') textarea-error @enderror"></textarea>
                                        <div class="absolute bottom-2 right-2 text-xs opacity-60">
                                            <span id="final-counter">0</span>/300
                                        </div>
                                    </div>
                                    @error('final_words') <span class="label-text-alt text-error mt-1">{{ $message }}</span> @enderror
                                </div>

                                <!-- Versteckte Felder -->
                                @foreach(request()->all() as $key => $value)
                                    @if(is_array($value))
                                        @foreach($value as $subValue)
                                            <input type="hidden" name="{{ $key }}[]" value="{{ $subValue }}">
                                        @endforeach
                                    @else
                                        <input type="hidden" name="{{ $key }}" value="{{ $value }}">
                                    @endif
                                @endforeach

                                <!-- Zusammenfassung und Buttons -->
                                <div class="card-actions flex flex-col md:flex-row md:justify-between items-center gap-4 mt-8 pt-6 border-t border-base-300">
                                    <div class="text-sm opacity-75">
                                        <div class="flex items-center gap-2">
                                            <x-heroicon-o-check-badge class="h-5 w-5 text-primary" />
                                            <span>Letzter Schritt vor der Überprüfung</span>
                                        </div>
                                    </div>
                                    
                                    <div class="flex items-center gap-3">
                                        <a href="{{ route('home') }}" class="btn btn-ghost">
                                            Abbrechen
                                        </a>
                                        <button type="submit" class="btn btn-primary btn-lg gap-2 min-w-[200px]">
                                            Bewerbung überprüfen
                                            <x-heroicon-o-document-check class="h-5 w-5" />
                                        </button>
                                    </div>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Zeichenzähler-Skript -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Zeichenzähler für alle Textfelder
            const textareas = {
                'about_you': 'about-you-counter',
                'strengths_weaknesses': 'strengths-counter',
                'final_words': 'final-counter'
            };

            for (const [id, counterId] of Object.entries(textareas)) {
                const textarea = document.getElementById(id);
                const counter = document.getElementById(counterId);
                
                if (textarea && counter) {
                    // Initialen Wert setzen
                    counter.textContent = textarea.value.length;
                    
                    // Event-Listener für Änderungen
                    textarea.addEventListener('input', function() {
                        counter.textContent = this.value.length;
                    });
                }
            }
        });
    </script>
</x-app-layout>
