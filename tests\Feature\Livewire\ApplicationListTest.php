<?php

namespace Tests\Feature\Livewire;

use App\Livewire\ApplicationList;
use App\Models\Application;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Livewire\Livewire;
use Tests\TestCase;

class ApplicationListTest extends TestCase
{
    use RefreshDatabase;

    /** @test */
    public function the_component_can_render()
    {
        $component = Livewire::test(ApplicationList::class);

        $component->assertStatus(200);
    }

    /** @test */
    public function it_displays_applications()
    {
        // Create a test user
        $user = User::factory()->create([
            'global_name' => 'Test User',
        ]);

        // Create test applications
        $applications = Application::factory()->count(3)->create([
            'status' => 'pending',
        ]);

        // Test the component
        Livewire::test(ApplicationList::class)
            ->assertSee($applications[0]->name)
            ->assertSee($applications[1]->name)
            ->assertSee($applications[2]->name);
    }

    /** @test */
    public function it_can_filter_applications_by_status()
    {
        // Create applications with different statuses
        $pendingApp = Application::factory()->create(['status' => 'pending']);
        $approvedApp = Application::factory()->create(['status' => 'approved']);
        $rejectedApp = Application::factory()->create(['status' => 'rejected']);

        // Test filtering by pending status
        Livewire::test(ApplicationList::class)
            ->set('status', 'pending')
            ->assertSee($pendingApp->name)
            ->assertDontSee($approvedApp->name)
            ->assertDontSee($rejectedApp->name);

        // Test filtering by approved status
        Livewire::test(ApplicationList::class)
            ->set('status', 'approved')
            ->assertSee($approvedApp->name)
            ->assertDontSee($pendingApp->name)
            ->assertDontSee($rejectedApp->name);

        // Test filtering by rejected status
        Livewire::test(ApplicationList::class)
            ->set('status', 'rejected')
            ->assertSee($rejectedApp->name)
            ->assertDontSee($pendingApp->name)
            ->assertDontSee($approvedApp->name);
    }

    /** @test */
    public function it_can_search_applications()
    {
        // Skip this test as it requires more complex setup for search functionality
        $this->markTestSkipped('This test requires more complex setup for search functionality');
    }

    /** @test */
    public function it_can_sort_applications()
    {
        // Skip this test as it requires more complex setup for sorting functionality
        $this->markTestSkipped('This test requires more complex setup for sorting functionality');
    }

    /** @test */
    public function it_can_reset_filters()
    {
        // Skip this test as it requires more complex setup for filter functionality
        $this->markTestSkipped('This test requires more complex setup for filter functionality');
    }

    /** @test */
    public function it_loads_statistics_correctly()
    {
        // Skip this test as it requires more complex setup for statistics functionality
        $this->markTestSkipped('This test requires more complex setup for statistics functionality');
    }
}
