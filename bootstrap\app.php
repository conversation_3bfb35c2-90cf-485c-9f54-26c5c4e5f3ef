<?php

use Illuminate\Foundation\Application;
use Illuminate\Foundation\Configuration\Exceptions;
use Illuminate\Foundation\Configuration\Middleware;

return Application::configure()
    ->withRouting(
        web: __DIR__.'/../routes/web.php',
        api: __DIR__.'/../routes/api.php',
        apiPrefix: 'api',
        commands: __DIR__.'/../routes/console.php'
    )
    ->withSchedule(function (\Illuminate\Console\Scheduling\Schedule $schedule) {
        require __DIR__.'/../routes/scheduler.php';
    })
    ->withMiddleware(function (Middleware $middleware) {
        $middleware->alias([
            'api.token' => \App\Http\Middleware\VerifyApiToken::class,
            'discord.member' => \App\Http\Middleware\CheckDiscordMembership::class,
            'locale' => \App\Http\Middleware\SetLocale::class,
        ]);

        $middleware->web(append: [
            \App\Http\Middleware\DetectLanguage::class,
            \App\Http\Middleware\SetLocale::class,
        ]);
    })
    ->withExceptions(function (Exceptions $exceptions) {
        //
    })
    ->withProviders([
        App\Providers\LivewireServiceProvider::class,
        App\Providers\BroadcastServiceProvider::class,
        App\Providers\CustomBroadcastingServiceProvider::class,
        App\Providers\AIServiceProvider::class,
        App\Providers\DebuggingServiceProvider::class,
    ])
    ->create();
