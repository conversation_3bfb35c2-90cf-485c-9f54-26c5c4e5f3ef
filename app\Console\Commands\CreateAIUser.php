<?php

namespace App\Console\Commands;

use App\Models\User;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\File;
use Illuminate\Support\Str;

class CreateAIUser extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:create-ai-user';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Create an AI user account for automated responses';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        // Check if AI user already exists
        $aiUser = User::where('username', 'AI Support')->first();

        if ($aiUser) {
            $this->info('AI user already exists with ID: ' . $aiUser->id);
            return;
        }

        // Create a new AI user
        $aiUser = new User();
        // Verwende eine numerische ID für den AI-User
        $aiUser->id = '999999999999999999'; // Eine eindeutige numerische ID für den AI-User
        $aiUser->username = 'AI Support';
        $aiUser->global_name = 'AI Support Assistant';
        $aiUser->discriminator = '0000';
        $aiUser->avatar = 'ai-assistant-avatar.png'; // Spezielles KI-Avatar
        $aiUser->verified = true;
        $aiUser->banner = null;
        $aiUser->banner_color = null;
        $aiUser->accent_color = null;
        $aiUser->locale = 'de';
        $aiUser->mfa_enabled = false;
        $aiUser->premium_type = 0;
        $aiUser->public_flags = 0;
        $aiUser->permissions = 0; // Keine speziellen Berechtigungen
        $aiUser->last_synced_at = now();
        $aiUser->save();

        $this->info('AI user created successfully with ID: ' . $aiUser->id);
        $this->info('Add this ID to your .env file as GEMINI_SYSTEM_USER_ID=' . $aiUser->id);

        // Überprüfen, ob das Avatar-Bild existiert
        $avatarDir = public_path('images');
        $avatarPath = $avatarDir . '/ai-assistant-avatar.png';

        // Erstelle das Verzeichnis, falls es nicht existiert
        if (!file_exists($avatarDir)) {
            if (!mkdir($avatarDir, 0755, true)) {
                $this->warn('Could not create directory: ' . $avatarDir);
            } else {
                $this->info('Created directory: ' . $avatarDir);
            }
        }

        if (!file_exists($avatarPath)) {
            // Erstelle ein einfaches Beispiel-Avatar-Bild
            $this->warn('AI avatar image not found!');
            $this->info('Creating a placeholder avatar image...');

            try {
                // Kopiere ein Standard-Avatar-Bild aus dem Internet
                $placeholderUrl = 'https://cdn.discordapp.com/embed/avatars/0.png';
                $imageContent = @file_get_contents($placeholderUrl);

                if ($imageContent) {
                    file_put_contents($avatarPath, $imageContent);
                    $this->info('Created placeholder avatar at: ' . $avatarPath);
                } else {
                    $this->warn('Failed to download placeholder avatar. Please create an image named "ai-assistant-avatar.png" in the "public/images" directory.');
                }
            } catch (\Exception $e) {
                $this->warn('Error creating placeholder avatar: ' . $e->getMessage());
                $this->info('Please create an image named "ai-assistant-avatar.png" in the "public/images" directory.');
            }
        } else {
            $this->info('AI avatar image found at: ' . $avatarPath);
        }
    }
}
