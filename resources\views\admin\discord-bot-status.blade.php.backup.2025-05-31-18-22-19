<x-app-layout>
    <x-slot name="header">
        <h2 class="font-display text-xl md:text-2xl text-gray-900 dark:text-white leading-tight">
            {{ __('Discord Integration') }}
        </h2>
    </x-slot>

    <div class="py-12">
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
            @if (session('success'))
                <div class="alert alert-success mb-6">
                    <svg xmlns="http://www.w3.org/2000/svg" class="stroke-current shrink-0 h-6 w-6" fill="none" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" /></svg>
                    <span>{{ session('success') }}</span>
                </div>
            @endif

            @if (session('error'))
                <div class="alert alert-error mb-6">
                    <svg xmlns="http://www.w3.org/2000/svg" class="stroke-current shrink-0 h-6 w-6" fill="none" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 14l2-2m0 0l2-2m-2 2l-2-2m2 2l2 2m7-2a9 9 0 11-18 0 9 9 0 0118 0z" /></svg>
                    <span>{{ session('error') }}</span>
                </div>
            @endif
            <!-- Status Card -->
            <div class="bg-white dark:bg-base-100 overflow-hidden shadow-xl rounded-lg mb-6">
                <div class="p-6">
                    <div class="flex items-center justify-between mb-4">
                        <h3 class="text-lg font-semibold text-gray-900 dark:text-white">Discord Bot Status</h3>

                        @if($status['online'] ?? false)
                            <div class="badge badge-success gap-2 p-3">
                                <div class="w-2 h-2 rounded-full bg-success-content animate-pulse"></div>
                                <span>Online</span>
                            </div>
                        @else
                            <div class="badge badge-error gap-2 p-3">
                                <div class="w-2 h-2 rounded-full bg-error-content animate-pulse"></div>
                                <span>Offline</span>
                            </div>
                        @endif
                    </div>

                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <div class="stats shadow w-full">
                                <div class="stat">
                                    <div class="stat-title">Status</div>
                                    <div class="stat-value text-lg">{{ $status['online'] ? 'Online' : 'Offline' }}</div>
                                    <div class="stat-desc">Last checked: {{ $status['last_check']->diffForHumans() }}</div>
                                </div>
                            </div>
                        </div>

                        <div>
                            <div class="stats shadow w-full">
                                <div class="stat">
                                    <div class="stat-title">Version</div>
                                    <div class="stat-value text-lg">{{ $status['version'] ?? 'Unknown' }}</div>
                                    <div class="stat-desc">Bot software version</div>
                                </div>
                            </div>
                        </div>
                    </div>

                    @if($status['online'] ?? false)
                        <div class="mt-6">
                            <div class="stats shadow w-full">
                                <div class="stat">
                                    <div class="stat-title">Uptime</div>
                                    <div class="stat-value text-lg">
                                        @if(isset($status['uptime']))
                                            @if(isset($status['uptime']['days']) && $status['uptime']['days'] > 0)
                                                {{ $status['uptime']['days'] }}d
                                            @endif
                                            {{ $status['uptime']['hours'] ?? 0 }}h
                                            {{ $status['uptime']['minutes'] ?? 0 }}m
                                            {{ $status['uptime']['seconds'] ?? 0 }}s
                                        @else
                                            Unknown
                                        @endif
                                    </div>
                                    <div class="stat-desc">Time since last restart</div>
                                </div>

                                @if(isset($status['guild']))
                                <div class="stat">
                                    <div class="stat-title">Server</div>
                                    <div class="stat-value text-lg">{{ $status['guild']['name'] ?? 'Unknown' }}</div>
                                    <div class="stat-desc">{{ $status['guild']['memberCount'] ?? 0 }} members</div>
                                </div>
                                @endif
                            </div>
                        </div>
                    @endif

                    @if(isset($status['error']) && $status['error'])
                        <div class="alert alert-error mt-6">
                            <svg xmlns="http://www.w3.org/2000/svg" class="stroke-current shrink-0 h-6 w-6" fill="none" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 14l2-2m0 0l2-2m-2 2l-2-2m2 2l2 2m7-2a9 9 0 11-18 0 9 9 0 0118 0z" /></svg>
                            <div>
                                <h3 class="font-bold">Error</h3>
                                <div class="text-xs">{{ $status['error'] }}</div>
                            </div>
                        </div>
                    @endif

                    <div class="mt-6 flex flex-wrap gap-2">
                        <a href="{{ route('admin.discord-bot-status') }}" class="btn btn-primary">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                            </svg>
                            Refresh Status
                        </a>

                        @if($status['online'] ?? false)
                            <form action="{{ route('admin.discord-bot-stop') }}" method="POST" class="inline">
                                @csrf
                                <button type="submit" class="btn btn-error">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                                    </svg>
                                    Stop Bot
                                </button>
                            </form>
                        @else
                            <form action="{{ route('admin.discord-bot-start') }}" method="POST" class="inline">
                                @csrf
                                <button type="submit" class="btn btn-success">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M14.752 11.168l-3.197-2.132A1 1 0 0010 9.87v4.263a1 1 0 001.555.832l3.197-2.132a1 1 0 000-1.664z" />
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                                    </svg>
                                    Start Bot
                                </button>
                            </form>
                        @endif

                        <form action="{{ route('admin.discord-bot-restart') }}" method="POST" class="inline">
                            @csrf
                            <button type="submit" class="btn btn-warning">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                                </svg>
                                Restart Bot
                            </button>
                        </form>
                    </div>

                    <div class="mt-6">
                        <div class="alert alert-info">
                            <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" class="stroke-current shrink-0 w-6 h-6"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path></svg>
                            <div>
                                <h3 class="font-bold">Bot Management</h3>
                                <div class="text-sm">
                                    <p>The Discord bot is automatically started by the production script. Use these controls only if the bot is not working properly.</p>
                                    <p class="mt-1">Bot logs are available at: <code>storage/logs/discord-bot.log</code> and <code>storage/logs/discord-bot-error.log</code></p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Configuration Card -->
            <div class="bg-white dark:bg-base-100 overflow-hidden shadow-xl rounded-lg mb-6">
                <div class="p-6">
                    <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">Bot Configuration</h3>

                    <div class="overflow-x-auto">
                        <table class="table w-full">
                            <tbody>
                                <tr>
                                    <td class="font-medium">API URL</td>
                                    <td>{{ config('services.discord.bot_url', env('DISCORD_BOT_API_URL', 'http://localhost:3001')) }}</td>
                                </tr>
                                <tr>
                                    <td class="font-medium">API Key</td>
                                    <td>
                                        <div class="flex items-center">
                                            <span class="text-opacity-50">•••••••••••••••</span>
                                            <button class="btn btn-ghost btn-xs ml-2" onclick="copyToClipboard('{{ config('services.discord.api_key') }}')">
                                                <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z" />
                                                </svg>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                                <tr>
                                    <td class="font-medium">Guild ID</td>
                                    <td>{{ config('services.discord.guild_id', env('LARASCORD_GUILD_ID', '1031202173826109591')) }}</td>
                                </tr>
                                <tr>
                                    <td class="font-medium">Client ID</td>
                                    <td>{{ config('services.discord.client_id', env('LARASCORD_CLIENT_ID', '1350931744593019002')) }}</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>

            <!-- Health Checks Card -->
            <div class="bg-white dark:bg-base-100 overflow-hidden shadow-xl rounded-lg mb-6">
                <div class="p-6">
                    <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">Health Checks</h3>

                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <div class="stats shadow w-full">
                                <div class="stat">
                                    <div class="stat-title">Bot Health Check</div>
                                    <div class="stat-value text-lg">Every 2 minutes</div>
                                    <div class="stat-desc">Automatic restart if offline for 2+ checks</div>
                                </div>
                            </div>
                        </div>

                        <div>
                            <div class="stats shadow w-full">
                                <div class="stat">
                                    <div class="stat-title">Laravel Health Check</div>
                                    <div class="stat-value text-lg">Every 5 minutes</div>
                                    <div class="stat-desc">Monitors system components</div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="mt-6">
                        <h4 class="font-medium mb-2">Recent Health Check Logs</h4>
                        <div class="mockup-code">
                            @php
                                $logFile = storage_path('logs/discord-bot-health.log');
                                $logs = [];
                                if (file_exists($logFile)) {
                                    $logs = array_slice(file($logFile), -10);
                                }
                            @endphp

                            @forelse($logs as $log)
                                <pre data-prefix="$"><code>{{ $log }}</code></pre>
                            @empty
                                <pre data-prefix="$"><code>No recent logs found.</code></pre>
                            @endforelse
                        </div>
                    </div>
                </div>
            </div>

            <!-- Role Mapping Card -->
            <div class="bg-white dark:bg-base-100 overflow-hidden shadow-xl rounded-lg">
                <div class="p-6">
                    <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">Discord Role Mapping</h3>

                    <div class="overflow-x-auto">
                        <table class="table w-full">
                            <thead>
                                <tr>
                                    <th>Role Name</th>
                                    <th>Discord Role ID</th>
                                    <th>Permission</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach(config('discord.roles', []) as $role => $id)
                                    <tr>
                                        <td>{{ ucfirst($role) }}</td>
                                        <td>{{ $id }}</td>
                                        <td>
                                            @if($role == 'accepted')
                                                MINEWACHE_ACCEPTED
                                            @elseif($role == 'default')
                                                MINEWACHE_DEFAULT
                                            @elseif($role == 'actor')
                                                MINEWACHE_ACTOR
                                            @elseif($role == 'builder')
                                                MINEWACHE_BUILDER
                                            @elseif($role == 'designer')
                                                MINEWACHE_DESIGNER
                                            @elseif($role == 'voice_actor')
                                                MINEWACHE_VOICE_ACTOR
                                            @elseif($role == 'modeler')
                                                MINEWACHE_MODELER
                                            @elseif($role == 'developer')
                                                MINEWACHE_DEVELOPER
                                            @elseif($role == 'cameraman' || $role == 'cutter')
                                                MINEWACHE_MEDIA
                                            @else
                                                -
                                            @endif
                                        </td>
                                    </tr>
                                @endforeach
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>

    @push('scripts')
    <script>
        function copyToClipboard(text) {
            navigator.clipboard.writeText(text).then(function() {
                // Show toast or notification
                alert('API key copied to clipboard');
            }, function(err) {
                console.error('Could not copy text: ', err);
            });
        }
    </script>
    @endpush
</x-app-layout>
