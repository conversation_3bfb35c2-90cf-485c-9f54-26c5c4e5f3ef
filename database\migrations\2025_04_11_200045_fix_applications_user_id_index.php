<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // First, drop the index if it exists
        Schema::table('applications', function (Blueprint $table) {
            // Check if the index exists before trying to drop it
            if (Schema::hasIndex('applications', 'applications_user_id_index')) {
                $table->dropIndex('applications_user_id_index');
            }
        });

        // Then add the user_id column if it doesn't exist
        Schema::table('applications', function (Blueprint $table) {
            if (!Schema::hasColumn('applications', 'user_id')) {
                $table->unsignedBigInteger('user_id')->nullable()->after('discord_id')
                    ->comment('ID des Benutzers, der die Bewerbung eingereicht hat');
            }
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('applications', function (Blueprint $table) {
            if (Schema::hasColumn('applications', 'user_id')) {
                $table->dropColumn('user_id');
            }
        });
    }
};
