<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Artisan;
use Illuminate\Support\Facades\Config;

class QueueWorkReverb extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'queue:work-reverb {--once : Only process the next job on the queue} {--queue= : The queue to listen on} {--timeout= : The number of seconds a job may take before timing out} {--tries= : Number of times to attempt a job before logging it failed}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Process the queue with Reverb broadcasting driver';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        // Force the broadcast driver to be 'reverb'
        Config::set('broadcasting.default', 'reverb');

        // Log the configuration
        Log::info('QueueWorkReverb: Setting broadcast driver to reverb', [
            'broadcast_driver' => Config::get('broadcasting.default')
        ]);

        // Build the queue:work command with all passed arguments
        $command = 'queue:work';
        $parameters = [];

        // Pass through all options
        if ($this->option('once')) {
            $parameters['--once'] = true;
        }

        if ($this->option('queue')) {
            $parameters['--queue'] = $this->option('queue');
        }

        if ($this->option('timeout')) {
            $parameters['--timeout'] = $this->option('timeout');
        }

        if ($this->option('tries')) {
            $parameters['--tries'] = $this->option('tries');
        }

        $this->info('Starting queue worker with Reverb broadcasting...');
        Artisan::call($command, $parameters, $this->output);
    }
}
