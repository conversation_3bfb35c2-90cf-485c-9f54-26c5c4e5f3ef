<div>
    <div class="bg-gradient-modern min-h-screen py-8 px-4">
        <?php if (isset($component)) { $__componentOriginalfc34df8c041cfce1b125f68563b94330 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginalfc34df8c041cfce1b125f68563b94330 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.modern-card','data' => ['variant' => 'elevated','size' => 'md']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('modern-card'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['variant' => 'elevated','size' => 'md']); ?>
            <div class="flex items-center gap-3 mb-6">
                <?php if (isset($component)) { $__componentOriginal19987cd45c699eefead8b7a8ac222798 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal19987cd45c699eefead8b7a8ac222798 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.modern-button','data' => ['variant' => 'ghost','size' => 'sm','href' => ''.e(route('tickets.index')).'','icon' => '<svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path></svg>']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('modern-button'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['variant' => 'ghost','size' => 'sm','href' => ''.e(route('tickets.index')).'','icon' => '<svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path></svg>']); ?>
                    <?php echo e(__('tickets.back_to_tickets')); ?>

                 <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal19987cd45c699eefead8b7a8ac222798)): ?>
<?php $attributes = $__attributesOriginal19987cd45c699eefead8b7a8ac222798; ?>
<?php unset($__attributesOriginal19987cd45c699eefead8b7a8ac222798); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal19987cd45c699eefead8b7a8ac222798)): ?>
<?php $component = $__componentOriginal19987cd45c699eefead8b7a8ac222798; ?>
<?php unset($__componentOriginal19987cd45c699eefead8b7a8ac222798); ?>
<?php endif; ?>
                <div class="h-6 w-px bg-base-300"></div>
                <h1 class="text-2xl font-bold text-base-content">
                    <?php echo e(__('tickets.create_ticket')); ?>

                </h1>
            </div>

            <form wire:submit.prevent="submit" class="space-y-6">
                <?php if (isset($component)) { $__componentOriginal17d23b099a28c4e2c5f6c448deb256e5 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal17d23b099a28c4e2c5f6c448deb256e5 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.modern-input','data' => ['wire:model' => 'title','type' => 'text','id' => 'title','label' => ''.e(__('tickets.title')).'','placeholder' => ''.e(__('tickets.title_placeholder')).'','variant' => 'glass','required' => true,'error' => $errors->first('title')]] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('modern-input'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['wire:model' => 'title','type' => 'text','id' => 'title','label' => ''.e(__('tickets.title')).'','placeholder' => ''.e(__('tickets.title_placeholder')).'','variant' => 'glass','required' => true,'error' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($errors->first('title'))]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal17d23b099a28c4e2c5f6c448deb256e5)): ?>
<?php $attributes = $__attributesOriginal17d23b099a28c4e2c5f6c448deb256e5; ?>
<?php unset($__attributesOriginal17d23b099a28c4e2c5f6c448deb256e5); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal17d23b099a28c4e2c5f6c448deb256e5)): ?>
<?php $component = $__componentOriginal17d23b099a28c4e2c5f6c448deb256e5; ?>
<?php unset($__componentOriginal17d23b099a28c4e2c5f6c448deb256e5); ?>
<?php endif; ?>

                <div>
                    <label for="description" class="block text-sm font-medium text-base-content/70 mb-2"><?php echo e(__('tickets.description')); ?></label>
                    <textarea
                        wire:model="description"
                        id="description"
                        rows="6"
                        class="w-full bg-base-100 border border-base-300 rounded-xl px-4 py-3 focus:outline-none focus:ring-2 focus:ring-primary focus:border-primary transition-all duration-300 resize-none"
                        placeholder="<?php echo e(__('tickets.description_placeholder')); ?>"
                        required
                    ></textarea>
                    <!--[if BLOCK]><![endif]--><?php $__errorArgs = ['description'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                        <div class="mt-2 text-error text-sm flex items-center gap-1">
                            <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clip-rule="evenodd"></path>
                            </svg>
                            <?php echo e($message); ?>

                        </div>
                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?><!--[if ENDBLOCK]><![endif]-->
                </div>

                <div class="flex justify-end gap-3 pt-4 border-t border-base-300/30">
                    <?php if (isset($component)) { $__componentOriginal19987cd45c699eefead8b7a8ac222798 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal19987cd45c699eefead8b7a8ac222798 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.modern-button','data' => ['variant' => 'ghost','size' => 'md','href' => ''.e(route('tickets.index')).'']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('modern-button'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['variant' => 'ghost','size' => 'md','href' => ''.e(route('tickets.index')).'']); ?>
                        <?php echo e(__('tickets.cancel')); ?>

                     <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal19987cd45c699eefead8b7a8ac222798)): ?>
<?php $attributes = $__attributesOriginal19987cd45c699eefead8b7a8ac222798; ?>
<?php unset($__attributesOriginal19987cd45c699eefead8b7a8ac222798); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal19987cd45c699eefead8b7a8ac222798)): ?>
<?php $component = $__componentOriginal19987cd45c699eefead8b7a8ac222798; ?>
<?php unset($__componentOriginal19987cd45c699eefead8b7a8ac222798); ?>
<?php endif; ?>
                    <?php if (isset($component)) { $__componentOriginal19987cd45c699eefead8b7a8ac222798 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal19987cd45c699eefead8b7a8ac222798 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.modern-button','data' => ['type' => 'submit','variant' => 'primary','size' => 'md','wire:loading.attr' => 'disabled','wire:target' => 'submit','icon' => '<svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8"></path></svg>']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('modern-button'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['type' => 'submit','variant' => 'primary','size' => 'md','wire:loading.attr' => 'disabled','wire:target' => 'submit','icon' => '<svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8"></path></svg>']); ?>
                        <span wire:loading.remove wire:target="submit"><?php echo e(__('tickets.submit')); ?></span>
                        <span wire:loading wire:target="submit" class="flex items-center gap-2">
                            <?php if (isset($component)) { $__componentOriginald97cbcc55aabfe6f286c335fdae5c7d3 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginald97cbcc55aabfe6f286c335fdae5c7d3 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.modern-loading','data' => ['type' => 'spinner','size' => 'xs','color' => 'base']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('modern-loading'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['type' => 'spinner','size' => 'xs','color' => 'base']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginald97cbcc55aabfe6f286c335fdae5c7d3)): ?>
<?php $attributes = $__attributesOriginald97cbcc55aabfe6f286c335fdae5c7d3; ?>
<?php unset($__attributesOriginald97cbcc55aabfe6f286c335fdae5c7d3); ?>
<?php endif; ?>
<?php if (isset($__componentOriginald97cbcc55aabfe6f286c335fdae5c7d3)): ?>
<?php $component = $__componentOriginald97cbcc55aabfe6f286c335fdae5c7d3; ?>
<?php unset($__componentOriginald97cbcc55aabfe6f286c335fdae5c7d3); ?>
<?php endif; ?>
                            <?php echo e(__('tickets.submitting')); ?>

                        </span>
                     <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal19987cd45c699eefead8b7a8ac222798)): ?>
<?php $attributes = $__attributesOriginal19987cd45c699eefead8b7a8ac222798; ?>
<?php unset($__attributesOriginal19987cd45c699eefead8b7a8ac222798); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal19987cd45c699eefead8b7a8ac222798)): ?>
<?php $component = $__componentOriginal19987cd45c699eefead8b7a8ac222798; ?>
<?php unset($__componentOriginal19987cd45c699eefead8b7a8ac222798); ?>
<?php endif; ?>
                </div>
            </form>
         <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginalfc34df8c041cfce1b125f68563b94330)): ?>
<?php $attributes = $__attributesOriginalfc34df8c041cfce1b125f68563b94330; ?>
<?php unset($__attributesOriginalfc34df8c041cfce1b125f68563b94330); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalfc34df8c041cfce1b125f68563b94330)): ?>
<?php $component = $__componentOriginalfc34df8c041cfce1b125f68563b94330; ?>
<?php unset($__componentOriginalfc34df8c041cfce1b125f68563b94330); ?>
<?php endif; ?>
    </div>
</div>
<?php /**PATH C:\Users\<USER>\Documents\GitHub\minewache-website\resources\views/livewire/ticket-create.blade.php ENDPATH**/ ?>