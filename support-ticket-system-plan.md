# Support Ticket System Development Plan

## Overview

This document outlines the development plan for a synchronized support ticket system that works across both the Laravel web application and the existing Discord bot. The system will allow users ("Members") and supporters ("Minewache Teammitglieder") to interact with tickets seamlessly on both platforms.

### Project Context

- **Tech Stack**:
  - Backend: Laravel 11.x (PHP 8.2+)
  - Frontend: Livewire 3.x, Tailwind CSS/DaisyUI, Alpine.js
  - Authentication: Lara<PERSON>rd (Discord OAuth)
  - Database: MySQL/MariaDB or SQLite
  - Development Tools: Telescope, Pulse, Pint
  - Discord Integration: Existing local Node.js bot (currently used for role synchronization)

- **User Roles**:
  - **Members**: Regular users authenticated via Larascord
  - **Supporters**: Users with the "Minewache Teammitglieder" role (identified via the `Role::MINEWACHE_TEAM` permission)

- **Synchronization Approach**:
  - Cost-effective solution using the existing API communication between Laravel and the Discord bot
  - No external broadcasting services (like <PERSON><PERSON><PERSON>) will be used

## 1. Database Schema

### Step 1.1: Create Ticket Model and Migration

```php
// Migration for tickets table
Schema::create('tickets', function (Blueprint $table) {
    $table->id();
    $table->string('user_id');  // Discord user ID
    $table->string('title');
    $table->text('description');
    $table->enum('status', ['open', 'in_progress', 'closed'])->default('open');
    $table->string('discord_channel_id')->nullable();  // Discord channel/thread ID
    $table->string('assigned_to')->nullable();  // Discord user ID of supporter
    $table->timestamps();

    $table->foreign('user_id')->references('id')->on('users');
    $table->foreign('assigned_to')->references('id')->on('users')->nullOnDelete();
});
```

### Step 1.2: Create TicketMessage Model and Migration

```php
// Migration for ticket_messages table
Schema::create('ticket_messages', function (Blueprint $table) {
    $table->id();
    $table->foreignId('ticket_id')->constrained()->cascadeOnDelete();
    $table->string('user_id');  // Discord user ID
    $table->text('message');
    $table->boolean('is_from_discord')->default(false);
    $table->string('discord_message_id')->nullable();
    $table->timestamps();

    $table->foreign('user_id')->references('id')->on('users');
});
```

### Step 1.3: Create TicketAttachment Model and Migration

```php
// Migration for ticket_attachments table
Schema::create('ticket_attachments', function (Blueprint $table) {
    $table->id();
    $table->foreignId('ticket_message_id')->constrained()->cascadeOnDelete();
    $table->string('filename');
    $table->string('original_filename');
    $table->string('file_path');  // Path in storage
    $table->string('mime_type');
    $table->integer('file_size');  // Size in bytes
    $table->string('discord_attachment_id')->nullable();
    $table->boolean('is_from_discord')->default(false);
    $table->timestamps();
});
```

## 2. Laravel Backend

### Step 2.1: Create Ticket and TicketMessage Models

Create the Eloquent models with relationships:

```php
// app/Models/Ticket.php
class Ticket extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id',
        'title',
        'description',
        'status',
        'discord_channel_id',
        'assigned_to',
    ];

    protected $casts = [
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    // Relationship to the user who created the ticket
    public function user()
    {
        return $this->belongsTo(User::class, 'user_id');
    }

    // Relationship to the supporter assigned to the ticket
    public function assignedTo()
    {
        return $this->belongsTo(User::class, 'assigned_to');
    }

    // Relationship to ticket messages
    public function messages()
    {
        return $this->hasMany(TicketMessage::class);
    }
}
```

```php
// app/Models/TicketMessage.php
class TicketMessage extends Model
{
    use HasFactory;

    protected $fillable = [
        'ticket_id',
        'user_id',
        'message',
        'is_from_discord',
        'discord_message_id',
    ];

    protected $casts = [
        'is_from_discord' => 'boolean',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    // Relationship to the ticket
    public function ticket()
    {
        return $this->belongsTo(Ticket::class);
    }

    // Relationship to the user who sent the message
    public function user()
    {
        return $this->belongsTo(User::class, 'user_id');
    }

    // Relationship to attachments
    public function attachments()
    {
        return $this->hasMany(TicketAttachment::class);
    }
}
```

```php
// app/Models/TicketAttachment.php
class TicketAttachment extends Model
{
    use HasFactory;

    protected $fillable = [
        'ticket_message_id',
        'filename',
        'original_filename',
        'file_path',
        'mime_type',
        'file_size',
        'discord_attachment_id',
        'is_from_discord',
    ];

    protected $casts = [
        'is_from_discord' => 'boolean',
        'file_size' => 'integer',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    // Relationship to the message
    public function message()
    {
        return $this->belongsTo(TicketMessage::class, 'ticket_message_id');
    }

    // Get the full storage path
    public function getFullPathAttribute()
    {
        return storage_path('app/' . $this->file_path);
    }

    // Get the download URL
    public function getDownloadUrlAttribute()
    {
        return route('tickets.attachments.download', $this->id);
    }
}
```

### Step 2.2: Update User Model

Update the User model to include relationships to tickets:

```php
// Add to app/Models/User.php
/**
 * Get all tickets created by this user
 *
 * @return \Illuminate\Database\Eloquent\Relations\HasMany
 */
public function tickets()
{
    return $this->hasMany(Ticket::class, 'user_id');
}

/**
 * Get all tickets assigned to this user
 *
 * @return \Illuminate\Database\Eloquent\Relations\HasMany
 */
public function assignedTickets()
{
    return $this->hasMany(Ticket::class, 'assigned_to');
}
```

### Step 2.3: Supporter Identification

We'll use the existing `Role::MINEWACHE_TEAM` permission to identify supporters. This is already implemented in the User model with the `hasRole()` method:

```php
// Example usage in controllers/policies
if ($user->hasRole(Role::MINEWACHE_TEAM)) {
    // User is a supporter
}
```

### Step 2.4: Create TicketController

Create a controller to handle ticket operations:

```php
// app/Http/Controllers/TicketController.php
class TicketController extends Controller
{
    /**
     * Display a listing of the tickets.
     */
    public function index(Request $request)
    {
        $user = $request->user();

        // If user is a supporter, show all tickets
        if ($user->hasRole(Role::MINEWACHE_TEAM)) {
            $tickets = Ticket::with('user', 'assignedTo')
                ->latest()
                ->paginate(15);
        } else {
            // Otherwise, show only user's tickets
            $tickets = $user->tickets()
                ->with('assignedTo')
                ->latest()
                ->paginate(15);
        }

        return view('tickets.index', compact('tickets'));
    }

    /**
     * Show the form for creating a new ticket.
     */
    public function create()
    {
        return view('tickets.create');
    }

    /**
     * Store a newly created ticket.
     */
    public function store(Request $request)
    {
        $validated = $request->validate([
            'title' => 'required|string|max:255',
            'description' => 'required|string',
        ]);

        $ticket = new Ticket([
            'user_id' => $request->user()->id,
            'title' => $validated['title'],
            'description' => $validated['description'],
            'status' => 'open',
        ]);

        $ticket->save();

        // Create a Discord channel for this ticket if Discord integration is enabled
        if (config('services.discord.enabled', false)) {
            $this->createDiscordChannel($ticket);
        }

        return redirect()->route('tickets.show', $ticket)
            ->with('success', 'Ticket created successfully.');
    }

    /**
     * Display the specified ticket.
     */
    public function show(Ticket $ticket)
    {
        $this->authorize('view', $ticket);

        $ticket->load(['messages.user', 'user', 'assignedTo']);

        return view('tickets.show', compact('ticket'));
    }

    /**
     * Add a reply to a ticket.
     */
    public function reply(Request $request, Ticket $ticket)
    {
        $this->authorize('reply', $ticket);

        $validated = $request->validate([
            'message' => 'required|string',
            'attachments.*' => 'nullable|file|max:10240', // 10MB max per file
        ]);

        $message = new TicketMessage([
            'ticket_id' => $ticket->id,
            'user_id' => $request->user()->id,
            'message' => $validated['message'],
        ]);

        $message->save();

        // Handle file attachments
        if ($request->hasFile('attachments')) {
            foreach ($request->file('attachments') as $file) {
                $path = $file->store('ticket-attachments/' . $ticket->id);

                $attachment = new TicketAttachment([
                    'ticket_message_id' => $message->id,
                    'filename' => basename($path),
                    'original_filename' => $file->getClientOriginalName(),
                    'file_path' => $path,
                    'mime_type' => $file->getMimeType(),
                    'file_size' => $file->getSize(),
                ]);

                $attachment->save();
            }
        }

        // Send the message to Discord if integration is enabled
        if (config('services.discord.enabled', false) && $ticket->discord_channel_id) {
            $this->sendMessageToDiscord($message);
        }

        return redirect()->route('tickets.show', $ticket)
            ->with('success', 'Reply added successfully.');
    }

    /**
     * Download a ticket attachment.
     */
    public function downloadAttachment(TicketAttachment $attachment)
    {
        // Check if user has permission to view the ticket
        $ticket = $attachment->message->ticket;
        $this->authorize('view', $ticket);

        return response()->download(
            $attachment->full_path,
            $attachment->original_filename,
            ['Content-Type' => $attachment->mime_type]
        );
    }

    /**
     * Update the status of a ticket.
     */
    public function updateStatus(Request $request, Ticket $ticket)
    {
        $this->authorize('update', $ticket);

        $validated = $request->validate([
            'status' => 'required|in:open,in_progress,closed',
        ]);

        $ticket->status = $validated['status'];
        $ticket->save();

        // Update the Discord channel if integration is enabled
        if (config('services.discord.enabled', false) && $ticket->discord_channel_id) {
            $this->updateDiscordChannel($ticket);
        }

        return redirect()->route('tickets.show', $ticket)
            ->with('success', 'Ticket status updated successfully.');
    }

    /**
     * Assign a ticket to a supporter.
     */
    public function assign(Request $request, Ticket $ticket)
    {
        $this->authorize('assign', $ticket);

        $validated = $request->validate([
            'assigned_to' => 'nullable|exists:users,id',
        ]);

        $ticket->assigned_to = $validated['assigned_to'];
        $ticket->save();

        return redirect()->route('tickets.show', $ticket)
            ->with('success', 'Ticket assigned successfully.');
    }

    // Helper methods for Discord integration

    private function createDiscordChannel(Ticket $ticket)
    {
        try {
            $botApiUrl = config('services.discord.bot_url');
            $endpoint = "{$botApiUrl}/api/tickets/create-channel";
            $apiKey = config('services.discord.api_key');

            $response = Http::withHeaders([
                'Authorization' => "Bearer {$apiKey}",
                'Accept' => 'application/json'
            ])->post($endpoint, [
                'ticket_id' => $ticket->id,
                'user_id' => $ticket->user_id,
                'title' => $ticket->title,
            ]);

            if ($response->successful()) {
                $data = $response->json();
                $ticket->discord_channel_id = $data['channel_id'];
                $ticket->save();
            }
        } catch (\Exception $e) {
            Log::error("Failed to create Discord channel for ticket {$ticket->id}: {$e->getMessage()}");
        }
    }

    private function sendMessageToDiscord(TicketMessage $message)
    {
        try {
            $botApiUrl = config('services.discord.bot_url');
            $endpoint = "{$botApiUrl}/api/tickets/send-message";
            $apiKey = config('services.discord.api_key');

            // Prepare data for the API call
            $data = [
                'channel_id' => $message->ticket->discord_channel_id,
                'user_id' => $message->user_id,
                'message' => $message->message,
                'ticket_id' => $message->ticket_id,
                'message_id' => $message->id,
            ];

            // Add attachments if present
            if ($message->attachments->count() > 0) {
                $attachments = [];

                foreach ($message->attachments as $attachment) {
                    $attachments[] = [
                        'id' => $attachment->id,
                        'filename' => $attachment->original_filename,
                        'size' => $attachment->file_size,
                        'mime_type' => $attachment->mime_type,
                        'download_url' => url("/api/tickets/attachments/{$attachment->id}/download?api_key={$apiKey}")
                    ];
                }

                $data['attachments'] = $attachments;
            }

            Http::withHeaders([
                'Authorization' => "Bearer {$apiKey}",
                'Accept' => 'application/json'
            ])->post($endpoint, $data);
        } catch (\Exception $e) {
            Log::error("Failed to send message to Discord for ticket {$message->ticket_id}: {$e->getMessage()}");
        }
    }

    private function updateDiscordChannel(Ticket $ticket)
    {
        try {
            $botApiUrl = config('services.discord.bot_url');
            $endpoint = "{$botApiUrl}/api/tickets/update-channel";
            $apiKey = config('services.discord.api_key');

            Http::withHeaders([
                'Authorization' => "Bearer {$apiKey}",
                'Accept' => 'application/json'
            ])->post($endpoint, [
                'channel_id' => $ticket->discord_channel_id,
                'status' => $ticket->status,
                'ticket_id' => $ticket->id,
            ]);
        } catch (\Exception $e) {
            Log::error("Failed to update Discord channel for ticket {$ticket->id}: {$e->getMessage()}");
        }
    }
}
```

### Step 2.5: Create TicketPolicy

Create a policy to handle authorization:

```php
// app/Policies/TicketPolicy.php
class TicketPolicy
{
    /**
     * Determine whether the user can view any tickets.
     */
    public function viewAny(User $user): bool
    {
        // All authenticated users can view the tickets index
        return true;
    }

    /**
     * Determine whether the user can view the ticket.
     */
    public function view(User $user, Ticket $ticket): bool
    {
        // Supporters can view all tickets
        if ($user->hasRole(Role::MINEWACHE_TEAM)) {
            return true;
        }

        // Users can only view their own tickets
        return $user->id === $ticket->user_id;
    }

    /**
     * Determine whether the user can create tickets.
     */
    public function create(User $user): bool
    {
        // All authenticated users can create tickets
        return true;
    }

    /**
     * Determine whether the user can reply to the ticket.
     */
    public function reply(User $user, Ticket $ticket): bool
    {
        // Closed tickets cannot be replied to
        if ($ticket->status === 'closed') {
            return false;
        }

        // Supporters can reply to all tickets
        if ($user->hasRole(Role::MINEWACHE_TEAM)) {
            return true;
        }

        // Users can only reply to their own tickets
        return $user->id === $ticket->user_id;
    }

    /**
     * Determine whether the user can update the ticket.
     */
    public function update(User $user, Ticket $ticket): bool
    {
        // Only supporters can update tickets
        return $user->hasRole(Role::MINEWACHE_TEAM);
    }

    /**
     * Determine whether the user can assign the ticket.
     */
    public function assign(User $user, Ticket $ticket): bool
    {
        // Only supporters can assign tickets
        return $user->hasRole(Role::MINEWACHE_TEAM);
    }
}
```

### Step 2.6: Create API Routes for Discord Bot Integration

Add API routes for the Discord bot to interact with the ticket system:

```php
// routes/api.php
Route::prefix('tickets')->middleware('api.token')->group(function () {
    // Create a ticket from Discord
    Route::post('/create', [TicketApiController::class, 'create']);

    // Add a message to a ticket from Discord
    Route::post('/{ticket}/messages', [TicketApiController::class, 'addMessage']);

    // Update ticket status from Discord
    Route::post('/{ticket}/status', [TicketApiController::class, 'updateStatus']);

    // Get ticket information
    Route::get('/{ticket}', [TicketApiController::class, 'show']);

    // Get all tickets for a user
    Route::get('/user/{userId}', [TicketApiController::class, 'userTickets']);

    // File attachment endpoints
    Route::get('/attachments/{attachment}/download', [TicketApiController::class, 'downloadAttachment'])
        ->name('api.tickets.attachments.download');

    // Upload attachment from Discord
    Route::post('/attachments/upload', [TicketApiController::class, 'uploadAttachment']);
});
```

## 3. Laravel Frontend (Livewire Components)

### Step 3.1: Create TicketList Component

Create a Livewire component to display a list of tickets:

```php
// app/Livewire/TicketList.php
class TicketList extends Component
{
    public $filter = 'all'; // 'all', 'open', 'in_progress', 'closed', 'assigned_to_me'
    public $search = '';
    public $perPage = 10;

    protected $queryString = [
        'filter' => ['except' => 'all'],
        'search' => ['except' => ''],
        'perPage' => ['except' => 10],
    ];

    public function render()
    {
        $user = auth()->user();

        // Base query
        $query = Ticket::query();

        // Apply user role filter
        if (!$user->hasRole(Role::MINEWACHE_TEAM)) {
            // Regular users can only see their own tickets
            $query->where('user_id', $user->id);
        } elseif ($this->filter === 'assigned_to_me') {
            // Supporters can filter to see only tickets assigned to them
            $query->where('assigned_to', $user->id);
        }

        // Apply status filter
        if (in_array($this->filter, ['open', 'in_progress', 'closed'])) {
            $query->where('status', $this->filter);
        }

        // Apply search
        if ($this->search) {
            $query->where(function ($q) {
                $q->where('title', 'like', "%{$this->search}%")
                  ->orWhere('description', 'like', "%{$this->search}%");
            });
        }

        // Get tickets with relationships
        $tickets = $query->with(['user', 'assignedTo'])
            ->latest()
            ->paginate($this->perPage);

        return view('livewire.ticket-list', [
            'tickets' => $tickets,
            'isSupporter' => $user->hasRole(Role::MINEWACHE_TEAM),
        ]);
    }

    public function updatingSearch()
    {
        $this->resetPage();
    }

    public function updatingFilter()
    {
        $this->resetPage();
    }
}
```

Create the corresponding Blade view:

```blade
<!-- resources/views/livewire/ticket-list.blade.php -->
<div>
    <div class="mb-6 flex flex-col md:flex-row justify-between items-start md:items-center gap-4">
        <div class="flex-1">
            <div class="relative">
                <input type="text" wire:model.live.debounce.300ms="search"
                    placeholder="Search tickets..."
                    class="input input-bordered w-full pl-10">
                <div class="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
                    <svg class="w-5 h-5 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                    </svg>
                </div>
            </div>
        </div>

        <div class="flex flex-wrap gap-2">
            <select wire:model.live="filter" class="select select-bordered">
                <option value="all">All Tickets</option>
                <option value="open">Open</option>
                <option value="in_progress">In Progress</option>
                <option value="closed">Closed</option>
                @if($isSupporter)
                    <option value="assigned_to_me">Assigned to Me</option>
                @endif
            </select>

            <select wire:model.live="perPage" class="select select-bordered">
                <option value="5">5 per page</option>
                <option value="10">10 per page</option>
                <option value="15">15 per page</option>
                <option value="25">25 per page</option>
            </select>

            <a href="{{ route('tickets.create') }}" class="btn btn-primary">
                <svg class="w-5 h-5 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                </svg>
                New Ticket
            </a>
        </div>
    </div>

    <div class="overflow-x-auto">
        @if($tickets->isEmpty())
            <div class="text-center py-12 bg-base-200 rounded-lg">
                <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                </svg>
                <h3 class="mt-2 text-sm font-medium text-gray-900 dark:text-gray-200">No tickets found</h3>
                <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">
                    @if($search)
                        No tickets match your search criteria.
                    @else
                        Get started by creating a new ticket.
                    @endif
                </p>
                <div class="mt-6">
                    <a href="{{ route('tickets.create') }}" class="btn btn-primary">
                        <svg class="w-5 h-5 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                        </svg>
                        New Ticket
                    </a>
                </div>
            </div>
        @else
            <table class="table w-full">
                <thead>
                    <tr>
                        <th>ID</th>
                        <th>Title</th>
                        @if($isSupporter)
                            <th>Created By</th>
                        @endif
                        <th>Status</th>
                        <th>Created</th>
                        <th>Assigned To</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
                    @foreach($tickets as $ticket)
                        <tr>
                            <td>{{ $ticket->id }}</td>
                            <td>
                                <a href="{{ route('tickets.show', $ticket) }}" class="font-medium hover:underline">
                                    {{ $ticket->title }}
                                </a>
                            </td>
                            @if($isSupporter)
                                <td>{{ $ticket->user->username }}</td>
                            @endif
                            <td>
                                <span class="badge badge-{{ $ticket->status === 'open' ? 'error' : ($ticket->status === 'in_progress' ? 'warning' : 'success') }}">
                                    {{ ucfirst(str_replace('_', ' ', $ticket->status)) }}
                                </span>
                            </td>
                            <td>{{ $ticket->created_at->diffForHumans() }}</td>
                            <td>
                                @if($ticket->assignedTo)
                                    {{ $ticket->assignedTo->username }}
                                @else
                                    <span class="text-gray-400">Unassigned</span>
                                @endif
                            </td>
                            <td>
                                <a href="{{ route('tickets.show', $ticket) }}" class="btn btn-sm btn-ghost">
                                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                                    </svg>
                                </a>
                            </td>
                        </tr>
                    @endforeach
                </tbody>
            </table>

            <div class="mt-4">
                {{ $tickets->links() }}
            </div>
        @endif
    </div>
</div>
```

### Step 3.2: Create TicketView Component

Create a Livewire component to view a single ticket:

```php
// app/Livewire/TicketView.php
class TicketView extends Component
{
    public Ticket $ticket;
    public $message = '';
    public $status;
    public $assignedTo;

    protected $listeners = ['refresh' => '$refresh'];

    public function mount(Ticket $ticket)
    {
        $this->ticket = $ticket;
        $this->status = $ticket->status;
        $this->assignedTo = $ticket->assigned_to;
    }

    public function render()
    {
        $this->ticket->load(['messages.user', 'user', 'assignedTo']);

        $supporters = [];
        if (auth()->user()->hasRole(Role::MINEWACHE_TEAM)) {
            $supporters = User::where('permissions', '&', Role::MINEWACHE_TEAM->value)
                ->orderBy('username')
                ->get()
                ->pluck('username', 'id')
                ->toArray();
        }

        return view('livewire.ticket-view', [
            'supporters' => $supporters,
            'isSupporter' => auth()->user()->hasRole(Role::MINEWACHE_TEAM),
        ]);
    }

    public $message = '';
    public $attachments = [];

    public function addReply()
    {
        $this->validate([
            'message' => 'required|string',
            'attachments.*' => 'nullable|file|max:10240', // 10MB max per file
        ]);

        $message = new TicketMessage([
            'ticket_id' => $this->ticket->id,
            'user_id' => auth()->id(),
            'message' => $this->message,
        ]);

        $message->save();

        // Handle file attachments
        foreach ($this->attachments as $file) {
            $path = $file->store('ticket-attachments/' . $this->ticket->id);

            $attachment = new TicketAttachment([
                'ticket_message_id' => $message->id,
                'filename' => basename($path),
                'original_filename' => $file->getClientOriginalName(),
                'file_path' => $path,
                'mime_type' => $file->getMimeType(),
                'file_size' => $file->getSize(),
            ]);

            $attachment->save();
        }

        // Send the message to Discord if integration is enabled
        if (config('services.discord.enabled', false) && $this->ticket->discord_channel_id) {
            $this->sendMessageToDiscord($message);
        }

        $this->message = '';
        $this->attachments = [];
        $this->emit('refresh');
    }

    public function updateStatus()
    {
        if (!auth()->user()->hasRole(Role::MINEWACHE_TEAM)) {
            return;
        }

        $this->validate([
            'status' => 'required|in:open,in_progress,closed',
        ]);

        $this->ticket->status = $this->status;
        $this->ticket->save();

        // Update the Discord channel if integration is enabled
        if (config('services.discord.enabled', false) && $this->ticket->discord_channel_id) {
            $this->updateDiscordChannel($this->ticket);
        }

        $this->emit('refresh');
        $this->dispatchBrowserEvent('notify', [
            'message' => 'Ticket status updated successfully.',
        ]);
    }

    public function assignTicket()
    {
        if (!auth()->user()->hasRole(Role::MINEWACHE_TEAM)) {
            return;
        }

        $this->validate([
            'assignedTo' => 'nullable|exists:users,id',
        ]);

        $this->ticket->assigned_to = $this->assignedTo;
        $this->ticket->save();

        $this->emit('refresh');
        $this->dispatchBrowserEvent('notify', [
            'message' => 'Ticket assigned successfully.',
        ]);
    }

    // Helper methods for Discord integration

    private function sendMessageToDiscord(TicketMessage $message)
    {
        try {
            $botApiUrl = config('services.discord.bot_url');
            $endpoint = "{$botApiUrl}/api/tickets/send-message";
            $apiKey = config('services.discord.api_key');

            Http::withHeaders([
                'Authorization' => "Bearer {$apiKey}",
                'Accept' => 'application/json'
            ])->post($endpoint, [
                'channel_id' => $message->ticket->discord_channel_id,
                'user_id' => $message->user_id,
                'message' => $message->message,
                'ticket_id' => $message->ticket_id,
                'message_id' => $message->id,
            ]);
        } catch (\Exception $e) {
            Log::error("Failed to send message to Discord for ticket {$message->ticket_id}: {$e->getMessage()}");
        }
    }

    private function updateDiscordChannel(Ticket $ticket)
    {
        try {
            $botApiUrl = config('services.discord.bot_url');
            $endpoint = "{$botApiUrl}/api/tickets/update-channel";
            $apiKey = config('services.discord.api_key');

            Http::withHeaders([
                'Authorization' => "Bearer {$apiKey}",
                'Accept' => 'application/json'
            ])->post($endpoint, [
                'channel_id' => $ticket->discord_channel_id,
                'status' => $ticket->status,
                'ticket_id' => $ticket->id,
            ]);
        } catch (\Exception $e) {
            Log::error("Failed to update Discord channel for ticket {$ticket->id}: {$e->getMessage()}");
        }
    }
}
```

### Step 3.3: Create TicketCreate Component

Create a Livewire component for creating new tickets:

```php
// app/Livewire/TicketCreate.php
class TicketCreate extends Component
{
    public $title = '';
    public $description = '';

    protected $rules = [
        'title' => 'required|string|max:255',
        'description' => 'required|string',
    ];

    public function render()
    {
        return view('livewire.ticket-create');
    }

    public function submit()
    {
        $this->validate();

        $ticket = new Ticket([
            'user_id' => auth()->id(),
            'title' => $this->title,
            'description' => $this->description,
            'status' => 'open',
        ]);

        $ticket->save();

        // Create a Discord channel for this ticket if Discord integration is enabled
        if (config('services.discord.enabled', false)) {
            $this->createDiscordChannel($ticket);
        }

        return redirect()->route('tickets.show', $ticket)
            ->with('success', 'Ticket created successfully.');
    }

    private function createDiscordChannel(Ticket $ticket)
    {
        try {
            $botApiUrl = config('services.discord.bot_url');
            $endpoint = "{$botApiUrl}/api/tickets/create-channel";
            $apiKey = config('services.discord.api_key');

            $response = Http::withHeaders([
                'Authorization' => "Bearer {$apiKey}",
                'Accept' => 'application/json'
            ])->post($endpoint, [
                'ticket_id' => $ticket->id,
                'user_id' => $ticket->user_id,
                'title' => $ticket->title,
            ]);

            if ($response->successful()) {
                $data = $response->json();
                $ticket->discord_channel_id = $data['channel_id'];
                $ticket->save();
            }
        } catch (\Exception $e) {
            Log::error("Failed to create Discord channel for ticket {$ticket->id}: {$e->getMessage()}");
        }
    }
}
```

## 4. Discord Bot Integration

### Step 4.1: Initial Server Setup

Implement a setup command to initialize the ticket system on a Discord server:

```javascript
// discord-bot/commands/setup.js
const { SlashCommandBuilder, PermissionFlagsBits, ActionRowBuilder, ButtonBuilder, ButtonStyle } = require('discord.js');
const { ChannelType } = require('discord.js');

module.exports = {
    data: new SlashCommandBuilder()
        .setName('setup-tickets')
        .setDescription('Set up the support ticket system')
        .setDefaultMemberPermissions(PermissionFlagsBits.Administrator)
        .addChannelOption(option =>
            option.setName('category')
                .setDescription('Category to create ticket channels in')
                .setRequired(true)
                .addChannelTypes(ChannelType.GuildCategory)),

    async execute(interaction) {
        const category = interaction.options.getChannel('category');

        try {
            // Create a ticket-panel channel
            const ticketChannel = await interaction.guild.channels.create({
                name: 'support-tickets',
                type: ChannelType.GuildText,
                parent: category.id,
                topic: 'Create a support ticket by clicking the button below.'
            });

            // Save the category ID to environment variables
            process.env.TICKET_CATEGORY_ID = category.id;

            // Create the ticket panel with a button
            const embed = {
                title: '🎫 Support Ticket System',
                description: 'Need help? Click the button below to create a support ticket. A team member will assist you as soon as possible.',
                color: 0x5865F2,
                fields: [
                    {
                        name: 'What happens when you create a ticket?',
                        value: 'A private channel will be created where you can discuss your issue with our support team.'
                    },
                    {
                        name: 'Guidelines',
                        value: '• Be respectful and patient\n• Provide as much detail as possible\n• One ticket per issue please'
                    }
                ],
                footer: {
                    text: 'Minewache Support System'
                }
            };

            const row = new ActionRowBuilder().addComponents(
                new ButtonBuilder()
                    .setCustomId('create_ticket')
                    .setLabel('Create Ticket')
                    .setEmoji('🎫')
                    .setStyle(ButtonStyle.Primary)
            );

            await ticketChannel.send({ embeds: [embed], components: [row] });

            // Create a log channel for ticket activities
            const logChannel = await interaction.guild.channels.create({
                name: 'ticket-logs',
                type: ChannelType.GuildText,
                parent: category.id,
                permissionOverwrites: [
                    {
                        id: interaction.guild.id,
                        deny: [PermissionFlagsBits.ViewChannel]
                    }
                ]
            });

            // Save the log channel ID to environment variables
            process.env.TICKET_LOG_CHANNEL_ID = logChannel.id;

            // Update the config file to persist these settings
            const fs = require('fs');
            const path = require('path');
            const envPath = path.join(__dirname, '..', '.env');

            let envContent = fs.readFileSync(envPath, 'utf8');
            envContent = envContent.replace(/TICKET_CATEGORY_ID=.*\n/, `TICKET_CATEGORY_ID=${category.id}\n`);

            if (!envContent.includes('TICKET_CATEGORY_ID=')) {
                envContent += `\nTICKET_CATEGORY_ID=${category.id}`;
            }

            if (!envContent.includes('TICKET_LOG_CHANNEL_ID=')) {
                envContent += `\nTICKET_LOG_CHANNEL_ID=${logChannel.id}`;
            } else {
                envContent = envContent.replace(/TICKET_LOG_CHANNEL_ID=.*\n/, `TICKET_LOG_CHANNEL_ID=${logChannel.id}\n`);
            }

            fs.writeFileSync(envPath, envContent);

            await interaction.reply({ content: `✅ Ticket system set up successfully! Created channels: ${ticketChannel} and ${logChannel}`, ephemeral: true });

            // Log the setup in the log channel
            await logChannel.send({
                embeds: [{
                    title: 'Ticket System Initialized',
                    description: `The ticket system was set up by ${interaction.user}`,
                    color: 0x00FF00,
                    timestamp: new Date()
                }]
            });
        } catch (error) {
            console.error(error);
            await interaction.reply({ content: `❌ Error setting up ticket system: ${error.message}`, ephemeral: true });
        }
    }
};
```

### Step 4.2: Button-Based Ticket Creation

Implement button handlers for ticket creation and management:

```javascript
// discord-bot/events/interactionCreate.js
const { ActionRowBuilder, ButtonBuilder, StringSelectMenuBuilder, ModalBuilder, TextInputBuilder } = require('discord.js');
const { ButtonStyle, TextInputStyle, PermissionFlagsBits } = require('discord.js');
const ticketService = require('../services/ticketService');

module.exports = {
    name: 'interactionCreate',
    async execute(interaction, client) {
        // Handle button interactions
        if (interaction.isButton()) {
            // Handle ticket creation button
            if (interaction.customId === 'create_ticket') {
                // Show a modal for ticket creation
                const modal = new ModalBuilder()
                    .setCustomId('create-ticket-modal')
                    .setTitle('Create Support Ticket');

                const titleInput = new TextInputBuilder()
                    .setCustomId('ticket-title')
                    .setLabel('Title')
                    .setStyle(TextInputStyle.Short)
                    .setPlaceholder('Brief description of your issue')
                    .setRequired(true)
                    .setMaxLength(100);

                const descriptionInput = new TextInputBuilder()
                    .setCustomId('ticket-description')
                    .setLabel('Description')
                    .setStyle(TextInputStyle.Paragraph)
                    .setPlaceholder('Please describe your issue in detail')
                    .setRequired(true);

                const firstActionRow = new ActionRowBuilder().addComponents(titleInput);
                const secondActionRow = new ActionRowBuilder().addComponents(descriptionInput);

                modal.addComponents(firstActionRow, secondActionRow);

                await interaction.showModal(modal);
            }
            // Handle ticket reply button
            else if (interaction.customId.startsWith('reply-ticket-')) {
                const ticketId = interaction.customId.replace('reply-ticket-', '');

                // Show a modal for reply
                const modal = new ModalBuilder()
                    .setCustomId(`reply-modal-${ticketId}`)
                    .setTitle('Reply to Ticket');

                const replyInput = new TextInputBuilder()
                    .setCustomId('ticket-reply')
                    .setLabel('Your Reply')
                    .setStyle(TextInputStyle.Paragraph)
                    .setPlaceholder('Type your reply here')
                    .setRequired(true);

                const actionRow = new ActionRowBuilder().addComponents(replyInput);
                modal.addComponents(actionRow);

                await interaction.showModal(modal);
            }
            // Handle ticket status change button
            else if (interaction.customId.startsWith('status-ticket-')) {
                const ticketId = interaction.customId.replace('status-ticket-', '');

                // Check if user is a supporter
                const isSupporter = await ticketService.isUserSupporter(interaction.user.id);
                if (!isSupporter) {
                    return interaction.reply({ content: 'Only supporters can change ticket status.', ephemeral: true });
                }

                // Create a select menu for status options
                const selectMenu = new StringSelectMenuBuilder()
                    .setCustomId(`status-select-${ticketId}`)
                    .setPlaceholder('Select a status')
                    .addOptions([
                        { label: 'Open', value: 'open', description: 'Ticket needs attention', emoji: '🔴' },
                        { label: 'In Progress', value: 'in_progress', description: 'Ticket is being worked on', emoji: '🟡' },
                        { label: 'Closed', value: 'closed', description: 'Ticket is resolved', emoji: '🟢' }
                    ]);

                const row = new ActionRowBuilder().addComponents(selectMenu);

                await interaction.reply({
                    content: 'Select a new status for the ticket:',
                    components: [row],
                    ephemeral: true
                });
            }
            // Handle ticket close button
            else if (interaction.customId.startsWith('close-ticket-')) {
                const ticketId = interaction.customId.replace('close-ticket-', '');

                // Create confirmation buttons
                const row = new ActionRowBuilder().addComponents(
                    new ButtonBuilder()
                        .setCustomId(`confirm-close-${ticketId}`)
                        .setLabel('Confirm Close')
                        .setStyle(ButtonStyle.Danger),
                    new ButtonBuilder()
                        .setCustomId('cancel-close')
                        .setLabel('Cancel')
                        .setStyle(ButtonStyle.Secondary)
                );

                await interaction.reply({
                    content: 'Are you sure you want to close this ticket?',
                    components: [row],
                    ephemeral: true
                });
            }
            // Handle ticket close confirmation
            else if (interaction.customId.startsWith('confirm-close-')) {
                const ticketId = interaction.customId.replace('confirm-close-', '');

                try {
                    // Update ticket status to closed
                    await ticketService.updateTicketStatus(ticketId, 'closed');

                    await interaction.reply({ content: 'Ticket closed successfully.', ephemeral: true });

                    // Get the ticket channel
                    const ticket = await ticketService.getTicket(ticketId);
                    if (ticket && ticket.discord_channel_id) {
                        const channel = client.channels.cache.get(ticket.discord_channel_id);
                        if (channel) {
                            // Send a message to the channel
                            await channel.send({
                                embeds: [{
                                    title: 'Ticket Closed',
                                    description: `This ticket has been closed by ${interaction.user}.`,
                                    color: 0x57F287,
                                    timestamp: new Date()
                                }]
                            });

                            // Update channel name to indicate it's closed
                            await channel.setName(`closed-${ticketId}`);
                        }
                    }
                } catch (error) {
                    console.error(error);
                    await interaction.reply({ content: 'There was an error closing the ticket.', ephemeral: true });
                }
            }
            // Handle cancel close
            else if (interaction.customId === 'cancel-close') {
                await interaction.reply({ content: 'Ticket close cancelled.', ephemeral: true });
            }
            // Handle view my tickets button
            else if (interaction.customId === 'view_my_tickets') {
                // Fetch user's tickets from the API
                const tickets = await ticketService.getUserTickets(interaction.user.id);

                if (tickets.length === 0) {
                    return interaction.reply({ content: 'You don\'t have any tickets.', ephemeral: true });
                }

                // Create a select menu for tickets
                const selectMenu = new StringSelectMenuBuilder()
                    .setCustomId('ticket-select')
                    .setPlaceholder('Select a ticket to view')
                    .addOptions(tickets.map(ticket => ({
                        label: `#${ticket.id}: ${ticket.title.substring(0, 90)}`,
                        description: `Status: ${ticket.status}`,
                        value: ticket.id.toString(),
                        emoji: ticket.status === 'open' ? '🔴' : (ticket.status === 'in_progress' ? '🟡' : '🟢')
                    })));

                const row = new ActionRowBuilder().addComponents(selectMenu);

                await interaction.reply({
                    content: 'Your support tickets:',
                    components: [row],
                    ephemeral: true
                });
            }
        }
    }
};
```

### Step 4.3: Modal and Select Menu Handlers

Implement handlers for modals and select menus:

```javascript
// discord-bot/events/interactionCreate.js - continued
module.exports = {
    name: 'interactionCreate',
    async execute(interaction, client) {
        // Handle modals (like ticket creation and replies)
        if (interaction.isModalSubmit()) {
            // Handle ticket creation modal
            if (interaction.customId === 'create-ticket-modal') {
                const title = interaction.fields.getTextInputValue('ticket-title');
                const description = interaction.fields.getTextInputValue('ticket-description');

                try {
                    const ticket = await ticketService.createTicket({
                        user_id: interaction.user.id,
                        title,
                        description
                    });

                    // Create a private channel for this ticket
                    await ticketService.createTicketChannel(ticket, interaction);

                    // Send confirmation with buttons to view the ticket
                    const row = new ActionRowBuilder().addComponents(
                        new ButtonBuilder()
                            .setURL(`https://discord.com/channels/${interaction.guild.id}/${ticket.discord_channel_id}`)
                            .setLabel('View Ticket Channel')
                            .setStyle(ButtonStyle.Link)
                    );

                    await interaction.reply({
                        content: `✅ Ticket #${ticket.id} created successfully! A private channel has been created for your ticket.`,
                        components: [row],
                        ephemeral: true
                    });

                    // Log the ticket creation to the log channel
                    const logChannelId = process.env.TICKET_LOG_CHANNEL_ID;
                    if (logChannelId) {
                        const logChannel = client.channels.cache.get(logChannelId);
                        if (logChannel) {
                            await logChannel.send({
                                embeds: [{
                                    title: 'New Ticket Created',
                                    description: `Ticket #${ticket.id} created by ${interaction.user}`,
                                    fields: [
                                        { name: 'Title', value: title },
                                        { name: 'Channel', value: `<#${ticket.discord_channel_id}>` }
                                    ],
                                    color: 0x5865F2,
                                    timestamp: new Date()
                                }]
                            });
                        }
                    }
                } catch (error) {
                    console.error(error);
                    await interaction.reply({ content: '❌ There was an error creating your ticket!', ephemeral: true });
                }
            }
            // Handle ticket reply modal
            else if (interaction.customId.startsWith('reply-modal-')) {
                const ticketId = interaction.customId.replace('reply-modal-', '');
                const reply = interaction.fields.getTextInputValue('ticket-reply');

                try {
                    // Add the reply to the ticket
                    const message = await ticketService.addMessage(ticketId, {
                        user_id: interaction.user.id,
                        message: reply,
                        is_from_discord: true
                    });

                    await interaction.reply({ content: '✅ Reply added successfully!', ephemeral: true });

                    // Get the ticket channel
                    const ticket = await ticketService.getTicket(ticketId);
                    if (ticket && ticket.discord_channel_id) {
                        const channel = client.channels.cache.get(ticket.discord_channel_id);
                        if (channel) {
                            // Send the reply to the channel
                            await channel.send({
                                content: `**${interaction.user}:** ${reply}`
                            });
                        }
                    }
                } catch (error) {
                    console.error(error);
                    await interaction.reply({ content: '❌ There was an error adding your reply!', ephemeral: true });
                }
            }
        }
        // Handle select menus
        else if (interaction.isStringSelectMenu()) {
            // Handle ticket selection from list
            if (interaction.customId === 'ticket-select') {
                const ticketId = interaction.values[0];
                const ticket = await ticketService.getTicket(ticketId);

                if (!ticket) {
                    return interaction.reply({ content: '❌ Ticket not found.', ephemeral: true });
                }

                // Create buttons for ticket actions
                const row = new ActionRowBuilder().addComponents(
                    new ButtonBuilder()
                        .setCustomId(`reply-ticket-${ticketId}`)
                        .setLabel('Reply')
                        .setEmoji('💬')
                        .setStyle(ButtonStyle.Primary),
                    new ButtonBuilder()
                        .setURL(`https://discord.com/channels/${interaction.guild.id}/${ticket.discord_channel_id}`)
                        .setLabel('Go to Channel')
                        .setEmoji('🔗')
                        .setStyle(ButtonStyle.Link)
                );

                // Add status change button for supporters
                if (await ticketService.isUserSupporter(interaction.user.id)) {
                    row.addComponents(
                        new ButtonBuilder()
                            .setCustomId(`status-ticket-${ticketId}`)
                            .setLabel('Change Status')
                            .setEmoji('🔄')
                            .setStyle(ButtonStyle.Secondary),
                        new ButtonBuilder()
                            .setCustomId(`close-ticket-${ticketId}`)
                            .setLabel('Close')
                            .setEmoji('🔒')
                            .setStyle(ButtonStyle.Danger)
                    );
                }

                // Format ticket details
                const embed = {
                    title: `Ticket #${ticket.id}: ${ticket.title}`,
                    description: ticket.description,
                    fields: [
                        { name: 'Status', value: ticket.status === 'open' ? '🔴 Open' : (ticket.status === 'in_progress' ? '🟡 In Progress' : '🟢 Closed'), inline: true },
                        { name: 'Created by', value: `<@${ticket.user_id}>`, inline: true },
                        { name: 'Created at', value: new Date(ticket.created_at).toLocaleString(), inline: true },
                        { name: 'Assigned to', value: ticket.assigned_to ? `<@${ticket.assigned_to}>` : 'Unassigned', inline: true }
                    ],
                    color: ticket.status === 'open' ? 0xED4245 : (ticket.status === 'in_progress' ? 0xFEE75C : 0x57F287),
                    footer: {
                        text: 'Minewache Support System'
                    }
                };

                await interaction.reply({
                    embeds: [embed],
                    components: [row],
                    ephemeral: true
                });
            }
            // Handle status change select menu
            else if (interaction.customId.startsWith('status-select-')) {
                const ticketId = interaction.customId.replace('status-select-', '');
                const newStatus = interaction.values[0];

                try {
                    // Update the ticket status
                    await ticketService.updateTicketStatus(ticketId, newStatus);

                    // Get status emoji
                    const statusEmoji = newStatus === 'open' ? '🔴' : (newStatus === 'in_progress' ? '🟡' : '🟢');

                    await interaction.reply({
                        content: `${statusEmoji} Ticket status updated to **${newStatus.replace('_', ' ')}**.`,
                        ephemeral: true
                    });

                    // Get the ticket channel
                    const ticket = await ticketService.getTicket(ticketId);
                    if (ticket && ticket.discord_channel_id) {
                        const channel = client.channels.cache.get(ticket.discord_channel_id);
                        if (channel) {
                            // Send a message to the channel
                            await channel.send({
                                embeds: [{
                                    title: 'Ticket Status Updated',
                                    description: `${interaction.user} changed the status to **${statusEmoji} ${newStatus.replace('_', ' ')}**`,
                                    color: newStatus === 'open' ? 0xED4245 : (newStatus === 'in_progress' ? 0xFEE75C : 0x57F287),
                                    timestamp: new Date()
                                }]
                            });
                        }
                    }
                } catch (error) {
                    console.error(error);
                    await interaction.reply({ content: '❌ There was an error updating the ticket status.', ephemeral: true });
                }
            }
        }
    }
};
```

### Step 4.4: Add Thread/Channel Management

Implement a service to manage Discord threads or channels for tickets:

```javascript
// discord-bot/services/ticketService.js
const axios = require('axios');
const { ChannelType, PermissionFlagsBits } = require('discord.js');
const { logWithTimestamp } = require('../utils/logger');

class TicketService {
    constructor() {
        this.apiUrl = process.env.LARAVEL_API_URL || 'http://localhost:8000/api';
        this.apiKey = process.env.API_KEY;
        this.ticketCategoryId = process.env.TICKET_CATEGORY_ID;
        this.guildId = process.env.GUILD_ID;
    }

    async createTicket(ticketData) {
        try {
            const response = await axios.post(`${this.apiUrl}/tickets/create`, ticketData, {
                headers: {
                    'Authorization': `Bearer ${this.apiKey}`,
                    'Content-Type': 'application/json'
                }
            });

            return response.data.ticket;
        } catch (error) {
            logWithTimestamp(`Error creating ticket: ${error.message}`);
            throw error;
        }
    }

    async getUserTickets(userId) {
        try {
            const response = await axios.get(`${this.apiUrl}/tickets/user/${userId}`, {
                headers: {
                    'Authorization': `Bearer ${this.apiKey}`
                }
            });

            return response.data.tickets;
        } catch (error) {
            logWithTimestamp(`Error fetching user tickets: ${error.message}`);
            return [];
        }
    }

    async getTicket(ticketId) {
        try {
            const response = await axios.get(`${this.apiUrl}/tickets/${ticketId}`, {
                headers: {
                    'Authorization': `Bearer ${this.apiKey}`
                }
            });

            return response.data.ticket;
        } catch (error) {
            logWithTimestamp(`Error fetching ticket: ${error.message}`);
            return null;
        }
    }

    async addMessage(ticketId, messageData) {
        try {
            const response = await axios.post(`${this.apiUrl}/tickets/${ticketId}/messages`, messageData, {
                headers: {
                    'Authorization': `Bearer ${this.apiKey}`,
                    'Content-Type': 'application/json'
                }
            });

            return response.data.message;
        } catch (error) {
            logWithTimestamp(`Error adding message: ${error.message}`);
            throw error;
        }
    }

    async updateTicketStatus(ticketId, status) {
        try {
            const response = await axios.post(`${this.apiUrl}/tickets/${ticketId}/status`, { status }, {
                headers: {
                    'Authorization': `Bearer ${this.apiKey}`,
                    'Content-Type': 'application/json'
                }
            });

            return response.data.ticket;
        } catch (error) {
            logWithTimestamp(`Error updating ticket status: ${error.message}`);
            throw error;
        }
    }

    async isUserSupporter(userId) {
        try {
            const response = await axios.get(`${this.apiUrl}/users/${userId}/roles`, {
                headers: {
                    'Authorization': `Bearer ${this.apiKey}`
                }
            });

            // Check if user has MINEWACHE_TEAM role
            return response.data.roles.some(role => role === 'MINEWACHE_TEAM');
        } catch (error) {
            logWithTimestamp(`Error checking user roles: ${error.message}`);
            return false;
        }
    }

    async createTicketChannel(ticket, interaction) {
        const client = interaction.client;
        const guild = client.guilds.cache.get(this.guildId);

        if (!guild) {
            throw new Error('Guild not found');
        }

        try {
            // Create a private channel for the ticket
            const channel = await guild.channels.create({
                name: `ticket-${ticket.id}`,
                type: ChannelType.GuildText,
                parent: this.ticketCategoryId,
                permissionOverwrites: [
                    {
                        id: guild.id, // @everyone role
                        deny: [PermissionFlagsBits.ViewChannel]
                    },
                    {
                        id: ticket.user_id,
                        allow: [PermissionFlagsBits.ViewChannel, PermissionFlagsBits.SendMessages, PermissionFlagsBits.ReadMessageHistory]
                    }
                ]
            });

            // Add permissions for supporters
            const supporterRole = await this.getSupporterRoleId(guild);
            if (supporterRole) {
                await channel.permissionOverwrites.create(supporterRole, {
                    ViewChannel: true,
                    SendMessages: true,
                    ReadMessageHistory: true
                });
            }

            // Send initial message with ticket details and buttons
            const embed = {
                title: `Ticket #${ticket.id}: ${ticket.title}`,
                description: ticket.description,
                fields: [
                    { name: 'Status', value: ticket.status, inline: true },
                    { name: 'Created by', value: `<@${ticket.user_id}>`, inline: true },
                    { name: 'Created at', value: new Date(ticket.created_at).toLocaleString(), inline: true }
                ],
                color: 0x5865F2
            };

            const row = new ActionRowBuilder().addComponents(
                new ButtonBuilder()
                    .setCustomId(`status-ticket-${ticket.id}`)
                    .setLabel('Change Status')
                    .setStyle(ButtonStyle.Secondary),
                new ButtonBuilder()
                    .setCustomId(`assign-ticket-${ticket.id}`)
                    .setLabel('Assign Ticket')
                    .setStyle(ButtonStyle.Primary),
                new ButtonBuilder()
                    .setCustomId(`close-ticket-${ticket.id}`)
                    .setLabel('Close Ticket')
                    .setStyle(ButtonStyle.Danger)
            );

            await channel.send({ content: `<@${ticket.user_id}> created a new ticket:`, embeds: [embed], components: [row] });

            // Update the ticket with the Discord channel ID
            await axios.post(`${this.apiUrl}/tickets/${ticket.id}/update-channel`, {
                discord_channel_id: channel.id
            }, {
                headers: {
                    'Authorization': `Bearer ${this.apiKey}`,
                    'Content-Type': 'application/json'
                }
            });

            return channel;
        } catch (error) {
            logWithTimestamp(`Error creating ticket channel: ${error.message}`);
            throw error;
        }
    }

    async getSupporterRoleId(guild) {
        try {
            // Get role mapping from API
            const response = await axios.get(`${this.apiUrl}/debug/role-mapping`, {
                headers: {
                    'Authorization': `Bearer ${this.apiKey}`
                }
            });

            const roleMapping = response.data.data.role_mappings;
            const minewacheTeamRoleId = Object.entries(roleMapping)
                .find(([discordRoleId, roleName]) => roleName === 'MINEWACHE_TEAM')?.[0];

            return minewacheTeamRoleId;
        } catch (error) {
            logWithTimestamp(`Error getting supporter role ID: ${error.message}`);
            return null;
        }
    }
}

module.exports = new TicketService();
```

### Step 4.5: Add Message Synchronization

Implement event listeners to sync messages between Discord and the web app:

```javascript
// discord-bot/events/messageCreate.js
const ticketService = require('../services/ticketService');
const { logWithTimestamp } = require('../utils/logger');
const fs = require('fs');
const path = require('path');
const axios = require('axios');

module.exports = {
    name: 'messageCreate',
    async execute(message) {
        // Ignore bot messages to prevent loops
        if (message.author.bot) return;

        // Check if the message is in a ticket channel
        const channelName = message.channel.name;
        if (!channelName.startsWith('ticket-') && !channelName.startsWith('closed-')) return;

        try {
            // Extract ticket ID from channel name
            const ticketId = channelName.replace(/^(ticket-|closed-)/, '');

            // Create message data
            const messageData = {
                user_id: message.author.id,
                message: message.content,
                is_from_discord: true,
                discord_message_id: message.id
            };

            // Handle attachments
            if (message.attachments.size > 0) {
                const attachments = [];

                // Process each attachment
                for (const [id, attachment] of message.attachments) {
                    try {
                        // Create a temporary directory for downloads if it doesn't exist
                        const tempDir = path.join(__dirname, '..', 'temp');
                        if (!fs.existsSync(tempDir)) {
                            fs.mkdirSync(tempDir, { recursive: true });
                        }

                        // Download the file
                        const tempFilePath = path.join(tempDir, attachment.name);
                        const response = await axios({
                            method: 'get',
                            url: attachment.url,
                            responseType: 'stream'
                        });

                        // Save the file locally
                        const writer = fs.createWriteStream(tempFilePath);
                        response.data.pipe(writer);

                        await new Promise((resolve, reject) => {
                            writer.on('finish', resolve);
                            writer.on('error', reject);
                        });

                        // Upload the file to the Laravel API
                        const formData = new FormData();
                        formData.append('file', fs.createReadStream(tempFilePath));
                        formData.append('original_filename', attachment.name);
                        formData.append('mime_type', attachment.contentType);
                        formData.append('file_size', attachment.size);
                        formData.append('discord_attachment_id', id);

                        const uploadResponse = await axios.post(
                            `${process.env.LARAVEL_API_URL}/tickets/attachments/upload`,
                            formData,
                            {
                                headers: {
                                    'Authorization': `Bearer ${process.env.API_KEY}`,
                                    'Content-Type': 'multipart/form-data'
                                }
                            }
                        );

                        // Add the attachment ID to the list
                        if (uploadResponse.data.success) {
                            attachments.push(uploadResponse.data.attachment_id);
                        }

                        // Clean up the temporary file
                        fs.unlinkSync(tempFilePath);
                    } catch (attachmentError) {
                        logWithTimestamp(`Error processing attachment: ${attachmentError.message}`);
                        // Continue with other attachments even if one fails
                    }
                }

                // Add attachment IDs to the message data
                if (attachments.length > 0) {
                    messageData.attachment_ids = attachments;
                }
            }

            // Sync message to the web app
            await ticketService.addMessage(ticketId, messageData);

            // React to the message to indicate it was synced
            await message.react('✅');
        } catch (error) {
            logWithTimestamp(`Error syncing message: ${error.message}`);
            await message.react('❌');
        }
    }
};
```

### Step 4.6: Add API Endpoints in Discord Bot

Implement API endpoints for Laravel to call:

```javascript
// Add to discord-bot/index.js
// API Routes for ticket management

app.post('/api/tickets/create-channel', authenticateToken, async (req, res) => {
    const { ticket_id, user_id, title } = req.body;

    if (!ticket_id || !user_id || !title) {
        return res.status(400).json({ success: false, message: 'Missing required parameters' });
    }

    try {
        const guild = client.guilds.cache.get(GUILD_ID);
        if (!guild) {
            return res.status(404).json({ success: false, message: 'Guild not found' });
        }

        // Create a private channel for the ticket
        const channel = await guild.channels.create({
            name: `ticket-${ticket_id}`,
            type: ChannelType.GuildText,
            parent: process.env.TICKET_CATEGORY_ID,
            permissionOverwrites: [
                {
                    id: guild.id, // @everyone role
                    deny: [PermissionFlagsBits.ViewChannel]
                },
                {
                    id: user_id,
                    allow: [PermissionFlagsBits.ViewChannel, PermissionFlagsBits.SendMessages, PermissionFlagsBits.ReadMessageHistory]
                }
            ]
        });

        // Add permissions for supporters
        const supporterRoleId = await ticketService.getSupporterRoleId(guild);
        if (supporterRoleId) {
            await channel.permissionOverwrites.create(supporterRoleId, {
                ViewChannel: true,
                SendMessages: true,
                ReadMessageHistory: true
            });
        }

        // Send initial message with ticket details
        const embed = {
            title: `Ticket #${ticket_id}: ${title}`,
            description: req.body.description || 'No description provided',
            fields: [
                { name: 'Status', value: 'open', inline: true },
                { name: 'Created by', value: `<@${user_id}>`, inline: true },
                { name: 'Created at', value: new Date().toLocaleString(), inline: true }
            ],
            color: 0x5865F2
        };

        const row = new ActionRowBuilder().addComponents(
            new ButtonBuilder()
                .setCustomId(`status-ticket-${ticket_id}`)
                .setLabel('Change Status')
                .setStyle(ButtonStyle.Secondary),
            new ButtonBuilder()
                .setCustomId(`assign-ticket-${ticket_id}`)
                .setLabel('Assign Ticket')
                .setStyle(ButtonStyle.Primary),
            new ButtonBuilder()
                .setCustomId(`close-ticket-${ticket_id}`)
                .setLabel('Close Ticket')
                .setStyle(ButtonStyle.Danger)
        );

        await channel.send({ content: `<@${user_id}> created a new ticket:`, embeds: [embed], components: [row] });

        return res.json({
            success: true,
            channel_id: channel.id,
            message: 'Ticket channel created successfully'
        });
    } catch (error) {
        logWithTimestamp(`Error creating ticket channel: ${error.message}`);
        return res.status(500).json({ success: false, message: `Error creating ticket channel: ${error.message}` });
    }
});

app.post('/api/tickets/send-message', authenticateToken, async (req, res) => {
    const { channel_id, user_id, message, ticket_id, message_id, attachments } = req.body;

    if (!channel_id || !user_id || !message) {
        return res.status(400).json({ success: false, message: 'Missing required parameters' });
    }

    try {
        const channel = client.channels.cache.get(channel_id);
        if (!channel) {
            return res.status(404).json({ success: false, message: 'Channel not found' });
        }

        // Handle attachments if present
        const files = [];
        if (attachments && attachments.length > 0) {
            for (const attachment of attachments) {
                try {
                    // Download the file from the Laravel API
                    const response = await axios({
                        method: 'get',
                        url: attachment.download_url,
                        responseType: 'arraybuffer',
                        headers: {
                            'Authorization': `Bearer ${process.env.API_KEY}`
                        }
                    });

                    // Add to files array for Discord
                    files.push({
                        attachment: Buffer.from(response.data),
                        name: attachment.filename,
                        description: `Attachment for ticket #${ticket_id}`
                    });
                } catch (attachmentError) {
                    logWithTimestamp(`Error downloading attachment: ${attachmentError.message}`);
                    // Continue with other attachments even if one fails
                }
            }
        }

        // Send message to Discord channel
        const sentMessage = await channel.send({
            content: `**<@${user_id}>:** ${message}`,
            files: files
        });

        return res.json({
            success: true,
            discord_message_id: sentMessage.id,
            message: 'Message sent successfully'
        });
    } catch (error) {
        logWithTimestamp(`Error sending message: ${error.message}`);
        return res.status(500).json({ success: false, message: `Error sending message: ${error.message}` });
    }
});

app.post('/api/tickets/update-channel', authenticateToken, async (req, res) => {
    const { channel_id, status, ticket_id } = req.body;

    if (!channel_id || !status || !ticket_id) {
        return res.status(400).json({ success: false, message: 'Missing required parameters' });
    }

    try {
        const channel = client.channels.cache.get(channel_id);
        if (!channel) {
            return res.status(404).json({ success: false, message: 'Channel not found' });
        }

        // Send status update message
        await channel.send({
            embeds: [{
                title: `Ticket Status Updated`,
                description: `This ticket's status has been changed to: **${status}**`,
                color: status === 'open' ? 0xED4245 : (status === 'in_progress' ? 0xFEE75C : 0x57F287),
                timestamp: new Date()
            }]
        });

        // If ticket is closed, update channel name
        if (status === 'closed') {
            await channel.setName(`closed-${ticket_id}`);
        }

        return res.json({
            success: true,
            message: 'Channel updated successfully'
        });
    } catch (error) {
        logWithTimestamp(`Error updating channel: ${error.message}`);
        return res.status(500).json({ success: false, message: `Error updating channel: ${error.message}` });
    }
});
```

## 5. Synchronization Strategy

### Step 5.1: Define API Communication Flow

#### From Web to Discord:
1. User creates/replies to a ticket on the web
2. Laravel calls Discord bot API to create channel/send message
3. Discord bot processes the request and updates Discord

#### From Discord to Web:
1. User creates/replies to a ticket on Discord
2. Discord bot calls Laravel API to create/update ticket
3. Laravel processes the request and updates database

### Step 5.2: Implement Error Handling

Implement error handling and retry logic for API calls:
- Handle network errors
- Implement retry mechanism for failed API calls
- Log errors for debugging

## 6. Testing

### Step 6.1: Create Unit Tests

Create unit tests for:
- Ticket and TicketMessage models
- TicketController methods
- Authorization policies

### Step 6.2: Create Feature Tests

Create feature tests for:
- Ticket creation flow
- Ticket reply flow
- Ticket assignment and status changes

### Step 6.3: Test Discord Integration

Test the integration between Laravel and Discord:
- Test creating tickets from both platforms
- Test replying from both platforms
- Test synchronization between platforms

## 7. Setup and Configuration

### Step 7.1: Initial Setup

#### Laravel Setup

1. Create the necessary models and migrations:
```bash
php artisan make:model Ticket -m
php artisan make:model TicketMessage -m
php artisan make:model TicketAttachment -m
```

2. Create the controllers and policies:
```bash
php artisan make:controller TicketController --resource
php artisan make:controller TicketApiController --api
php artisan make:policy TicketPolicy --model=Ticket
```

3. Create the Livewire components:
```bash
php artisan make:livewire TicketList
php artisan make:livewire TicketView
php artisan make:livewire TicketCreate
```

4. Create storage directories for attachments:
```bash
php artisan storage:link
mkdir -p storage/app/ticket-attachments
chmod -R 775 storage/app/ticket-attachments
```

#### Discord Bot Setup

1. Create new command files in the Discord bot directory:
```bash
cd discord-bot
mkdir -p temp
touch commands/setup-tickets.js
touch commands/support.js
touch services/ticketService.js
touch events/messageCreate.js
```

### Step 7.2: Database Migration

Run database migrations on the production server:
```bash
php artisan migrate
```

### Step 7.3: Update Discord Bot

Deploy the updated Discord bot code to the production server:
```bash
cd discord-bot
npm install
pm2 restart bot
```

### Step 7.4: Configure Environment Variables

Update environment variables on both Laravel and Discord bot:
```
# Laravel .env
DISCORD_BOT_API_URL=http://localhost:3000
DISCORD_API_KEY=your_api_key
TICKET_CATEGORY_ID=discord_category_id_for_tickets
TICKET_STORAGE_PATH=ticket-attachments

# Discord bot .env
LARAVEL_API_URL=http://your-laravel-app.com/api
API_KEY=your_api_key
TICKET_CATEGORY_ID=discord_category_id_for_tickets
TICKET_LOG_CHANNEL_ID=discord_log_channel_id
GUILD_ID=your_discord_server_id
```

## Detailed Implementation Plan

### Phase 1: Database and Models

1. Create migrations for `tickets` and `ticket_messages` tables
2. Create Eloquent models with relationships
3. Update the `Role` enum to include the `SUPPORTER` role
4. Run migrations and test the models

### Phase 2: Backend Logic

1. Create `TicketController` with basic CRUD operations
2. Create `TicketPolicy` for authorization
3. Add API routes for Discord bot integration
4. Implement service classes for ticket management

### Phase 3: Frontend Components

1. Create `TicketList` Livewire component
2. Create `TicketView` Livewire component
3. Create `TicketCreate` Livewire component
4. Add routes and views for ticket management

### Phase 4: Discord Bot Integration

1. Add ticket-related slash commands to the Discord bot
2. Implement channel/thread management logic
3. Add event listeners for Discord messages
4. Add API endpoints for Laravel to call

### Phase 5: Testing and Deployment

1. Create unit and feature tests
2. Test the integration between Laravel and Discord
3. Deploy the changes to production
4. Monitor for issues and fix bugs

## Conclusion

This development plan outlines the steps needed to implement a synchronized support ticket system that works across both the Laravel web application and the Discord bot. By following this plan, we can create a seamless experience for users and supporters, allowing them to interact with tickets on their preferred platform.
