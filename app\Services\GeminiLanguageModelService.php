<?php

namespace App\Services;

use App\Contracts\LanguageModelInterface;
use App\Models\Ticket;
use App\Models\User;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;

class GeminiLanguageModelService implements LanguageModelInterface
{
    /**
     * The API key for the Gemini API.
     *
     * @var string|null
     */
    protected ?string $apiKey = null;

    /**
     * The base URL for the Gemini API, updated for Gemini 2.0 Flash.
     *
     * @var string
     */
    protected string $apiUrl = 'https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash:generateContent'; // Updated for Gemini 2.0 Flash

    /**
     * Create a new GeminiLanguageModelService instance.
     *
     * @param string|null $apiKey
     */
    public function __construct(?string $apiKey = null)
    {
        $this->apiKey = $apiKey ?? config('services.gemini.api_key');
    }

    /**
     * Generate a response to a ticket message.
     *
     * @param Ticket $ticket The ticket to generate a response for
     * @param User $user The user who will receive the response
     * @param string|null $systemPrompt Optional custom system prompt
     * @return string The generated response
     */
    public function generateTicketResponse(Ticket $ticket, User $user, ?string $systemPrompt = null): string
    {
        Log::debug('GeminiLanguageModelService: Starting response generation', [
            'ticket_id' => $ticket->id,
            'user_id' => $user->id,
            'has_api_key' => !empty($this->apiKey)
        ]);

        if (empty($this->apiKey)) {
            Log::error('GeminiLanguageModelService: Gemini API key is not configured.');
            return $this->getFallbackResponse($this->determineLanguage($user));
        }

        // Sammle Benutzerdaten basierend auf den Berechtigungen
        $userData = $this->collectUserData($ticket->user);
        Log::debug('GeminiLanguageModelService: Collected user data', [
            'ticket_id' => $ticket->id,
            'user_id' => $ticket->user->id,
            'data_types' => array_keys($userData)
        ]);

        // Prüfe, ob wir zusätzliche Daten anfordern sollten
        $requestedDataTypes = $this->checkForDataRequest($ticket);
        if (!empty($requestedDataTypes)) {
            Log::debug('GeminiLanguageModelService: AI is requesting additional data', [
                'ticket_id' => $ticket->id,
                'requested_data_types' => $requestedDataTypes
            ]);

            // Erstelle eine Antwort, die nach Daten fragt
            return $this->createDataRequestResponse($requestedDataTypes, $this->determineLanguage($user));
        }

        try {
            // Determine the language based on user locale
            $language = $this->determineLanguage($user);
            Log::debug('GeminiLanguageModelService: Determined language', [
                'language' => $language,
                'user_locale' => $user->locale
            ]);

            // Get the ticket context
            $context = $this->buildTicketContext($ticket, $userData);
            Log::debug('GeminiLanguageModelService: Built ticket context', [
                'ticket_id' => $ticket->id,
                'context_length' => strlen($context),
                'message_count' => $ticket->messages->count()
            ]);

            // Build the system prompt
            $systemPromptText = $systemPrompt ?? $this->getDefaultSystemPrompt($language);
            Log::debug('GeminiLanguageModelService: Using system prompt', [
                'prompt_length' => strlen($systemPromptText),
                'is_custom' => $systemPrompt !== null
            ]);

            // Build the request payload
            $payload = [
                'contents' => [
                    [
                        'role' => 'user', // Role is valid for the 'contents' structure
                        'parts' => [
                            ['text' => $systemPromptText . "\n\n" . $context]
                        ]
                    ]
                ],
                'generationConfig' => [
                    'temperature' => 0.7,
                    'topK' => 40,
                    'topP' => 0.95,
                    'maxOutputTokens' => 1024,
                ]
            ];

            Log::debug('GeminiLanguageModelService: Prepared API payload', [
                'payload_size' => strlen(json_encode($payload)),
                'temperature' => 0.7,
                'maxOutputTokens' => 1024
            ]);

            // Construct the full API URL with the key
            $urlWithKey = $this->apiUrl . '?key=' . $this->apiKey;

            Log::debug('GeminiLanguageModelService: Sending request to Gemini API', [
                'api_url' => $this->apiUrl, // Log base URL without key
                'api_key_length' => strlen($this->apiKey)
            ]);

            // Make the API request using POST with JSON body
            $response = Http::withHeaders([
                'Content-Type' => 'application/json',
            ])->post($urlWithKey, $payload); // Changed to POST and payload sent as request body

            if ($response->successful()) {
                $responseData = $response->json();
                Log::debug('GeminiLanguageModelService: Received successful response from Gemini API', [
                    'status_code' => $response->status(),
                    'response_size' => strlen($response->body()),
                    'has_candidates' => isset($responseData['candidates'])
                ]);

                // Navigate through the response structure to get the text
                // Based on typical Gemini API responses for generateContent
                if (isset($responseData['candidates'][0]['content']['parts'][0]['text'])) {
                    $generatedText = $responseData['candidates'][0]['content']['parts'][0]['text'];
                    Log::debug('GeminiLanguageModelService: Successfully extracted text from response', [
                        'text_length' => strlen($generatedText),
                        'text_preview' => substr($generatedText, 0, 100) . '...'
                    ]);
                    return $generatedText;
                }

                Log::warning('GeminiLanguageModelService: Gemini API response format unexpected.', [
                    'response' => $responseData,
                    'response_keys' => array_keys($responseData)
                ]);
                return $this->getFallbackResponse($language);
            } else {
                Log::error('GeminiLanguageModelService: Gemini API error', [
                    'status' => $response->status(),
                    'response' => $response->body(),
                    'url' => $this->apiUrl, // Log the base URL without key for security
                    'headers' => $response->headers()
                ]);
                return $this->getFallbackResponse($language);
            }
        } catch (\Exception $e) {
            Log::error('GeminiLanguageModelService: Error generating Gemini response', [
                'exception' => $e->getMessage(),
                'ticket_id' => $ticket->id,
                'trace' => $e->getTraceAsString()
            ]);
            return $this->getFallbackResponse($language ?? 'de');
        }
    }

    /**
     * Set the API key for the language model.
     *
     * @param string $apiKey The API key
     * @return void
     */
    public function setApiKey(string $apiKey): void
    {
        $this->apiKey = $apiKey;
    }

    /**
     * Get the name of the language model.
     *
     * @return string The name of the language model
     */
    public function getModelName(): string
    {
        return 'Gemini 2.0 Flash'; // Updated model name
    }

    /**
     * Determine the language based on user locale.
     *
     * @param User $user
     * @return string 'de' or 'en'
     */
    protected function determineLanguage(User $user): string
    {
        // Default to German if no locale is set or if it's German
        $locale = $user->locale ?? 'de';

        // If locale starts with 'de', use German, otherwise use English
        return str_starts_with($locale, 'de') ? 'de' : 'en';
    }

    /**
     * Collect user data based on consent.
     *
     * @param User $user
     * @return array
     */
    protected function collectUserData(User $user): array
    {
        $userData = [];

        // Immer den Benutzernamen hinzufügen, wenn die Zustimmung gegeben wurde
        if ($user->hasConsentedToShareData('username')) {
            $userData['username'] = $user->username;
            $userData['global_name'] = $user->global_name;
        }

        // Benutzerrollen immer hinzufügen, da diese für die korrekte Bearbeitung wichtig sind
        $userData['roles'] = $this->getUserRoles($user);

        // Bewerbungsdaten hinzufügen, wenn die Zustimmung gegeben wurde
        if ($user->hasConsentedToShareData('applications')) {
            $applications = $user->applications()->get();
            if ($applications->count() > 0) {
                $userData['applications'] = [
                    'count' => $applications->count(),
                    'latest_status' => $applications->sortByDesc('created_at')->first()->status,
                ];
            }
        }

        // Ticket-Historie hinzufügen, wenn die Zustimmung gegeben wurde
        if ($user->hasConsentedToShareData('tickets')) {
            $tickets = $user->tickets()->get();
            if ($tickets->count() > 0) {
                $userData['tickets'] = [
                    'count' => $tickets->count(),
                    'open_count' => $tickets->where('status', '!=', 'closed')->count(),
                ];
            }
        }

        // Discord-Informationen hinzufügen, wenn die Zustimmung gegeben wurde
        if ($user->hasConsentedToShareData('discord_info')) {
            $userData['discord_info'] = [
                'discriminator' => $user->discriminator,
                'locale' => $user->locale,
            ];
        }

        return $userData;
    }

    /**
     * Get the user's roles.
     *
     * @param User $user
     * @return array
     */
    protected function getUserRoles(User $user): array
    {
        $roles = [];

        // Prüfe, ob der Benutzer ein Supporter ist
        if ($user->isSupporter()) {
            $roles[] = 'supporter';
        }

        // Prüfe, ob der Benutzer ein Admin ist
        if ($user->isAdmin()) {
            $roles[] = 'admin';
        }

        // Prüfe, ob der Benutzer ein Teammitglied ist
        if ($user->hasRole('MINEWACHE_TEAM')) {
            $roles[] = 'team_member';
        }

        // Prüfe, ob der Benutzer ein Moderator ist
        if ($user->hasRole('MODERATOR')) {
            $roles[] = 'moderator';
        }

        // Prüfe, ob der Benutzer ein Bewerber ist
        if ($user->hasRole('BEWERBER')) {
            $roles[] = 'applicant';
        }

        // Wenn keine Rollen gefunden wurden, ist es ein normaler Benutzer
        if (empty($roles)) {
            $roles[] = 'user';
        }

        return $roles;
    }

    /**
     * Check if the AI should request additional data from the user.
     *
     * @param Ticket $ticket
     * @return array List of data types to request
     */
    protected function checkForDataRequest(Ticket $ticket): array
    {
        $requestedDataTypes = [];
        $lastMessages = $ticket->messages()
            ->orderBy('created_at', 'desc')
            ->take(5)
            ->get()
            ->reverse();

        // Wenn keine Nachrichten vorhanden sind, keine Datenanfrage
        if ($lastMessages->isEmpty()) {
            return [];
        }

        // Prüfe, ob die letzte Nachricht vom Benutzer ist
        $lastMessage = $lastMessages->last();
        if ($lastMessage->message_source !== 'user') {
            return [];
        }

        // Prüfe, ob der Benutzer nach Informationen fragt, die wir nicht haben
        $user = $ticket->user;

        // Prüfe auf Bewerbungsbezogene Fragen
        if (
            $this->containsApplicationKeywords($lastMessage->message) &&
            !$user->hasConsentedToShareData('applications')
        ) {
            $requestedDataTypes[] = 'applications';
        }

        // Prüfe auf Ticket-Historie-bezogene Fragen
        if (
            $this->containsTicketHistoryKeywords($lastMessage->message) &&
            !$user->hasConsentedToShareData('tickets')
        ) {
            $requestedDataTypes[] = 'tickets';
        }

        // Prüfe auf Discord-bezogene Fragen
        if (
            $this->containsDiscordKeywords($lastMessage->message) &&
            !$user->hasConsentedToShareData('discord_info')
        ) {
            $requestedDataTypes[] = 'discord_info';
        }

        return $requestedDataTypes;
    }

    /**
     * Create a response that requests data from the user.
     *
     * @param array $dataTypes
     * @param string $language
     * @return string
     */
    protected function createDataRequestResponse(array $dataTypes, string $language): string
    {
        $dataTypeLabels = [
            'applications' => $language === 'de' ? 'Bewerbungsdaten' : 'application data',
            'tickets' => $language === 'de' ? 'Ticket-Historie' : 'ticket history',
            'discord_info' => $language === 'de' ? 'Discord-Informationen' : 'Discord information',
        ];

        $dataTypeDescriptions = [
            'applications' => $language === 'de'
                ? 'Informationen über Ihre Bewerbungen, wie Anzahl und Status'
                : 'information about your applications, such as count and status',
            'tickets' => $language === 'de'
                ? 'Informationen über Ihre früheren Tickets'
                : 'information about your previous tickets',
            'discord_info' => $language === 'de'
                ? 'Informationen über Ihr Discord-Profil'
                : 'information about your Discord profile',
        ];

        if ($language === 'de') {
            $response = "Um Ihnen besser helfen zu können, benötige ich Zugriff auf zusätzliche Informationen. ";

            if (count($dataTypes) === 1) {
                $dataType = $dataTypes[0];
                $response .= "Darf ich auf Ihre **{$dataTypeLabels[$dataType]}** zugreifen? ";
                $response .= "Dies umfasst {$dataTypeDescriptions[$dataType]}. ";
            } else {
                $response .= "Darf ich auf die folgenden Informationen zugreifen?\n\n";
                foreach ($dataTypes as $dataType) {
                    $response .= "- **{$dataTypeLabels[$dataType]}**: {$dataTypeDescriptions[$dataType]}\n";
                }
            }

            $response .= "\nSie können den Zugriff jederzeit in Ihren Profileinstellungen ändern.";
        } else {
            $response = "To better assist you, I need access to additional information. ";

            if (count($dataTypes) === 1) {
                $dataType = $dataTypes[0];
                $response .= "May I access your **{$dataTypeLabels[$dataType]}**? ";
                $response .= "This includes {$dataTypeDescriptions[$dataType]}. ";
            } else {
                $response .= "May I access the following information?\n\n";
                foreach ($dataTypes as $dataType) {
                    $response .= "- **{$dataTypeLabels[$dataType]}**: {$dataTypeDescriptions[$dataType]}\n";
                }
            }

            $response .= "\nYou can change access permissions at any time in your profile settings.";
        }

        return $response;
    }

    /**
     * Check if a message contains application-related keywords.
     *
     * @param string $message
     * @return bool
     */
    protected function containsApplicationKeywords(string $message): bool
    {
        $keywords = [
            'bewerbung', 'bewerbungen', 'bewerben', 'application', 'applications', 'apply',
            'status', 'fortschritt', 'progress', 'angenommen', 'accepted', 'abgelehnt', 'rejected'
        ];

        return $this->containsKeywords($message, $keywords);
    }

    /**
     * Check if a message contains ticket history-related keywords.
     *
     * @param string $message
     * @return bool
     */
    protected function containsTicketHistoryKeywords(string $message): bool
    {
        $keywords = [
            'ticket', 'tickets', 'historie', 'history', 'früher', 'previous', 'vergangen', 'past',
            'support', 'anfrage', 'request', 'problem', 'issue'
        ];

        return $this->containsKeywords($message, $keywords);
    }

    /**
     * Check if a message contains Discord-related keywords.
     *
     * @param string $message
     * @return bool
     */
    protected function containsDiscordKeywords(string $message): bool
    {
        $keywords = [
            'discord', 'server', 'kanal', 'channel', 'rolle', 'role', 'mitglied', 'member',
            'beigetreten', 'joined', 'tag', 'discriminator'
        ];

        return $this->containsKeywords($message, $keywords);
    }

    /**
     * Check if a message contains any of the given keywords.
     *
     * @param string $message
     * @param array $keywords
     * @return bool
     */
    protected function containsKeywords(string $message, array $keywords): bool
    {
        $message = mb_strtolower($message);

        foreach ($keywords as $keyword) {
            if (mb_strpos($message, mb_strtolower($keyword)) !== false) {
                return true;
            }
        }

        return false;
    }

    /**
     * Build the context from the ticket and its messages.
     *
     * @param Ticket $ticket
     * @param array $userData Optional user data to include
     * @return string
     */
    protected function buildTicketContext(Ticket $ticket, array $userData = []): string
    {
        $ticket->loadMissing(['messages.user', 'user']); // Use loadMissing for efficiency

        $context = "Ticket ID: {$ticket->id}\n";
        $context .= "Title: {$ticket->title}\n";
        $context .= "Description: {$ticket->description}\n";
        $context .= "Status: {$ticket->status}\n";
        $context .= "Created by: {$ticket->user->username}\n";

        // Füge Benutzerdaten hinzu, wenn vorhanden
        if (!empty($userData)) {
            $context .= "\nUser Information:\n";

            if (isset($userData['username'])) {
                $context .= "Username: {$userData['username']}\n";
                if (isset($userData['global_name'])) {
                    $context .= "Display Name: {$userData['global_name']}\n";
                }
            }

            // Benutzerrollen hinzufügen
            if (isset($userData['roles']) && !empty($userData['roles'])) {
                $context .= "User Roles: " . implode(', ', $userData['roles']) . "\n";
            }

            if (isset($userData['applications'])) {
                $context .= "Applications: {$userData['applications']['count']}\n";
                $context .= "Latest Application Status: {$userData['applications']['latest_status']}\n";
            }

            if (isset($userData['tickets'])) {
                $context .= "Total Tickets: {$userData['tickets']['count']}\n";
                $context .= "Open Tickets: {$userData['tickets']['open_count']}\n";
            }

            if (isset($userData['discord_info'])) {
                $context .= "Discord Locale: {$userData['discord_info']['locale']}\n";
            }
        }

        $context .= "\n";

        $context .= "Conversation History:\n";

        // Format the conversation history in a clear, chronological format
        foreach ($ticket->messages as $message) {
            $username = $message->user->username ?? 'Unknown User';
            $role = $message->message_source === 'ai' ? '[AI Assistant]' :
                   ($message->is_system_message ? '[System]' : '');

            if ($role) {
                $username .= " $role";
            }

            $timestamp = $message->created_at->format('Y-m-d H:i:s');
            $context .= "[$timestamp] {$username}:\n{$message->message}\n\n";
        }

        return $context;
    }

    /**
     * Get the default system prompt based on language.
     *
     * @param string $language
     * @return string
     */
    protected function getDefaultSystemPrompt(string $language): string
    {
        if ($language === 'de') {
            return "Du bist ein Support-Assistent für das Minewache-Ticketsystem. Deine Aufgabe ist es, Benutzern bei ihren Anfragen zu helfen.

Wichtige Anweisungen:
1. Antworte höflich, präzise und hilfreich.
2. Beziehe dich nur auf Informationen, die im Ticket enthalten sind.
3. Wenn du ein Problem nicht lösen kannst, sage klar: 'Für dieses spezifische Problem muss ich dich mit einem menschlichen Support-Mitarbeiter verbinden.'
4. Unterteile komplexe Lösungen in nummerierte Schritte.
5. Halte Antworten unter 250 Wörtern, es sei denn, detaillierte technische Anweisungen sind erforderlich.
6. Antworte auf Deutsch.

Du bist ein KI-Assistent und kein menschlicher Mitarbeiter. Stelle dies in deinen Antworten klar.";
        } else {
            return "You are a support assistant for the Minewache ticket system. Your task is to help users with their inquiries.

Important instructions:
1. Maintain a helpful and professional tone.
2. Reference only information provided in the ticket history.
3. If you cannot resolve an issue, clearly state: 'For this specific issue, I'll need to connect you with a human support agent.'
4. Break down complex solutions into numbered steps when appropriate.
5. Keep responses under 250 words unless detailed technical instructions are required.
6. Respond in English.

You are an AI assistant, not a human staff member. Make this clear in your responses.";
        }
    }

    /**
     * Get a fallback response if the API call fails.
     *
     * @param string $language
     * @return string
     */
    protected function getFallbackResponse(string $language): string
    {
        if ($language === 'de') {
            return "Entschuldigung, ich konnte keine Antwort generieren. Bitte versuchen Sie es später noch einmal oder kontaktieren Sie einen menschlichen Supporter.";
        } else {
            return "Sorry, I couldn't generate a response. Please try again later or contact a human supporter.";
        }
    }
}
