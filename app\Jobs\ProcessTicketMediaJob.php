<?php

namespace App\Jobs;

use App\Events\AttachmentProcessingFinished;
use App\Models\TicketAttachment;
use Exception;
use FFMpeg\Coordinate\Dimension;
use FFMpeg\Coordinate\TimeCode;
use FFMpeg\Format\Audio\Mp3;
use FFMpeg\Format\Video\X264;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldBeUnique;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;
use ProtoneMedia\LaravelFFMpeg\Support\FFMpeg;
use Spatie\PdfToImage\Pdf; // Import the Pdf class
use Illuminate\Support\Str; // Import Str facade

class ProcessTicketMediaJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    // The queue property is already defined in the Queueable trait
    // We'll set it in the constructor instead

    /**
     * The number of seconds the job can run before timing out.
     *
     * @var int
     */
    public $timeout = 3600;

    /**
     * The number of times the job may be attempted.
     *
     * @var int
     */
    public $tries = 3;

    /**
     * The ticket attachment instance.
     *
     * @var \App\Models\TicketAttachment
     */
    protected $attachment;

    /**
     * Create a new job instance.
     *
     * @param  \App\Models\TicketAttachment  $attachment
     * @return void
     */
    public function __construct(TicketAttachment $attachment)
    {
        $this->attachment = $attachment;
        $this->queue = 'media';
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle(): void
    {
        // Reload the attachment to ensure we have the latest data
        $this->attachment->refresh();

        // Skip if already processed or processing
        if ($this->attachment->processing_status !== 'pending') {
            return;
        }

        try {
            // Update status to processing
            $this->attachment->update(['processing_status' => 'processing']);

            // Check if the file exists
            if (!Storage::exists($this->attachment->file_path)) {
                throw new Exception("Original file not found: {$this->attachment->file_path}");
            }

            // Determine media type based on mime type
            $mediaType = $this->determineMediaType();
            $this->attachment->update(['media_type' => $mediaType]);

            // Process based on media type
            switch ($mediaType) {
                case 'video':
                    $this->processVideo();
                    break;
                case 'audio':
                    $this->processAudio();
                    break;
                case 'image':
                    $this->processImage();
                    break;
                case 'pdf': // Add case for PDF
                    $this->processPdf();
                    break;
                default:
                    // For other file types, just mark as completed without processing
                    $this->attachment->update([
                        'processing_status' => 'completed',
                        'media_metadata' => ['type' => 'document']
                    ]);
                    break;
            }

            // Fire event that processing is finished (success)
            event(new AttachmentProcessingFinished($this->attachment));

        } catch (Exception $e) {
            Log::error('Media processing failed', [
                'attachment_id' => $this->attachment->id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            // Update status to failed
            $this->attachment->update([
                'processing_status' => 'failed',
                'processing_error' => $e->getMessage()
            ]);

            // Fire event that processing is finished (failure)
            event(new AttachmentProcessingFinished($this->attachment));
        }
    }

    /**
     * Determine the media type based on mime type
     *
     * @return string
     */
    protected function determineMediaType(): string
    {
        $mimeType = $this->attachment->mime_type;

        if (str_starts_with($mimeType, 'video/')) {
            return 'video';
        }

        if (str_starts_with($mimeType, 'audio/')) {
            return 'audio';
        }

        if (str_starts_with($mimeType, 'image/')) {
            return 'image';
        }

        if ($mimeType === 'application/pdf') {
            return 'pdf';
        }

        return 'document';
    }

    /**
     * Process video files
     *
     * @return void
     */
    protected function processVideo(): void
    {
        $originalPath = $this->attachment->file_path;
        $ticketId = $this->attachment->message->ticket_id;
        $filename = pathinfo($this->attachment->filename, PATHINFO_FILENAME);

        // Define output paths
        $processedPath = "ticket-attachments/{$ticketId}/processed/{$filename}.mp4";
        $thumbnailPath = "ticket-attachments/{$ticketId}/thumbnails/{$filename}.jpg";

        // Ensure directories exist
        Storage::makeDirectory("ticket-attachments/{$ticketId}/processed");
        Storage::makeDirectory("ticket-attachments/{$ticketId}/thumbnails");

        // Get media info
        $media = FFMpeg::fromDisk('local')
            ->open($originalPath);

        // Extract video metadata
        $durationInSeconds = $media->getDurationInSeconds();
        $dimensions = $media->getVideoStream()->getDimensions();

        // Generate thumbnail at 1 second or 10% of the video, whichever is less
        $thumbnailTime = min(1, $durationInSeconds * 0.1);
        $media->getFrameFromSeconds($thumbnailTime)
            ->export()
            ->toDisk('local')
            ->save($thumbnailPath);

        // Convert video to MP4 (H.264/AAC)
        $format = new X264('aac');
        $format->setKiloBitrate(1000); // Adjust bitrate as needed

        // If video is larger than 720p, resize it
        if ($dimensions->getWidth() > 1280 || $dimensions->getHeight() > 720) {
            $media->addFilter(function ($filters) {
                $filters->resize(new Dimension(1280, 720));
            });
        }

        $media->export()
            ->toDisk('local')
            ->inFormat($format)
            ->save($processedPath);

        // Update attachment record
        $this->attachment->update([
            'processed_file_path' => $processedPath,
            'thumbnail_path' => $thumbnailPath,
            'processing_status' => 'completed',
            'media_metadata' => [
                'duration' => $durationInSeconds,
                'width' => $dimensions->getWidth(),
                'height' => $dimensions->getHeight(),
                'format' => 'mp4'
            ]
        ]);
    }

    /**
     * Process audio files
     *
     * @return void
     */
    protected function processAudio(): void
    {
        $originalPath = $this->attachment->file_path;
        $ticketId = $this->attachment->message->ticket_id;
        $filename = pathinfo($this->attachment->filename, PATHINFO_FILENAME);

        // Define output path
        $processedPath = "ticket-attachments/{$ticketId}/processed/{$filename}.mp3";

        // Ensure directory exists
        Storage::makeDirectory("ticket-attachments/{$ticketId}/processed");

        // Get media info
        $media = FFMpeg::fromDisk('local')
            ->open($originalPath);

        // Extract audio metadata
        $durationInSeconds = $media->getDurationInSeconds();

        // Convert audio to MP3
        $format = new Mp3();
        $format->setAudioKiloBitrate(192); // Adjust bitrate as needed

        $media->export()
            ->toDisk('local')
            ->inFormat($format)
            ->save($processedPath);

        // Update attachment record
        $this->attachment->update([
            'processed_file_path' => $processedPath,
            'processing_status' => 'completed',
            'media_metadata' => [
                'duration' => $durationInSeconds,
                'format' => 'mp3'
            ]
        ]);
    }

    /**
     * Process image files
     *
     * @return void
     */
    protected function processImage(): void
    {
        $originalPath = $this->attachment->file_path;
        $ticketId = $this->attachment->message->ticket_id;
        $filename = pathinfo($this->attachment->filename, PATHINFO_FILENAME);
        $extension = pathinfo($this->attachment->filename, PATHINFO_EXTENSION);

        // For images, we'll just create a thumbnail if it's a large image
        // Otherwise, we'll use the original
        $thumbnailPath = null;

        // Get image dimensions
        list($width, $height) = getimagesize(Storage::path($originalPath));

        // If image is larger than 1200px in any dimension, create a thumbnail
        if ($width > 1200 || $height > 1200) {
            $thumbnailPath = "ticket-attachments/{$ticketId}/thumbnails/{$filename}.jpg";
            Storage::makeDirectory("ticket-attachments/{$ticketId}/thumbnails");

            // Use FFMpeg to resize the image
            FFMpeg::fromDisk('local')
                ->open($originalPath)
                ->export()
                ->toDisk('local')
                ->save($thumbnailPath);
        }

        // Update attachment record
        $this->attachment->update([
            'thumbnail_path' => $thumbnailPath,
            'processing_status' => 'completed',
            'media_metadata' => [
                'width' => $width,
                'height' => $height,
                'format' => $extension
            ]
        ]);
    }

    /**
     * Process PDF files
     *
     * @return void
     * @throws \Spatie\PdfToImage\Exceptions\PdfDoesNotExist
     * @throws \Spatie\PdfToImage\Exceptions\InvalidFormat
     */
    protected function processPdf(): void
    {
        $originalPath = $this->attachment->file_path;
        $ticketId = $this->attachment->message->ticket_id;
        $filename = pathinfo($this->attachment->filename, PATHINFO_FILENAME);
        $safeFilename = Str::slug($filename); // Create a URL-safe filename

        // Define output paths
        $thumbnailPath = "ticket-attachments/{$ticketId}/thumbnails/{$safeFilename}.jpg";
        $fullOriginalPath = Storage::path($originalPath); // Get absolute path for spatie/pdf-to-image
        $fullThumbnailPath = Storage::path($thumbnailPath); // Get absolute path for saving

        // Ensure thumbnail directory exists
        Storage::makeDirectory("ticket-attachments/{$ticketId}/thumbnails");

        // Check if Ghostscript is available
        // Note: This requires Ghostscript (gs) to be installed on the server
        // You might need to add more robust checking or configuration
        if (!shell_exec('command -v gs')) {
             throw new Exception('Ghostscript (gs) is not installed or not in PATH. PDF preview generation failed.');
        }

        Log::info("Generating PDF thumbnail for: {$originalPath} to {$thumbnailPath}");

        // Generate thumbnail of the first page
        try {
            (new Pdf($fullOriginalPath))
                ->setPage(1)
                ->setOutputFormat('jpg')
                ->saveImage($fullThumbnailPath);
        } catch (Exception $e) {
            Log::error("Spatie PDF to Image failed: " . $e->getMessage());
            throw $e; // Re-throw the exception to be caught by the main handler
        }

        // Check if thumbnail was created
        if (!Storage::exists($thumbnailPath)) {
            throw new Exception("Failed to generate PDF thumbnail at: {$thumbnailPath}");
        }

        // Update attachment record
        $this->attachment->update([
            'processing_status' => 'completed',
            'thumbnail_path' => $thumbnailPath,
            'media_url' => Storage::url($originalPath), // Keep original PDF accessible
            'thumbnail_url' => Storage::url($thumbnailPath),
            'media_metadata' => [
                'type' => 'pdf',
                // Add more metadata if needed, e.g., page count
                // 'page_count' => (new Pdf($fullOriginalPath))->getNumberOfPages(),
            ]
        ]);

        Log::info("PDF thumbnail generated successfully for attachment ID: {$this->attachment->id}");
    }
}
