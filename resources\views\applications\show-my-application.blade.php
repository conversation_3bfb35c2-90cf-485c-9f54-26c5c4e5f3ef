<x-app-layout>
    <x-slot name="title">Bewerbung Ansehen - MineWache</x-slot>

    <div class="container mx-auto px-4 py-8">
        <div class="max-w-4xl mx-auto">
            <div class="flex items-center mb-6">
                <x-modern-button variant="ghost" href="{{ route('my.applications.index') }}" ><svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                        <path fill-rule="evenodd" d="M12.707 5.293a1 1 0 010 1.414L9.414 10l3.293 3.293a1 1 0 01-1.414 1.414l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 0z" clip-rule="evenodd" />
                    </svg>
                    Zurück</x-modern-button>
                <h1 class="text-2xl font-bold">Bewerbung #{{ $application->id }}</h1>
            </div>

            <div class="bg-base-200 p-6 rounded-box mb-6">
                <div class="flex justify-between mb-4">
                    <div>
                        <p class="text-sm opacity-70">Eingereicht am:</p>
                        <p>{{ $application->created_at->format('d.m.Y H:i') }}</p>
                    </div>
                    <div>
                        <p class="text-sm opacity-70">Status:</p>
                        <p>
                            @switch($application->status)
                                @case('pending')
                                    <span class="badge badge-warning">In Bearbeitung</span>
                                    @break
                                @case('approved')
                                    <span class="badge badge-success">Angenommen</span>
                                    @break
                                @case('rejected')
                                    <span class="badge badge-error">Abgelehnt</span>
                                    @break
                                @default
                                    <span class="badge">{{ $application->status }}</span>
                            @endswitch
                        </p>
                    </div>
                </div>

                @if($application->feedback)
                    <div class="mt-4 p-4 bg-base-300 rounded-box">
                        <h3 class="font-bold mb-2">Feedback:</h3>
                        <p>{{ $application->feedback }}</p>
                    </div>
                @endif
            </div>

            <div class="card bg-base-100 shadow-xl mb-6">
                <div class="card-body">
                    <h2 class="card-title">Persönliche Informationen</h2>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                            <p class="text-sm opacity-70">Discord Name:</p>
                            <p>{{ $application->discord_name }}</p>
                        </div>
                        <div>
                            <p class="text-sm opacity-70">Minecraft Name:</p>
                            <p>{{ $application->minecraft_name }}</p>
                        </div>
                        <div>
                            <p class="text-sm opacity-70">Alter:</p>
                            <p>{{ $application->age }}</p>
                        </div>
                        <div>
                            <p class="text-sm opacity-70">Land:</p>
                            <p>{{ $application->country }}</p>
                        </div>
                    </div>
                </div>
            </div>

            <div class="card bg-base-100 shadow-xl mb-6">
                <div class="card-body">
                    <h2 class="card-title">Bewerbungsdetails</h2>
                    <div class="space-y-4">
                        <div>
                            <p class="text-sm opacity-70">Motivation:</p>
                            <p class="whitespace-pre-line">{{ $application->motivation }}</p>
                        </div>
                        <div>
                            <p class="text-sm opacity-70">Erfahrung:</p>
                            <p class="whitespace-pre-line">{{ $application->experience }}</p>
                        </div>
                        <div>
                            <p class="text-sm opacity-70">Stärken:</p>
                            <p class="whitespace-pre-line">{{ $application->strengths }}</p>
                        </div>
                        <div>
                            <p class="text-sm opacity-70">Schwächen:</p>
                            <p class="whitespace-pre-line">{{ $application->weaknesses }}</p>
                        </div>
                    </div>
                </div>
            </div>

            @if($application->editable)
                <div class="flex justify-center mt-6">
                    <a href="{{ route('my.applications.edit', $application->id) }}" class="btn btn-primary">
                        Bewerbung bearbeiten
                    </a>
                </div>
            @endif
        </div>
    </div>
</x-app-layout>