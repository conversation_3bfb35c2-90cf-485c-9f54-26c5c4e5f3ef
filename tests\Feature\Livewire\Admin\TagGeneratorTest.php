<?php

namespace Tests\Feature\Livewire\Admin;

use App\Livewire\Admin\TagGenerator;
use App\Models\User; // Assuming you have a User model
use App\Enums\Role; // Assuming you have Role enum
use App\Services\GeminiTagService;
use Illuminate\Http\UploadedFile;
use Illuminate\Support\Facades\Storage;
use Livewire\Livewire;
use Mockery\MockInterface;
use Tests\TestCase;
use Exception;

class TagGeneratorTest extends TestCase
{
    protected function getAdminUser()
    {
        // Create an admin user. Adjust if your User factory or creation logic differs.
        $user = User::factory()->create([
            'permissions' => (string)Role::MINEWACHE_TEAM->value, 
        ]);
        $this->actingAs($user);
        return $user;
    }

    public function test_component_renders_successfully_for_admin()
    {
        $this->getAdminUser();
        Livewire::test(TagGenerator::class)
            ->assertStatus(200)
            ->assertSee('AI Video Tag Generator');
    }

    public function test_video_upload_and_successful_processing()
    {
        $this->getAdminUser();
        Storage::fake('livewire-tmp'); // Fake the temporary upload disk

        $file = UploadedFile::fake()->create('video.mp4', 10000, 'video/mp4'); // 10MB

        // Mock GeminiTagService
        $this->instance(
            GeminiTagService::class,
            \Mockery::mock(GeminiTagService::class, function (MockInterface $mock) {
                $mock->shouldReceive('generateTagsForVideo')
                    ->once()
                    // ->with(base64_encode($file->get()), 'video/mp4') // This can be tricky with base64 string matching
                    ->andReturn([
                        'videoSummary' => ['overallGerman' => 'Mocked summary'],
                        'tiktokHashtags' => ['#mockedtiktok'],
                        'instagramHashtags' => ['#mockedinsta'],
                        'categorizedTags' => [['category' => 'Mocked', 'tags' => ['tag']]],
                    ]);
            })
        );

        Livewire::test(TagGenerator::class)
            ->set('videoFile', $file)
            ->call('processVideo')
            ->assertHasNoErrors()
            ->assertSet('isLoading', false)
            ->assertNotSet('error', null) // Should be null or empty
            ->assertSee('Mocked summary')
            ->assertSee('#mockedtiktok');
    }

    public function test_video_upload_handles_service_exception()
    {
        $this->getAdminUser();
        Storage::fake('livewire-tmp');
        $file = UploadedFile::fake()->create('video.mp4', 1000, 'video/mp4');

        $this->instance(
            GeminiTagService::class,
            \Mockery::mock(GeminiTagService::class, function (MockInterface $mock) {
                $mock->shouldReceive('generateTagsForVideo')
                    ->once()
                    ->andThrow(new Exception('Service failed'));
            })
        );

        Livewire::test(TagGenerator::class)
            ->set('videoFile', $file)
            ->call('processVideo')
            ->assertSet('isLoading', false)
            ->assertSet('error', 'An error occurred: Service failed')
            ->assertSee('An error occurred: Service failed');
    }

    public function test_video_file_validation_required()
    {
        $this->getAdminUser();
        Livewire::test(TagGenerator::class)
            ->call('processVideo')
            ->assertHasErrors(['videoFile' => 'required']);
    }
    
    public function test_video_file_validation_mimes()
    {
        $this->getAdminUser();
        Storage::fake('livewire-tmp');
        $file = UploadedFile::fake()->create('document.pdf', 1000, 'application/pdf');

        Livewire::test(TagGenerator::class)
            ->set('videoFile', $file)
            ->call('processVideo')
            ->assertHasErrors(['videoFile' => 'mimes']);
    }

    public function test_video_file_validation_max_size()
    {
        $this->getAdminUser();
        Storage::fake('livewire-tmp');
        // Exceed MAX_FILE_SIZE_MB (default 50MB in component)
        $file = UploadedFile::fake()->create('large_video.mp4', (TagGenerator::MAX_FILE_SIZE_MB * 1024) + 100, 'video/mp4'); 

        Livewire::test(TagGenerator::class)
            ->set('videoFile', $file)
            ->call('processVideo')
            ->assertHasErrors(['videoFile' => 'max']);
    }
    
    public function test_analyze_another_resets_state()
    {
        $this->getAdminUser();
        Storage::fake('livewire-tmp');
        $file = UploadedFile::fake()->create('video.mp4', 1000, 'video/mp4');

        // Mock service for initial processing
        $this->instance(GeminiTagService::class, \Mockery::mock(GeminiTagService::class, function (MockInterface $mock) {
            $mock->shouldReceive('generateTagsForVideo')->andReturn(['videoSummary' => ['overallGerman' => 'Test']]);
        }));

        Livewire::test(TagGenerator::class)
            ->set('videoFile', $file)
            ->call('processVideo') // Populate some state
            ->assertNotNil('analysisResult') 
            ->call('analyzeAnother')
            ->assertSet('videoFile', null)
            ->assertSet('analysisResult', null)
            ->assertSet('isLoading', false)
            ->assertSet('error', null);
    }
}
