@echo off
echo Starting Minewache Ticket System Services...

:: Start Laravel development server
start "Laravel Server" cmd /c "php artisan serve"

:: Start Vite development server
start "Vite Server" cmd /c "npm run dev"

:: Start Reverb WebSocket server
start "Reverb Server" cmd /c "php artisan reverb:start --host=127.0.0.1 --port=6001"

:: Start Queue Worker with Reverb broadcasting
start "Queue Worker" cmd /c "php artisan queue:work-reverb --queue=default,media --tries=3 --timeout=600"

echo All services started successfully!
echo.
echo Press any key to stop all services...
pause > nul

:: Kill all services
taskkill /FI "WINDOWTITLE eq Laravel Server*" /F
taskkill /FI "WINDOWTITLE eq Vite Server*" /F
taskkill /FI "WINDOWTITLE eq Reverb Server*" /F
taskkill /FI "WINDOWTITLE eq Queue Worker*" /F

echo All services stopped successfully!
