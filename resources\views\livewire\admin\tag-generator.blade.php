<div>
    <header class="mb-8 text-center">
        <h1 class="text-3xl sm:text-4xl font-bold text-gray-800 dark:text-white">
            AI Video Tag Generator
        </h1>
        <p class="mt-2 text-md text-gray-600 dark:text-gray-400">
            Upload your video to generate SEO-optimized tags and summaries. (Max {{ \App\Livewire\Admin\TagGenerator::MAX_FILE_SIZE_MB }}MB)
        </p>
    </header>

            @if (session('error_outside')) {{-- For errors set outside Livewire, e.g. by service provider --}}
                <div class="mb-4 p-4 bg-red-100 border border-red-400 text-red-700 rounded dark:bg-red-700 dark:text-red-100 dark:border-red-900" role="alert">
                    <p class="font-bold">Error</p>
                    <p>{{ session('error_outside') }}</p>
                </div>
            @endif

            @if ($error)
                <div wire:key="error-alert" class="mb-4 p-4 bg-red-100 border border-red-400 text-red-700 rounded dark:bg-red-700 dark:text-red-100 dark:border-red-900" role="alert">
                    <div class="flex">
                        <div class="py-1"><svg class="fill-current h-6 w-6 text-red-500 dark:text-red-300 mr-4" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20"><path d="M2.93 17.07A10 10 0 1 1 17.07 2.93 10 10 0 0 1 2.93 17.07zM10 18a8 8 0 1 0 0-16 8 8 0 0 0 0 16zm-.89-3.89a.9.9 0 0 1 1.78 0V15a.9.9 0 0 1-1.78 0v-.89zM10 5a.9.9 0 0 1 .89.91V11a.9.9 0 0 1-1.78 0V5.91A.9.9 0 0 1 10 5z"/></svg></div>
                        <div>
                            <p class="font-bold">An Error Occurred</p>
                            <p class="text-sm">{{ $error }}</p>
                        </div>
                    </div>
                </div>
            @endif

            @if (!$analysisResult && !$isLoading)
                <form wire:submit.prevent="processVideo" id="videoUploadForm">
                    @csrf {{-- Not strictly needed for Livewire submit but good practice if form could post traditionally --}}

                    <div class="mb-6">
                        <label for="videoFile" class="block mb-2 text-sm font-medium text-gray-900 dark:text-gray-300">Upload Video File</label>
                        <input type="file" id="videoFile" wire:model="videoFile" class="block w-full text-sm text-gray-900 bg-gray-50 rounded-lg border border-gray-300 cursor-pointer dark:text-gray-400 focus:outline-none dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 @error('videoFile') border-red-500 dark:border-red-500 @enderror" aria-describedby="videoFile_help">
                        <p class="mt-1 text-xs text-gray-500 dark:text-gray-400" id="videoFile_help">Supported formats: MP4, MOV, WEBM, MKV, AVI. Max size: {{ \App\Livewire\Admin\TagGenerator::MAX_FILE_SIZE_MB }}MB.</p>
                        @error('videoFile') <span class="mt-1 text-xs text-red-600 dark:text-red-400">{{ $message }}</span> @enderror
                    </div>

                    <div class="flex items-center justify-center">
                        {{-- Use your app's primary button style --}}
                        <button type="submit" wire:loading.attr="disabled" wire:target="processVideo, videoFile"
                                class="px-6 py-2.5 text-white bg-blue-600 hover:bg-blue-700 focus:ring-4 focus:outline-none focus:ring-blue-300 font-medium rounded-lg text-sm w-full sm:w-auto text-center dark:bg-blue-500 dark:hover:bg-blue-600 dark:focus:ring-blue-700 disabled:opacity-50">
                            <span wire:loading.remove wire:target="processVideo, videoFile">Generate Tags</span>
                            <span wire:loading wire:target="processVideo, videoFile">
                                <svg class="animate-spin -ml-1 mr-3 h-5 w-5 text-white inline" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                    <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                                    <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                                  </svg>
                                Processing...
                            </span>
                        </button>
                    </div>
                </form>
            @elseif ($isLoading)
                <div wire:key="loading-state" class="text-center p-6">
                    <div class="flex justify-center items-center mb-4">
                        <svg class="animate-spin h-8 w-8 text-blue-600 dark:text-blue-400" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                            <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                        </svg>
                    </div>
                    <p class="text-lg font-medium text-gray-700 dark:text-gray-300">{{ $progressMessage ?: 'Processing...' }}</p>
                    @if ($videoFile && property_exists($videoFile, 'getClientOriginalName'))
                        <p class="text-sm text-gray-500 dark:text-gray-400 mt-1">File: {{ $videoFile->getClientOriginalName() }}</p>
                    @endif
                    <div class="w-full bg-gray-200 dark:bg-gray-600 rounded-full h-2.5 mt-4">
                        <div class="bg-blue-600 dark:bg-blue-500 h-2.5 rounded-full animate-pulse" style="width: 75%"></div> {{-- Indeterminate progress bar --}}
                    </div>
                </div>
            @elseif ($analysisResult)
                <div wire:key="results-display" class="mt-6">
                    <h2 class="text-2xl font-semibold text-gray-800 dark:text-gray-200 mb-4">Analysis Results</h2>

                    {{-- Hashtag Preference --}}
                    <div class="mb-6 p-4 bg-gray-50 dark:bg-gray-700 rounded-lg">
                        <label for="hashtagPreference" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Hashtag Display Preference:</label>
                        <select id="hashtagPreference" wire:model.live="hashtagPreference" class="block w-full sm:w-1/2 mt-1 p-2 border-gray-300 rounded-md shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50 dark:bg-gray-600 dark:border-gray-500 dark:text-gray-200">
                            <option value="both">TikTok & Instagram</option>
                            <option value="tiktok">TikTok Only</option>
                            <option value="instagram">Instagram Only</option>
                        </select>
                    </div>

                    {{-- Video Summary --}}
                    <div class="mb-6">
                        <h3 class="text-xl font-semibold text-gray-700 dark:text-gray-300 mb-2">Video Summary</h3>
                        <div class="p-4 bg-gray-100 dark:bg-gray-700 rounded-lg prose dark:prose-invert max-w-none">
                            <p class="text-gray-700 dark:text-gray-300"><strong>Overall:</strong> {{ $analysisResult['videoSummary']['overallGerman'] ?? 'N/A' }}</p>
                            @if(!empty($analysisResult['videoSummary']['keyVisualElements']))
                                <p class="text-gray-700 dark:text-gray-300"><strong>Key Visuals:</strong> {{ implode(', ', $analysisResult['videoSummary']['keyVisualElements']) }}</p>
                            @endif
                            @if(!empty($analysisResult['videoSummary']['detectedObjects']))
                                <p class="text-gray-700 dark:text-gray-300"><strong>Detected Objects:</strong> {{ implode(', ', $analysisResult['videoSummary']['detectedObjects']) }}</p>
                            @endif
                             @if(!empty($analysisResult['videoSummary']['detectedActions']))
                                <p class="text-gray-700 dark:text-gray-300"><strong>Detected Actions:</strong> {{ implode(', ', $analysisResult['videoSummary']['detectedActions']) }}</p>
                            @endif
                            @if(!empty($analysisResult['videoSummary']['spokenNamesOrRoles']))
                                <p class="text-gray-700 dark:text-gray-300"><strong>Spoken Names/Roles:</strong> {{ implode(', ', $analysisResult['videoSummary']['spokenNamesOrRoles']) }}</p>
                            @endif
                            @if(!empty($analysisResult['videoSummary']['dialogueThemes']))
                                <p class="text-gray-700 dark:text-gray-300"><strong>Dialogue Themes:</strong> {{ implode(', ', $analysisResult['videoSummary']['dialogueThemes']) }}</p>
                            @endif
                        </div>
                    </div>

                    {{-- TikTok Hashtags --}}
                    @if(in_array($hashtagPreference, ['both', 'tiktok']) && !empty($analysisResult['tiktokHashtags']))
                        <div class="mb-6">
                            <h3 class="text-xl font-semibold text-gray-700 dark:text-gray-300 mb-2">TikTok Hashtags</h3>
                            <div class="p-4 bg-gray-100 dark:bg-gray-700 rounded-lg">
                                <p class="text-gray-600 dark:text-gray-300 break-words">{{ implode(' ', $analysisResult['tiktokHashtags']) }}</p>
                            </div>
                        </div>
                    @endif

                    {{-- Instagram Hashtags --}}
                    @if(in_array($hashtagPreference, ['both', 'instagram']) && !empty($analysisResult['instagramHashtags']))
                        <div class="mb-6">
                            <h3 class="text-xl font-semibold text-gray-700 dark:text-gray-300 mb-2">Instagram Hashtags</h3>
                            <div class="p-4 bg-gray-100 dark:bg-gray-700 rounded-lg">
                                <p class="text-gray-600 dark:text-gray-300 break-words">{{ implode(' ', $analysisResult['instagramHashtags']) }}</p>
                            </div>
                        </div>
                    @endif

                    {{-- Categorized Tags --}}
                    @if(!empty($analysisResult['categorizedTags']))
                        <div class="mb-6">
                            <h3 class="text-xl font-semibold text-gray-700 dark:text-gray-300 mb-2">Categorized Keywords</h3>
                            <div class="space-y-3">
                                @foreach($analysisResult['categorizedTags'] as $category)
                                    <div class="p-3 bg-gray-100 dark:bg-gray-700 rounded-lg">
                                        <h4 class="font-semibold text-gray-700 dark:text-gray-300">{{ $category['category'] }}</h4>
                                        <p class="text-sm text-gray-600 dark:text-gray-400">{{ implode(', ', $category['tags']) }}</p>
                                    </div>
                                @endforeach
                            </div>
                        </div>
                    @endif

                    <div class="mt-8 text-center">
                        <button wire:click="analyzeAnother" class="px-6 py-2.5 text-white bg-blue-600 hover:bg-blue-700 focus:ring-4 focus:ring-blue-300 font-medium rounded-lg text-sm dark:bg-blue-500 dark:hover:bg-blue-600 dark:focus:ring-blue-700">
                            Analyze Another Video
                        </button>
                    </div>
                </div>
            @endif

    <footer class="mt-12 text-center text-sm text-gray-500 dark:text-gray-400">
        <p>&copy; {{ date('Y') }} Minewache. AI Tag Generator.</p>
    </footer>
</div>

@script
<script>
    Livewire.on('fileInputReset', () => {
        if (document.getElementById('videoUploadForm')) {
            document.getElementById('videoUploadForm').reset();
        }
    });
</script>
@endscript
