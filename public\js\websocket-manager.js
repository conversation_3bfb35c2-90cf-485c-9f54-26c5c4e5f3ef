/**
 * WebSocket Connection Manager
 *
 * This script provides enhanced WebSocket connection management for the ticket system.
 * It handles connection errors, provides visual feedback, and implements fallback mechanisms.
 */

class WebSocketManager {
    constructor(options = {}) {
        this.options = {
            connectionTimeout: options.connectionTimeout || 5000,
            reconnectInterval: options.reconnectInterval || 3000,
            maxReconnectAttempts: options.maxReconnectAttempts || 5,
            notificationContainer: options.notificationContainer || '#websocket-status',
            debug: options.debug || false,
            ...options
        };

        this.reconnectAttempts = 0;
        this.connected = false;
        this.channels = new Map();
        this.notificationElement = null;
        this.pollingEnabled = false;
        this.pollingInterval = null;

        this.initNotificationElement();
    }

    /**
     * Initialize the notification element for connection status
     */
    initNotificationElement() {
        const container = document.querySelector(this.options.notificationContainer);

        if (!container) {
            if (this.options.debug) {
                console.warn(`WebSocket notification container ${this.options.notificationContainer} not found`);
            }
            return;
        }

        // Create notification element if it doesn't exist
        let notificationElement = container.querySelector('.websocket-notification');

        if (!notificationElement) {
            notificationElement = document.createElement('div');
            notificationElement.className = 'websocket-notification hidden';
            notificationElement.innerHTML = `
                <div class="flex items-center p-2 text-sm rounded-lg">
                    <span class="status-icon mr-2"></span>
                    <span class="status-text"></span>
                    <button class="retry-button ml-2 px-2 py-1 text-xs rounded hidden">Retry</button>
                </div>
            `;
            container.appendChild(notificationElement);
        }

        this.notificationElement = notificationElement;

        // Add event listener to retry button
        const retryButton = notificationElement.querySelector('.retry-button');
        if (retryButton) {
            retryButton.addEventListener('click', () => this.reconnect());
        }
    }

    /**
     * Initialize WebSocket connection
     */
    init() {
        // Try to load Echo from bootstrap.js if it's not already loaded
        this.tryLoadEcho();

        // Check if Echo is available
        if (typeof window.Echo === 'undefined') {
            console.error('Laravel Echo is not available. WebSocket functionality will be disabled.');
            this.log('Critical: Echo not available after attempting to load. WebSocket functionality disabled.');
            this.updateStatus('unavailable', 'WebSocket features unavailable (Echo not loaded).');
            return false;
        }

        // Log connection configuration
        if (window.Echo.connector && window.Echo.connector.options) {
            const options = window.Echo.connector.options;
            const host = options.wsHost || 'unknown';
            const port = options.wsPort || 'unknown';

            // Prominent console log of actual connection details
            console.log('Echo connecting to', host, port);

            this.log('WebSocket connection configuration:', {
                host: host,
                port: port,
                path: options.wsPath || '/',
                forceTLS: options.forceTLS || false,
                scheme: options.forceTLS ? 'wss' : 'ws',
                currentHost: window.location.hostname,
                currentProtocol: window.location.protocol
            });
        } else {
            this.log('Echo connector or options not available');
        }

        this.setupConnectionListeners();

        // Set a timeout to check if connection was established
        setTimeout(() => {
            if (!this.connected) {
                this.log('Connection timeout, falling back to polling');
                this.updateStatus('timeout', 'Connection timeout');
                this.enablePolling();
            }
        }, this.options.connectionTimeout);

        return true;
    }

    /**
     * Try to load Echo if it's not already available
     */
    tryLoadEcho() {
        this.log('Checking if Echo is available.');
        if (window.Echo) {
            this.log('Echo is available.');
        } else {
            this.log('Echo is not available.');
        }
    }

    /**
     * Setup listeners for connection events
     */
    setupConnectionListeners() {
        if (!window.Echo || !window.Echo.connector || !window.Echo.connector.pusher) {
            return false;
        }

        const connection = window.Echo.connector.pusher.connection;

        connection.bind('state_change', (states) => {
            this.log(`Connection state changed from ${states.previous} to ${states.current}`);

            if (states.current === 'connected') {
                this.connected = true;
                this.reconnectAttempts = 0;
                this.updateStatus('connected', 'Connected');
                this.disablePolling();
            } else if (states.current === 'disconnected' || states.current === 'failed') {
                this.connected = false;
                this.updateStatus('disconnected', 'Disconnected');
                this.attemptReconnect();
            }
        });

        connection.bind('connected', () => {
            this.log('Successfully connected to WebSocket server');
            this.connected = true;
            this.reconnectAttempts = 0;
            this.updateStatus('connected', 'Connected');
            this.disablePolling();

            // Resubscribe to all channels
            this.resubscribeToChannels();
        });

        connection.bind('disconnected', () => {
            this.log('Disconnected from WebSocket server');
            this.connected = false;
            this.updateStatus('disconnected', 'Disconnected');
            this.attemptReconnect();
        });

        connection.bind('error', (error) => {
            this.log('WebSocket connection error', error);

            // Log detailed connection information for debugging
            if (window.Echo.connector && window.Echo.connector.options) {
                const options = window.Echo.connector.options;
                this.log('Connection details at error time:', {
                    wsHost: options.wsHost,
                    wsPort: options.wsPort,
                    wssPort: options.wssPort,
                    forceTLS: options.forceTLS,
                    enabledTransports: options.enabledTransports,
                    currentUrl: window.location.href,
                    errorType: error?.type || 'unknown',
                    errorCode: error?.status || 'unknown'
                });
            }

            this.connected = false;
            this.updateStatus('error', `Connection error: ${this.getErrorMessage(error)}`);
            this.attemptReconnect();
        });

        return true;
    }

    /**
     * Subscribe to a private channel
     *
     * @param {string} channelName - The channel name without 'private-' prefix
     * @param {object} eventHandlers - Object with event names as keys and handler functions as values
     * @returns {object|null} - The channel object or null if subscription failed
     */
    subscribeToPrivateChannel(channelName, eventHandlers = {}) {
        if (!window.Echo) {
            this.log(`Cannot subscribe to channel ${channelName}: Echo not available`);
            return null;
        }

        try {
            // Debug Echo connection state before subscribing
            if (window.Echo.connector && window.Echo.connector.pusher) {
                this.log(`Echo connection state before subscribing to ${channelName}:`,
                    window.Echo.connector.pusher.connection.state);
            }

            this.log(`Attempting to subscribe to private channel: ${channelName}`);
            const channel = window.Echo.private(channelName);

            // Store channel for resubscription if needed
            this.channels.set(channelName, { type: 'private', handlers: eventHandlers });

            // Add error handler
            channel.error((error) => {
                this.log(`Error subscribing to channel ${channelName}:`, error);
                this.updateStatus('channel-error', `Channel error: ${this.getErrorMessage(error)}`);

                // If this is an authorization error, we might need to refresh the page
                if (error && (error.type === 'AuthError' || error.status === 403)) {
                    this.showAuthError(channelName, error);
                }
            });

            // Add event handlers
            for (const [event, handler] of Object.entries(eventHandlers)) {
                this.log(`Adding event handler for ${event} on channel ${channelName}`);
                channel.listen(event, handler);
            }

            this.log(`Successfully subscribed to private channel: ${channelName}`);
            return channel;
        } catch (error) {
            this.log(`Exception subscribing to channel ${channelName}:`, error);
            this.updateStatus('channel-error', `Channel error: ${error.message}`);
            return null;
        }
    }

    /**
     * Resubscribe to all previously subscribed channels
     */
    resubscribeToChannels() {
        for (const [channelName, config] of this.channels.entries()) {
            this.log(`Resubscribing to ${config.type} channel: ${channelName}`);

            if (config.type === 'private') {
                this.subscribeToPrivateChannel(channelName, config.handlers);
            } else {
                // Handle other channel types if needed
                window.Echo.channel(channelName);
            }
        }
    }

    /**
     * Attempt to reconnect to the WebSocket server
     */
    attemptReconnect() {
        if (this.reconnectAttempts >= this.options.maxReconnectAttempts) {
            this.log(`Max reconnect attempts (${this.options.maxReconnectAttempts}) reached, falling back to polling`);
            this.updateStatus('max-attempts', 'Could not reconnect');
            this.enablePolling();
            return;
        }

        this.reconnectAttempts++;

        this.log(`Attempting to reconnect (${this.reconnectAttempts}/${this.options.maxReconnectAttempts})...`);
        this.updateStatus('reconnecting', `Reconnecting (${this.reconnectAttempts}/${this.options.maxReconnectAttempts})...`);

        setTimeout(() => this.reconnect(), this.options.reconnectInterval);
    }

    /**
     * Force a reconnection attempt
     */
    reconnect() {
        if (!window.Echo || !window.Echo.connector || !window.Echo.connector.pusher) {
            this.log('Cannot reconnect: Echo or Pusher not available');
            return;
        }

        try {
            // Disconnect and reconnect
            window.Echo.connector.pusher.disconnect();
            setTimeout(() => {
                window.Echo.connector.pusher.connect();
            }, 1000);
        } catch (error) {
            this.log('Error during reconnection:', error);
            this.updateStatus('reconnect-error', `Reconnection error: ${error.message}`);
            this.attemptReconnect();
        }
    }

    /**
     * Enable polling as a fallback mechanism
     */
    enablePolling() {
        if (this.pollingEnabled) {
            return;
        }

        this.pollingEnabled = true;

        // Dispatch an event that Livewire components can listen for
        window.dispatchEvent(new CustomEvent('websocket-fallback-to-polling', {
            detail: { reason: 'WebSocket connection failed' }
        }));

        this.log('Polling enabled as fallback mechanism');
    }

    /**
     * Disable polling when WebSocket is working
     */
    disablePolling() {
        if (!this.pollingEnabled) {
            return;
        }

        this.pollingEnabled = false;

        // Dispatch an event that Livewire components can listen for
        window.dispatchEvent(new CustomEvent('websocket-connection-restored', {
            detail: { message: 'WebSocket connection restored' }
        }));

        this.log('Polling disabled, using WebSocket connection');
    }

    /**
     * Update the connection status and notification
     *
     * @param {string} status - The connection status
     * @param {string} message - The status message to display
     */
    updateStatus(status, message) {
        if (!this.notificationElement) {
            return;
        }

        const statusIcon = this.notificationElement.querySelector('.status-icon');
        const statusText = this.notificationElement.querySelector('.status-text');
        const retryButton = this.notificationElement.querySelector('.retry-button');

        if (!statusIcon || !statusText) {
            return;
        }

        // Show the notification
        this.notificationElement.classList.remove('hidden');

        // Update status icon and text
        statusText.textContent = message;

        // Reset classes
        statusIcon.className = 'status-icon mr-2';
        this.notificationElement.querySelector('.flex').className = 'flex items-center p-2 text-sm rounded-lg';

        // Apply appropriate styling based on status
        switch (status) {
            case 'connected':
                statusIcon.innerHTML = '<svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path></svg>';
                this.notificationElement.querySelector('.flex').classList.add('bg-green-100', 'text-green-800', 'dark:bg-green-900', 'dark:text-green-300');

                // Hide after 3 seconds
                setTimeout(() => {
                    this.notificationElement.classList.add('hidden');
                }, 3000);

                // Hide retry button
                retryButton.classList.add('hidden');
                break;

            case 'disconnected':
                statusIcon.innerHTML = '<svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clip-rule="evenodd"></path></svg>';
                this.notificationElement.querySelector('.flex').classList.add('bg-yellow-100', 'text-yellow-800', 'dark:bg-yellow-900', 'dark:text-yellow-300');
                retryButton.classList.remove('hidden');
                break;

            case 'error':
            case 'channel-error':
            case 'reconnect-error':
            case 'max-attempts':
                statusIcon.innerHTML = '<svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clip-rule="evenodd"></path></svg>';
                this.notificationElement.querySelector('.flex').classList.add('bg-red-100', 'text-red-800', 'dark:bg-red-900', 'dark:text-red-300');
                retryButton.classList.remove('hidden');
                break;

            case 'reconnecting':
                statusIcon.innerHTML = '<svg class="w-4 h-4 animate-spin" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path></svg>';
                this.notificationElement.querySelector('.flex').classList.add('bg-blue-100', 'text-blue-800', 'dark:bg-blue-900', 'dark:text-blue-300');
                retryButton.classList.add('hidden');
                break;

            case 'unavailable':
            case 'timeout':
                statusIcon.innerHTML = '<svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clip-rule="evenodd"></path></svg>';
                this.notificationElement.querySelector('.flex').classList.add('bg-gray-100', 'text-gray-800', 'dark:bg-gray-800', 'dark:text-gray-300');
                retryButton.classList.remove('hidden');
                break;
        }
    }

    /**
     * Show authentication error with refresh option
     *
     * @param {string} channelName - The channel name
     * @param {object} error - The error object
     */
    showAuthError(channelName, error) {
        if (!this.notificationElement) {
            return;
        }

        // Create a more detailed error message
        const errorDiv = document.createElement('div');
        errorDiv.className = 'mt-2 p-3 bg-red-100 dark:bg-red-900 text-red-800 dark:text-red-200 rounded';
        errorDiv.innerHTML = `
            <h3 class="font-bold">Authentication Error</h3>
            <p class="text-sm mt-1">Could not authenticate to channel: ${channelName}</p>
            <p class="text-sm mt-2">Your session may have expired. Try refreshing the page.</p>
            <button id="refresh-page" class="mt-2 px-3 py-1 bg-blue-500 text-white rounded hover:bg-blue-600 text-sm">Refresh Page</button>
        `;

        // Add the error message to the page
        const container = document.querySelector(this.options.notificationContainer);
        if (container) {
            // Remove any existing error messages
            const existingError = container.querySelector('.bg-red-100, .bg-red-900');
            if (existingError && existingError !== this.notificationElement) {
                existingError.remove();
            }

            container.appendChild(errorDiv);

            // Add refresh button functionality
            document.getElementById('refresh-page').addEventListener('click', function() {
                window.location.reload();
            });
        }
    }

    /**
     * Get a user-friendly error message
     *
     * @param {object} error - The error object
     * @returns {string} - A user-friendly error message
     */
    getErrorMessage(error) {
        if (!error) {
            return 'Unknown error';
        }

        if (typeof error === 'string') {
            return error;
        }

        if (error.message) {
            return error.message;
        }

        if (error.type === 'AuthError') {
            return 'Authentication failed';
        }

        if (error.status) {
            return `Server error (${error.status})`;
        }

        return 'Connection error';
    }

    /**
     * Log a message if debug is enabled
     */
    log(...args) {
        if (this.options.debug) {
            console.log('[WebSocketManager]', ...args);
        }
    }
}

// Initialize the WebSocket manager when the DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    // Check if the notification container exists
    const container = document.querySelector('#websocket-status');
    if (!container) {
        console.warn('WebSocket status container #websocket-status not found, creating it');
        // Create the container if it doesn't exist
        const newContainer = document.createElement('div');
        newContainer.id = 'websocket-status';
        newContainer.className = 'fixed bottom-4 left-4 z-50';
        document.body.appendChild(newContainer);
    }

    // Create a global instance
    window.webSocketManager = new WebSocketManager({
        debug: true,
        notificationContainer: '#websocket-status',
        connectionTimeout: 5000,
        reconnectInterval: 3000,
        maxReconnectAttempts: 3
    });

    // Initialize the connection with a slight delay to ensure all scripts are loaded
    setTimeout(() => {
        window.webSocketManager.init();
    }, 500);
});
