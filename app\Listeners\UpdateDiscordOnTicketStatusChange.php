<?php

namespace App\Listeners;

use App\Events\TicketStatusChangedByWebApp;
use App\Services\DiscordMessagingService;
use App\Services\DiscordTicketChannelService;
use App\Models\Ticket; // For status constants
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Support\Facades\Log;

class UpdateDiscordOnTicketStatusChange implements ShouldQueue
{
    use InteractsWithQueue;

    protected DiscordMessagingService $messagingService;
    protected DiscordTicketChannelService $channelService;

    public function __construct(DiscordMessagingService $messagingService, DiscordTicketChannelService $channelService)
    {
        $this->messagingService = $messagingService;
        $this->channelService = $channelService;
    }

    public function handle(TicketStatusChangedByWebApp $event): void
    {
        $ticket = $event->ticket;
        $newStatus = $event->newStatus;
        $oldStatus = $event->oldStatus;
        $actingUser = $event->actingUser;
        $actingUserTag = $actingUser ? $actingUser->name : 'System';

        if (empty($ticket->discord_channel_id)) {
            Log::info("UpdateDiscordOnTicketStatusChange: Ticket #{$ticket->id} has no Discord channel ID. No Discord update needed.");
            return;
        }

        Log::info("UpdateDiscordOnTicketStatusChange: Processing status change for ticket #{$ticket->id} to '{$newStatus}' for Discord channel {$ticket->discord_channel_id}.");

        // Explicitly use constants if Ticket::STATUS_CLOSED is defined, otherwise use string.
        // Assuming 'closed' is the string representation for Ticket::STATUS_CLOSED.
        $closedStatusString = defined(Ticket::class . '::STATUS_CLOSED') ? Ticket::STATUS_CLOSED : 'closed';

        if ($newStatus === $closedStatusString) {
            $this->channelService->closeChannel(
                $ticket->discord_channel_id,
                $ticket,
                "Closed via Web Application by {$actingUserTag}",
                $actingUserTag
            );
        } else {
            $message = "Ticket #{$ticket->id} status has been updated from '{$oldStatus}' to '{$newStatus}' by {$actingUserTag} via the web application.";
            $embedData = [
                'title' => "Ticket #{$ticket->id} Status Updated",
                'description' => $message,
                'color' => 0xfcba03,
                'timestamp' => now()->toIso8601String(),
            ];
            $embed = $this->messagingService->createEmbed($embedData);
            if ($embed) {
                $this->messagingService->sendMessageToChannel($ticket->discord_channel_id, '', [$embed]);
            } else {
                $this->messagingService->sendMessageToChannel($ticket->discord_channel_id, $message);
            }
        }
    }
}
