/**
 * Relative Time Formatter
 * Converts timestamps to relative time format (e.g., "5 minutes ago")
 */

document.addEventListener('DOMContentLoaded', function() {
    // Initialize relative timestamps
    updateRelativeTimestamps();
    
    // Update timestamps every minute
    setInterval(updateRelativeTimestamps, 60000);
    
    // Update timestamps when new messages arrive
    document.addEventListener('livewire:load', function() {
        Livewire.hook('message.processed', (message, component) => {
            if (component.fingerprint.name === 'ticket-view') {
                updateRelativeTimestamps();
            }
        });
    });
});

/**
 * Update all timestamp elements with relative time
 */
function updateRelativeTimestamps() {
    const timestampElements = document.querySelectorAll('.relative-timestamp');
    
    timestampElements.forEach(element => {
        const timestamp = element.getAttribute('data-timestamp');
        if (timestamp) {
            const date = new Date(timestamp);
            element.textContent = formatRelativeTime(date);
        }
    });
}

/**
 * Format a date as relative time
 * @param {Date} date - The date to format
 * @returns {string} - Formatted relative time
 */
function formatRelativeTime(date) {
    const now = new Date();
    const diffInSeconds = Math.floor((now - date) / 1000);
    
    // Less than a minute
    if (diffInSeconds < 60) {
        return 'just now';
    }
    
    // Less than an hour
    if (diffInSeconds < 3600) {
        const minutes = Math.floor(diffInSeconds / 60);
        return `${minutes} ${minutes === 1 ? 'minute' : 'minutes'} ago`;
    }
    
    // Less than a day
    if (diffInSeconds < 86400) {
        const hours = Math.floor(diffInSeconds / 3600);
        return `${hours} ${hours === 1 ? 'hour' : 'hours'} ago`;
    }
    
    // Less than a week
    if (diffInSeconds < 604800) {
        const days = Math.floor(diffInSeconds / 86400);
        return `${days} ${days === 1 ? 'day' : 'days'} ago`;
    }
    
    // Format as date
    return date.toLocaleDateString();
}
