<?php

namespace Tests\Feature\Admin;

use App\Models\User;
use App\Enums\Role;
use Tests\TestCase;
use Illuminate\Foundation\Testing\RefreshDatabase;

class TagGeneratorAccessTest extends TestCase
{
    use RefreshDatabase;

    /** @test */
    public function admin_user_can_access_tag_generator()
    {
        // Create an admin user
        $adminUser = User::factory()->create([
            'permissions' => (string)Role::MINEWACHE_TEAM->value,
        ]);

        // Act as the admin user
        $this->actingAs($adminUser);

        // First test if the user can access admin dashboard
        $dashboardResponse = $this->get(route('admin.dashboard'));
        $this->assertEquals(200, $dashboardResponse->status(), 'Admin user should be able to access dashboard');

        // Try to access the tag generator
        try {
            $response = $this->get(route('admin.tag.generator'));

            // Should be successful
            $response->assertStatus(200);
            $response->assertSee('AI Video Tag Generator');
        } catch (\Exception $e) {
            $this->fail('Exception occurred: ' . $e->getMessage());
        }
    }

    /** @test */
    public function non_admin_user_cannot_access_tag_generator()
    {
        // Create a regular user with some other role
        $regularUser = User::factory()->create([
            'permissions' => (string)Role::BUILDER->value,
        ]);

        // Act as the regular user
        $this->actingAs($regularUser);

        // Try to access the tag generator
        $response = $this->get(route('admin.tag.generator'));

        // Should be forbidden
        $response->assertStatus(403);
    }

    /** @test */
    public function guest_user_cannot_access_tag_generator()
    {
        // Try to access the tag generator without authentication
        $response = $this->get(route('admin.tag.generator'));

        // Should redirect to login
        $response->assertRedirect();
    }

    /** @test */
    public function admin_user_has_correct_permissions()
    {
        // Create an admin user
        $adminUser = User::factory()->create([
            'permissions' => (string)Role::MINEWACHE_TEAM->value,
        ]);

        // Test the hasRole method directly
        $this->assertTrue($adminUser->hasRole(Role::MINEWACHE_TEAM));

        // Test that admin has all other roles too
        $this->assertTrue($adminUser->hasRole(Role::BUILDER));
        $this->assertTrue($adminUser->hasRole(Role::DESIGNER));
        $this->assertTrue($adminUser->hasRole(Role::DEVELOPER));
    }

    /** @test */
    public function admin_gate_works_correctly()
    {
        // Create an admin user
        $adminUser = User::factory()->create([
            'permissions' => (string)Role::MINEWACHE_TEAM->value,
        ]);

        // Act as the admin user
        $this->actingAs($adminUser);

        // Test the gate directly
        $this->assertTrue(\Gate::allows('role', Role::MINEWACHE_TEAM));
        $this->assertTrue(\Gate::allows('MINEWACHE_TEAM'));
    }
}
