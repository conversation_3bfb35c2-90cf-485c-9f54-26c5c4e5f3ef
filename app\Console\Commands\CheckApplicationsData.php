<?php

namespace App\Console\Commands;

use App\Models\Application;
use App\Models\User;
use Illuminate\Console\Command;

class CheckApplicationsData extends Command
{
    protected $signature = 'check:applications-data';
    protected $description = 'Check applications data in the database';

    public function handle()
    {
        $this->info('Checking applications data...');

        // Get all applications
        $applications = Application::all();
        $this->info('Total applications: ' . $applications->count());

        // Show applications with user_id
        $applicationsWithUserId = Application::whereNotNull('user_id')->get();
        $this->info('Applications with user_id: ' . $applicationsWithUserId->count());

        if ($applicationsWithUserId->count() > 0) {
            $this->table(
                ['ID', 'User ID', 'Discord ID', 'Name', 'Status', 'Editable'],
                $applicationsWithUserId->map(function ($app) {
                    return [
                        'id' => $app->id,
                        'user_id' => $app->user_id,
                        'discord_id' => $app->discord_id,
                        'name' => $app->name,
                        'status' => $app->status,
                        'editable' => $app->editable ? 'Yes' : 'No',
                    ];
                })
            );
        }

        // Show applications without user_id
        $applicationsWithoutUserId = Application::whereNull('user_id')->get();
        $this->info('Applications without user_id: ' . $applicationsWithoutUserId->count());

        if ($applicationsWithoutUserId->count() > 0) {
            $this->table(
                ['ID', 'Discord ID', 'Name', 'Status', 'Editable'],
                $applicationsWithoutUserId->map(function ($app) {
                    return [
                        'id' => $app->id,
                        'discord_id' => $app->discord_id,
                        'name' => $app->name,
                        'status' => $app->status,
                        'editable' => $app->editable ? 'Yes' : 'No',
                    ];
                })
            );
        }

        // Get all users
        $users = User::all();
        $this->info('Total users: ' . $users->count());

        // Show users with their IDs
        $this->table(
            ['ID', 'Username', 'Email'],
            $users->map(function ($user) {
                return [
                    'id' => $user->id,
                    'username' => $user->username,
                    'email' => $user->email,
                ];
            })
        );

        return 0;
    }
}
