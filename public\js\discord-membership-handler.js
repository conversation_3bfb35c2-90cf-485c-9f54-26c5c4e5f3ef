/**
 * Discord Membership Handler
 *
 * This script handles AJAX responses related to Discord membership checks.
 * It detects when a response indicates the user is not a member of the Discord server
 * and redirects them to the membership required page.
 */
document.addEventListener('DOMContentLoaded', function() {
    // Function to check if the response is a Larascord guild membership error
    function isLarascordGuildError(data) {
        return data &&
               data.larascord_message &&
               data.larascord_message.message &&
               data.larascord_message.message.includes('not a member of the required guilds');
    }

    // Function to handle Larascord guild membership errors
    function handleLarascordGuildError(data) {
        console.log('Larascord guild membership error detected:', data.larascord_message.message);
        // Use the redirect URL from the response if available, otherwise use our custom page
        const redirectUrl = data.larascord_message.redirect || '/discord/membership-required';
        window.location.href = redirectUrl;
        return true;
    }

    // Add a global AJAX error handler
    $(document).ajaxError(function(event, jqXHR, ajaxSettings, thrownError) {
        // Check if the response is a JSON response
        try {
            const response = JSON.parse(jqXHR.responseText);

            // Check if this is a Larascord guild membership error
            if (isLarascordGuildError(response)) {
                return handleLarascordGuildError(response);
            }

            // Check if this is our custom Discord membership error
            if (response.error_code === 'not_guild_member' || response.error_code === 'discord_api_error') {
                console.log('Discord membership error detected:', response.message);

                // If there's a redirect URL, redirect to it
                if (response.redirect_url) {
                    window.location.href = response.redirect_url;
                    return true;
                }
            }
        } catch (e) {
            // Not a JSON response or parsing error, ignore
            console.log('Error parsing AJAX response:', e);
        }
    });

    // Add a fetch API interceptor
    const originalFetch = window.fetch;
    window.fetch = async function(url, options) {
        try {
            const response = await originalFetch(url, options);

            // Clone the response to read it without consuming it
            const clone = response.clone();

            // Only process JSON responses
            const contentType = response.headers.get('content-type');
            if (contentType && contentType.includes('application/json')) {
                try {
                    const data = await clone.json();

                    // Check if this is a Larascord guild membership error
                    if (isLarascordGuildError(data)) {
                        handleLarascordGuildError(data);
                        // Still return the original response to not break the application flow
                        return response;
                    }

                    // Check if this is our custom Discord membership error
                    if (data.error_code === 'not_guild_member' || data.error_code === 'discord_api_error') {
                        console.log('Discord membership error detected in fetch:', data.message);

                        // If there's a redirect URL, redirect to it
                        if (data.redirect_url) {
                            window.location.href = data.redirect_url;
                        }
                    }
                } catch (e) {
                    // JSON parsing error, ignore
                    console.log('Error parsing fetch response:', e);
                }
            }

            return response;
        } catch (error) {
            throw error;
        }
    };

    // Add an Axios interceptor if Axios is available
    if (typeof axios !== 'undefined') {
        axios.interceptors.response.use(
            response => {
                // Check for Larascord guild membership error in successful responses
                // (Larascord might return 200 OK with error message)
                if (isLarascordGuildError(response.data)) {
                    handleLarascordGuildError(response.data);
                }
                return response;
            },
            error => {
                if (error.response && error.response.data) {
                    const data = error.response.data;

                    // Check if this is a Larascord guild membership error
                    if (isLarascordGuildError(data)) {
                        handleLarascordGuildError(data);
                        // Still reject the promise to not break application flow
                        return Promise.reject(error);
                    }

                    // Check if this is our custom Discord membership error
                    if (data.error_code === 'not_guild_member' || data.error_code === 'discord_api_error') {
                        console.log('Discord membership error detected in Axios:', data.message);

                        // If there's a redirect URL, redirect to it
                        if (data.redirect_url) {
                            window.location.href = data.redirect_url;
                        }
                    }
                }

                return Promise.reject(error);
            }
        );
    }

    // Direct script execution for non-AJAX responses
    // This handles the case when the page is loaded directly with the error response
    try {
        // Check if we're on a page that might contain the Larascord error in a script tag
        const scripts = document.querySelectorAll('script:not([src])');
        scripts.forEach(script => {
            const content = script.textContent;
            if (content && content.includes('larascord_message') && content.includes('not a member of the required guilds')) {
                // Try to extract the JSON
                const match = content.match(/({\s*"larascord_message"[^}]+})/g);
                if (match && match[0]) {
                    try {
                        const data = JSON.parse(match[0]);
                        if (isLarascordGuildError(data)) {
                            handleLarascordGuildError(data);
                        }
                    } catch (e) {
                        console.log('Error parsing inline script JSON:', e);
                    }
                }
            }
        });
    } catch (e) {
        console.log('Error checking inline scripts:', e);
    }
});
