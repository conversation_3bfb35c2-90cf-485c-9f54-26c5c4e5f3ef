<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\Artisan;

class SetupTicketSystem extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'tickets:setup {--fresh : Refresh the database}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Set up the ticket system by running migrations and seeders';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('Setting up the ticket system...');

        if ($this->option('fresh')) {
            $this->info('Refreshing the database...');
            Artisan::call('migrate:fresh', ['--seed' => true]);
            $this->info(Artisan::output());
        } else {
            $this->info('Running migrations...');
            Artisan::call('migrate');
            $this->info(Artisan::output());

            $this->info('Seeding the ticket system...');
            Artisan::call('db:seed', ['--class' => 'TicketSeeder']);
            $this->info(Artisan::output());
        }

        $this->info('Creating storage link if needed...');
        Artisan::call('storage:link');
        $this->info(Artisan::output());

        $this->info('Ticket system setup complete!');
    }
}
