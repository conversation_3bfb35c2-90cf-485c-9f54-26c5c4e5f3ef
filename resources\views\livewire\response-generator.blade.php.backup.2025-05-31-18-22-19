<div>
    @if(!$application)
        <div class="alert alert-warning shadow-lg mb-6">
            <div>
                <x-heroicon-o-exclamation-triangle class="w-6 h-6"/>
                <span>{{ __('admin.application_not_found') }}</span>
            </div>
            <div class="flex-none">
                <button wire:click="backToDetail" class="btn btn-sm btn-ghost">{{ __('admin.back_to_overview') }}</button>
            </div>
        </div>
    @else
        <!-- Processing/Error State Indicators -->
        @if($processingState === 'loading' || $processingState === 'processing')
            <div class="alert alert-info shadow-lg mb-6 animate-fade-in">
                <div>
                    <x-heroicon-o-arrow-path class="w-6 h-6 animate-spin"/>
                    <span>{{ __('admin.processing_application_data') }}</span>
                </div>
            </div>
        @endif

        @if($processingState === 'error')
            <div class="alert alert-error shadow-lg mb-6 animate-fade-in">
                <div>
                    <x-heroicon-o-exclamation-triangle class="w-6 h-6"/>
                    <span>{{ __('admin.error_processing_application') }}</span>
                </div>
                <div class="flex-none">
                    <button wire:click="refreshData" class="btn btn-sm btn-ghost gap-2">
                        <x-heroicon-o-arrow-path class="w-4 h-4"/>
                        {{ __('admin.try_again') }}
                    </button>
                </div>
            </div>
        @endif

        <!-- Discord-Bot Status Indicator -->
        <div class="flex justify-end mb-4">
            <div class="badge {{ $discordBotStatus ? 'badge-success' : 'badge-error' }} gap-2 p-3">
                <div class="w-2 h-2 rounded-full {{ $discordBotStatus ? 'bg-success-content' : 'bg-error-content' }} animate-pulse"></div>
                <span>Discord-Bot: {{ $discordBotStatus ? 'Online' : 'Offline' }}</span>
            </div>
        </div>

        <!-- Quick Action Buttons -->
        <div class="flex justify-center gap-4 mb-6">
            <button wire:click="quickApprove" class="btn btn-success gap-2" {{ !$application ? 'disabled' : '' }}>
                <x-heroicon-o-check-circle class="w-5 h-5"/>
                Schnell-Annahme
            </button>
            <button wire:click="quickReject" class="btn btn-error gap-2" {{ !$application ? 'disabled' : '' }}>
                <x-heroicon-o-x-circle class="w-5 h-5"/>
                Schnell-Ablehnung
            </button>
        </div>

        <!-- Discord Message Sent Notification -->
        @if($discordMessageSent)
            <div class="alert {{ $discordMessageStatus === 'success' ? 'alert-success' : 'alert-error' }} shadow-lg mb-6 animate-fade-in">
                <div>
                    @if($discordMessageStatus === 'success')
                        <x-heroicon-o-check-circle class="w-6 h-6"/>
                        <span>Nachricht erfolgreich an den Bewerber gesendet!</span>
                    @else
                        <x-heroicon-o-exclamation-triangle class="w-6 h-6"/>
                        <span>Fehler beim Senden der Nachricht an den Bewerber.</span>
                    @endif
                </div>
            </div>
        @endif

    <!-- Antwortgenerator -->
    <div class="bg-white dark:bg-base-100 rounded-xl shadow-lg p-6">
        <div class="flex justify-between items-center mb-6">
            <button wire:click="backToDetail" class="btn btn-ghost gap-2">
                <x-heroicon-o-arrow-left class="w-5 h-5"/>
                Zurück zur Detailansicht
            </button>

            <div class="flex items-center gap-4">
                <div class="badge badge-primary gap-2">
                    <x-heroicon-o-sparkles class="w-4 h-4"/>
                    Smart-Antwort
                </div>
                <h2 class="text-xl font-bold">Antwort generieren</h2>
            </div>
        </div>

        <!-- Clipboard-Meldung -->
        @if(session()->has('clipboard'))
            <div class="alert alert-success mb-6 shadow-lg">
                <div>
                    <x-heroicon-o-clipboard-document-check class="w-5 h-5"/>
                    <span>{{ session('clipboard') }}</span>
                </div>
            </div>
        @endif

        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <!-- Konfigurationsbereich -->
            <div class="space-y-6">
                <!-- Bewerbungsanalyse -->
                <div class="card bg-base-200 shadow-md">
                    <div class="card-body">
                        <h3 class="card-title flex items-center gap-2">
                            <x-heroicon-o-chart-bar class="w-5 h-5 text-primary"/>
                            Bewerbungsanalyse
                        </h3>

                        <div class="mt-4">
                            <!-- Empfehlung -->
                            <div
                                class="alert {{ $applicationStatus === 'approved' ? 'alert-success' : 'alert-error' }} mb-4">
                                @if($applicationStatus === 'approved')
                                    <x-heroicon-o-check-circle class="w-5 h-5"/>
                                    <span>Empfehlung: <strong>Annahme</strong></span>
                                @else
                                    <x-heroicon-o-x-circle class="w-5 h-5"/>
                                    <span>Empfehlung: <strong>Ablehnung</strong></span>
                                @endif
                            </div>

                            <div class="flex items-center gap-2 mb-2">
                                <span class="font-medium">Qualitätsbewertung:</span>
                                <div
                                    class="radial-progress {{ $applicationQuality >= 70 ? 'text-success' : ($applicationQuality >= 40 ? 'text-warning' : 'text-error') }}"
                                    style="--value:{{ $applicationQuality }}; --size:2rem;">{{ $applicationQuality }}
                                    %
                                </div>
                            </div>

                            <div class="divider my-2">Stärken</div>
                            @if(count($applicationStrengths) > 0)
                                <ul class="list-disc list-inside">
                                    @foreach($applicationStrengths as $strength)
                                        <li class="text-success">{{ $strength }}</li>
                                    @endforeach
                                </ul>
                            @else
                                <p class="text-sm italic">Keine besonderen Stärken erkannt.</p>
                            @endif

                            <div class="divider my-2">Schwächen</div>
                            @if(count($applicationWeaknesses) > 0)
                                <ul class="list-disc list-inside">
                                    @foreach($applicationWeaknesses as $weakness)
                                        <li class="text-error">{{ $weakness }}</li>
                                    @endforeach
                                </ul>
                            @else
                                <p class="text-sm italic">Keine besonderen Schwächen erkannt.</p>
                            @endif
                        </div>
                    </div>
                </div>

                <!-- Status-Auswahl -->
                <div class="card bg-base-200 shadow-md">
                    <div class="card-body">
                        <h3 class="card-title flex items-center gap-2">
                            <x-heroicon-o-flag class="w-5 h-5 text-primary"/>
                            Status ändern
                        </h3>

                        <div class="alert alert-info mb-4">
                            <x-heroicon-o-information-circle class="w-5 h-5"/>
                            <span>Die Antwort wurde basierend auf der Analyse automatisch generiert. Sie können den Status bei Bedarf ändern.</span>
                        </div>

                        <div class="mt-2">
                            <div class="flex gap-4">
                                <label class="label cursor-pointer flex items-center gap-2">
                                    <input type="radio" name="appStatus" value="approved"
                                           class="radio radio-success"
                                           wire:model.live="applicationStatus"/>
                                    <span class="label-text">Angenommen</span>
                                    @if($applicationStatus === 'approved')
                                        <span class="badge badge-sm badge-success">Empfohlen</span>
                                    @endif
                                </label>

                                <label class="label cursor-pointer flex items-center gap-2">
                                    <input type="radio" name="appStatus" value="rejected"
                                           class="radio radio-error"
                                           wire:model.live="applicationStatus"/>
                                    <span class="label-text">Abgelehnt</span>
                                    @if($applicationStatus === 'rejected')
                                        <span class="badge badge-sm badge-error">Empfohlen</span>
                                    @endif
                                </label>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Ablehnungsgründe (nur bei Ablehnung) -->
                @if($applicationStatus === 'rejected')
                    <div class="card bg-base-200 shadow-md">
                        <div class="card-body">
                            <h3 class="card-title flex items-center gap-2">
                                <x-heroicon-o-x-circle class="w-5 h-5 text-error"/>
                                Ablehnungsgründe
                            </h3>

                            <!-- Vorgeschlagene Gründe, wenn vorhanden -->
                            @if(count($suggestedReasons) > 0)
                                <div class="alert alert-info mb-4">
                                    <x-heroicon-o-light-bulb class="w-5 h-5"/>
                                    <span>Basierend auf der Bewerbung wurden folgende Ablehnungsgründe erkannt:</span>
                                </div>

                                <div class="flex flex-wrap gap-2 mb-4">
                                    @foreach($suggestedReasons as $reason)
                                        <button class="btn btn-sm btn-outline btn-info gap-1"
                                                wire:click="addSuggestedReason('{{ $reason }}')">
                                            <x-heroicon-o-plus class="w-4 h-4"/>
                                            @switch($reason)
                                                @case('RP')
                                                    RP-Server Missverständnis
                                                    @break
                                                @case('PC')
                                                    Kein PC
                                                    @break
                                                @case('PCMC')
                                                    Kein Minecraft Java
                                                    @break
                                                @case('INSUFFICIENT_RAM')
                                                    Zu wenig RAM
                                                    @break
                                                @case('INSUFFICIENT_FPS')
                                                    Zu wenig FPS
                                                    @break
                                                @case('NO_MICROPHONE')
                                                    Kein Mikrofon
                                                    @break
                                                @case('NO_DAW')
                                                    Keine DAW
                                                    @break
                                                @case('TOO_YOUNG')
                                                    Zu jung (unter 12 Jahre)
                                                    @break
                                                @default
                                                    {{ $reason }}
                                            @endswitch
                                        </button>
                                    @endforeach
                                </div>
                            @endif

                            <div class="mt-4 space-y-2">
                                <div class="form-control">
                                    <label class="label cursor-pointer justify-start gap-2">
                                        <input type="checkbox" class="checkbox checkbox-error"
                                               wire:click="toggleReason('RP')"
                                               @if(in_array('RP', $selectedReasons)) checked @endif />
                                        <span class="label-text">RP Server Missverständnis</span>
                                    </label>
                                    <div class="text-xs text-base-content/70 ml-7">
                                        Bewerber denkt, wir sind ein RP-Server
                                    </div>
                                </div>

                                <div class="form-control">
                                    <label class="label cursor-pointer justify-start gap-2">
                                        <input type="checkbox" class="checkbox checkbox-error"
                                               wire:click="toggleReason('PC')"
                                               @if(in_array('PC', $selectedReasons)) checked @endif />
                                        <span class="label-text">Kein PC vorhanden</span>
                                    </label>
                                </div>

                                <div class="form-control">
                                    <label class="label cursor-pointer justify-start gap-2">
                                        <input type="checkbox" class="checkbox checkbox-error"
                                               wire:click="toggleReason('PCMC')"
                                               @if(in_array('PCMC', $selectedReasons)) checked @endif />
                                        <span class="label-text">Kein Minecraft Java</span>
                                    </label>
                                </div>

                                <div class="form-control">
                                    <label class="label cursor-pointer justify-start gap-2">
                                        <input type="checkbox" class="checkbox checkbox-error"
                                               wire:click="toggleReason('INSUFFICIENT_RAM')"
                                               @if(in_array('INSUFFICIENT_RAM', $selectedReasons)) checked @endif />
                                        <span class="label-text">Zu wenig RAM</span>
                                    </label>
                                    <div class="text-xs text-base-content/70 ml-7">
                                        Weniger als 8GB RAM
                                    </div>
                                </div>

                                <div class="form-control">
                                    <label class="label cursor-pointer justify-start gap-2">
                                        <input type="checkbox" class="checkbox checkbox-error"
                                               wire:click="toggleReason('INSUFFICIENT_FPS')"
                                               @if(in_array('INSUFFICIENT_FPS', $selectedReasons)) checked @endif />
                                        <span class="label-text">Zu wenig FPS</span>
                                    </label>
                                    <div class="text-xs text-base-content/70 ml-7">
                                        Weniger als 30 FPS
                                    </div>
                                </div>

                                <div class="form-control">
                                    <label class="label cursor-pointer justify-start gap-2">
                                        <input type="checkbox" class="checkbox checkbox-error"
                                               wire:click="toggleReason('NO_MICROPHONE')"
                                               @if(in_array('NO_MICROPHONE', $selectedReasons)) checked @endif />
                                        <span class="label-text">Kein Mikrofon</span>
                                    </label>
                                </div>

                                <div class="form-control">
                                    <label class="label cursor-pointer justify-start gap-2">
                                        <input type="checkbox" class="checkbox checkbox-error"
                                               wire:click="toggleReason('NO_DAW')"
                                               @if(in_array('NO_DAW', $selectedReasons)) checked @endif />
                                        <span class="label-text">Keine DAW</span>
                                    </label>
                                    <div class="text-xs text-base-content/70 ml-7">
                                        Für Musikproduzenten erforderlich
                                    </div>
                                </div>

                                <div class="form-control">
                                    <label class="label cursor-pointer justify-start gap-2">
                                        <input type="checkbox" class="checkbox checkbox-error"
                                               wire:click="toggleReason('TOO_YOUNG')"
                                               @if(in_array('TOO_YOUNG', $selectedReasons)) checked @endif />
                                        <span class="label-text">Zu jung</span>
                                    </label>
                                    <div class="text-xs text-base-content/70 ml-7">
                                        Unter 12 Jahre alt
                                    </div>
                                </div>

                                <div class="form-control">
                                    <label class="label cursor-pointer justify-start gap-2">
                                        <input type="checkbox" class="checkbox checkbox-error"
                                               wire:click="toggleReason('QUALITY')"
                                               @if(in_array('QUALITY', $selectedReasons)) checked @endif />
                                        <span class="label-text">Qualitativ unzureichend</span>
                                    </label>
                                    <div class="text-xs text-base-content/70 ml-7">
                                        Bewerbung entspricht nicht unseren Qualitätsanforderungen
                                    </div>
                                </div>

                                <div class="form-control">
                                    <label class="label cursor-pointer justify-start gap-2">
                                        <input type="checkbox" class="checkbox checkbox-error"
                                               wire:click="toggleReason('TEAM_FULL')"
                                               @if(in_array('TEAM_FULL', $selectedReasons)) checked @endif />
                                        <span class="label-text">Team voll</span>
                                    </label>
                                    <div class="text-xs text-base-content/70 ml-7">
                                        Wir suchen derzeit keine weiteren Mitglieder in diesem Bereich
                                    </div>
                                </div>

                                <div class="form-control">
                                    <label class="label">
                                        <span class="label-text">Benutzerdefinierte Gründe:</span>
                                    </label>
                                    <textarea wire:model="customReasons" class="textarea textarea-bordered h-24"
                                              placeholder="Geben Sie hier benutzerdefinierte Ablehnungsgründe ein..."></textarea>
                                </div>
                            </div>
                        </div>
                    </div>
                @endif

                <!-- Aktionen -->
                <div class="card bg-base-200 shadow-md">
                    <div class="card-body">
                        <h3 class="card-title flex items-center gap-2">
                            <x-heroicon-o-cog-6-tooth class="w-5 h-5 text-primary"/>
                            Aktionen
                        </h3>

                        <div class="flex flex-wrap gap-2 mt-4">
                            <button wire:click="generateResponse"
                                    class="btn btn-primary gap-2 {{ ($processingState === 'loading' || $processingState === 'processing') ? 'btn-disabled loading' : '' }}"
                                    @if($processingState === 'loading' || $processingState === 'processing') disabled @endif>
                                <x-heroicon-o-arrow-path class="w-5 h-5 {{ $processingState !== 'processing' ? '' : 'animate-spin' }}"/>
                                {{ ($processingState === 'processing') ? 'Generiere...' : 'Antwort neu generieren' }}
                            </button>

                            <button wire:click="generateSmartResponse"
                                    class="btn btn-accent gap-2 {{ ($processingState === 'loading' || $processingState === 'processing') ? 'btn-disabled' : '' }}"
                                    @if($processingState === 'loading' || $processingState === 'processing') disabled @endif>
                                <x-heroicon-o-sparkles class="w-5 h-5"/>
                                Smart-Response
                            </button>

                            <button wire:click="copyToClipboard('copyable')"
                                    class="btn btn-secondary gap-2 {{ ($processingState === 'loading' || $processingState === 'processing' || empty($generatedResponse['copyable'])) ? 'btn-disabled' : '' }}"
                                    @if($processingState === 'loading' || $processingState === 'processing' || empty($generatedResponse['copyable'])) disabled @endif>
                                <x-heroicon-o-clipboard-document class="w-5 h-5"/>
                                In Zwischenablage kopieren
                            </button>

                            <!-- Discord-Nachricht senden -->
                            @if($discordBotStatus)
                                <button wire:click="sendDiscordMessage"
                                        class="btn btn-accent gap-2 {{ ($processingState === 'loading' || $processingState === 'processing' || $discordMessageSent) ? 'btn-disabled' : '' }}"
                                        @if($processingState === 'loading' || $processingState === 'processing' || $discordMessageSent) disabled @endif>
                                    <x-heroicon-o-paper-airplane class="w-5 h-5"/>
                                    {{ $discordMessageSent ? 'Nachricht gesendet' : 'An Bewerber senden' }}
                                </button>
                            @else
                                <button class="btn btn-disabled gap-2 tooltip" data-tip="Discord-Bot ist offline">
                                    <x-heroicon-o-paper-airplane class="w-5 h-5"/>
                                    An Bewerber senden
                                </button>
                            @endif
                        </div>

                        @if($processingState === 'success')
                            <div class="mt-4 text-sm text-success flex items-center gap-2">
                                <x-heroicon-o-check-circle class="w-5 h-5"/>
                                <span>Antwort erfolgreich generiert</span>
                            </div>
                        @endif
                    </div>
                </div>
            </div>

            <!-- Antwortvorschau -->
            <div class="card bg-base-200 shadow-md">
                <div class="card-body">
                    <h3 class="card-title flex items-center gap-2">
                        <x-heroicon-o-document-text class="w-5 h-5 text-primary"/>
                        Generierte Antwort
                    </h3>

                    <div class="tabs tabs-boxed bg-base-300 mb-4" id="response-tabs" wire:ignore>
                        <a class="tab tab-active" data-tab="preview" id="tab-preview">Vorschau</a>
                        <a class="tab" data-tab="markdown" id="tab-markdown">Markdown</a>
                        <a class="tab" data-tab="text" id="tab-text">Text</a>
                        <a class="tab {{ $discordBotStatus ? '' : 'text-base-content/50' }}" data-tab="discord" id="tab-discord">
                            <div class="flex items-center gap-1">
                                <span>Discord</span>
                                @if(!$discordBotStatus)
                                    <x-heroicon-o-exclamation-circle class="w-4 h-4 text-warning" />
                                @endif
                            </div>
                        </a>
                    </div>

                    <!-- Loading Overlay -->
                    @if($processingState === 'loading' || $processingState === 'processing')
                        <div class="bg-base-300/50 rounded-lg p-8 flex flex-col items-center justify-center mb-4 animate-pulse">
                            <div class="radial-progress text-primary animate-spin" style="--value:70; --size:3rem;"></div>
                            <p class="mt-4 font-medium text-base-content/70">Generiere Antwort...</p>
                        </div>
                    @endif

                    <div class="tab-content" id="response-tab-content">
                        <!-- Vorschau-Tab -->
                        <div class="p-4 bg-base-100 rounded-lg prose max-w-none {{ ($processingState === 'loading' || $processingState === 'processing') ? 'opacity-50' : '' }}" data-tab-content="preview" id="content-preview">
                            @if(isset($generatedResponse['markdown']) && !empty($generatedResponse['markdown']))
                                <div class="markdown-content">
                                    {!! Illuminate\Support\Str::markdown($generatedResponse['markdown']) !!}
                                </div>
                            @else
                                <p class="italic text-base-content/70">{{ ($processingState === 'error') ? 'Fehler bei der Antwortgenerierung' : 'Keine Antwort generiert.' }}</p>
                            @endif
                        </div>

                        <!-- Markdown-Tab -->
                        <div class="hidden" data-tab-content="markdown" id="content-markdown">
                            <div class="relative">
                                <div class="flex justify-between items-center mb-2">
                                    <h4 class="font-medium text-base-content">Markdown-Quelltext</h4>
                                    <button wire:click="copyToClipboard('markdown')"
                                            class="btn btn-sm btn-ghost tooltip {{ (!isset($generatedResponse['markdown']) || empty($generatedResponse['markdown']) || $processingState === 'loading' || $processingState === 'processing') ? 'btn-disabled opacity-50' : '' }}"
                                            data-tip="Markdown kopieren"
                                            @if(!isset($generatedResponse['markdown']) || empty($generatedResponse['markdown']) || $processingState === 'loading' || $processingState === 'processing') disabled @endif>
                                        <x-heroicon-o-clipboard class="w-4 h-4 mr-1"/> Kopieren
                                    </button>
                                </div>
                                <pre class="p-4 bg-base-100 rounded-lg overflow-x-auto text-sm border border-base-300 {{ ($processingState === 'loading' || $processingState === 'processing') ? 'opacity-50' : '' }}"><code>{{ isset($generatedResponse['markdown']) && !empty($generatedResponse['markdown']) ? $generatedResponse['markdown'] : 'Keine Antwort generiert.' }}</code></pre>
                            </div>
                        </div>

                        <!-- Text-Tab -->
                        <div class="hidden" data-tab-content="text" id="content-text">
                            <div class="relative">
                                <div class="flex justify-between items-center mb-2">
                                    <h4 class="font-medium text-base-content">Kopierbarer Text</h4>
                                    <button wire:click="copyToClipboard('copyable')"
                                            class="btn btn-sm btn-ghost tooltip {{ (!isset($generatedResponse['copyable']) || empty($generatedResponse['copyable']) || $processingState === 'loading' || $processingState === 'processing') ? 'btn-disabled opacity-50' : '' }}"
                                            data-tip="Text kopieren"
                                            @if(!isset($generatedResponse['copyable']) || empty($generatedResponse['copyable']) || $processingState === 'loading' || $processingState === 'processing') disabled @endif>
                                        <x-heroicon-o-clipboard class="w-4 h-4 mr-1"/> Kopieren
                                    </button>
                                </div>
                                <pre class="p-4 bg-base-100 rounded-lg overflow-x-auto text-sm whitespace-pre-wrap border border-base-300 {{ ($processingState === 'loading' || $processingState === 'processing') ? 'opacity-50' : '' }}"><code>{{ isset($generatedResponse['copyable']) && !empty($generatedResponse['copyable']) ? $generatedResponse['copyable'] : 'Keine Antwort generiert.' }}</code></pre>
                            </div>
                        </div>

                        <!-- Discord-Tab -->
                        <div class="hidden" data-tab-content="discord" id="content-discord">
                            <div class="relative">
                                <div class="flex justify-between items-center mb-2">
                                    <h4 class="font-medium text-base-content">Discord-Vorschau</h4>
                                    @if($discordBotStatus)
                                        <div class="badge badge-success gap-1">
                                            <div class="w-2 h-2 rounded-full bg-success-content animate-pulse"></div>
                                            Bot Online
                                        </div>
                                    @else
                                        <div class="badge badge-warning gap-1">
                                            <div class="w-2 h-2 rounded-full bg-warning-content animate-pulse"></div>
                                            Bot Offline
                                        </div>
                                    @endif
                                </div>

                                @if(!$discordBotStatus)
                                    <div class="alert alert-warning mb-4">
                                        <div>
                                            <x-heroicon-o-exclamation-triangle class="w-5 h-5"/>
                                            <span>Der Discord-Bot ist derzeit offline. Die Nachricht kann nicht gesendet werden.</span>
                                        </div>
                                    </div>
                                @endif

                                <div class="bg-[#36393f] text-[#dcddde] p-4 rounded-lg mb-4 font-sans border border-[#202225]">
                                    <div class="flex items-start gap-3 mb-2">
                                        <div class="w-10 h-10 rounded-full bg-[#5865f2] flex items-center justify-center text-white font-bold text-lg">M</div>
                                        <div class="flex-1">
                                            <div class="flex items-center gap-2">
                                                <span class="font-semibold text-white">Minewache Bot</span>
                                                <span class="text-xs bg-[#5865f2] text-white px-1 rounded">BOT</span>
                                                <span class="text-xs text-[#a3a6aa]">heute um {{ now()->format('H:i') }}</span>
                                            </div>
                                            <div class="mt-1 whitespace-pre-wrap">
                                                @if(isset($generatedResponse['discord']) && !empty($generatedResponse['discord']))
                                                    {!! nl2br(e($generatedResponse['discord'])) !!}
                                                @else
                                                    <span class="text-[#dcddde]/70 italic">Keine Discord-Antwort generiert.</span>
                                                @endif
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                @if($discordBotStatus)
                                    <div class="flex justify-center">
                                        <button wire:click="sendDiscordMessage"
                                                class="btn btn-primary gap-2 {{ ($processingState === 'loading' || $processingState === 'processing' || $discordMessageSent) ? 'btn-disabled' : '' }}"
                                                @if($processingState === 'loading' || $processingState === 'processing' || $discordMessageSent) disabled @endif>
                                            <x-heroicon-o-paper-airplane class="w-5 h-5"/>
                                            {{ $discordMessageSent ? 'Nachricht gesendet' : 'Diese Nachricht an Bewerber senden' }}
                                        </button>
                                    </div>
                                @endif
                            </div>
                        </div>
                    </div>

                    <!-- Tabs für verschiedene Ansichten der generierten Antwort -->
                    <div class="mt-6 bg-base-100 rounded-lg shadow-md p-4">
                        <h3 class="text-lg font-semibold mb-3 flex items-center gap-2">
                            <x-heroicon-o-document-text class="w-5 h-5 text-primary"/>
                            Generierte Antwort
                        </h3>

                        @if($processingState === 'loading' || $processingState === 'processing')
                            <div class="flex items-center justify-center p-8">
                                <div class="radial-progress text-primary animate-spin" style="--value:70; --size:3rem;"></div>
                                <span class="ml-4 text-base-content/70">Generiere Antwort...</span>
                            </div>
                        @elseif(isset($generatedResponse['markdown']) && !empty($generatedResponse['markdown']))
                            <!-- Tabs für verschiedene Ansichten -->
                            <div class="tabs tabs-boxed bg-base-300 mb-4">
                                <a class="tab tab-active" id="view-tab-preview">Vorschau</a>
                                <a class="tab" id="view-tab-markdown">Markdown</a>
                                <a class="tab" id="view-tab-text">Text</a>
                                <a class="tab" id="view-tab-discord">Discord</a>
                            </div>

                            <!-- Tab-Inhalte -->
                            <div class="tab-views">
                                <!-- Vorschau-Ansicht -->
                                <div class="view-content" id="view-content-preview">
                                    <div class="prose max-w-none markdown-content">
                                        {!! Illuminate\Support\Str::markdown($generatedResponse['markdown']) !!}
                                    </div>
                                </div>

                                <!-- Markdown-Ansicht -->
                                <div class="view-content hidden" id="view-content-markdown">
                                    <pre class="p-4 bg-base-200 rounded-lg overflow-x-auto text-sm border border-base-300"><code>{{ $generatedResponse['markdown'] }}</code></pre>
                                </div>

                                <!-- Text-Ansicht -->
                                <div class="view-content hidden" id="view-content-text">
                                    <pre class="p-4 bg-base-200 rounded-lg overflow-x-auto text-sm whitespace-pre-wrap border border-base-300"><code>{{ isset($generatedResponse['copyable']) && !empty($generatedResponse['copyable']) ? $generatedResponse['copyable'] : 'Kein kopierbarer Text generiert.' }}</code></pre>
                                </div>

                                <!-- Discord-Ansicht -->
                                <div class="view-content hidden" id="view-content-discord">
                                    <div class="bg-[#36393f] text-[#dcddde] p-4 rounded-lg mb-4 font-sans border border-[#202225]">
                                        <div class="flex items-start gap-3 mb-2">
                                            <div class="w-10 h-10 rounded-full bg-[#5865f2] flex items-center justify-center text-white font-bold text-lg">M</div>
                                            <div class="flex-1">
                                                <div class="flex items-center gap-2">
                                                    <span class="font-semibold text-white">Minewache Bot</span>
                                                    <span class="text-xs bg-[#5865f2] text-white px-1 rounded">BOT</span>
                                                    <span class="text-xs text-[#a3a6aa]">heute um {{ now()->format('H:i') }}</span>
                                                </div>
                                                <div class="mt-1 whitespace-pre-wrap">
                                                    @if(isset($generatedResponse['discord']) && !empty($generatedResponse['discord']))
                                                        @php
                                                            // Entferne übermäßige Leerzeilen (mehr als 2 aufeinanderfolgende Zeilenumbrüche)
                                                            $discordText = preg_replace('/\n{3,}/', "\n\n", $generatedResponse['discord']);
                                                        @endphp
                                                        {!! nl2br(e($discordText)) !!}
                                                    @else
                                                        <span class="text-[#dcddde]/70 italic">Keine Discord-Antwort generiert.</span>
                                                    @endif
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Kopier-Buttons -->
                            <div class="mt-4 flex justify-end gap-2">
                                <button wire:click="copyToClipboard('markdown')"
                                        class="btn btn-sm btn-outline gap-1">
                                    <x-heroicon-o-clipboard-document class="w-4 h-4"/>
                                    Markdown kopieren
                                </button>
                                <button wire:click="copyToClipboard('copyable')"
                                        class="btn btn-sm btn-primary gap-1">
                                    <x-heroicon-o-clipboard-document-check class="w-4 h-4"/>
                                    Text kopieren
                                </button>
                                @if(isset($generatedResponse['discord']) && !empty($generatedResponse['discord']))
                                <button wire:click="copyToClipboard('discord')"
                                        class="btn btn-sm btn-accent gap-1">
                                    <x-heroicon-o-clipboard-document class="w-4 h-4"/>
                                    Discord kopieren
                                </button>
                                @endif
                            </div>

                            <!-- JavaScript für die Tabs -->
                            <script>
                                document.addEventListener('DOMContentLoaded', function() {
                                    const viewTabs = document.querySelectorAll('[id^="view-tab-"]');
                                    const viewContents = document.querySelectorAll('[id^="view-content-"]');

                                    viewTabs.forEach(tab => {
                                        tab.addEventListener('click', function() {
                                            // Tab-ID extrahieren (z.B. "preview" aus "view-tab-preview")
                                            const viewType = this.id.replace('view-tab-', '');

                                            // Alle Tabs deaktivieren und den geklickten aktivieren
                                            viewTabs.forEach(t => t.classList.remove('tab-active'));
                                            this.classList.add('tab-active');

                                            // Alle Inhalte ausblenden und den passenden einblenden
                                            viewContents.forEach(content => {
                                                content.classList.add('hidden');
                                            });
                                            document.getElementById(`view-content-${viewType}`).classList.remove('hidden');
                                        });
                                    });
                                });
                            </script>
                        @else
                            <div class="p-8 text-center text-base-content/70 italic">
                                <p>{{ ($processingState === 'error') ? 'Fehler bei der Antwortgenerierung. Bitte versuchen Sie es erneut.' : 'Keine Antwort generiert. Klicken Sie auf "Antwort neu generieren" oder "Smart-Response", um eine Antwort zu erstellen.' }}</p>
                            </div>
                        @endif
                    </div>

                    <!-- Copy Success Notification - This will be shown by JS -->
                    <div id="copy-notification" class="mt-4 hidden">
                        <div class="alert alert-success shadow-sm">
                            <div>
                                <x-heroicon-o-clipboard-document-check class="w-5 h-5"/>
                                <span>Text erfolgreich in die Zwischenablage kopiert!</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- CSS für den Response Generator -->
    <style>
        .markdown-content h1, .markdown-content h2, .markdown-content h3, .markdown-content h4, .markdown-content h5, .markdown-content h6 {
            margin-top: 1rem;
            margin-bottom: 0.5rem;
            font-weight: 600;
            line-height: 1.25;
        }

        .markdown-content h1 {
            font-size: 1.5rem;
        }

        .markdown-content h2 {
            font-size: 1.25rem;
        }

        .markdown-content h3 {
            font-size: 1.125rem;
        }

        .markdown-content p {
            margin-bottom: 1rem;
        }

        .markdown-content ul, .markdown-content ol {
            margin-bottom: 1rem;
            padding-left: 1.5rem;
        }

        .markdown-content ul {
            list-style-type: disc;
        }

        .markdown-content ol {
            list-style-type: decimal;
        }

        .markdown-content li {
            margin-bottom: 0.25rem;
        }

        .markdown-content a {
            color: hsl(var(--p));
            text-decoration: underline;
        }

        .markdown-content blockquote {
            border-left: 4px solid hsl(var(--b3));
            padding-left: 1rem;
            margin-left: 0;
            margin-right: 0;
            font-style: italic;
        }

        .markdown-content code {
            background-color: hsl(var(--b3));
            padding: 0.2rem 0.4rem;
            border-radius: 0.25rem;
            font-family: monospace;
        }

        .markdown-content pre {
            background-color: hsl(var(--b3));
            padding: 1rem;
            border-radius: 0.5rem;
            overflow-x: auto;
            margin-bottom: 1rem;
        }

        .markdown-content pre code {
            background-color: transparent;
            padding: 0;
            border-radius: 0;
        }
    </style>

    <!-- JavaScript für den Response Generator -->
    <script>
        document.addEventListener('DOMContentLoaded', () => {

        // Livewire event listeners
        document.addEventListener('livewire:initialized', () => {
            // Copy to clipboard functionality with animation
            const copyNotification = document.getElementById('copy-notification');

            Livewire.on('copyToClipboard', (event) => {
                const text = event.text;

                navigator.clipboard.writeText(text).then(() => {
                    // Show notification
                    copyNotification.classList.remove('hidden');
                    copyNotification.classList.add('animate-fade-in');

                    // Hide after delay
                    setTimeout(() => {
                        copyNotification.classList.add('animate-fade-out');
                        setTimeout(() => {
                            copyNotification.classList.add('hidden');
                            copyNotification.classList.remove('animate-fade-out');
                        }, 500);
                    }, 3000);
                });
            });

            // Processing state changes
            Livewire.on('processing-started', () => {
                // Scroll to top of the container to show the loading state
                const container = document.querySelector('.card');
                if (container) {
                    container.scrollIntoView({ behavior: 'smooth' });
                }
            });

            Livewire.on('response-generated', (event) => {
                console.log('Response generated event received');

                // Initialize tabs
                if (typeof initResponseGeneratorTabs === 'function') {
                    setTimeout(() => {
                        initResponseGeneratorTabs();

                        // Ensure the preview tab is active
                        const previewTab = document.getElementById('tab-preview');
                        if (previewTab) {
                            previewTab.click();
                        }
                    }, 100);
                }

                // Highlight the status based on the response
                const status = event.status;
                if (status === 'approved' || status === 'rejected') {
                    const statusInput = document.querySelector(`input[name="appStatus"][value="${status}"]`);
                    if (statusInput) {
                        statusInput.focus();
                        statusInput.parentElement.classList.add('ring', 'ring-primary', 'animate-pulse');

                        // Remove highlighting after a delay
                        setTimeout(() => {
                            statusInput.parentElement.classList.remove('ring', 'ring-primary', 'animate-pulse');
                        }, 2000);
                    }
                }
            });

            // Listen for response-updated event
            Livewire.on('response-updated', () => {
                console.log('Response updated event received');

                // Initialize tabs
                if (typeof initResponseGeneratorTabs === 'function') {
                    setTimeout(() => {
                        initResponseGeneratorTabs();

                        // Ensure the preview tab is active
                        const previewTab = document.getElementById('tab-preview');
                        if (previewTab) {
                            previewTab.click();
                        }
                    }, 100);
                }
            });

            // Listen for response-component-rendered event
            Livewire.on('response-component-rendered', () => {
                console.log('Response component rendered event received');

                // Initialize tabs
                if (typeof initResponseGeneratorTabs === 'function') {
                    setTimeout(() => {
                        initResponseGeneratorTabs();

                        // Ensure the preview tab is active and visible
                        const previewTab = document.getElementById('tab-preview');
                        const previewContent = document.getElementById('content-preview');

                        if (previewTab && previewContent) {
                            // Activate the preview tab
                            const tabs = document.querySelectorAll('.tabs .tab');
                            tabs.forEach(t => t.classList.remove('tab-active'));
                            previewTab.classList.add('tab-active');

                            // Show the preview content
                            const tabContents = document.querySelectorAll('[data-tab-content]');
                            tabContents.forEach(content => {
                                content.classList.add('hidden');
                            });
                            previewContent.classList.remove('hidden');

                            console.log('Preview tab activated after component rendered');
                        }
                    }, 100);
                }

                // Überprüfen, ob die Antwort angezeigt wird
                setTimeout(() => {
                    const previewContent = document.getElementById('content-preview');
                    if (previewContent) {
                        const markdownContent = previewContent.querySelector('.markdown-content');
                        if (markdownContent) {
                            console.log('Markdown content found:', markdownContent.innerHTML.substring(0, 100) + '...');
                        } else {
                            console.log('Markdown content not found in preview tab');
                        }
                    }
                }, 200);
            });
        });
    </script>
    @endif
</div>
