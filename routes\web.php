<?php

use App\Http\Controllers\AdminController;
use App\Http\Controllers\LogController;
use App\Http\Controllers\SystemController;
use App\Http\Controllers\PermissionController;
use App\Http\Controllers\ProfileController;
use App\Http\Controllers\DataDeletionController;
use App\Http\Controllers\AuthConsentController;
use App\Http\Controllers\CustomLoginController;
use App\Http\Controllers\CustomLarascordController;
use App\Http\Controllers\ModController;
use Illuminate\Support\Facades\Route;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\App;
use Illuminate\Support\Facades\Session;
use Illuminate\Support\Facades\Cookie;
use Illuminate\Support\Facades\Broadcast;
use App\Http\Controllers\YoutubeController;
use App\Http\Controllers\QuestionController;
use App\Http\Controllers\ApplicationController;
use App\Http\Middleware\CheckToken;
use App\Livewire\Admin\TagGenerator;

/*
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
|
| Here is where you can register web routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| contains the "web" middleware group. Now create something great!
|
*/

// Broadcasting routes
Broadcast::routes(['middleware' => ['web', 'auth']]);

// Home route
Route::get('/', function () {
    return view('welcome');
})->name('home');

// Auth consent routes
Route::get('/auth/consent', [AuthConsentController::class, 'show'])->name('auth.consent');
Route::post('/auth/consent', [AuthConsentController::class, 'store'])->name('auth.consent.store');
Route::post('/auth/consent/clear', [AuthConsentController::class, 'clearConsent'])->name('auth.consent.clear');
Route::get('/auth/login-with-consent', function() {
    return view('auth.login-with-consent');
})->name('auth.login-with-consent');

// Discord routes
Route::get('/auth/discord', [CustomLoginController::class, 'login'])->name('auth.discord');
Route::get('/discord/membership-required', [\App\Http\Controllers\DiscordController::class, 'showMembershipRequired'])->name('discord.membership.required');
Route::get('/discord/refresh-membership', [\App\Http\Controllers\DiscordController::class, 'refreshMembership'])->middleware('auth')->name('discord.refresh.membership');

// Custom route to handle Discord OAuth callback
Route::get('/larascord/callback', [CustomLarascordController::class, 'handle'])->name('larascord.callback');

// Development routes for simulating Discord membership (only in non-production environments)
if (config('app.debug') || !app()->environment('production')) {
    Route::get('/dev/simulate-discord-membership/{status}', [\App\Http\Controllers\DiscordController::class, 'simulateMembership'])->name('dev.simulate.discord.membership');
}


// Language switcher route
Route::get('/language/{locale}', [\App\Http\Controllers\LanguageController::class, 'change'])->name('language.switch');



// Debug routes (only available in non-production environments or when APP_DEBUG=true)
if (config('app.debug') || !app()->environment('production')) {
    Route::prefix('debug')->middleware(['auth'])->group(function () {
        // Discord configuration debug
        Route::get('/discord-config', function () {
            return response()->json([
                'guild_id' => config('services.discord.guild_id'),
                'guild_id_set' => !empty(config('services.discord.guild_id')),
                'larascord_scopes' => config('larascord.scopes'),
                'larascord_guilds' => config('larascord.guilds'),
                'larascord_guilds_strict' => config('larascord.guilds_strict'),
                'user' => auth()->check() ? auth()->user()->only(['id', 'username', 'global_name']) : null,
            ]);
        });

        // WebSocket connection debug pages
        Route::get('/websocket', function () {
            return view('debug.websocket-status');
        })->name('debug.websocket');

        Route::get('/websocket-debug', function () {
            return view('debug.websocket-debug');
        })->name('debug.websocket-debug');
    });
}

// Additional debug routes (only available in non-production environments or when APP_DEBUG=true)
if (config('app.debug') || !app()->environment('production')) {
    // Debug route to check Larascord configuration
    Route::get('/debug/larascord-config', function () {
        return response()->json([
            'client_id' => config('larascord.client_id'),
            'client_id_set' => !empty(config('larascord.client_id')),
            'scopes' => config('larascord.scopes'),
            'guilds' => config('larascord.guilds'),
            'guilds_strict' => config('larascord.guilds_strict'),
            'route_prefix' => config('larascord.route_prefix'),
            'prompt' => config('larascord.prompt'),
        ]);
    });

    // Debug route to check session state
    Route::get('/debug/session-state', function () {
        return response()->json([
            'session_id' => session()->getId(),
            'fresh_discord_login' => session('fresh_discord_login'),
            'discord_login_timestamp' => session('discord_login_timestamp'),
            'time_since_login' => session('discord_login_timestamp') ? now()->timestamp - session('discord_login_timestamp') : null,
            'gdpr_consent' => Cookie::has('gdpr_consent'),
            'intended_url' => session('url.intended'),
            'authenticated' => auth()->check(),
            'user' => auth()->check() ? auth()->user()->only(['id', 'username', 'global_name']) : null,
        ]);
    });

    // Debug route to check if user is a member of the Discord server
    Route::get('/debug/discord-membership', function () {
        if (!auth()->check()) {
            return response()->json(['error' => 'Not logged in']);
        }

        $user = auth()->user();
        $guildId = config('services.discord.guild_id');

        try {
            $guildMember = $user->getGuildMember($guildId);

            return response()->json([
                'user_id' => $user->id,
                'guild_id' => $guildId,
                'is_member' => $guildMember ? true : false,
                'guild_member' => $guildMember,
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'error' => $e->getMessage(),
                'file' => $e->getFile(),
                'line' => $e->getLine(),
            ], 500);
        }
    });
}

Route::get('/bewerben', function () {
    return view('bewerben');
})->middleware(['auth', 'discord.member'])->name('bewerben');

// Updated route for the new application wizard
Route::get('/bewerben/wizard', function () {
    return view('application_wizard');
})->middleware(['auth', 'discord.member', CheckToken::class])->name('application_wizard');

// Success page after application submission
Route::get('/bewerben/erfolg', function () {
    if (!session()->has('success')) {
        return redirect()->route('bewerben');
    }
    return view('application_success');
})->middleware(['auth'])->name('applications.success');

Route::get('/bewerben/form', function () {
    return view('application_form');
})->middleware(['auth', 'discord.member', CheckToken::class])->name('application_form');

Route::middleware(['auth', 'discord.member', CheckToken::class])->group(function () {
    Route::post('/applications/store', [ApplicationController::class, 'store'])->name('applications.store');
    Route::get('bewerben/questions', [QuestionController::class, 'show'])->name('questions');
    Route::get('bewerben/about-you', [QuestionController::class, 'showAboutYouForm'])->name('questions.about_you');
    Route::post('bewerben/about-you', [QuestionController::class, 'submitAboutYou'])->name('submit_about_you');
    Route::get('bewerben/verify', [QuestionController::class, 'showVerify'])->name('questions.verify');
});

// Routen für Bewerbungsmanagement durch den Benutzer (nur Auth erforderlich)
Route::middleware(['auth'])->group(function () {
    Route::get('meine-bewerbungen', [ApplicationController::class, 'myApplications'])->name('my.applications');
    Route::get('meine-bewerbungen/{id}', [ApplicationController::class, 'viewMyApplication'])->name('my.applications.show');
    Route::get('meine-bewerbungen/{id}/bearbeiten', [ApplicationController::class, 'editMyApplication'])->name('my.applications.edit');
    Route::post('meine-bewerbungen/{id}/update', [ApplicationController::class, 'updateMyApplication'])->name('my.applications.update');
});

Route::get('/bewerbungen', [ApplicationController::class, 'index'])->name('applications.index');

Route::get('/bewerbungen/{discord_id}/{id}', [ApplicationController::class, 'show'])->middleware(['auth'])->name('applications.show');

Route::get('/mods', [ModController::class, 'index'])->name('mods');

Route::get('/youtube', function () {
    return view('youtube-livewire');
})->name('youtube');

Route::get('/partner', function () {
    return view('partner');
})->name('partner');

Route::get('/branding', function () {
    return view('branding');
})->name('branding');

Route::get('/links', function () {
    return view('links');
})->name('links');

Route::get('/serie', function () {
    return view('serie');
})->name('serie');

Route::get('/impressum', function () {
    return view('impressum');
})->name('impressum');

Route::get('/discord', function () {
    return redirect()->away('https://discord.gg/wwrK2csZnX');
})->name('discord');

Route::get('/datenschutz', function () {
    return view('datenschutz');
})->name('datenschutz');

Route::middleware('auth')->group(function () {
    // Profile routes
    Route::get('/profile', [ProfileController::class, 'edit'])->name('profile.edit');
    Route::patch('/profile', [ProfileController::class, 'update'])->name('profile.update');
    Route::delete('/profile', [ProfileController::class, 'destroy'])->name('profile.destroy');

    // Data protection routes
    Route::get('/profile/data', [DataDeletionController::class, 'showRequestForm'])->name('profile.data');
    Route::post('/profile/data/request', [DataDeletionController::class, 'submitRequest'])->name('profile.data.request');

    // Permission initialization
    Route::get('/permsinit', [PermissionController::class, 'initialize'])
        ->middleware('auth')->middleware(\App\Http\Middleware\GdprConsentMiddleware::class);

    // Ticket system routes
    Route::prefix('tickets')->group(function () {
        Route::get('/', [\App\Http\Controllers\TicketController::class, 'index'])->name('tickets.index');
        Route::get('/create', [\App\Http\Controllers\TicketController::class, 'create'])->name('tickets.create');
        Route::post('/', [\App\Http\Controllers\TicketController::class, 'store'])->name('tickets.store');
        Route::get('/{ticket}', [\App\Http\Controllers\TicketController::class, 'show'])->name('tickets.show');
        Route::post('/{ticket}/reply', [\App\Http\Controllers\TicketController::class, 'reply'])->name('tickets.reply');
        Route::post('/{ticket}/status', [\App\Http\Controllers\TicketController::class, 'updateStatus'])->name('tickets.status');
        Route::post('/{ticket}/assign', [\App\Http\Controllers\TicketController::class, 'assign'])->name('tickets.assign');
        Route::get('/attachments/{attachment}/download', [\App\Http\Controllers\TicketController::class, 'downloadAttachment'])->name('tickets.attachments.download');

        // AI support routes
        Route::post('/{ticket}/ai/generate', [\App\Http\Controllers\TicketAiController::class, 'generateResponse'])->name('tickets.ai.generate');
        Route::post('/{ticket}/ai/reply', [\App\Http\Controllers\TicketAiController::class, 'addAiResponse'])->name('tickets.ai.reply');
        Route::post('/ai/consent', [\App\Http\Controllers\TicketAiController::class, 'updateConsent'])->name('tickets.ai.consent');
    });
});

// Test route for media display (only in local environment)
Route::get('/test-media', [\App\Http\Controllers\TestMediaController::class, 'index'])
    ->middleware(['auth'])
    ->name('test.media');

// Admin-Routen mit Authentifizierung
Route::middleware(['auth', 'can:MINEWACHE_TEAM'])->prefix('admin')->name('admin.')->group(function () {
    // Dashboard und Bewerbungen
    Route::get('/bewerbungen', [AdminController::class, 'applications'])->name('applications');
    Route::get('/dashboard', [AdminController::class, 'dashboard'])->name('dashboard');
    Route::post('/bewerbungen/{id}/toggle-editability', [ApplicationController::class, 'toggleEditability'])->name('applications.toggle-editability');
    Route::get('/bewerbungen/{id}/edit', [ApplicationController::class, 'editApplication'])->name('applications.edit');

    // GDPR Data Deletion Requests
    Route::get('/data-requests', [DataDeletionController::class, 'index'])->name('data-requests.index');
    Route::get('/data-requests/{deletionRequest}', [DataDeletionController::class, 'show'])->name('data-requests.show');
    Route::post('/data-requests/{deletionRequest}/process', [DataDeletionController::class, 'processRequest'])->name('data-requests.process');

    // Benutzerverwaltung
    Route::get('/benutzer', [AdminController::class, 'users'])->name('users');
    Route::get('/benutzer/{user}/sync', [AdminController::class, 'syncRoles'])->name('sync-roles');

    // Logs & Monitoring
    Route::get('/logs', [LogController::class, 'index'])->name('logs');
    Route::get('/logs/{filename}', [LogController::class, 'show'])->name('logs.show');
    Route::delete('/logs/{filename}', [LogController::class, 'destroy'])->name('logs.destroy');
    Route::delete('/logs', [LogController::class, 'destroyAll'])->name('logs.clear-all');

    // Systemeinstellungen
    Route::get('/system', [SystemController::class, 'index'])->name('system');
    Route::post('/system/clear-cache', [SystemController::class, 'clearCache'])->name('system.clear-cache');
    Route::post('/system/generate-token', [SystemController::class, 'generateApiToken'])->name('system.generate-token');
    Route::delete('/system/tokens/{id}', [SystemController::class, 'deleteApiToken'])->name('system.delete-token');

    // API-Dokumentation
    Route::get('/api-docs', [AdminController::class, 'apiDocs'])->name('api-docs');

    // Discord Bot Management
    Route::get('/discord', [AdminController::class, 'discordBotStatus'])->name('discord-bot-status');
    Route::post('/discord/start', [AdminController::class, 'startDiscordBot'])->name('discord-bot-start');
    Route::post('/discord/stop', [AdminController::class, 'stopDiscordBot'])->name('discord-bot-stop');
    Route::post('/discord/restart', [AdminController::class, 'restartDiscordBot'])->name('discord-bot-restart');

    // Gemini AI Monitoring
    Route::get('/gemini', [\App\Http\Controllers\Admin\GeminiMonitoringController::class, 'index'])->name('gemini.monitoring');
    Route::post('/gemini/reset-limits', [\App\Http\Controllers\Admin\GeminiMonitoringController::class, 'resetRateLimits'])->name('gemini.reset-limits');

    // Route for the Tag Generator
    Route::get('/tag-generator', TagGenerator::class)
        ->name('tag.generator'); // Full name will be admin.tag.generator
});
