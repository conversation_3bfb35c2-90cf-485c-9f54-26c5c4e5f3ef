<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Cache;

class DiscordBotCommand extends Command
{
    protected $signature = 'discord:bot {action : start|stop|status|restart}';
    protected $description = 'Manage the Discord bot (start, stop, status, restart)';

    public function handle()
    {
        $action = $this->argument('action');

        switch ($action) {
            case 'start':
                return $this->startBot();
            case 'stop':
                return $this->stopBot();
            case 'status':
                return $this->statusBot();
            case 'restart':
                return $this->restartBot();
            default:
                $this->error("Invalid action: {$action}");
                $this->line('Available actions: start, stop, status, restart');
                return Command::FAILURE;
        }
    }

    private function startBot()
    {
        $this->info('Starting Discord bot...');

        // Check if bot is already running
        if ($this->isBotRunning()) {
            $this->warn('Discord bot is already running.');
            return Command::SUCCESS;
        }

        // Start the bot in the background
        $command = 'php artisan minewache:run-discord-bot > /dev/null 2>&1 &';
        exec($command);

        // Wait a moment and check if it started
        sleep(2);

        if ($this->isBotRunning()) {
            $this->info('✅ Discord bot started successfully.');
            return Command::SUCCESS;
        } else {
            $this->error('❌ Failed to start Discord bot.');
            return Command::FAILURE;
        }
    }

    private function stopBot()
    {
        $this->info('Stopping Discord bot...');

        if (!$this->isBotRunning()) {
            $this->warn('Discord bot is not running.');
            return Command::SUCCESS;
        }

        // Kill the Discord bot process
        if (PHP_OS_FAMILY === 'Windows') {
            // Windows command - kill all php.exe processes running the Discord bot
            exec('taskkill /F /IM php.exe /FI "WINDOWTITLE eq *minewache:run-discord-bot*" 2>nul');
        } else {
            // Linux/Unix command
            exec('pkill -f "php artisan minewache:run-discord-bot"');
        }

        // Wait a moment and check if it stopped
        sleep(2);

        if (!$this->isBotRunning()) {
            $this->info('✅ Discord bot stopped successfully.');
            return Command::SUCCESS;
        } else {
            $this->error('❌ Failed to stop Discord bot. You may need to kill it manually.');
            return Command::FAILURE;
        }
    }

    private function statusBot()
    {
        $this->info('Checking Discord bot status...');

        if ($this->isBotRunning()) {
            $this->info('✅ Discord bot is running.');

            // Try to get additional status from the bot service
            try {
                $discordService = app(\App\Services\DiscordService::class);
                $status = $discordService->getBotStatus();

                if ($status['online']) {
                    $this->line('🟢 Bot is online and responding');
                    if (isset($status['uptime'])) {
                        $this->line("⏱️  Uptime: {$status['uptime']}");
                    }
                    if (isset($status['version'])) {
                        $this->line("📦 Version: {$status['version']}");
                    }
                } else {
                    $this->warn('🟡 Bot process is running but not responding to status checks');
                }
            } catch (\Exception $e) {
                $this->warn('🟡 Bot process is running but status check failed: ' . $e->getMessage());
            }
        } else {
            $this->error('❌ Discord bot is not running.');
        }

        return Command::SUCCESS;
    }

    private function restartBot()
    {
        $this->info('Restarting Discord bot...');

        $this->stopBot();
        sleep(1);
        return $this->startBot();
    }

    private function isBotRunning(): bool
    {
        // Check if the Discord bot process is running
        if (PHP_OS_FAMILY === 'Windows') {
            // Windows command
            $output = shell_exec('tasklist /FI "IMAGENAME eq php.exe" /FO CSV | findstr "minewache:run-discord-bot"');
        } else {
            // Linux/Unix command
            $output = shell_exec('pgrep -f "php artisan minewache:run-discord-bot"');
        }

        return !empty(trim($output));
    }
}
