<x-app-layout>
    <x-slot name="header">
        <h2 class="font-display text-xl md:text-2xl text-gray-900 dark:text-white leading-tight">
            {{ __('API-Dokumentation') }}
        </h2>
    </x-slot>

    <div class="py-12">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="bg-white dark:bg-base-100 rounded-lg shadow-lg p-6">
                <h3 class="text-lg font-bold mb-4">API-Endpunkte</h3>

                <div class="overflow-x-auto">
                    <table class="table w-full">
                        <thead>
                            <tr>
                                <th>Endpunkt</th>
                                <th>Methode</th>
                                <th>Beschreibung</th>
                                <th>Authentifizierung</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td><code>/api/ping</code></td>
                                <td><span class="badge badge-success">GET</span></td>
                                <td>Einfacher Health-Check-Endpunkt</td>
                                <td>Keine</td>
                            </tr>
                            <tr>
                                <td><code>/api/discord/role-update</code></td>
                                <td><span class="badge badge-warning">POST</span></td>
                                <td>Aktualisiert Benutzerrollen vom Discord-Bot</td>
                                <td>API-Token</td>
                            </tr>
                            <tr>
                                <td><code>/api/auth-test</code></td>
                                <td><span class="badge badge-success">GET</span></td>
                                <td>Testet die API-Token-Authentifizierung</td>
                                <td>API-Token</td>
                            </tr>
                            <tr>
                                <td><code>/api/debug/role-mapping</code></td>
                                <td><span class="badge badge-success">GET</span></td>
                                <td>Zeigt Informationen zur Rollenzuordnung</td>
                                <td>API-Token</td>
                            </tr>
                            <tr>
                                <td><code>/api/discord-bot/users/{userId}/roles</code></td>
                                <td><span class="badge badge-success">GET</span></td>
                                <td>Ruft Benutzerrollen vom lokalen Discord-Bot ab</td>
                                <td>API-Token</td>
                            </tr>
                            <tr>
                                <td><code>/api/discord-bot/health/check</code></td>
                                <td><span class="badge badge-success">GET</span></td>
                                <td>Prüft den Status des Discord-Bots</td>
                                <td>API-Token</td>
                            </tr>
                            <tr>
                                <td><code>/api/discord-bot/health/status</code></td>
                                <td><span class="badge badge-success">GET</span></td>
                                <td>Gibt den aktuellen Status des Discord-Bots zurück</td>
                                <td>API-Token</td>
                            </tr>
                            <tr>
                                <td><code>/api/discord-bot/users/{userId}/sync</code></td>
                                <td><span class="badge badge-warning">POST</span></td>
                                <td>Synchronisiert Benutzerrollen vom lokalen Discord-Bot</td>
                                <td>API-Token</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>

            <div class="bg-white dark:bg-base-100 rounded-lg shadow-lg p-6 mt-6">
                <h3 class="text-lg font-bold mb-4">Authentifizierung</h3>

                <div class="mb-4">
                    <p class="mb-2">Die API verwendet Token-basierte Authentifizierung. Es gibt zwei Möglichkeiten, den Token zu übermitteln:</p>

                    <ol class="list-decimal list-inside space-y-2 ml-4">
                        <li>
                            <strong>Bearer-Token im Authorization-Header:</strong>
                            <pre class="bg-base-200 p-2 rounded-lg mt-1 overflow-x-auto">Authorization: Bearer YOUR_API_TOKEN</pre>
                        </li>
                        <li>
                            <strong>Query-Parameter:</strong>
                            <pre class="bg-base-200 p-2 rounded-lg mt-1 overflow-x-auto">https://example.com/api/endpoint?api_token=YOUR_API_TOKEN</pre>
                        </li>
                    </ol>
                </div>

                <div class="alert alert-info">
                    <svg xmlns="http://www.w3.org/2000/svg" class="stroke-current shrink-0 h-6 w-6" fill="none" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" /></svg>
                    <span>API-Tokens können im <a href="{{ route('admin.system') }}" class="underline">Systemeinstellungen</a>-Bereich generiert und verwaltet werden.</span>
                </div>
            </div>

            <div class="bg-white dark:bg-base-100 rounded-lg shadow-lg p-6 mt-6">
                <h3 class="text-lg font-bold mb-4">Beispielanfragen</h3>

                <div class="mb-6">
                    <h4 class="font-medium mb-2">Ping-Endpunkt (ohne Authentifizierung)</h4>
                    <pre class="bg-base-200 p-4 rounded-lg overflow-x-auto">curl -X GET "{{ config('app.url') }}/api/ping"</pre>

                    <div class="mt-2">
                        <p class="font-medium">Beispielantwort:</p>
                        <pre class="bg-base-200 p-4 rounded-lg overflow-x-auto">{
  "status": "ok",
  "timestamp": "2023-04-10T12:34:56+00:00"
}</pre>
                    </div>
                </div>

                <div class="mb-6">
                    <h4 class="font-medium mb-2">Auth-Test (mit Authentifizierung)</h4>
                    <pre class="bg-base-200 p-4 rounded-lg overflow-x-auto">curl -X GET "{{ config('app.url') }}/api/auth-test" \
  -H "Authorization: Bearer YOUR_API_TOKEN"</pre>

                    <div class="mt-2">
                        <p class="font-medium">Beispielantwort:</p>
                        <pre class="bg-base-200 p-4 rounded-lg overflow-x-auto">{
  "success": true,
  "status": "success",
  "message": "API token is valid",
  "data": {
    "token_type": "Bearer",
    "client_ip": "127.0.0.1",
    "server_time": "2023-04-10T12:34:56+00:00"
  },
  "timestamp": "2023-04-10T12:34:56+00:00"
}</pre>
                    </div>
                </div>

                <div class="mb-6">
                    <h4 class="font-medium mb-2">Discord-Rollen aktualisieren (mit Authentifizierung)</h4>
                    <pre class="bg-base-200 p-4 rounded-lg overflow-x-auto">curl -X POST "{{ config('app.url') }}/api/discord/role-update" \
  -H "Authorization: Bearer YOUR_API_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "user_id": "123456789012345678",
    "roles": ["1071024925437067294", "1033491844715257866"]
  }'</pre>

                    <div class="mt-2">
                        <p class="font-medium">Beispielantwort:</p>
                        <pre class="bg-base-200 p-4 rounded-lg overflow-x-auto">{
  "success": true,
  "status": "success",
  "message": "User roles updated successfully",
  "data": {
    "user_id": "123456789012345678",
    "roles_updated": true,
    "last_synced_at": "2023-04-10T12:34:56+00:00"
  },
  "timestamp": "2023-04-10T12:34:56+00:00"
}</pre>
                    </div>
                </div>

                <div class="mb-6">
                    <h4 class="font-medium mb-2">Benutzerrollen vom lokalen Discord-Bot abrufen</h4>
                    <pre class="bg-base-200 p-4 rounded-lg overflow-x-auto">curl -X GET "{{ config('app.url') }}/api/discord-bot/users/123456789012345678/roles" \
  -H "Authorization: Bearer YOUR_API_TOKEN"</pre>

                    <div class="mt-2">
                        <p class="font-medium">Beispielantwort:</p>
                        <pre class="bg-base-200 p-4 rounded-lg overflow-x-auto">{
  "success": true,
  "status": "success",
  "message": "Rollen für Benutzer username abgerufen",
  "data": {
    "username": "username",
    "roles": ["1071024925437067294", "1031205205934604439"],
    "roles_count": 2
  },
  "timestamp": "2023-04-10T12:34:56+00:00"
}</pre>
                    </div>
                </div>

                <div>
                    <h4 class="font-medium mb-2">Benutzerrollen vom lokalen Discord-Bot synchronisieren</h4>
                    <pre class="bg-base-200 p-4 rounded-lg overflow-x-auto">curl -X POST "{{ config('app.url') }}/api/discord-bot/users/123456789012345678/sync" \
  -H "Authorization: Bearer YOUR_API_TOKEN" \
  -H "Content-Type: application/json"</pre>

                    <div class="mt-2">
                        <p class="font-medium">Beispielantwort:</p>
                        <pre class="bg-base-200 p-4 rounded-lg overflow-x-auto">{
  "success": true,
  "status": "success",
  "message": "Berechtigungen für Benutzer username aktualisiert",
  "data": {
    "username": "username",
    "roles_count": 2,
    "permissions_bitmask": 256,
    "updated_at": "2023-04-10T12:34:56+00:00"
  },
  "timestamp": "2023-04-10T12:34:56+00:00"
}</pre>
                    </div>
                </div>
            </div>
        </div>
    </div>
</x-app-layout>
