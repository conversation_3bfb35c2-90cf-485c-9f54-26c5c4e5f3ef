<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Cache;
use App\Http\Middleware\CheckDiscordMembership;

class DiscordController extends Controller
{
    /**
     * Zeigt die Seite an, die erklärt, dass eine Discord-Mitgliedschaft erforderlich ist
     *
     * @return \Illuminate\View\View
     */
    public function showMembershipRequired()
    {
        return view('discord.membership-required');
    }

    /**
     * Aktualisiert den Discord-Mitgliedschaftsstatus nach erfolgreicher Anmeldung
     *
     * @param Request $request
     * @return \Illuminate\Http\RedirectResponse
     */
    public function refreshMembership(Request $request)
    {
        $user = Auth::user();

        if (!$user) {
            return redirect()->route('login');
        }

        // Cache löschen, um eine neue Überprüfung zu erzwingen
        CheckDiscordMembership::clearMembershipCache($user->id);

        Log::info('Discord membership cache cleared for refresh', [
            'user_id' => $user->id,
            'username' => $user->username
        ]);

        // Zurück zur vorherigen Seite oder zur Startseite
        return redirect()->back()
            ->with('success', 'Discord-Mitgliedschaftsstatus wurde aktualisiert.');
    }

    /**
     * Simuliert einen Discord-Mitgliedschaftsstatus (nur in Nicht-Produktionsumgebungen)
     *
     * @param Request $request
     * @param string $status 'true', 'false' oder 'reset'
     * @return \Illuminate\Http\RedirectResponse
     */
    public function simulateMembership(Request $request, $status)
    {
        // Nur in Nicht-Produktionsumgebungen oder wenn Debug-Modus aktiviert ist
        if (!config('app.debug') && app()->environment('production')) {
            abort(404);
        }

        if ($status === 'true') {
            $request->session()->put('simulate_discord_membership', true);
            $message = 'Discord-Mitgliedschaft wird als AKTIV simuliert.';
        } elseif ($status === 'false') {
            $request->session()->put('simulate_discord_membership', false);
            $message = 'Discord-Mitgliedschaft wird als INAKTIV simuliert.';
        } else {
            $request->session()->forget('simulate_discord_membership');
            $message = 'Discord-Mitgliedschaftssimulation wurde zurückgesetzt.';
        }

        if (config('app.debug')) {
            Log::debug('Discord membership simulation status changed', [
                'status' => $status,
                'user_id' => Auth::id(),
                'environment' => app()->environment()
            ]);
        }

        return redirect()->back()->with('info', $message);
    }
}
