name: Deploy to Production Server

on:
  push:
    branches: [ main ]
  workflow_dispatch:
    inputs:
      confirm:
        description: 'Type "yes" to confirm deployment to production'
        required: true
        default: 'no'

jobs:
  deploy:
    runs-on: ubuntu-latest
    if: github.event_name == 'workflow_dispatch' && github.event.inputs.confirm == 'yes' || github.event_name == 'push'
    
    steps:
    - uses: actions/checkout@v3
      with:
        fetch-depth: 0

    - name: Setup SSH
      uses: webfactory/ssh-agent@v0.7.0
      with:
        ssh-private-key: ${{ secrets.PROD_SSH_PRIVATE_KEY }}

    - name: Add SSH Known Hosts
      run: |
        mkdir -p ~/.ssh
        ssh-keyscan -H ${{ secrets.PROD_SERVER_IP }} >> ~/.ssh/known_hosts

    - name: Deploy to Production Server
      env:
        SERVER_IP: ${{ secrets.PROD_SERVER_IP }}
        SERVER_USER: ${{ secrets.PROD_SERVER_USER }}
        SERVER_PATH: ${{ secrets.PROD_SERVER_PATH }}
      run: |
        # Create a deployment log
        echo "Starting deployment to production server at $(date)" > deploy.log
        
        # SSH into the server and run deployment commands
        ssh $SERVER_USER@$SERVER_IP << 'EOF'
          cd ${{ secrets.PROD_SERVER_PATH }}
          
          # Create a backup before deployment
          timestamp=$(date +%Y%m%d_%H%M%S)
          mkdir -p backups
          tar -czf backups/backup_$timestamp.tar.gz --exclude=backups --exclude=vendor --exclude=node_modules .
          
          # Enable maintenance mode
          php artisan down --message="Die Website wird aktualisiert und ist in Kürze wieder verfügbar." --retry=60
          
          # Pull the latest changes
          git fetch --all
          git checkout main
          git pull origin main
          
          # Install dependencies
          composer install --no-interaction --prefer-dist --optimize-autoloader --no-dev
          npm ci
          npm run build
          
          # Run migrations
          php artisan migrate --force
          
          # Clear caches
          php artisan optimize:clear
          php artisan config:cache
          php artisan route:cache
          php artisan view:cache
          
          # Set proper permissions
          chown -R www-data:www-data .
          chmod -R 755 .
          chmod -R 775 storage bootstrap/cache
          
          # Restart services
          sudo systemctl restart minewache-reverb
          sudo systemctl restart minewache-queue
          sudo systemctl restart minewache-discord
          
          # Disable maintenance mode
          php artisan up
        EOF
        
        # Log the result
        if [ $? -eq 0 ]; then
          echo "Deployment to production server completed successfully at $(date)" >> deploy.log
          exit 0
        else
          echo "Deployment to production server failed at $(date)" >> deploy.log
          exit 1
        fi

    - name: Upload Deployment Log
      if: always()
      uses: actions/upload-artifact@v4
      with:
        name: deployment-log
        path: ./deploy.log
