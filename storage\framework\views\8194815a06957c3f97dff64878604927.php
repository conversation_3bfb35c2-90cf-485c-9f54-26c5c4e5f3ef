<?php if (isset($component)) { $__componentOriginal9ac128a9029c0e4701924bd2d73d7f54 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54 = $attributes; } ?>
<?php $component = App\View\Components\AppLayout::resolve([] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('app-layout'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\App\View\Components\AppLayout::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
     <?php $__env->slot('header', null, []); ?> 
        <h2 class="font-display text-xl md:text-2xl text-gray-900 dark:text-white leading-tight">
            <?php echo e(__('Discord Integration')); ?>

        </h2>
     <?php $__env->endSlot(); ?>

    <div class="py-12">
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
            <?php if(session('success')): ?>
                <div class="alert alert-success mb-6">
                    <svg xmlns="http://www.w3.org/2000/svg" class="stroke-current shrink-0 h-6 w-6" fill="none" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" /></svg>
                    <span><?php echo e(session('success')); ?></span>
                </div>
            <?php endif; ?>

            <?php if(session('error')): ?>
                <div class="alert alert-error mb-6">
                    <svg xmlns="http://www.w3.org/2000/svg" class="stroke-current shrink-0 h-6 w-6" fill="none" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 14l2-2m0 0l2-2m-2 2l-2-2m2 2l2 2m7-2a9 9 0 11-18 0 9 9 0 0118 0z" /></svg>
                    <span><?php echo e(session('error')); ?></span>
                </div>
            <?php endif; ?>
            <!-- Status Card -->
            <div class="bg-white dark:bg-base-100 overflow-hidden shadow-xl rounded-lg mb-6">
                <div class="p-6">
                    <div class="flex items-center justify-between mb-4">
                        <h3 class="text-lg font-semibold text-gray-900 dark:text-white">Discord Bot Status</h3>

                        <?php if($status['online'] ?? false): ?>
                            <div class="badge badge-success gap-2 p-3">
                                <div class="w-2 h-2 rounded-full bg-success-content animate-pulse"></div>
                                <span>Online</span>
                            </div>
                        <?php else: ?>
                            <div class="badge badge-error gap-2 p-3">
                                <div class="w-2 h-2 rounded-full bg-error-content animate-pulse"></div>
                                <span>Offline</span>
                            </div>
                        <?php endif; ?>
                    </div>

                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <div class="stats shadow w-full">
                                <div class="stat">
                                    <div class="stat-title">Status</div>
                                    <div class="stat-value text-lg"><?php echo e($status['online'] ? 'Online' : 'Offline'); ?></div>
                                    <div class="stat-desc">Last checked: <?php echo e($status['last_check']->diffForHumans()); ?></div>
                                </div>
                            </div>
                        </div>

                        <div>
                            <div class="stats shadow w-full">
                                <div class="stat">
                                    <div class="stat-title">Version</div>
                                    <div class="stat-value text-lg"><?php echo e($status['version'] ?? 'Unknown'); ?></div>
                                    <div class="stat-desc">Bot software version</div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <?php if($status['online'] ?? false): ?>
                        <div class="mt-6">
                            <div class="stats shadow w-full">
                                <div class="stat">
                                    <div class="stat-title">Uptime</div>
                                    <div class="stat-value text-lg">
                                        <?php if(isset($status['uptime'])): ?>
                                            <?php if(isset($status['uptime']['days']) && $status['uptime']['days'] > 0): ?>
                                                <?php echo e($status['uptime']['days']); ?>d
                                            <?php endif; ?>
                                            <?php echo e($status['uptime']['hours'] ?? 0); ?>h
                                            <?php echo e($status['uptime']['minutes'] ?? 0); ?>m
                                            <?php echo e($status['uptime']['seconds'] ?? 0); ?>s
                                        <?php else: ?>
                                            Unknown
                                        <?php endif; ?>
                                    </div>
                                    <div class="stat-desc">Time since last restart</div>
                                </div>

                                <?php if(isset($status['guild'])): ?>
                                <div class="stat">
                                    <div class="stat-title">Server</div>
                                    <div class="stat-value text-lg"><?php echo e($status['guild']['name'] ?? 'Unknown'); ?></div>
                                    <div class="stat-desc"><?php echo e($status['guild']['memberCount'] ?? 0); ?> members</div>
                                </div>
                                <?php endif; ?>
                            </div>
                        </div>
                    <?php endif; ?>

                    <?php if(isset($status['error']) && $status['error']): ?>
                        <div class="alert alert-error mt-6">
                            <svg xmlns="http://www.w3.org/2000/svg" class="stroke-current shrink-0 h-6 w-6" fill="none" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 14l2-2m0 0l2-2m-2 2l-2-2m2 2l2 2m7-2a9 9 0 11-18 0 9 9 0 0118 0z" /></svg>
                            <div>
                                <h3 class="font-bold">Error</h3>
                                <div class="text-xs"><?php echo e($status['error']); ?></div>
                            </div>
                        </div>
                    <?php endif; ?>

                    <div class="mt-6 flex flex-wrap gap-2">
                        <?php if (isset($component)) { $__componentOriginal19987cd45c699eefead8b7a8ac222798 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal19987cd45c699eefead8b7a8ac222798 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.modern-button','data' => ['variant' => 'primary','href' => ''.e(route('admin.discord-bot-status')).'']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('modern-button'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['variant' => 'primary','href' => ''.e(route('admin.discord-bot-status')).'']); ?><svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                            </svg>
                            Refresh Status <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal19987cd45c699eefead8b7a8ac222798)): ?>
<?php $attributes = $__attributesOriginal19987cd45c699eefead8b7a8ac222798; ?>
<?php unset($__attributesOriginal19987cd45c699eefead8b7a8ac222798); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal19987cd45c699eefead8b7a8ac222798)): ?>
<?php $component = $__componentOriginal19987cd45c699eefead8b7a8ac222798; ?>
<?php unset($__componentOriginal19987cd45c699eefead8b7a8ac222798); ?>
<?php endif; ?>

                        <?php if($status['online'] ?? false): ?>
                            <form action="<?php echo e(route('admin.discord-bot-stop')); ?>" method="POST" class="inline">
                                <?php echo csrf_field(); ?>
                                <button type="submit" class="btn btn-error">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                                    </svg>
                                    Stop Bot
                                </button>
                            </form>
                        <?php else: ?>
                            <form action="<?php echo e(route('admin.discord-bot-start')); ?>" method="POST" class="inline">
                                <?php echo csrf_field(); ?>
                                <button type="submit" class="btn btn-success">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M14.752 11.168l-3.197-2.132A1 1 0 0010 9.87v4.263a1 1 0 001.555.832l3.197-2.132a1 1 0 000-1.664z" />
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                                    </svg>
                                    Start Bot
                                </button>
                            </form>
                        <?php endif; ?>

                        <form action="<?php echo e(route('admin.discord-bot-restart')); ?>" method="POST" class="inline">
                            <?php echo csrf_field(); ?>
                            <button type="submit" class="btn btn-warning">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                                </svg>
                                Restart Bot
                            </button>
                        </form>
                    </div>

                    <div class="mt-6">
                        <div class="alert alert-info">
                            <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" class="stroke-current shrink-0 w-6 h-6"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path></svg>
                            <div>
                                <h3 class="font-bold">Bot Management</h3>
                                <div class="text-sm">
                                    <p>The Discord bot is automatically started by the production script. Use these controls only if the bot is not working properly.</p>
                                    <p class="mt-1">Bot logs are available at: <code>storage/logs/discord-bot.log</code> and <code>storage/logs/discord-bot-error.log</code></p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Configuration Card -->
            <div class="bg-white dark:bg-base-100 overflow-hidden shadow-xl rounded-lg mb-6">
                <div class="p-6">
                    <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">Bot Configuration</h3>

                    <div class="overflow-x-auto">
                        <table class="table w-full">
                            <tbody>
                                <tr>
                                    <td class="font-medium">API URL</td>
                                    <td><?php echo e(config('services.discord.bot_url', env('DISCORD_BOT_API_URL', 'http://localhost:3001'))); ?></td>
                                </tr>
                                <tr>
                                    <td class="font-medium">API Key</td>
                                    <td>
                                        <div class="flex items-center">
                                            <span class="text-opacity-50">•••••••••••••••</span>
                                            <button class="btn btn-ghost btn-xs ml-2" onclick="copyToClipboard('<?php echo e(config('services.discord.api_key')); ?>')">
                                                <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z" />
                                                </svg>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                                <tr>
                                    <td class="font-medium">Guild ID</td>
                                    <td><?php echo e(config('services.discord.guild_id', env('LARASCORD_GUILD_ID', '1031202173826109591'))); ?></td>
                                </tr>
                                <tr>
                                    <td class="font-medium">Client ID</td>
                                    <td><?php echo e(config('services.discord.client_id', env('LARASCORD_CLIENT_ID', '1350931744593019002'))); ?></td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>

            <!-- Health Checks Card -->
            <div class="bg-white dark:bg-base-100 overflow-hidden shadow-xl rounded-lg mb-6">
                <div class="p-6">
                    <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">Health Checks</h3>

                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <div class="stats shadow w-full">
                                <div class="stat">
                                    <div class="stat-title">Bot Health Check</div>
                                    <div class="stat-value text-lg">Every 2 minutes</div>
                                    <div class="stat-desc">Automatic restart if offline for 2+ checks</div>
                                </div>
                            </div>
                        </div>

                        <div>
                            <div class="stats shadow w-full">
                                <div class="stat">
                                    <div class="stat-title">Laravel Health Check</div>
                                    <div class="stat-value text-lg">Every 5 minutes</div>
                                    <div class="stat-desc">Monitors system components</div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="mt-6">
                        <h4 class="font-medium mb-2">Recent Health Check Logs</h4>
                        <div class="mockup-code">
                            <?php
                                $logFile = storage_path('logs/discord-bot-health.log');
                                $logs = [];
                                if (file_exists($logFile)) {
                                    $logs = array_slice(file($logFile), -10);
                                }
                            ?>

                            <?php $__empty_1 = true; $__currentLoopData = $logs; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $log): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                                <pre data-prefix="$"><code><?php echo e($log); ?></code></pre>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                                <pre data-prefix="$"><code>No recent logs found.</code></pre>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Role Mapping Card -->
            <div class="bg-white dark:bg-base-100 overflow-hidden shadow-xl rounded-lg">
                <div class="p-6">
                    <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">Discord Role Mapping</h3>

                    <div class="overflow-x-auto">
                        <table class="table w-full">
                            <thead>
                                <tr>
                                    <th>Role Name</th>
                                    <th>Discord Role ID</th>
                                    <th>Permission</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php $__currentLoopData = config('discord.roles', []); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $role => $id): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <tr>
                                        <td><?php echo e(ucfirst($role)); ?></td>
                                        <td><?php echo e($id); ?></td>
                                        <td>
                                            <?php if($role == 'accepted'): ?>
                                                MINEWACHE_ACCEPTED
                                            <?php elseif($role == 'default'): ?>
                                                MINEWACHE_DEFAULT
                                            <?php elseif($role == 'actor'): ?>
                                                MINEWACHE_ACTOR
                                            <?php elseif($role == 'builder'): ?>
                                                MINEWACHE_BUILDER
                                            <?php elseif($role == 'designer'): ?>
                                                MINEWACHE_DESIGNER
                                            <?php elseif($role == 'voice_actor'): ?>
                                                MINEWACHE_VOICE_ACTOR
                                            <?php elseif($role == 'modeler'): ?>
                                                MINEWACHE_MODELER
                                            <?php elseif($role == 'developer'): ?>
                                                MINEWACHE_DEVELOPER
                                            <?php elseif($role == 'cameraman' || $role == 'cutter'): ?>
                                                MINEWACHE_MEDIA
                                            <?php else: ?>
                                                -
                                            <?php endif; ?>
                                        </td>
                                    </tr>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <?php $__env->startPush('scripts'); ?>
    <script>
        function copyToClipboard(text) {
            navigator.clipboard.writeText(text).then(function() {
                // Show toast or notification
                alert('API key copied to clipboard');
            }, function(err) {
                console.error('Could not copy text: ', err);
            });
        }
    </script>
    <?php $__env->stopPush(); ?>
 <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54)): ?>
<?php $attributes = $__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54; ?>
<?php unset($__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal9ac128a9029c0e4701924bd2d73d7f54)): ?>
<?php $component = $__componentOriginal9ac128a9029c0e4701924bd2d73d7f54; ?>
<?php unset($__componentOriginal9ac128a9029c0e4701924bd2d73d7f54); ?>
<?php endif; ?>
<?php /**PATH C:\Users\<USER>\Documents\GitHub\minewache-website\resources\views/admin/discord-bot-status.blade.php ENDPATH**/ ?>