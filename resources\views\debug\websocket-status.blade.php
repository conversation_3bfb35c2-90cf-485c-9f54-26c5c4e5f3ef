<x-app-layout>
    <livewire:debug.web-socket-test />
    <div class="container mx-auto px-4 py-8">
        <div class="bg-white dark:bg-gray-800 shadow-md rounded-lg p-6">
            <h1 class="text-2xl font-bold mb-6">WebSocket Connection Status</h1>

            <div class="mb-6">
                <h2 class="text-xl font-semibold mb-2">Connection Information</h2>
                <div class="overflow-x-auto">
                    <table class="min-w-full bg-white dark:bg-gray-700 rounded-lg overflow-hidden">
                        <tbody>
                            <tr class="border-b dark:border-gray-600">
                                <td class="py-2 px-4 font-medium">Status</td>
                                <td id="connection-status" class="py-2 px-4">Checking...</td>
                            </tr>
                            <tr class="border-b dark:border-gray-600">
                                <td class="py-2 px-4 font-medium">Connection ID</td>
                                <td id="connection-id" class="py-2 px-4">-</td>
                            </tr>
                            <tr class="border-b dark:border-gray-600">
                                <td class="py-2 px-4 font-medium">Socket ID</td>
                                <td id="socket-id" class="py-2 px-4">-</td>
                            </tr>
                            <tr class="border-b dark:border-gray-600">
                                <td class="py-2 px-4 font-medium">Host</td>
                                <td id="ws-host" class="py-2 px-4">-</td>
                            </tr>
                            <tr class="border-b dark:border-gray-600">
                                <td class="py-2 px-4 font-medium">Port</td>
                                <td id="ws-port" class="py-2 px-4">-</td>
                            </tr>
                            <tr class="border-b dark:border-gray-600">
                                <td class="py-2 px-4 font-medium">Secure</td>
                                <td id="ws-secure" class="py-2 px-4">-</td>
                            </tr>
                            <tr class="border-b dark:border-gray-600">
                                <td class="py-2 px-4 font-medium">Browser URL</td>
                                <td id="browser-url" class="py-2 px-4">-</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>

            <div class="mb-6">
                <h2 class="text-xl font-semibold mb-2">Actions</h2>
                <div class="flex flex-wrap gap-2">
                    <button id="btn-reconnect" class="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600">
                        Reconnect
                    </button>
                    <button id="btn-test-event" class="px-4 py-2 bg-green-500 text-white rounded hover:bg-green-600">
                        Test Event
                    </button>
                    <button id="btn-refresh" class="px-4 py-2 bg-gray-500 text-white rounded hover:bg-gray-600">
                        Refresh Page
                    </button>
                </div>
            </div>

            <div>
                <h2 class="text-xl font-semibold mb-2">Event Log</h2>
                <div id="event-log" class="h-64 overflow-y-auto p-4 bg-gray-100 dark:bg-gray-900 rounded font-mono text-sm"></div>
            </div>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const connectionStatus = document.getElementById('connection-status');
            const connectionId = document.getElementById('connection-id');
            const socketId = document.getElementById('socket-id');
            const wsHost = document.getElementById('ws-host');
            const wsPort = document.getElementById('ws-port');
            const wsSecure = document.getElementById('ws-secure');
            const browserUrl = document.getElementById('browser-url');
            const eventLog = document.getElementById('event-log');
            const btnReconnect = document.getElementById('btn-reconnect');
            const btnTestEvent = document.getElementById('btn-test-event');
            const btnRefresh = document.getElementById('btn-refresh');

            // Set browser URL
            browserUrl.textContent = window.location.href;

            // Function to log messages
            function log(message, type = 'info') {
                const timestamp = new Date().toISOString().split('T')[1].split('.')[0];
                const entry = document.createElement('div');
                entry.className = `mb-1 ${type === 'error' ? 'text-red-500' : type === 'success' ? 'text-green-500' : 'text-gray-700 dark:text-gray-300'}`;
                entry.textContent = `[${timestamp}] ${message}`;
                eventLog.appendChild(entry);
                eventLog.scrollTop = eventLog.scrollHeight;
            }

            // Function to update connection status
            function updateConnectionStatus(state) {
                connectionStatus.textContent = state;

                // Apply appropriate styling
                connectionStatus.className = 'py-2 px-4 ';
                if (state === 'connected') {
                    connectionStatus.classList.add('text-green-500', 'font-semibold');
                } else if (state === 'connecting') {
                    connectionStatus.classList.add('text-yellow-500', 'font-semibold');
                } else {
                    connectionStatus.classList.add('text-red-500', 'font-semibold');
                }
            }

            // Check if Echo is available
            if (window.Echo) {
                log('Echo is available');

                // Get and display WebSocket configuration
                if (window.Echo.connector && window.Echo.connector.options) {
                    const options = window.Echo.connector.options;
                    wsHost.textContent = options.wsHost || 'unknown';
                    wsPort.textContent = options.wsPort || 'unknown';
                    wsSecure.textContent = options.forceTLS ? 'Yes (WSS)' : 'No (WS)';

                    log(`WebSocket configuration: ${options.wsHost}:${options.wsPort} (${options.forceTLS ? 'WSS' : 'WS'})`);
                } else {
                    log('Echo connector options not available', 'error');
                }

                // Check connection status
                if (window.Echo.connector && window.Echo.connector.pusher) {
                    const pusher = window.Echo.connector.pusher;

                    // Update initial connection status
                    updateConnectionStatus(pusher.connection.state);
                    log(`Initial connection state: ${pusher.connection.state}`);

                    // Listen for connection state changes
                    pusher.connection.bind('state_change', (states) => {
                        log(`Connection state changed: ${states.previous} -> ${states.current}`);
                        updateConnectionStatus(states.current);
                    });

                    // Listen for connection established
                    pusher.connection.bind('connected', () => {
                        log('Connection established', 'success');
                        connectionId.textContent = pusher.connection.connectionId || 'Unknown';
                        socketId.textContent = pusher.connection.socket_id || 'Unknown';
                    });

                    // Listen for connection errors
                    pusher.connection.bind('error', (error) => {
                        log(`Connection error: ${JSON.stringify(error)}`, 'error');
                    });

                    // Reconnect button
                    btnReconnect.addEventListener('click', () => {
                        log('Manual reconnection requested');
                        pusher.disconnect();
                        setTimeout(() => {
                            pusher.connect();
                        }, 1000);
                    });

                    // Test event button
                    btnTestEvent.addEventListener('click', () => {
                        log('Sending test event');
                        // Create a simple event using Livewire
                        if (window.Livewire) {
                            window.Livewire.dispatch('echo-test');
                            log('Test event dispatched via Livewire');

                            // Listen for the response
                            window.Livewire.on('echo-test-response', (data) => {
                                log(`Test response received: ${data.message}`, 'success');
                                log(`Response timestamp: ${data.timestamp}`);
                            });
                        } else {
                            log('Livewire not available, cannot send test event', 'error');
                        }
                    });
                } else {
                    log('Echo connector or pusher not available', 'error');
                    updateConnectionStatus('Not Available');
                }
            } else {
                log('Echo is not available', 'error');
                updateConnectionStatus('Not Available');
            }

            // Refresh button
            btnRefresh.addEventListener('click', () => {
                window.location.reload();
            });
        });
    </script>
</x-app-layout>
