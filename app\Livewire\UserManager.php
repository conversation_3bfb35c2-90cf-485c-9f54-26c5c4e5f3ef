<?php

namespace App\Livewire;

use App\Enums\Role;
use App\Models\User;
use Illuminate\Support\Facades\Gate;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Cache;
use Livewire\Component;
use Livewire\WithPagination;
use Livewire\Attributes\Url;

class UserManager extends Component
{
    use WithPagination;

    // Filter und Sortieroptionen mit URL-Synchronisierung für Bookmarking
    #[Url]
    public $search = '';

    #[Url]
    public $role = 'all';

    #[Url]
    public $sortField = 'created_at';

    #[Url]
    public $sortDirection = 'desc';

    // Variable für verzögerte Suche
    public $searchTerm = '';

    // Für Detailansicht und Bearbeitung
    public $selectedUser = null;
    public $viewMode = 'list'; // 'list', 'detail', 'edit'
    public $userRoles = [];

    // Lifecycle Hooks
    public function mount()
    {
        $this->searchTerm = $this->search;
    }

    public function updatedSearchTerm()
    {
        $this->search = $this->searchTerm;
        $this->resetPage();
    }

    // Sortierung
    public function sortBy($field)
    {
        if ($this->sortField === $field) {
            $this->sortDirection = $this->sortDirection === 'asc' ? 'desc' : 'asc';
        } else {
            $this->sortField = $field;
            $this->sortDirection = 'asc';
        }
    }

    // Benutzer-Aktionen
    public function viewUser($userId)
    {
        \Log::info('User viewed', ['user_id' => $userId]);
        // Ensure userId is treated as a string to preserve precision
        $this->selectedUser = User::where('id', $userId)->firstOrFail();
        $this->viewMode = 'detail';
    }

    public function editUser($userId)
    {
        // Ensure userId is treated as a string to preserve precision
        $this->selectedUser = User::where('id', $userId)->firstOrFail();
        $this->userRoles = $this->getUserRoles($this->selectedUser);
        $this->viewMode = 'edit';
    }

    public function syncUserRoles($userId)
    {
        // Hier würde die Synchronisation mit Discord stattfinden
        // Wir rufen den PermissionController auf
        // Ensure userId is passed as a string to preserve precision
        return redirect()->route('admin.sync-roles', ['user' => (string)$userId]);
    }

    public function updateUser()
    {
        Gate::authorize('MINEWACHE_TEAM');

        // Rate limiting - prevent too frequent updates
        $cacheKey = 'user_update_' . $this->selectedUser->id;
        if (Cache::has($cacheKey)) {
            session()->flash('warning', __('admin.update_rate_limited', ['seconds' => 30]));
            return;
        }

        // Set rate limit for 30 seconds
        Cache::put($cacheKey, true, now()->addSeconds(30));

        $this->validate([
            'userRoles' => 'array',
        ]);

        // Berechne den neuen Berechtigungs-Bitmask basierend auf den ausgewählten Rollen
        $permissions = 0;
        foreach ($this->userRoles as $role => $isChecked) {
            if ($isChecked) {
                try {
                    $roleValue = constant("App\\Enums\\Role::{$role}")->value;
                    $permissions |= $roleValue;
                } catch (\Error $e) {
                    Log::warning("Ungültiger Rollenname beim Update von Benutzer {$this->selectedUser->username}", [
                        'role' => $role,
                        'error' => $e->getMessage()
                    ]);
                    // Fahre mit der nächsten Rolle fort
                    continue;
                }
            }
        }

        // Speichere den vorherigen Berechtigungswert für die Protokollierung
        $previousPermissions = $this->selectedUser->permissions;

        // Status-Variable für UI-Feedback
        $this->dispatch('update-started');

        try {
            // Versuche zuerst, die Rollen über den Discord-Bot zu aktualisieren
            $updatedViaBot = false;

            if ($permissions !== $previousPermissions) {
                try {
                    // Konvertiere die ausgewählten Rollen in Discord-Rollen-IDs
                    $discordRoleIds = $this->getDiscordRoleIdsFromRoles($this->userRoles);

                    // Zuerst lokale Datenbank aktualisieren, um Konsistenz zu gewährleisten
                    $this->selectedUser->update([
                        'permissions' => $permissions,
                        'permissions_updated_at' => now()
                    ]);

                    if (!empty($discordRoleIds)) {
                        // Beste Praxis: Konfigurationen aus config abrufen, mit Fallback auf env
                        $botApiUrl = config('services.discord.bot_url', env('DISCORD_BOT_API_URL', 'http://localhost:3001'));
                        $endpoint = "{$botApiUrl}/api/users/{$this->selectedUser->id}/roles";
                        $apiKey = config('services.discord.api_key');

                        if (empty($apiKey)) {
                            throw new \Exception('Discord Bot API-Key ist nicht konfiguriert');
                        }

                        // Ermittle die zu entfernenden Rollen
                        $currentRoles = $this->getCurrentDiscordRoles($this->selectedUser->id, $apiKey, $botApiUrl);
                        $rolesToRemove = $this->getRolesToRemove($currentRoles, $discordRoleIds);

                        Log::info("Rollen-Update für Benutzer {$this->selectedUser->username}", [
                            'user_id' => $this->selectedUser->id,
                            'roles_to_add' => $discordRoleIds,
                            'roles_to_remove' => $rolesToRemove,
                            'previous_permissions' => $previousPermissions,
                            'new_permissions' => $permissions
                        ]);

                        // Unified Endpoint für beide Operationen verwenden
                        $response = Http::withHeaders([
                            'Authorization' => "Bearer {$apiKey}",
                            'Accept' => 'application/json',
                            'Content-Type' => 'application/json',
                            'X-Request-Source' => 'UserManager'
                        ])
                        ->timeout(10) // Längeres Timeout für zuverlässigere Ausführung
                        ->retry(3, 500) // Retry mit Backoff
                        ->post($endpoint, [
                            'rolesToAdd' => !empty($discordRoleIds) ? $discordRoleIds : ['1335685796979671111'], // @everyone Rolle als Fallback
                            'rolesToRemove' => array_values($rolesToRemove), // Array-Indizes sicherstellen
                            'permissions' => $permissions // Neue Berechtigungen mitschicken
                        ]);

                        if ($response->successful()) {
                            // Erfolgreiche Aktualisierung mit dem Bot
                            $data = $response->json();

                            // Log für Debugging
                            Log::info("Benutzerrollen erfolgreich mit Discord-Bot aktualisiert", [
                                'user_id' => $this->selectedUser->id,
                                'username' => $this->selectedUser->username,
                                'added_count' => count($data['added'] ?? []),
                                'removed_count' => count($data['removed'] ?? [])
                            ]);

                            // Jetzt die Berechtigungen in der Datenbank aktualisieren
                            $this->selectedUser->update([
                                'last_synced_at' => now()
                            ]);

                            $updatedViaBot = true;
                            session()->flash('success', __('admin.permissions_updated_successfully_discord', [
                                'username' => $this->selectedUser->username
                            ]));

                            // Broadcast ein Event, damit andere Komponenten reagieren können
                            $this->dispatch('user-roles-updated', [
                                'userId' => $this->selectedUser->id,
                                'updatedViaBot' => true
                            ]);
                        } else {
                            // Bot-Anfrage fehlgeschlagen, aber wir haben bereits die lokale Datenbank aktualisiert
                            Log::warning("Discord-Bot Rollenaktualisierung fehlgeschlagen", [
                                'user_id' => $this->selectedUser->id,
                                'status' => $response->status(),
                                'body' => $response->body()
                            ]);

                            throw new \Exception(
                                "Discord-Bot konnte die Rollen nicht synchronisieren (Status: " . $response->status() . "). " .
                                "Die lokalen Berechtigungen wurden aktualisiert."
                            );
                        }
                    } else {
                        // Keine Discord-Rollen zu synchronisieren
                        Log::info("Keine Discord-Rollen zu synchronisieren für {$this->selectedUser->username}");
                        $updatedViaBot = false;
                    }
                } catch (\Exception $botError) {
                    // Bei Fehlern mit dem lokalen Bot auf die direkte Aktualisierung zurückfallen
                    Log::warning("Fehler bei der Verwendung des Discord-Bots für Rollenaktualisierung", [
                        'user_id' => $this->selectedUser->id,
                        'username' => $this->selectedUser->username,
                        'error' => $botError->getMessage(),
                        'trace' => $botError->getTraceAsString()
                    ]);

                    // Werfe die Exception weiter, damit sie vom äußeren try-catch-Block erfasst wird
                    throw $botError;
                }
            } else {
                // Keine Änderung an den Berechtigungen
                Log::info("Keine Änderung an den Berechtigungen von {$this->selectedUser->username}");
                session()->flash('info', __('admin.no_permission_changes', ['username' => $this->selectedUser->username]));
            }

            // Wenn die Aktualisierung über den Bot nicht erfolgreich war, lokale Aktualisierung
            if (!$updatedViaBot && $permissions !== $previousPermissions) {
                $this->selectedUser->update([
                    'permissions' => $permissions,
                    'permissions_updated_at' => now()
                ]);

                session()->flash('success', __('admin.permissions_updated_successfully', ['username' => $this->selectedUser->username]));

                // Broadcast ein Event
                $this->dispatch('user-roles-updated', [
                    'userId' => $this->selectedUser->id,
                    'updatedViaBot' => false
                ]);
            }

            $this->dispatch('update-completed', ['success' => true]);
            $this->backToList();
        } catch (\Exception $e) {
            Log::error("Fehler bei der Rollenaktualisierung für {$this->selectedUser->username}", [
                'user_id' => $this->selectedUser->id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            session()->flash('error', __('admin.error_updating_permissions', [
                'username' => $this->selectedUser->username,
                'error' => $e->getMessage()
            ]));

            $this->dispatch('update-completed', ['success' => false, 'error' => $e->getMessage()]);
        }
    }

    public function backToList()
    {
        $this->selectedUser = null;
        $this->viewMode = 'list';
        $this->userRoles = [];
    }

    public function backToDetail()
    {
        $this->viewMode = 'detail';
    }

    // Hilfsfunktionen
    private function getUserRoles(User $user)
    {
        $roles = [];
        foreach (Role::cases() as $role) {
            $roles[$role->name] = $user->hasRole($role);
        }
        return $roles;
    }

    /**
     * Konvertiert die ausgewählten Rollen in Discord-Rollen-IDs
     *
     * @param array $selectedRoles
     * @return array
     */
    private function getDiscordRoleIdsFromRoles(array $selectedRoles): array
    {
        // Mapping von Rollen zu Discord-Rollen-IDs
        $roleMapping = $this->getRoleMapping();

        $discordRoleIds = [];

        foreach ($selectedRoles as $role => $isChecked) {
            if ($isChecked && isset($roleMapping[$role])) {
                // Füge alle Discord-Rollen-IDs für diese Rolle hinzu
                foreach ($roleMapping[$role] as $discordRoleId) {
                    $discordRoleIds[] = $discordRoleId;
                }
            }
        }

        // Entferne Duplikate
        return array_unique($discordRoleIds);
    }

    /**
     * Gibt das Mapping von Rollen zu Discord-Rollen-IDs zurück
     *
     * @return array
     */
    private function getRoleMapping(): array
    {
        return [
            'MINEWACHE_TEAM' => ['1071024925437067294', '1335685797080465434'],
            'SCHAUSPIELER' => ['1183491860304494642', '1335685797055041541'],
            'BUILDER' => ['1033491844715257866', '1335685797055041544'],
            'DESIGNER' => ['1033523833514242068', '1335685797067755581'],
            'SYNCHRONSPRECHER' => ['1033474411963109466', '1335685797055041539'],
            'MODELLIERER' => ['1041839025696292885', '1335685797067755582'],
            'DEVELOPER' => ['1033491763144433724', '1335685797067755586'],
            'KAMERAMANN' => ['1033491696144621658', '1335685797067755584'],
            'CUTTER' => ['1033491854995505213', '1335685797067755585'],
            'ANGENOMMEN' => ['1031205205934604439', '1335685797080465428'],
            'ANGENOMMENPLUS' => ['1113859130961166468', '1335685797080465429']
        ];
    }

    /**
     * Ruft die aktuellen Discord-Rollen eines Benutzers ab
     *
     * @param string $userId
     * @param string $apiKey
     * @param string $botApiUrl
     * @return array
     */
    private function getCurrentDiscordRoles(string $userId, string $apiKey, string $botApiUrl): array
    {
        // Cache-Key für die Benutzerrollen
        $cacheKey = "discord_user_roles_{$userId}";

        // Versuche die Rollen aus dem Cache zu laden (3 Minuten gültig)
        return Cache::remember($cacheKey, now()->addMinutes(3), function () use ($userId, $apiKey, $botApiUrl) {
            try {
                $endpoint = "{$botApiUrl}/api/users/{$userId}/roles";

                $response = Http::withHeaders([
                    'Authorization' => "Bearer {$apiKey}",
                    'Accept' => 'application/json',
                    'X-Request-Source' => 'UserManager'
                ])
                ->timeout(8)  // Längeres Timeout für zuverlässigere Antwort
                ->retry(2, 1000)  // 2 Wiederholungsversuche mit 1s Pause
                ->get($endpoint);

                if ($response->successful()) {
                    $data = $response->json();
                    $roles = collect($data['roles'] ?? [])->pluck('id')->toArray();

                    Log::info("Discord-Rollen erfolgreich abgerufen", [
                        'user_id' => $userId,
                        'role_count' => count($roles)
                    ]);

                    return $roles;
                } else {
                    Log::warning("Fehler beim Abrufen der Discord-Rollen", [
                        'user_id' => $userId,
                        'status' => $response->status(),
                        'response' => $response->body()
                    ]);
                }
            } catch (\Exception $e) {
                Log::error("Ausnahme beim Abrufen der Discord-Rollen", [
                    'user_id' => $userId,
                    'error' => $e->getMessage(),
                    'trace' => $e->getTraceAsString()
                ]);
            }

            return [];
        });
    }

    /**
     * Ermittelt die zu entfernenden Discord-Rollen
     *
     * @param array $currentRoles Aktuelle Discord-Rollen-IDs des Benutzers
     * @param array $newRoles Neue Discord-Rollen-IDs, die der Benutzer haben soll
     * @return array Discord-Rollen-IDs, die entfernt werden sollen
     */
    private function getRolesToRemove(array $currentRoles, array $newRoles): array
    {
        try {
            // Hole das Mapping von Rollen zu Discord-Rollen-IDs
            $roleMapping = $this->getRoleMapping();

            // Prüfe, ob das Mapping gültig ist
            if (empty($roleMapping)) {
                Log::warning('Leeres Rollen-Mapping bei Berechnung der zu entfernenden Rollen');
                return [];
            }

            // Alle verwalteten Rollen-IDs (flache Liste aller Werte aus dem Mapping)
            $allManagedRoleIds = [];

            // Reservierte Rollen, die nie entfernt werden sollten (z.B. Basis-Mitgliedsrollen)
            $protectedRoleIds = $this->getProtectedRoleIds();

            foreach ($roleMapping as $roleIds) {
                if (is_array($roleIds)) {
                    foreach ($roleIds as $id) {
                        if (!empty($id) && is_string($id)) {
                            $allManagedRoleIds[] = $id;
                        }
                    }
                }
            }

            // Entferne Duplikate
            $allManagedRoleIds = array_unique($allManagedRoleIds);

            // Filtere die aktuellen Rollen, um nur die zu behalten, die wir verwalten
            $managedCurrentRoles = array_intersect($currentRoles, $allManagedRoleIds);

            // Rollen, die der Benutzer hat, aber nicht mehr haben soll
            $rolesToRemove = array_diff($managedCurrentRoles, $newRoles);

            // Geschützte Rollen NIE entfernen
            $rolesToRemove = array_diff($rolesToRemove, $protectedRoleIds);

            Log::info('Rollen-Entfernungs-Berechnung', [
                'managed_role_count' => count($allManagedRoleIds),
                'current_roles_count' => count($currentRoles),
                'managed_current_roles_count' => count($managedCurrentRoles),
                'new_roles_count' => count($newRoles),
                'roles_to_remove_count' => count($rolesToRemove)
            ]);

            // Detailliertes Debug-Logging
            Log::debug('Detaillierte Rollen-Entfernungs-Berechnung', [
                'all_managed_role_ids' => $allManagedRoleIds,
                'current_roles' => $currentRoles,
                'managed_current_roles' => $managedCurrentRoles,
                'new_roles' => $newRoles,
                'roles_to_remove' => $rolesToRemove,
                'protected_roles' => $protectedRoleIds
            ]);

            return $rolesToRemove;
        } catch (\Exception $e) {
            Log::error('Fehler bei der Berechnung der zu entfernenden Rollen', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            // Im Fehlerfall lieber keine Rollen entfernen
            return [];
        }
    }

    /**
     * Gibt eine Liste von geschützten Discord-Rollen zurück, die nie entfernt werden sollten
     *
     * @return array Array mit geschützten Discord-Rollen-IDs
     */
    private function getProtectedRoleIds(): array
    {
        // Rollen, die niemals entfernt werden sollten, wie Basis-Server-Mitgliedschaft
        return [
            '1031202173826109591', // @everyone Rolle (Server-ID)
            '1031205518441431071', // Mitglied
            // Weitere geschützte Rollen hier hinzufügen
        ];
    }

    /**
     * Gibt eine formatierte Liste der Rollen eines Benutzers zurück
     */
    private function getFormattedUserRoles(User $user)
    {
        $roleLabels = [];
        foreach (Role::cases() as $role) {
            if ($user->hasRole($role)) {
                $roleLabels[] = $role->label();
            }
        }
        return $roleLabels;
    }

    // Render
    public function render()
    {
        $query = User::query();

        // Suche
        if (!empty($this->search)) {
            $query->where(function ($q) {
                $q->where('username', 'like', '%' . $this->search . '%')
                    ->orWhere('global_name', 'like', '%' . $this->search . '%')
                    ->orWhere('email', 'like', '%' . $this->search . '%');
            });
        }

        // Rollenfilter
        if ($this->role !== 'all') {
            $roleValue = constant("App\\Enums\\Role::{$this->role}")->value;
            $query->whereRaw('permissions & ? > 0', [$roleValue]);
        }

        // Sortierung
        $query->orderBy($this->sortField, $this->sortDirection);

        // Paginierung
        $users = $query->paginate(10);

        return view('livewire.user-manager', [
            'users' => $users,
            'roles' => Role::cases(),
        ]);
    }
}
