<?php

namespace App\Providers;

use Illuminate\Support\Facades\Gate;
use <PERSON><PERSON>\Telescope\IncomingEntry;
use <PERSON><PERSON>\Telescope\Telescope;
use <PERSON><PERSON>\Telescope\TelescopeApplicationServiceProvider;

class TelescopeServiceProvider extends TelescopeApplicationServiceProvider
{
    /**
     * Register any application services.
     */
    public function register(): void
    {
        // Don't register Telescope in production unless explicitly enabled
        if ($this->app->environment('production') && !config('telescope.enabled', false)) {
            return;
        }

        // Telescope::night();

        $this->hideSensitiveRequestDetails();

        $isDebugMode = config('app.debug') || !$this->app->environment('production');

        Telescope::filter(function (IncomingEntry $entry) use ($isDebugMode) {
            return $isDebugMode ||
                   $entry->isReportableException() ||
                   $entry->isFailedRequest() ||
                   $entry->isFailedJob() ||
                   $entry->isScheduledTask() ||
                   $entry->hasMonitoredTag();
        });
    }

    /**
     * Prevent sensitive request details from being logged by Telescope.
     */
    protected function hideSensitiveRequestDetails(): void
    {
        // Only show sensitive details in debug mode and non-production environments
        if (config('app.debug') && !$this->app->environment('production')) {
            return;
        }

        Telescope::hideRequestParameters(['_token', 'password', 'password_confirmation']);

        Telescope::hideRequestHeaders([
            'cookie',
            'x-csrf-token',
            'x-xsrf-token',
            'authorization',
            'x-api-token',
        ]);
    }

    /**
     * Register the Telescope gate.
     *
     * This gate determines who can access Telescope in non-local environments.
     */
    protected function gate(): void
    {
        Gate::define('viewTelescope', function ($user) {
            // In production, only allow specific team members to access Telescope
            if ($this->app->environment('production')) {
                return in_array($user->email, [
                    // Add authorized team member emails here
                ]) || $user->hasPermission('MINEWACHE_TEAM');
            }

            // In non-production environments, allow access to team members
            return $user->hasPermission('MINEWACHE_TEAM');
        });
    }
}
