
export const SUPPORTED_VIDEO_TYPES: string[] = ['video/mp4', 'video/quicktime', 'video/webm', 'video/x-matroska', 'video/avi']; // MOV is quicktime, MKV, AVI
export const MAX_FILE_SIZE_MB = 100; // Max file size in MB for direct video content analysis
export const GEMINI_MODEL_NAME = "gemini-2.5-flash-preview-04-17"; // Ensure this is the desired model

// Storage Keys
export const API_KEY_STORAGE_KEY = 'geminiApiKey';

// Regex for basic API Key client-side validation (starts with AIza, 39 chars total)
export const API_KEY_REGEX = /^AIza[0-9A-Za-z\-_]{35}$/;

// Mandatory Hashtag
export const MANDATORY_HASHTAG = '#dieminewache';
