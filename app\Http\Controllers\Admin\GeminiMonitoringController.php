<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Ticket;
use App\Models\TicketMessage;
use App\Services\GeminiRateLimiter;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\DB;

class GeminiMonitoringController extends Controller
{
    /**
     * The rate limiter instance.
     *
     * @var GeminiRateLimiter
     */
    protected $rateLimiter;

    /**
     * Create a new controller instance.
     *
     * @param GeminiRateLimiter $rateLimiter
     * @return void
     */
    public function __construct(GeminiRateLimiter $rateLimiter)
    {
        // Middleware wird in den Routen definiert, nicht im Controller
        $this->rateLimiter = $rateLimiter;
    }

    /**
     * Display the Gemini monitoring dashboard.
     *
     * @return \Illuminate\View\View
     */
    public function index()
    {
        // Get rate limit usage statistics
        $usageStats = $this->rateLimiter->getUsageStats();

        // Get AI message statistics
        $totalAIMessages = TicketMessage::where('message_source', 'ai')->count();
        $aiMessagesToday = TicketMessage::where('message_source', 'ai')
            ->whereDate('created_at', today())
            ->count();
        $aiMessagesThisWeek = TicketMessage::where('message_source', 'ai')
            ->whereBetween('created_at', [now()->startOfWeek(), now()])
            ->count();
        $aiMessagesThisMonth = TicketMessage::where('message_source', 'ai')
            ->whereMonth('created_at', now()->month)
            ->whereYear('created_at', now()->year)
            ->count();

        // Get tickets with AI consent
        $ticketsWithConsent = Ticket::where('gemini_consent', true)->count();
        $totalTickets = Ticket::count();
        $consentPercentage = $totalTickets > 0 ? round(($ticketsWithConsent / $totalTickets) * 100, 2) : 0;

        // Get AI message distribution by hour
        $messagesByHour = TicketMessage::where('message_source', 'ai')
            ->whereDate('created_at', '>=', now()->subDays(7))
            ->select(DB::raw("strftime('%H', created_at) as hour"), DB::raw('COUNT(*) as count'))
            ->groupBy('hour')
            ->orderBy('hour')
            ->get()
            ->pluck('count', 'hour')
            ->toArray();

        // Fill in missing hours with zeros
        $hourlyData = [];
        for ($i = 0; $i < 24; $i++) {
            $hourlyData[$i] = $messagesByHour[$i] ?? 0;
        }

        // Get recent AI messages
        $recentMessages = TicketMessage::where('message_source', 'ai')
            ->with(['ticket', 'user'])
            ->orderBy('created_at', 'desc')
            ->limit(10)
            ->get();

        return view('admin.gemini-monitoring', compact(
            'usageStats',
            'totalAIMessages',
            'aiMessagesToday',
            'aiMessagesThisWeek',
            'aiMessagesThisMonth',
            'ticketsWithConsent',
            'totalTickets',
            'consentPercentage',
            'hourlyData',
            'recentMessages'
        ));
    }

    /**
     * Reset the rate limiter counters.
     *
     * @return \Illuminate\Http\RedirectResponse
     */
    public function resetRateLimits()
    {
        $minuteKey = 'gemini_rate_limit:minute:' . now()->format('Y-m-d-H-i');
        $hourKey = 'gemini_rate_limit:hour:' . now()->format('Y-m-d-H');
        $dayKey = 'gemini_rate_limit:day:' . now()->format('Y-m-d');

        Cache::forget($minuteKey);
        Cache::forget($hourKey);
        Cache::forget($dayKey);

        // Also clear any ticket-specific rate limits
        $ticketKeys = Cache::get('gemini_rate_limit:ticket_keys', []);
        foreach ($ticketKeys as $key) {
            Cache::forget($key);
        }
        Cache::forget('gemini_rate_limit:ticket_keys');

        return redirect()->route('admin.gemini.monitoring')
            ->with('success', 'Rate limits have been reset successfully.');
    }
}
