<?php

namespace Database\Factories;

use App\Models\TicketMessage;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\TicketAttachment>
 */
class TicketAttachmentFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $filename = fake()->uuid() . '.jpg';
        
        return [
            'ticket_message_id' => TicketMessage::factory(),
            'filename' => $filename,
            'original_filename' => fake()->word() . '.jpg',
            'file_path' => 'ticket-attachments/' . $filename,
            'mime_type' => 'image/jpeg',
            'file_size' => fake()->numberBetween(10000, 5000000),
            'discord_attachment_id' => fake()->optional()->numerify('##########'),
            'is_from_discord' => fake()->boolean(20),
            'created_at' => fake()->dateTimeBetween('-1 month', 'now'),
            'updated_at' => function (array $attributes) {
                return fake()->dateTimeBetween($attributes['created_at'], 'now');
            },
        ];
    }

    /**
     * Indicate that the attachment is from Discord.
     */
    public function fromDiscord(): static
    {
        return $this->state(fn (array $attributes) => [
            'is_from_discord' => true,
            'discord_attachment_id' => fake()->numerify('##########'),
        ]);
    }

    /**
     * Indicate that the attachment is from the web app.
     */
    public function fromWeb(): static
    {
        return $this->state(fn (array $attributes) => [
            'is_from_discord' => false,
            'discord_attachment_id' => null,
        ]);
    }
}
