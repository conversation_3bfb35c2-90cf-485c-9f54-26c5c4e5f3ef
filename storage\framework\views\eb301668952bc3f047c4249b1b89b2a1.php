<?php $attributes ??= new \Illuminate\View\ComponentAttributeBag;

$__newAttributes = [];
$__propNames = \Illuminate\View\ComponentAttributeBag::extractPropNames((['ticket', 'isSupporter']));

foreach ($attributes->all() as $__key => $__value) {
    if (in_array($__key, $__propNames)) {
        $$__key = $$__key ?? $__value;
    } else {
        $__newAttributes[$__key] = $__value;
    }
}

$attributes = new \Illuminate\View\ComponentAttributeBag($__newAttributes);

unset($__propNames);
unset($__newAttributes);

foreach (array_filter((['ticket', 'isSupporter']), 'is_string', ARRAY_FILTER_USE_KEY) as $__key => $__value) {
    $$__key = $$__key ?? $__value;
}

$__defined_vars = get_defined_vars();

foreach ($attributes->all() as $__key => $__value) {
    if (array_key_exists($__key, $__defined_vars)) unset($$__key);
}

unset($__defined_vars); ?>

<?php if (isset($component)) { $__componentOriginalfc34df8c041cfce1b125f68563b94330 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginalfc34df8c041cfce1b125f68563b94330 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.modern-card','data' => ['variant' => 'elevated','size' => 'sm']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('modern-card'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['variant' => 'elevated','size' => 'sm']); ?>
    <div class="flex flex-col md:flex-row justify-between items-start md:items-center gap-4">
        
        <div class="flex-1 min-w-0">
            <div class="flex items-center gap-3">
                <img
                    src="<?php echo e($ticket->user->getAvatar(['extension' => 'webp', 'size' => 40])); ?>"
                    class="w-10 h-10 rounded-full border-2 border-slate-700"
                    alt="<?php echo e($ticket->user->username); ?>"
                />
                <div>
                    <h1 class="text-lg sm:text-xl md:text-2xl font-bold text-base-content truncate">
                        <?php echo e($ticket->title); ?>

                    </h1>
                    <p class="text-xs sm:text-sm text-base-content/60 flex flex-wrap items-center gap-x-2 gap-y-1">
                        <span>#<?php echo e($ticket->id); ?></span>
                        <span class="text-base-content/30">•</span>
                        <span><?php echo e($ticket->user->username); ?></span>
                    </p>
                </div>
            </div>
        </div>

        
        <!--[if BLOCK]><![endif]--><?php if($isSupporter): ?>
            <div class="flex items-center space-x-2">
                
            </div>
        <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
    </div>
 <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginalfc34df8c041cfce1b125f68563b94330)): ?>
<?php $attributes = $__attributesOriginalfc34df8c041cfce1b125f68563b94330; ?>
<?php unset($__attributesOriginalfc34df8c041cfce1b125f68563b94330); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalfc34df8c041cfce1b125f68563b94330)): ?>
<?php $component = $__componentOriginalfc34df8c041cfce1b125f68563b94330; ?>
<?php unset($__componentOriginalfc34df8c041cfce1b125f68563b94330); ?>
<?php endif; ?>
<?php /**PATH C:\Users\<USER>\Documents\GitHub\minewache-website\resources\views/livewire/ticket/partials/header.blade.php ENDPATH**/ ?>