[program:minewache-discord-bot]
; Replace /path/to/your/laravel/ with the actual absolute path to your Laravel project root
command=php /path/to/your/laravel/artisan minewache:run-discord-bot
process_name=%(program_name)s_%(process_num)02d
autostart=true
autorestart=true
user=www-data ; Replace with the appropriate user that your web server/application runs as
numprocs=1
redirect_stderr=true
stdout_logfile=/path/to/your/laravel/storage/logs/discord-bot-supervisor.log
stderr_logfile=/path/to/your/laravel/storage/logs/discord-bot-supervisor.error.log

; Note:
; 1. Replace all instances of '/path/to/your/laravel/' with the actual absolute path to your Laravel project directory.
; 2. Ensure the 'user' directive is set to the user that owns the Laravel files and can run PHP/Artisan.
; 3. Ensure the log file paths are writable by the specified user.
; 4. For Laravel Octane with Swoole/OpenSwoole, the command might be different, e.g., managing the Octane worker itself.
;    This configuration is for running the Artisan command directly with Supervisor.
;    If Octane is used, Octane's own workers would run this command, or the bot logic
;    might be integrated into an Octane worker. This config assumes a non-Octane or
;    a scenario where the bot is a separate supervised Artisan command even with Octane.
;    The issue statement (NFR-TECH-003) says: "For the asynchronous operation and handling
;    of the Discord Gateway connection, Laravel Octane with Swoole or OpenSwoole should be
;    evaluated and preferably used."
;    If Octane is managing the command (e.g. `php artisan octane:start` and the bot command
;    is registered as an Octane worker), then the Supervisor config would be for Octane itself.
;    This sample is a general Supervisor config for an Artisan command.
