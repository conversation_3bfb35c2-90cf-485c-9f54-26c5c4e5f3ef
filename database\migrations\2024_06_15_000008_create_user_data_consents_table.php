<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('user_data_consents', function (Blueprint $table) {
            $table->id();
            $table->string('user_id');
            $table->boolean('share_username')->default(true);
            $table->boolean('share_applications')->default(false);
            $table->boolean('share_tickets')->default(false);
            $table->boolean('share_discord_info')->default(false);
            $table->timestamps();
            
            $table->foreign('user_id')->references('id')->on('users')->onDelete('cascade');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('user_data_consents');
    }
};
