@props(['dismissible' => false])

<div {{ $attributes->merge(['class' => 'relative bg-gradient-to-r from-[#265FC2] to-[#1A4494] p-6 md:p-8 rounded-lg overflow-hidden']) }}>
    <!-- Background pattern -->
    <div class="absolute inset-0 opacity-10">
        <div class="absolute inset-0" style="background-image: url('data:image/svg+xml,%3Csvg width=\'20\' height=\'20\' viewBox=\'0 0 20 20\' xmlns=\'http://www.w3.org/2000/svg\'%3E%3Cg fill=\'%23ffffff\' fill-opacity=\'1\' fill-rule=\'evenodd\'%3E%3Ccircle cx=\'3\' cy=\'3\' r=\'1\'/%3E%3Ccircle cx=\'13\' cy=\'13\' r=\'1\'/%3E%3C/g%3E%3C/svg%3E');"></div>
    </div>

    @if($dismissible)
    <!-- Close button -->
    <div class="absolute top-2 right-2 z-10">
        <button class="btn btn-circle btn-xs bg-base-100/20 hover:bg-base-100/40 border-0">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
            </svg>
        </button>
    </div>
    @endif

    <div class="flex flex-col md:flex-row items-center gap-6 relative z-10">
        <!-- Logo Placeholder -->
        <div class="w-32 h-32 bg-white rounded-lg flex items-center justify-center p-4 shadow-lg">
            <!-- Replace this with actual logo when available -->
            <div class="text-center text-[#265FC2] font-bold">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-16 w-16 mx-auto mb-1" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5">
                    <path stroke-linecap="round" stroke-linejoin="round" d="M5.25 14.25h13.5m-13.5 0a3 3 0 01-3-3m3 3a3 3 0 100 6h13.5a3 3 0 100-6m-16.5-3a3 3 0 013-3h13.5a3 3 0 013 3m-19.5 0a4.5 4.5 0 01.9-2.7L5.737 5.1a3.375 3.375 0 012.7-1.35h7.126c1.062 0 2.062.5 2.7 1.35l2.587 3.45a4.5 4.5 0 01.9 2.7m0 0a3 3 0 01-3 3m0 3h.008v.008h-.008v-.008zm0-6h.008v.008h-.008v-.008zm-3 6h.008v.008h-.008v-.008zm0-6h.008v.008h-.008v-.008z" />
                </svg>
                Bisect Hosting
            </div>
        </div>

        <!-- Ad Content -->
        <div class="flex-1 text-center md:text-left">
            <h2 class="text-2xl md:text-3xl font-display font-bold text-white mb-2">Minecraft Server bei Bisect Hosting</h2>
            <p class="text-white/90 mb-4 max-w-2xl">
                Hoste deinen eigenen Minecraft-Server mit unserem Partner Bisect Hosting. Zuverlässige Server, einfache Einrichtung und exzellenter Support für dein Minecraft-Abenteuer.
            </p>
            <x-modern-button variant="primary" href="https://www.bisecthosting.com/sarocesch" target="_blank" rel="noopener noreferrer">Server erstellen
                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                    <path fill-rule="evenodd" d="M10.293 5.293a1 1 0 011.414 0l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414-1.414L12.586 11H5a1 1 0 110-2h7.586l-2.293-2.293a1 1 0 010-1.414z" clip-rule="evenodd" />
                </svg></x-modern-button>
        </div>
    </div>
</div>
