<?php $attributes ??= new \Illuminate\View\ComponentAttributeBag;

$__newAttributes = [];
$__propNames = \Illuminate\View\ComponentAttributeBag::extractPropNames((['type' => 'default']));

foreach ($attributes->all() as $__key => $__value) {
    if (in_array($__key, $__propNames)) {
        $$__key = $$__key ?? $__value;
    } else {
        $__newAttributes[$__key] = $__value;
    }
}

$attributes = new \Illuminate\View\ComponentAttributeBag($__newAttributes);

unset($__propNames);
unset($__newAttributes);

foreach (array_filter((['type' => 'default']), 'is_string', ARRAY_FILTER_USE_KEY) as $__key => $__value) {
    $$__key = $$__key ?? $__value;
}

$__defined_vars = get_defined_vars();

foreach ($attributes->all() as $__key => $__value) {
    if (array_key_exists($__key, $__defined_vars)) unset($$__key);
}

unset($__defined_vars); ?>

<div <?php echo e($attributes->merge(['class' => 'bg-base-200 p-4 rounded-lg border border-base-300 text-sm'])); ?>>
    <div class="flex items-start space-x-3">
        <div class="shrink-0 mt-0.5">
            <?php if (isset($component)) { $__componentOriginal643fe1b47aec0b76658e1a0200b34b2c = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal643fe1b47aec0b76658e1a0200b34b2c = $attributes; } ?>
<?php $component = BladeUI\Icons\Components\Svg::resolve([] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('heroicon-o-shield-check'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\BladeUI\Icons\Components\Svg::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['class' => 'h-5 w-5 text-info']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal643fe1b47aec0b76658e1a0200b34b2c)): ?>
<?php $attributes = $__attributesOriginal643fe1b47aec0b76658e1a0200b34b2c; ?>
<?php unset($__attributesOriginal643fe1b47aec0b76658e1a0200b34b2c); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal643fe1b47aec0b76658e1a0200b34b2c)): ?>
<?php $component = $__componentOriginal643fe1b47aec0b76658e1a0200b34b2c; ?>
<?php unset($__componentOriginal643fe1b47aec0b76658e1a0200b34b2c); ?>
<?php endif; ?>
        </div>
        <div>
            <h4 class="font-medium mb-1">Datenschutzhinweis</h4>

            <?php if($type === 'application'): ?>
            <p class="mb-2">
                Die von dir angegebenen Daten (persönliche Informationen, technische Daten wie RAM/FPS und rollenspezifische Angaben) werden ausschließlich zum Zweck der Bearbeitung deiner Bewerbung verarbeitet.
                Rechtsgrundlage ist Art. 6 Abs. 1 lit. a DSGVO (deine Einwilligung).
            </p>
            <p class="mb-2">
                Wir erheben technische Daten (RAM, FPS, GPU, etc.) nur, um deine Eignung für die gewünschte Position zu beurteilen und sicherzustellen, dass du über die notwendige Ausstattung verfügst.
            </p>
            <p>
                Deine Daten werden für die Dauer des Bewerbungsverfahrens gespeichert und anschließend für maximal 6 Monate aufbewahrt,
                sofern du nicht einer längeren Speicherung zustimmst oder die Löschung beantragst.
                Weitere Informationen findest du in unserer <a href="<?php echo e(route('datenschutz')); ?>" class="text-info underline" target="_blank">Datenschutzerklärung</a>.
            </p>
            <?php elseif($type === 'profile'): ?>
            <p class="mb-2">
                Die von dir angegebenen Daten werden zur Verwaltung deines Benutzerkontos verarbeitet.
                Rechtsgrundlage ist Art. 6 Abs. 1 lit. b DSGVO (Erfüllung des Nutzungsvertrags).
            </p>
            <p>
                Deine Daten werden für die Dauer deiner Mitgliedschaft gespeichert. Du kannst jederzeit die Löschung deiner Daten
                über die Datenschutzeinstellungen beantragen. Weitere Informationen findest du in unserer
                <a href="<?php echo e(route('datenschutz')); ?>" class="text-info underline" target="_blank">Datenschutzerklärung</a>.
            </p>
            <?php else: ?>
            <p>
                Die von dir angegebenen Daten werden gemäß unserer <a href="<?php echo e(route('datenschutz')); ?>" class="text-info underline" target="_blank">Datenschutzerklärung</a> verarbeitet.
                Du hast jederzeit das Recht auf Auskunft, Berichtigung, Löschung und Einschränkung der Verarbeitung deiner Daten.
            </p>
            <?php endif; ?>
        </div>
    </div>
</div>
<?php /**PATH C:\Users\<USER>\Documents\GitHub\minewache-website\resources\views/components/gdpr-data-notice.blade.php ENDPATH**/ ?>