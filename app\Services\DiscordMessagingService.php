<?php

namespace App\Services;

// Remove DiscordPHP specific imports if they are no longer directly used for sending
// use Discord\Discord;
// use Discord\Parts\Channel\Message; // Will not be returning this type directly anymore
// use Discord\Parts\Embed\Embed; // Still used for structuring, but not sending directly via Discord object
// use Discord\Http\Exceptions\NotFoundException;
// use Discord\Http\Exceptions\ForbiddenException;
// use Discord\Http\Exceptions\RequestFailedException;

use Illuminate\Support\Facades\Http; // Use Laravel's HTTP Client
use Illuminate\Support\Facades\Log;
// Assuming EmbedData might be a helper or DTO in future, for now, arrays are fine.
// use App\Models\EmbedData;
use Exception;

class DiscordMessagingService
{
    protected string $botToken;
    protected string $discordApiBaseUrl = 'https://discord.com/api/v10'; // Use current API version

    public function __construct()
    {
        $this->botToken = config('minewache_discord_bot.token');
        if (empty($this->botToken)) {
            Log::critical('DiscordMessagingService: Bot Token is not configured!');
            // Consider throwing an exception or ensuring this service cannot be used if token is missing
        }
    }

    /**
     * Send a message to a specific Discord channel using direct REST API calls.
     *
     * @param string $channelId The ID of the channel.
     * @param string $content The text content of the message.
     * @param array $embedsData Optional. Array of embed data structures (compatible with Discord API).
     * @param array $componentsData Optional. Array of component data structures (compatible with Discord API).
     * @param array $laravelAttachments Optional. Array of attachment details (['path' => '/path/to/file', 'filename' => 'name.jpg']).
     * @return array|null The Discord API response as an array or null on failure.
     */
    public function sendMessageToChannel(string $channelId, string $content = '', array $embedsData = [], array $componentsData = [], array $laravelAttachments = []): ?array
    {
        if (empty($this->botToken)) {
            Log::error('DiscordMessagingService: Cannot send message, bot token not configured.');
            return null;
        }
        // Discord allows messages with only embeds or only attachments
        if (empty($content) && empty($embedsData) && empty($laravelAttachments)) {
            Log::warning('DiscordMessagingService: Attempted to send an empty message (no content, embeds, or attachments) to channel ' . $channelId);
            return null;
        }

        $url = "{$this->discordApiBaseUrl}/channels/{$channelId}/messages";

        $payload = [];
        if (!empty($content)) {
            $payload['content'] = $content;
        }
        if (!empty($embedsData)) {
            $payload['embeds'] = $embedsData;
        }
        if (!empty($componentsData)) {
            $payload['components'] = $componentsData;
        }

        try {
            $httpClient = Http::withToken($this->botToken, 'Bot')
                              ->acceptJson();

            if (!empty($laravelAttachments)) {
                $httpClient = $httpClient->asMultipart();

                // Attach payload_json only if there's actual payload data (content, embeds, components)
                // If only sending attachments, payload_json is not needed with content in multipart.
                if (!empty($payload)) {
                     $httpClient->attach('payload_json', json_encode($payload), 'application/json');
                } else if (empty($content) && empty($laravelAttachments[0]['filename'])) {
                    // If only attachments and no other payload, Discord API might require 'content' to be present.
                    // This case is tricky. If there's no content/embed but there ARE attachments,
                    // the content for the files is sent in their respective parts.
                    // If sending ONLY attachments, no payload_json or empty content might be needed.
                    // Let's assume if payload is empty but attachments exist, we might need an empty content string for safety
                    // if the API complains, though usually, it's not needed for multipart file uploads if files have content.
                    // For now, if payload is truly empty, we won't add payload_json.
                    // If there is content or embeds/components, payload_json must be there.
                }

                foreach ($laravelAttachments as $index => $attachmentItem) {
                    if (isset($attachmentItem['path']) && isset($attachmentItem['filename']) && file_exists($attachmentItem['path'])) {
                        $fileContents = file_get_contents($attachmentItem['path']);
                        if ($fileContents === false) {
                            Log::warning("DiscordMessagingService: Could not read file contents for attachment.", ['item' => $attachmentItem]);
                            continue;
                        }
                        // Discord API expects files[n] for multipart names
                        $httpClient->attach("files[{$index}]", $fileContents, $attachmentItem['filename']);
                        Log::info("DiscordMessagingService: Adding file {$attachmentItem['filename']} to multipart request for channel {$channelId}.");
                    } else {
                        Log::warning("DiscordMessagingService: Attachment file path invalid, not found, or filename missing.", ['item' => $attachmentItem]);
                    }
                }
            } else {
                $httpClient = $httpClient->contentType('application/json');
            }

            $response = $httpClient->post($url, empty($laravelAttachments) ? $payload : []); // If attachments, payload already part of multipart


            if ($response->successful()) {
                Log::info("DiscordMessagingService: Message successfully sent to channel {$channelId} via REST API.");
                return $response->json();
            } else {
                Log::error("DiscordMessagingService: Failed to send message to channel {$channelId} via REST API.", [
                    'status' => $response->status(),
                    'response_body' => $response->body() // Log the actual response body
                ]);
                return null;
            }
        } catch (Exception $e) {
            Log::error("DiscordMessagingService: Exception sending message to channel {$channelId} via REST API.", [
                'exception_message' => $e->getMessage(),
                // 'exception_trace' => $e->getTraceAsString() // Limit trace in production
            ]);
            return null;
        }
    }

    /**
     * Creates an embed structure (array) compatible with Discord's API from a data array.
     * This method no longer relies on new Discord\Discord() instance.
     *
     * @param array $data
     * @return array Embed structure as an array.
     */
    public function createEmbed(array $data): array
    {
        $embed = [];
        if (isset($data['title'])) $embed['title'] = $data['title'];
        if (isset($data['description'])) $embed['description'] = $data['description'];
        if (isset($data['url'])) $embed['url'] = $data['url'];
        if (isset($data['timestamp'])) $embed['timestamp'] = $data['timestamp'];
        if (isset($data['color'])) $embed['color'] = $data['color'];

        if (isset($data['footer'])) {
            $embed['footer'] = $data['footer'];
        }
        if (isset($data['image'])) {
            $embed['image'] = $data['image'];
        }
        if (isset($data['thumbnail'])) {
            $embed['thumbnail'] = $data['thumbnail'];
        }
        if (isset($data['author'])) {
            $embed['author'] = $data['author'];
        }
        if (isset($data['fields'])) {
            $embed['fields'] = $data['fields'];
        }
        return $embed;
    }


    /**
     * Send a direct message to a specific Discord user using direct REST API calls.
     *
     * @param string $discordUserId The ID of the target Discord user.
     * @param string $content The text content of the message.
     * @param array $embedsData Optional. Array of embed data structures.
     * @param array $componentsData Optional. Array of component data structures.
     * @return array|null The Discord API response for the sent message or null on failure.
     */
    public function sendDirectMessage(string $discordUserId, string $content = '', array $embedsData = [], array $componentsData = []): ?array
    {
        if (empty($this->botToken)) {
            Log::error('DiscordMessagingService: Cannot send DM, bot token not configured.');
            return null;
        }

        if (empty($content) && empty($embedsData)) { // Components usually need content/embeds
            Log::warning('DiscordMessagingService: Attempted to send an empty DM (no content or embeds) to user ' . $discordUserId);
            return null;
        }

        // 1. Open/Get DM Channel with the user
        $dmChannelId = $this->getDmChannelId($discordUserId);
        if (!$dmChannelId) {
            // Error already logged by getDmChannelId
            return null;
        }

        // 2. Send the message to the obtained DM channel ID
        // We are not supporting attachments for DMs in this iteration for simplicity,
        // but it could be added similarly to sendMessageToChannel if needed.
        Log::info("DiscordMessagingService: Sending DM to user {$discordUserId} via channel {$dmChannelId}.");

        return $this->sendMessageToChannel($dmChannelId, $content, $embedsData, $componentsData, []);
    }

    /**
     * Opens or retrieves an existing DM channel ID for a given Discord User ID.
     *
     * @param string $discordUserId The ID of the target Discord user.
     * @return string|null The DM channel ID or null on failure.
     */
    protected function getDmChannelId(string $discordUserId): ?string
    {
        $url = "{$this->discordApiBaseUrl}/users/@me/channels";
        $payload = ['recipient_id' => $discordUserId];

        try {
            $response = Http::withToken($this->botToken, 'Bot')
                              ->contentType('application/json')
                              ->acceptJson()
                              ->post($url, $payload);

            if ($response->successful() && isset($response->json()['id'])) {
                $dmChannelId = $response->json()['id'];
                Log::info("DiscordMessagingService: Successfully opened/retrieved DM channel {$dmChannelId} for user {$discordUserId}.");
                return $dmChannelId;
            } else {
                Log::error("DiscordMessagingService: Failed to open/retrieve DM channel for user {$discordUserId}.", [
                    'status' => $response->status(),
                    'response_body' => $response->body()
                ]);
                return null;
            }
        } catch (Exception $e) {
            Log::error("DiscordMessagingService: Exception opening/retrieving DM channel for user {$discordUserId}.", [
                'exception_message' => $e->getMessage(),
            ]);
            return null;
        }
    }
}
