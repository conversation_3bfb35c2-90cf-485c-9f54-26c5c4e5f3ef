<x-app-layout>
    <x-slot name="heading">
        {{ __('navigation.youtube') }}
    </x-slot>

    <x-slot name="breadcrumbs">
        <x-breadcrumbs :items="[
            ['label' => __('navigation.youtube')]
        ]" />
    </x-slot>

    <style>
        /* Custom animations */
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(10px); }
            to { opacity: 1; transform: translateY(0); }
        }

        .animate-fade-in {
            animation: fadeIn 0.5s ease-out forwards;
        }

        .animate-slide-up {
            animation: slideUp 0.8s ease-out forwards;
            animation-delay: 0.3s;
        }

        @keyframes slideUp {
            from { opacity: 0; transform: translateY(30px); }
            to { opacity: 1; transform: translateY(0); }
        }

        /* Hide scrollbar but keep functionality */
        .scrollbar-hide {
            -ms-overflow-style: none;  /* IE and Edge */
            scrollbar-width: none;  /* Firefox */
        }
        .scrollbar-hide::-webkit-scrollbar {
            display: none; /* Chrome, Safari, Opera */
        }

        /* Custom tab styling (Aniworld-like) */
        .tabs-boxed .tab {
            margin: 0 2px;
            border-radius: 0.5rem;
            transition: all 0.3s ease;
        }

        .tabs-boxed .tab-active {
            background-color: theme('colors.primary');
            color: theme('colors.primary-content');
            font-weight: bold;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
        }

        .tabs-boxed .tab:hover:not(.tab-active),
        .tabs-boxed .tab:focus-visible:not(.tab-active) {
            background-color: rgba(var(--color-primary-rgb), 0.2);
            outline: none;
        }

        /* Season content transition */
        .season-content {
            transition: opacity 0.3s ease;
        }

        /* Video card hover effects */
        .netflix-card:hover,
        .netflix-card:focus-within {
            z-index: 10;
        }

        /* Card styling */
        .netflix-card {
            background-color: theme('colors.base-200');
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }

        .netflix-card:hover {
            transform: scale(1.03);
            box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
        }

        /* Card info styling */
        .card-info {
            background-color: theme('colors.base-200');
            color: theme('colors.base-content');
            opacity: 1 !important;
            visibility: visible !important;
            transform: translateY(0) !important;
        }

        /* Accessibility improvements */
        .netflix-card button:focus-visible {
            outline: 3px solid theme('colors.primary');
            outline-offset: 2px;
            box-shadow: 0 0 0 3px rgba(var(--color-primary-rgb), 0.4);
        }

        /* Improved focus styles for interactive elements */
        .input:focus-visible,
        .btn:focus-visible,
        a:focus-visible {
            outline: 3px solid theme('colors.primary');
            outline-offset: 2px;
            box-shadow: 0 0 0 3px rgba(var(--color-primary-rgb), 0.4);
        }

        /* Modal styling */
        .youtube-modal {
            background-color: rgba(var(--color-base-200-rgb), 0.95);
            backdrop-filter: blur(8px);
        }

        .modal-content {
            background-color: theme('colors.base-100');
            color: theme('colors.base-content');
        }

        .modal-header {
            background-color: theme('colors.base-200');
            color: theme('colors.base-content');
        }

        .modal-footer {
            background-color: theme('colors.base-200');
            color: theme('colors.base-content');
        }

        /* Featured section styling */
        .featured-overlay {
            background: linear-gradient(to top,
                theme('colors.base-100') 0%,
                rgba(var(--color-base-100-rgb), 0.8) 50%,
                rgba(var(--color-base-100-rgb), 0) 100%);
        }

        /* Season tabs styling */
        .season-tabs-container {
            background-color: rgba(var(--color-base-200-rgb), 0.9);
            backdrop-filter: blur(8px);
            border-bottom: 1px solid rgba(var(--color-base-content-rgb), 0.1);
        }

        /* Video metadata styling */
        .video-metadata {
            color: rgba(var(--color-base-content-rgb), 0.7);
        }

        /* Search input styling */
        .search-input {
            background-color: theme('colors.base-200');
            color: theme('colors.base-content');
            border: 1px solid rgba(var(--color-base-content-rgb), 0.2);
        }

        .search-input:focus {
            border-color: theme('colors.primary');
            box-shadow: 0 0 0 2px rgba(var(--color-primary-rgb), 0.2);
        }

        /* Play button styling */
        .play-button {
            background-color: theme('colors.primary');
            color: theme('colors.primary-content');
        }

        .play-button:hover {
            background-color: theme('colors.primary-focus');
        }
    </style>

    <!-- Netflix-style background that respects theme -->
    <div class="w-full bg-base-100 text-base-content min-h-screen pb-12" x-data="{ isDarkMode: document.documentElement.getAttribute('data-theme') === 'minewache' }" x-init="$watch('isDarkMode', value => console.log('Dark mode:', value))" @theme-changed.window="isDarkMode = document.documentElement.getAttribute('data-theme') === 'minewache'">
        <!-- Hero section with featured video -->
        @php
            $seasonGroups = $youtubeLinks->groupBy('season')->reverse();
        @endphp

        @if($featuredVideo)
        <div class="relative w-full" style="height: 80vh;">
            <!-- Theme-aware gradient overlays for better text readability -->
            <div class="absolute inset-0 featured-overlay z-10"></div>

            <!-- Background thumbnail only -->
            <div class="w-full h-full">
                @if($featuredVideo->thumbnail_url)
                    <img src="{{ $featuredVideo->thumbnail_url }}" alt="{{ $featuredVideo->title }}" class="w-full h-full object-cover">
                @else
                    <img src="https://img.youtube.com/vi/{{ $featuredVideo->link }}/maxresdefault.jpg" alt="{{ $featuredVideo->title }}" class="w-full h-full object-cover" onerror="this.src='https://img.youtube.com/vi/{{ $featuredVideo->link }}/0.jpg';">
                @endif
            </div>

            <!-- Netflix-style content positioning -->
            <div class="absolute bottom-0 left-0 p-12 z-20 w-full lg:w-2/3 pb-24">
                <div class="animate-slide-up opacity-0">
                    <!-- Netflix-style typography -->
                    <h1 class="text-5xl font-bold mb-2 text-base-content">{{ __('youtube.site_name') }}</h1>
                    <div class="flex items-center gap-3 mb-4">
                        <span class="bg-primary text-primary-content px-2 py-1 text-sm font-bold">{{ __('youtube.new_label') }}</span>
                        <h2 class="text-2xl font-semibold text-base-content">{{ $featuredVideo->title ?: __('youtube.season_episode', ['season' => $featuredVideo->season, 'episode' => $featuredVideo->episode]) }}</h2>
                    </div>

                    <!-- Video metadata -->
                    <div class="flex items-center gap-4 text-sm video-metadata mb-4">
                        @if($featuredVideo->published_at)
                            <span>{{ $featuredVideo->published_time_ago }}</span>
                        @endif
                        @if($featuredVideo->duration_seconds)
                            <span>{{ $featuredVideo->formatted_duration }}</span>
                        @endif
                        @if($featuredVideo->view_count)
                            <span>{{ $featuredVideo->formatted_view_count }}</span>
                        @endif
                    </div>

                    <p class="text-lg mb-8 video-metadata max-w-2xl line-clamp-2 hover:line-clamp-none transition-all duration-300">
                        {{ Str::limit($featuredVideo->description, 150) ?: __('youtube.check_latest_videos') }}
                    </p>

                    <!-- Theme-aware buttons -->
                    <div class="flex gap-4">
                        <a href="#season{{ $featuredVideo->season }}" class="btn play-button gap-2 px-8 py-3 text-lg" aria-label="{{ __('youtube.jump_to_season', ['season' => $featuredVideo->season]) }}">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M14.752 11.168l-3.197-2.132A1 1 0 0010 9.87v4.263a1 1 0 001.555.832l3.197-2.132a1 1 0 000-1.664z" />
                            </svg>
                            {{ __('youtube.play_button') }}
                        </a>
                        <button onclick="playVideo('{{ $featuredVideo->link }}')" class="btn btn-outline btn-secondary gap-2 px-8 py-3 text-lg" aria-label="{{ __('youtube.more_info_for', ['title' => $featuredVideo->title]) }}">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                            </svg>
                            {{ __('youtube.more_info') }}
                        </button>
                    </div>
                </div>
            </div>
        </div>
        @endif

        <!-- Season tabs navigation (Aniworld style) with Alpine.js -->
        <div x-data="youtubeManager()" class="sticky top-0 season-tabs-container z-30 py-2 mb-4">
            <div class="container mx-auto px-4">
                <div class="flex justify-between items-center">
                    <div class="tabs tabs-boxed bg-base-300/50 p-1 overflow-x-auto scrollbar-hide" role="tablist" aria-label="{{ __('youtube.seasons') }}">
                        @php
                            // Get the latest season number
                            $latestSeason = $seasonGroups->keys()->max();
                        @endphp

                        @foreach($seasonGroups as $season => $links)
                            <a id="tab-season{{ $season }}"
                               class="tab tab-lg"
                               :class="{ 'tab-active': activeSeason === {{ $season }} }"
                               @click="switchSeason({{ $season }})"
                               role="tab"
                               :aria-selected="activeSeason === {{ $season }}"
                               aria-controls="season{{ $season }}">
                                {{ __('youtube.season', ['number' => $season]) }}
                                <span class="ml-1 text-xs opacity-70" x-text="getSeasonCountDisplay({{ $season }}, {{ $links->count() }})"></span>
                            </a>
                        @endforeach
                    </div>
                    <div class="relative">
                        <input type="text" x-model="searchTerm" @input="filterVideos()" placeholder="{{ __('youtube.search_videos') }}" class="input input-sm input-bordered w-full max-w-xs search-input" aria-label="{{ __('youtube.search_videos') }}">
                    </div>
                </div>
            </div>

        <!-- Season content (Only one visible at a time) -->
        <div class="container mx-auto px-4 py-4 -mt-16 relative z-20 pt-20">
            @foreach($seasonGroups as $season => $links)
                <div id="season{{ $season }}" class="season-content mb-12" x-show="activeSeason === {{ $season }}" x-transition:enter="transition ease-out duration-300" x-transition:enter-start="opacity-0" x-transition:enter-end="opacity-100" role="tabpanel" aria-labelledby="tab-season{{ $season }}">
                    <h2 class="text-2xl font-bold mb-6 ml-2 flex items-center" :class="isDarkMode ? 'text-white' : 'text-base-content'">
                        <span>{{ __('youtube.season', ['number' => $season]) }}</span>
                        <span class="text-sm ml-2" :class="isDarkMode ? 'text-gray-400' : 'text-base-content/70'" x-text="getSeasonCountDisplay({{ $season }}, {{ $links->count() }})"></span>
                    </h2>

                    <div class="relative">
                        <!-- Responsive grid layout -->
                        <div class="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-6">
                            <!-- No results message -->
                            <div class="col-span-full text-center py-16 hidden no-results-message" :class="isDarkMode ? 'text-white' : 'text-base-content'" x-show="searchTerm && !Array.from(document.querySelectorAll('#season{{ $season }} .video-item')).some(el => !el.hasAttribute('hidden'))">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-16 w-16 mx-auto text-gray-500 mb-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                                </svg>
                                <h3 class="text-xl font-bold mb-2">{{ __('youtube.no_videos_found') }}</h3>
                                <p :class="isDarkMode ? 'text-gray-400' : 'text-base-content/70'">{{ __('youtube.try_different_search') }}</p>
                            </div>

                            @foreach($links->sortBy('episode') as $link)
                                <div class="netflix-card video-item transform transition-all hover:scale-105 hover:z-10"
                                     x-show="isVideoVisible({{ $season }}, {{ $link->episode }}, '{{ addslashes(strtolower($link->title ?: 'Staffel ' . $season . ' Folge ' . $link->episode)) }}')">
                                    <div class="relative group rounded overflow-hidden shadow-lg bg-base-100 h-full border border-gray-800 hover:border-primary hover:shadow-xl transition-all duration-300">
                                        <!-- Thumbnail with play button overlay -->
                                        <div class="relative aspect-video bg-gray-900 overflow-hidden">
                                            @if($link->thumbnail_url)
                                                <img
                                                    src="{{ $link->thumbnail_url }}"
                                                    alt="{{ $link->title ?: __('youtube.season_episode', ['season' => $season, 'episode' => $link->episode]) }}"
                                                    class="w-full h-full object-cover transition-all group-hover:opacity-70 group-hover:scale-105"
                                                    loading="lazy"
                                                >
                                            @else
                                                <img
                                                    src="https://img.youtube.com/vi/{{ $link->link }}/maxresdefault.jpg"
                                                    alt="{{ $link->title ?: __('youtube.season_episode', ['season' => $season, 'episode' => $link->episode]) }}"
                                                    class="w-full h-full object-cover transition-all group-hover:opacity-70 group-hover:scale-105"
                                                    onerror="this.src='https://img.youtube.com/vi/{{ $link->link }}/0.jpg';"
                                                    loading="lazy"
                                                >
                                            @endif

                                            <!-- Episode badge -->
                                            <div class="absolute top-2 left-2 bg-primary/90 text-white text-xs px-2 py-1 rounded-full font-bold">
                                                {{ __('youtube.episode_short', ['number' => $link->episode]) }}
                                            </div>

                                            <!-- Duration badge -->
                                            @if($link->duration_seconds)
                                                <div class="absolute bottom-2 right-2 bg-black/70 text-white text-xs px-2 py-1 rounded-full">
                                                    {{ $link->formatted_duration }}
                                                </div>
                                            @endif

                                            <!-- Always visible play button -->
                                            <div class="absolute inset-0 flex items-center justify-center bg-black/30 opacity-0 group-hover:opacity-100 transition-opacity">
                                                <button @click="playVideo('{{ $link->link }}')" class="play-button rounded-full p-3 transform transition-transform hover:scale-110 shadow-lg" aria-label="{{ __('youtube.play_video', ['title' => $link->title ?: __('youtube.season_episode', ['season' => $season, 'episode' => $link->episode])]) }}">
                                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-8 w-8" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M14.752 11.168l-3.197-2.132A1 1 0 0010 9.87v4.263a1 1 0 001.555.832l3.197-2.132a1 1 0 000-1.664z" />
                                                    </svg>
                                                </button>
                                            </div>
                                        </div>

                                        <!-- Video info (Always visible) -->
                                        <div class="p-3 card-info">
                                            <!-- Title with tooltip for full title -->
                                            <div class="mb-1">
                                                <h3 class="text-base font-bold line-clamp-2 group-hover:text-primary transition-colors duration-300" title="{{ $link->title ?: __('youtube.season_episode', ['season' => $season, 'episode' => $link->episode]) }}">{{ $link->title ?: __('youtube.episode', ['number' => $link->episode]) }}</h3>
                                            </div>

                                            <!-- Compact metadata row -->
                                            <div class="flex flex-wrap text-xs video-metadata gap-x-2 mb-2">
                                                @if($link->published_at)
                                                    <span class="inline-flex items-center gap-1">
                                                        <svg xmlns="http://www.w3.org/2000/svg" class="h-3 w-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
                                                        </svg>
                                                        <span>{{ $link->published_at->format('d.m.Y') }}</span>
                                                    </span>
                                                @endif
                                                @if($link->view_count)
                                                    <span class="inline-flex items-center gap-1">
                                                        <svg xmlns="http://www.w3.org/2000/svg" class="h-3 w-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                                                        </svg>
                                                        <span>{{ $link->formatted_view_count }}</span>
                                                    </span>
                                                @endif
                                            </div>

                                            <!-- Watch button -->
                                            <button @click="playVideo('{{ $link->link }}')" class="btn btn-xs play-button w-full gap-2" aria-label="{{ __('youtube.play_video', ['title' => $link->title ?: __('youtube.season_episode', ['season' => $season, 'episode' => $link->episode])]) }}">
                                                <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M14.752 11.168l-3.197-2.132A1 1 0 0010 9.87v4.263a1 1 0 001.555.832l3.197-2.132a1 1 0 000-1.664z" />
                                                </svg>
                                                {{ __('youtube.play_button') }}
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            @endforeach
                        </div>
                    </div>
                </div>
            @endforeach
        </div>

        <!-- Enhanced video player modal with Alpine.js -->
        <div x-show="showModal"
             x-transition:enter="transition ease-out duration-300"
             x-transition:enter-start="opacity-0 scale-95"
             x-transition:enter-end="opacity-100 scale-100"
             x-transition:leave="transition ease-in duration-200"
             x-transition:leave-start="opacity-100 scale-100"
             x-transition:leave-end="opacity-0 scale-95"
             @click.self="closeModal()"
             @keydown.escape.window="closeModal()"
             class="fixed inset-0 youtube-modal z-50 flex items-center justify-center"
             role="dialog"
             aria-modal="true"
             :aria-label="currentVideo.title">
            <div class="relative w-full max-w-5xl mx-auto p-4 rounded-lg overflow-hidden">
                <button @click="closeModal()" class="absolute -top-2 right-0 text-base-content text-3xl bg-base-200 hover:bg-primary hover:text-primary-content rounded-full w-10 h-10 flex items-center justify-center transition-colors z-50" aria-label="{{ __('youtube.close_video') }}">&times;</button>
                <div class="aspect-video w-full rounded-t-lg overflow-hidden shadow-2xl">
                    <iframe x-ref="videoPlayer" class="w-full h-full" src="" frameborder="0" allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture" allowfullscreen title="{{ __('youtube.player_title') }}"></iframe>
                </div>
                <!-- Video info section with improved layout -->
                <div class="p-4 modal-footer rounded-b-lg">
                    <h3 x-text="currentVideo.title" class="text-xl font-bold truncate"></h3>
                    <div class="flex flex-wrap items-center gap-2 text-sm video-metadata my-2" x-html="currentVideo.metadataHtml"></div>
                    <div class="relative">
                        <p x-text="currentVideo.description" x-bind:class="{ 'line-clamp-2': !descriptionExpanded }" class="text-sm video-metadata mt-2 transition-all duration-300"></p>
                        <button @click="descriptionExpanded = !descriptionExpanded" x-text="descriptionExpanded ? '{{ __('messages.show_less') }}' : '{{ __('messages.show_more') }}'" class="text-xs text-primary hover:text-primary-focus mt-1 transition-colors" aria-expanded="descriptionExpanded"></button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        document.addEventListener('alpine:init', () => {
            Alpine.data('youtubeManager', () => ({
                // Data
                activeSeason: {{ $seasonGroups->keys()->max() }},
                searchTerm: '',
                showModal: false,
                descriptionExpanded: false,
                currentVideo: {
                    title: '',
                    description: '',
                    metadataHtml: '',
                    videoId: ''
                },
                videoData: {
                    @foreach($youtubeLinks as $link)
                    '{{ $link->link }}': {
                        title: '{{ addslashes($link->title ?: "Staffel {$link->season} Folge {$link->episode}") }}',
                        season: {{ $link->season }},
                        episode: {{ $link->episode }},
                        description: '{{ addslashes($link->description ?: "") }}',
                        duration: '{{ $link->formatted_duration }}',
                        views: '{{ $link->formatted_view_count }}',
                        published: '{{ $link->published_time_ago }}'
                    },
                    @endforeach
                },

                // Lifecycle
                init() {
                    // Store for focus management
                    this._lastFocusedElement = null;

                    // Handle URL hash for direct season navigation
                    if (window.location.hash) {
                        const seasonMatch = window.location.hash.match(/season(\d+)/);
                        if (seasonMatch && seasonMatch[1]) {
                            this.activeSeason = parseInt(seasonMatch[1]);
                        }
                    }

                    // Add animation classes to cards for staggered appearance
                    this.$nextTick(() => {
                        const cards = document.querySelectorAll('.netflix-card');
                        cards.forEach((card, index) => {
                            card.classList.add('animate-fade-in');
                            card.style.animationDelay = `${index * 0.05}s`;

                            // Make video cards keyboard focusable
                            const playButton = card.querySelector('button[aria-label]');
                            if (playButton) {
                                playButton.setAttribute('tabindex', '0');
                            }
                        });

                        // Hero section animations are now handled by CSS
                    });

                    // Add keyboard navigation for seasons
                    window.addEventListener('keydown', (e) => this.handleKeyNavigation(e));
                },

                // Methods
                switchSeason(seasonNumber) {
                    this.activeSeason = seasonNumber;
                    this.searchTerm = '';

                    // Update URL hash without scrolling
                    const scrollPosition = window.scrollY;
                    window.location.hash = 'season' + seasonNumber;
                    window.scrollTo(0, scrollPosition);
                },

                playVideo(videoId) {
                    if (this.videoData[videoId]) {
                        const data = this.videoData[videoId];

                        // Store the currently focused element for returning focus later
                        this._lastFocusedElement = document.activeElement;

                        // Build metadata HTML
                        let metadataHTML = `<span>{{ __('youtube.season_episode_text') }}${data.season} {{ __('youtube.episode_text') }}${data.episode}</span>`;

                        if (data.published) {
                            metadataHTML += `<span class="mx-2">•</span><span>${data.published}</span>`;
                        }

                        if (data.duration) {
                            metadataHTML += `<span class="mx-2">•</span><span>${data.duration}</span>`;
                        }

                        if (data.views) {
                            metadataHTML += `<span class="mx-2">•</span><span>${data.views}</span>`;
                        }

                        // Set current video data
                        this.currentVideo = {
                            title: data.title,
                            description: data.description || '{{ __('youtube.no_description') }}',
                            metadataHtml: metadataHTML,
                            videoId: videoId
                        };

                        // Reset description expanded state
                        this.descriptionExpanded = false;

                        // Show modal and set iframe source
                        this.showModal = true;
                        this.$nextTick(() => {
                            this.$refs.videoPlayer.src = `https://www.youtube.com/embed/${videoId}?autoplay=1&rel=1&showinfo=1`;

                            // Focus the close button for keyboard accessibility
                            const closeButton = document.querySelector('[aria-label="{{ __('youtube.close_video') }}"]');
                            if (closeButton) {
                                setTimeout(() => closeButton.focus(), 100);
                            }
                        });
                    }
                },

                closeModal() {
                    this.$refs.videoPlayer.src = '';
                    this.showModal = false;

                    // Return focus to the element that opened the modal for accessibility
                    if (this._lastFocusedElement) {
                        this._lastFocusedElement.focus();
                        this._lastFocusedElement = null;
                    }
                },

                filterVideos() {
                    // No need to implement this as Alpine.js will handle filtering with x-show directive
                    // This is just a placeholder for the method called by the input field
                },

                isVideoVisible(season, episode, title) {
                    if (this.activeSeason !== season) return false;

                    if (!this.searchTerm) return true;

                    const searchTerm = this.searchTerm.toLowerCase().trim();
                    return title.includes(searchTerm) ||
                           ('{{ __('youtube.season_text_lowercase') }}' + season).includes(searchTerm) ||
                           ('{{ __('youtube.episode_text_lowercase') }}' + episode).includes(searchTerm) ||
                           ('s' + season + 'e' + episode).includes(searchTerm);
                },

                getSeasonCountDisplay(season, totalCount) {
                    if (this.activeSeason !== season || !this.searchTerm) {
                        return `(${totalCount})`;
                    }

                    // Count visible items for the active season when searching
                    const visibleCount = Array.from(document.querySelectorAll(`#season${season} .video-item`)).filter(item => {
                        const title = item.getAttribute('data-title') || '';
                        const episode = item.getAttribute('data-episode') || '';
                        const searchTerm = this.searchTerm.toLowerCase().trim();

                        return title.includes(searchTerm) ||
                               ('{{ __('youtube.season_text_lowercase') }}' + season).includes(searchTerm) ||
                               ('{{ __('youtube.episode_text_lowercase') }}' + episode).includes(searchTerm) ||
                               ('s' + season + 'e' + episode).includes(searchTerm);
                    }).length;

                    return `(${visibleCount}/${totalCount})`;
                },

                handleKeyNavigation(e) {
                    // Get all season numbers
                    const seasonNumbers = Object.keys(this.videoData)
                        .map(key => this.videoData[key].season)
                        .filter((value, index, self) => self.indexOf(value) === index)
                        .sort((a, b) => a - b);

                    // Navigate with arrow keys
                    if (e.key === 'ArrowRight' || e.key === 'ArrowDown') {
                        const currentIndex = seasonNumbers.indexOf(this.activeSeason);
                        if (currentIndex < seasonNumbers.length - 1) {
                            this.switchSeason(seasonNumbers[currentIndex + 1]);
                            // Focus the active tab for accessibility
                            this.$nextTick(() => {
                                document.getElementById(`tab-season${seasonNumbers[currentIndex + 1]}`).focus();
                            });
                        }
                    } else if (e.key === 'ArrowLeft' || e.key === 'ArrowUp') {
                        const currentIndex = seasonNumbers.indexOf(this.activeSeason);
                        if (currentIndex > 0) {
                            this.switchSeason(seasonNumbers[currentIndex - 1]);
                            // Focus the active tab for accessibility
                            this.$nextTick(() => {
                                document.getElementById(`tab-season${seasonNumbers[currentIndex - 1]}`).focus();
                            });
                        }
                    }
                }
            }));

        });

        // Alpine.js initialization complete
    </script>
</x-app-layout>
