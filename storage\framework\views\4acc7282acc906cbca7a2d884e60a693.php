
<?php $attributes ??= new \Illuminate\View\ComponentAttributeBag;

$__newAttributes = [];
$__propNames = \Illuminate\View\ComponentAttributeBag::extractPropNames((['ticket']));

foreach ($attributes->all() as $__key => $__value) {
    if (in_array($__key, $__propNames)) {
        $$__key = $$__key ?? $__value;
    } else {
        $__newAttributes[$__key] = $__value;
    }
}

$attributes = new \Illuminate\View\ComponentAttributeBag($__newAttributes);

unset($__propNames);
unset($__newAttributes);

foreach (array_filter((['ticket']), 'is_string', ARRAY_FILTER_USE_KEY) as $__key => $__value) {
    $$__key = $$__key ?? $__value;
}

$__defined_vars = get_defined_vars();

foreach ($attributes->all() as $__key => $__value) {
    if (array_key_exists($__key, $__defined_vars)) unset($$__key);
}

unset($__defined_vars); ?>

<div class="grid grid-cols-1 md:grid-cols-2 lg:col-span-4 gap-4">
    
    <div>
        <h2 class="text-lg font-semibold mb-3 flex items-center gap-2">
            <svg class="w-5 h-5 opacity-70" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 8h10M7 12h10M7 16h10"></path>
            </svg>
            <?php echo e(__('tickets.description')); ?>

        </h2>
        <div class="prose prose-sm md:prose-base dark:prose-invert max-w-none whitespace-pre-wrap">
            <?php echo e($ticket->description); ?>

        </div>
    </div>

    
    <div class="border-t md:border-t-0 md:border-l border-base-300/30 pt-4 md:pt-0 lg:pt-0 md:pl-6">
        <h2 class="text-lg font-semibold mb-3 flex items-center gap-2">
            <svg class="w-5 h-5 opacity-70" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
            </svg>
            <?php echo e(__('tickets.details')); ?>

        </h2>
        <ul class="space-y-3 text-sm md:text-base">
            <li class="flex justify-between items-center">
                <span class="text-base-content/60"><?php echo e(__('tickets.status')); ?>:</span>
                <?php if (isset($component)) { $__componentOriginal7c5961ba4242859e8f30202299af2419 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal7c5961ba4242859e8f30202299af2419 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.modern-status-badge','data' => ['status' => $ticket->status,'size' => 'sm']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('modern-status-badge'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['status' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($ticket->status),'size' => 'sm']); ?>
                    <?php echo e($ticket->statusLabel); ?>

                 <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal7c5961ba4242859e8f30202299af2419)): ?>
<?php $attributes = $__attributesOriginal7c5961ba4242859e8f30202299af2419; ?>
<?php unset($__attributesOriginal7c5961ba4242859e8f30202299af2419); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal7c5961ba4242859e8f30202299af2419)): ?>
<?php $component = $__componentOriginal7c5961ba4242859e8f30202299af2419; ?>
<?php unset($__componentOriginal7c5961ba4242859e8f30202299af2419); ?>
<?php endif; ?>
            </li>
            <li class="flex justify-between items-center">
                <span class="text-base-content/60"><?php echo e(__('tickets.assigned_to')); ?>:</span>
                <!--[if BLOCK]><![endif]--><?php if($ticket->assignedTo): ?>
                    <div class="flex items-center gap-2">
                        <img class="w-5 h-5 rounded-full modern-avatar"
                             src="<?php echo e($ticket->assignedTo->getAvatar(['extension' => 'webp', 'size' => 20])); ?>"
                             alt="<?php echo e($ticket->assignedTo->username); ?>">
                        <span class="font-medium"><?php echo e($ticket->assignedTo->username); ?></span>
                    </div>
                <?php else: ?>
                    <span class="text-base-content/50 italic"><?php echo e(__('tickets.unassigned')); ?></span>
                <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
            </li>
            <li class="flex justify-between items-center">
                <span class="text-base-content/60"><?php echo e(__('tickets.created_at')); ?>:</span>
                <span class="font-mono text-sm"><?php echo e($ticket->created_at->format('d.m.Y H:i')); ?></span>
            </li>
            <li class="flex justify-between items-center">
                <span class="text-base-content/60"><?php echo e(__('tickets.updated_at')); ?>:</span>
                <span class="font-mono text-sm"><?php echo e($ticket->updated_at->format('d.m.Y H:i')); ?></span>
            </li>
            <li class="flex justify-between items-center">
                <span class="text-base-content/60"><?php echo e(__('tickets.message_count')); ?>:</span>
                <?php if (isset($component)) { $__componentOriginal7c5961ba4242859e8f30202299af2419 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal7c5961ba4242859e8f30202299af2419 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.modern-status-badge','data' => ['status' => 'default','size' => 'sm','variant' => 'filled']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('modern-status-badge'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['status' => 'default','size' => 'sm','variant' => 'filled']); ?>
                    <?php echo e($ticket->messages->count()); ?>

                 <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal7c5961ba4242859e8f30202299af2419)): ?>
<?php $attributes = $__attributesOriginal7c5961ba4242859e8f30202299af2419; ?>
<?php unset($__attributesOriginal7c5961ba4242859e8f30202299af2419); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal7c5961ba4242859e8f30202299af2419)): ?>
<?php $component = $__componentOriginal7c5961ba4242859e8f30202299af2419; ?>
<?php unset($__componentOriginal7c5961ba4242859e8f30202299af2419); ?>
<?php endif; ?>
            </li>
        </ul>
    </div>
</div>
<?php /**PATH C:\Users\<USER>\Documents\GitHub\minewache-website\resources\views/livewire/ticket/partials/info.blade.php ENDPATH**/ ?>