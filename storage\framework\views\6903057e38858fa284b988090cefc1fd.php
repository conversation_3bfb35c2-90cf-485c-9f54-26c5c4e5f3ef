<?php $attributes ??= new \Illuminate\View\ComponentAttributeBag;

$__newAttributes = [];
$__propNames = \Illuminate\View\ComponentAttributeBag::extractPropNames(([
    'level' => 'default', // default, light, dark
    'border' => null, // null, primary, secondary, accent
    'padding' => 'p-6 md:p-8', // custom padding classes
    'rounded' => 'rounded-xl', // custom border radius
    'shadow' => 'shadow-xl', // custom shadow
    'hover' => false, // enable hover effect
]));

foreach ($attributes->all() as $__key => $__value) {
    if (in_array($__key, $__propNames)) {
        $$__key = $$__key ?? $__value;
    } else {
        $__newAttributes[$__key] = $__value;
    }
}

$attributes = new \Illuminate\View\ComponentAttributeBag($__newAttributes);

unset($__propNames);
unset($__newAttributes);

foreach (array_filter(([
    'level' => 'default', // default, light, dark
    'border' => null, // null, primary, secondary, accent
    'padding' => 'p-6 md:p-8', // custom padding classes
    'rounded' => 'rounded-xl', // custom border radius
    'shadow' => 'shadow-xl', // custom shadow
    'hover' => false, // enable hover effect
]), 'is_string', ARRAY_FILTER_USE_KEY) as $__key => $__value) {
    $$__key = $$__key ?? $__value;
}

$__defined_vars = get_defined_vars();

foreach ($attributes->all() as $__key => $__value) {
    if (array_key_exists($__key, $__defined_vars)) unset($$__key);
}

unset($__defined_vars); ?>

<?php
    $baseClasses = 'glassmorphism backdrop-blur-md';

    // Background opacity based on level
    $bgClasses = match($level) {
        'light' => 'bg-white/20 dark:bg-gray-800/15',
        'dark' => 'bg-white/60 dark:bg-gray-800/50',
        default => 'bg-white/40 dark:bg-gray-800/30',
    };

    // Border color
    $borderClasses = match($border) {
        'primary' => 'border-l-4 border-primary',
        'secondary' => 'border-l-4 border-secondary',
        'accent' => 'border-l-4 border-accent',
        'primary-all' => 'border-2 border-primary',
        'secondary-all' => 'border-2 border-secondary',
        'accent-all' => 'border-2 border-accent',
        default => 'border border-gray-200 dark:border-gray-700',
    };

    // Hover effect
    $hoverClasses = $hover ? 'transition-all duration-300 hover:shadow-2xl hover:-translate-y-1' : '';

    $classes = $baseClasses . ' ' . $bgClasses . ' ' . $borderClasses . ' ' . $padding . ' ' . $rounded . ' ' . $shadow . ' ' . $hoverClasses;
?>

<div <?php echo e($attributes->merge(['class' => $classes])); ?>>
    <?php echo e($slot); ?>

</div>
<?php /**PATH C:\Users\<USER>\Documents\GitHub\minewache-website\resources\views/components/glassmorphism-card.blade.php ENDPATH**/ ?>