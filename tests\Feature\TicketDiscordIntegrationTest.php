<?php

namespace Tests\Feature;

use Tests\TestCase;
use Illuminate\Foundation\Testing\RefreshDatabase;
use App\Models\User;
use App\Models\Ticket;
use App\Models\TicketMessage;
use App\Services\DiscordService;
use App\Http\Livewire\TicketView;
use Livewire\Livewire;
use Mockery;

class TicketDiscordIntegrationTest extends TestCase
{
    use RefreshDatabase;

    protected User $user;

    protected function setUp(): void
    {
        parent::setUp();

        // Create and authenticate a user
        $this->user = User::factory()->create();
        $this->actingAs($this->user);
    }

    protected function tearDown(): void
    {
        Mockery::close();
        parent::tearDown();
    }

    /** @test */
    public function when_user_replies_discord_service_sendTicketMessage_is_called()
    {
        // Given an existing ticket
        $ticket = Ticket::factory()->create([
            'user_id' => $this->user->id,
        ]);

        // Prepare a mock for DiscordService
        $discordMock = Mockery::mock(DiscordService::class);
        // Expect sendTicketMessage to be called once with the newly created TicketMessage
        $discordMock
            ->shouldReceive('sendTicketMessage')
            ->once()
            ->withArgs(function ($message, $retryCount, $maxRetries) use ($ticket) {
                return $message instanceof TicketMessage
                    && $message->ticket_id === $ticket->id
                    && $message->user_id === $this->user->id
                    && $message->message === 'Hello Discord'
                    && $retryCount === 0
                    && $maxRetries === 3;
            })
            ->andReturnTrue();

        $this->app->instance(DiscordService::class, $discordMock);

        // When the user posts a reply via the Livewire component
        Livewire::test(TicketView::class, ['ticket' => $ticket])
            ->set('form.message', 'Hello Discord')
            ->call('addReply')
            ->assertHasNoErrors();
    }

    /** @test */
    public function when_user_closes_ticket_discord_service_updateTicketStatus_is_called()
    {
        // Given an open ticket
        $ticket = Ticket::factory()->create([
            'user_id' => $this->user->id,
            'status'  => 'open',
        ]);

        // Prepare a mock for DiscordService
        $discordMock = Mockery::mock(DiscordService::class);
        // Expect updateTicketStatus to be called once with the ticket status updated to closed
        $discordMock
            ->shouldReceive('updateTicketStatus')
            ->once()
            ->withArgs(function ($t) use ($ticket) {
                return $t instanceof Ticket
                    && $t->id === $ticket->id
                    && $t->status === 'closed';
            })
            ->andReturnTrue();

        $this->app->instance(DiscordService::class, $discordMock);

        // When the user closes the ticket via Livewire
        Livewire::test(TicketView::class, ['ticket' => $ticket])
            ->call('setStatusAndUpdate', 'closed')
            ->assertHasNoErrors();
    }

    /** @test */
    public function when_user_assigns_ticket_discord_service_updateTicketAssignment_is_called()
    {
        // Given an unassigned ticket
        $ticket = Ticket::factory()->create([
            'user_id'     => $this->user->id,
            'status'      => 'open',
            'assigned_to' => null,
        ]);

        // Prepare a mock for DiscordService
        $discordMock = Mockery::mock(DiscordService::class);
        // Expect updateTicketAssignment to be called once with the ticket having assigned_to = current user
        $discordMock
            ->shouldReceive('updateTicketAssignment')
            ->once()
            ->withArgs(function ($t) use ($ticket) {
                return $t instanceof Ticket
                    && $t->id === $ticket->id
                    && $t->assigned_to === $this->user->id;
            })
            ->andReturnTrue();

        $this->app->instance(DiscordService::class, $discordMock);

        // When the user assigns themselves to the ticket via Livewire
        Livewire::test(TicketView::class, ['ticket' => $ticket])
            ->call('assignTo', $this->user->id)
            ->assertHasNoErrors();
    }
}