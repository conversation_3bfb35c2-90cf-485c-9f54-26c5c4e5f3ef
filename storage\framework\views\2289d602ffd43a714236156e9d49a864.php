<!--[if BLOCK]><![endif]--><?php if($showAiConsentPrompt): ?>
<div class="mb-4 p-4 bg-blue-900/30 border border-blue-500/30 rounded-xl shadow-lg animate-fadeIn ai-consent-prompt">
    <div class="flex items-start gap-3">
        <div class="shrink-0">
            <div class="w-10 h-10 bg-blue-500/20 rounded-full flex items-center justify-center">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-blue-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z" />
                </svg>
            </div>
        </div>
        <div class="flex-1">
            <h3 class="text-base font-semibold text-white mb-1"><?php echo e(__('tickets.gemini_consent_title')); ?></h3>
            <p class="text-sm text-blue-100 mb-3">
                <?php echo e(__('tickets.gemini_consent_description')); ?>

            </p>
            <div class="flex flex-wrap gap-2">
                <?php if (isset($component)) { $__componentOriginal19987cd45c699eefead8b7a8ac222798 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal19987cd45c699eefead8b7a8ac222798 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.modern-button','data' => ['wire:click' => 'setGeminiConsent(false)','variant' => 'ghost','size' => 'sm']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('modern-button'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['wire:click' => 'setGeminiConsent(false)','variant' => 'ghost','size' => 'sm']); ?>
                    <?php echo e(__('tickets.gemini_consent_decline')); ?>

                 <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal19987cd45c699eefead8b7a8ac222798)): ?>
<?php $attributes = $__attributesOriginal19987cd45c699eefead8b7a8ac222798; ?>
<?php unset($__attributesOriginal19987cd45c699eefead8b7a8ac222798); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal19987cd45c699eefead8b7a8ac222798)): ?>
<?php $component = $__componentOriginal19987cd45c699eefead8b7a8ac222798; ?>
<?php unset($__componentOriginal19987cd45c699eefead8b7a8ac222798); ?>
<?php endif; ?>
                <?php if (isset($component)) { $__componentOriginal19987cd45c699eefead8b7a8ac222798 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal19987cd45c699eefead8b7a8ac222798 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.modern-button','data' => ['wire:click' => 'setGeminiConsent(true)','variant' => 'primary','size' => 'sm']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('modern-button'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['wire:click' => 'setGeminiConsent(true)','variant' => 'primary','size' => 'sm']); ?>
                    <?php echo e(__('tickets.gemini_consent_accept')); ?>

                 <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal19987cd45c699eefead8b7a8ac222798)): ?>
<?php $attributes = $__attributesOriginal19987cd45c699eefead8b7a8ac222798; ?>
<?php unset($__attributesOriginal19987cd45c699eefead8b7a8ac222798); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal19987cd45c699eefead8b7a8ac222798)): ?>
<?php $component = $__componentOriginal19987cd45c699eefead8b7a8ac222798; ?>
<?php unset($__componentOriginal19987cd45c699eefead8b7a8ac222798); ?>
<?php endif; ?>
            </div>
        </div>
    </div>
</div>
<?php endif; ?><!--[if ENDBLOCK]><![endif]--><?php /**PATH C:\Users\<USER>\Documents\GitHub\minewache-website\resources\views/livewire/ticket/partials/ai-consent.blade.php ENDPATH**/ ?>