<?php

namespace Tests\Unit\Models;

use App\Enums\Role;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;
use Illuminate\Support\Facades\Gate;
use App\Providers\AppServiceProvider; // To ensure gates are registered if not already by default in tests

class UserTest extends TestCase
{
    use RefreshDatabase;

    /** @test */
    public function it_has_fillable_attributes()
    {
        $fillable = [
            'id',
            'username',
            'global_name',
            'discriminator',
            'avatar',
            'verified',
            'banner',
            'permissions',
            'last_synced_at',
            'banner_color',
            'accent_color',
            'locale',
            'mfa_enabled',
            'premium_type',
            'public_flags',
        ];

        $user = new User();
        $this->assertEquals($fillable, $user->getFillable());
    }

    /** @test */
    public function it_casts_attributes_correctly()
    {
        $user = new User();
        $casts = $user->getCasts();

        $this->assertArrayHasKey('id', $casts);
        $this->assertEquals('string', $casts['id']);

        $this->assertArrayHasKey('verified', $casts);
        $this->assertEquals('boolean', $casts['verified']);

        $this->assertArrayHasKey('mfa_enabled', $casts);
        $this->assertEquals('boolean', $casts['mfa_enabled']);

        $this->assertArrayHasKey('last_synced_at', $casts);
        $this->assertEquals('datetime', $casts['last_synced_at']);
    }

    /** @test */
    public function it_can_check_if_user_has_role()
    {
        $user = User::factory()->create([
            'permissions' => Role::BUILDER->value
        ]);

        $this->assertTrue($user->hasRole(Role::BUILDER));
        $this->assertFalse($user->hasRole(Role::DESIGNER));
    }

    /** @test */
    public function admin_has_all_roles()
    {
        $user = User::factory()->create([
            'permissions' => Role::MINEWACHE_TEAM->value
        ]);

        // Admin should have all roles
        $this->assertTrue($user->hasRole(Role::BUILDER));
        $this->assertTrue($user->hasRole(Role::DESIGNER));
        $this->assertTrue($user->hasRole(Role::SCHAUSPIELER));
        $this->assertTrue($user->hasRole(Role::DEVELOPER));
    }

    /** @test */
    public function it_can_check_if_user_has_any_role()
    {
        $user = User::factory()->create([
            'permissions' => Role::BUILDER->value
        ]);

        $this->assertTrue($user->hasAnyRole([Role::BUILDER, Role::DESIGNER]));
        $this->assertFalse($user->hasAnyRole([Role::DESIGNER, Role::DEVELOPER]));
    }

    /** @test */
    public function it_can_check_if_user_has_all_roles()
    {
        $user = User::factory()->create([
            'permissions' => Role::BUILDER->value | Role::DESIGNER->value
        ]);

        $this->assertTrue($user->hasAllRoles([Role::BUILDER, Role::DESIGNER]));
        $this->assertFalse($user->hasAllRoles([Role::BUILDER, Role::DEVELOPER]));
    }

    /** @test */
    public function it_can_have_multiple_roles()
    {
        $user = User::factory()->create([
            'permissions' => Role::BUILDER->value | Role::DESIGNER->value | Role::DEVELOPER->value
        ]);

        $this->assertTrue($user->hasRole(Role::BUILDER));
        $this->assertTrue($user->hasRole(Role::DESIGNER));
        $this->assertTrue($user->hasRole(Role::DEVELOPER));
        $this->assertFalse($user->hasRole(Role::SCHAUSPIELER));
    }

    /** @test */
    public function test_user_with_full_bitmask_is_admin()
    {
        $user = User::factory()->create([
            'permissions' => (string)Role::MINEWACHE_TEAM->value // "**********"
        ]);

        // Assert that the user has the MINEWACHE_TEAM role
        $this->assertTrue($user->hasRole(Role::MINEWACHE_TEAM), 'User should have MINEWACHE_TEAM role by string comparison.');

        // Assert that Gate allows 'MINEWACHE_TEAM'
        // Note: This requires Gate setup. If AuthServiceProvider wires this up, it should work.
        // If not, this specific assertion might need adjustment based on test environment capabilities.
        // For now, we assume Gate is resolvable and uses hasRole internally or a similar logic.
        // \Illuminate\Support\Facades\Gate::define('MINEWACHE_TEAM', fn(User $user) => $user->hasRole(Role::MINEWACHE_TEAM));
        // This line above is typically in AuthServiceProvider, not in the test itself.
        // $this->assertTrue(app('Illuminate\Contracts\Auth\Access\Gate')->forUser($user)->allows('MINEWACHE_TEAM'), 'Gate should allow MINEWACHE_TEAM for this user.');

        // Assert that a MINEWACHE_TEAM user also has a smaller role (e.g., BUILDER)
        $this->assertTrue($user->hasRole(Role::BUILDER), 'MINEWACHE_TEAM user should also have BUILDER role.');
    }

    public function test_permission_gate_logic()
    {
        // Ensure gates from AppServiceProvider are registered for this test
        // (This might sometimes be needed if tests don't fully boot the app or specific providers)
        // If gates are typically available in your test environment, this line might not be strictly necessary.
        // (new AppServiceProvider(app()))->boot(); 

        // 1. Test Admin Check via 'permission' Gate
        $adminUser = new User();
        $adminUser->permissions = (string)Role::MINEWACHE_TEAM->value;
        $this->assertTrue(Gate::forUser($adminUser)->allows('permission', Role::MINEWACHE_TEAM->value), "Admin user should pass 'permission' gate for MINEWACHE_TEAM value.");

        // 2. Test Non-Admin Specific Bitmask (User has permission)
        $builderUser = new User();
        $builderUser->permissions = (string)Role::BUILDER->value;
        $this->assertTrue(Gate::forUser($builderUser)->allows('permission', Role::BUILDER->value), "Builder user should pass 'permission' gate for BUILDER value.");

        // 3. Test Non-Admin Specific Bitmask (User does NOT have permission)
        $cutterUser = new User();
        $cutterUser->permissions = (string)Role::CUTTER->value;
        $this->assertFalse(Gate::forUser($cutterUser)->allows('permission', Role::BUILDER->value), "Cutter user should NOT pass 'permission' gate for BUILDER value.");

        // 4. Test Admin with Specific Non-Admin Bitmask
        $this->assertTrue(Gate::forUser($adminUser)->allows('permission', Role::BUILDER->value), "Admin user should pass 'permission' gate for BUILDER value.");

        // 5. Test user with multiple permissions (non-admin)
        $multiRoleUser = new User();
        $multiRoleUser->permissions = (string)(Role::BUILDER->value | Role::CUTTER->value); // e.g., 2 | 128 = 130
        $this->assertTrue(Gate::forUser($multiRoleUser)->allows('permission', Role::BUILDER->value), "Multi-role user should pass for BUILDER bitmask.");
        $this->assertTrue(Gate::forUser($multiRoleUser)->allows('permission', Role::CUTTER->value), "Multi-role user should pass for CUTTER bitmask.");
        $this->assertFalse(Gate::forUser($multiRoleUser)->allows('permission', Role::DEVELOPER->value), "Multi-role user should NOT pass for DEVELOPER bitmask.");
        
        // 6. Test with a zero permission user
        $noPermissionUser = new User();
        $noPermissionUser->permissions = "0";
        $this->assertFalse(Gate::forUser($noPermissionUser)->allows('permission', Role::BUILDER->value), "User with 0 permissions should NOT pass for BUILDER bitmask.");
        $this->assertFalse(Gate::forUser($noPermissionUser)->allows('permission', Role::MINEWACHE_TEAM->value), "User with 0 permissions should NOT pass 'permission' gate for MINEWACHE_TEAM value.");

    }
}
