<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Cookie;

class AuthConsentController extends Controller
{
    /**
     * Show the pre-login consent page.
     */
    public function show()
    {
        // If user has already given consent, redirect to login
        if (Cookie::has('gdpr_consent')) {
            return redirect()->route('login');
        }

        return view('auth.pre-login-consent');
    }

    /**
     * Store the user's consent and redirect to Discord OAuth.
     */
    public function store(Request $request)
    {
        try {
            $validated = $request->validate([
                'consent' => 'required|accepted',
            ], [
                'consent.required' => 'Du musst der Datenverarbeitung zustimmen, um fortzufahren.',
                'consent.accepted' => 'Du musst der Datenverarbeitung zustimmen, um fortzufahren.',
            ]);

            // Set a secure cookie to remember the consent with enhanced security
            $minutes = 60 * 24 * 30; // 30 days
            $cookie = Cookie::make('gdpr_consent', 'true', $minutes);
            $cookie->withSecure(config('app.env') !== 'local'); // Secure in production
            $cookie->withHttpOnly(); // Not accessible via JavaScript
            $cookie->withSameSite('lax'); // Restrict cross-site requests
            Cookie::queue($cookie);

            // Only log in local environment
            if (app()->environment('local')) {
                \Illuminate\Support\Facades\Log::debug('Setting GDPR consent cookie for ' . $minutes . ' minutes');
            }

            // Check if this is an AJAX request or expects JSON
            if ($request->ajax() || $request->expectsJson()) {
                return response()->json(['success' => true]);
            }

            // Log for debugging
            if (app()->environment('local')) {
                \Illuminate\Support\Facades\Log::debug('Consent given, redirecting to: ' . (session('url.intended') ?: route('login')));
            }

            // Get the intended URL from the session
            $intendedUrl = session('url.intended');

            // Only log in debug mode
            if (config('app.debug')) {
                \Illuminate\Support\Facades\Log::debug('Consent given, redirecting to: ' . ($intendedUrl ?: route('login')));
            }

            // Clear the intended URL to prevent redirect loops
            session()->forget('url.intended');

            // Redirect to the intended URL or login
            if ($intendedUrl) {
                return redirect($intendedUrl);
            } else {
                return redirect()->route('login');
            }
        } catch (\Illuminate\Validation\ValidationException $e) {
            if ($request->ajax() || $request->expectsJson()) {
                return response()->json([
                    'success' => false,
                    'errors' => $e->errors()
                ], 422);
            }

            throw $e;
        }
    }

    /**
     * Clear the GDPR consent cookie (for testing purposes).
     */
    public function clearConsent(Request $request)
    {
        $cookie = Cookie::forget('gdpr_consent');
        $cookie->withSecure(config('app.env') !== 'local');
        $cookie->withHttpOnly();
        $cookie->withSameSite('lax');
        Cookie::queue($cookie);

        // Only log in local environment
        if (app()->environment('local')) {
            \Illuminate\Support\Facades\Log::debug('Clearing GDPR consent cookie');
        }

        // Also clear any session data related to consent
        $request->session()->forget('gdpr_consent');

        return redirect()->back()->with('status', 'GDPR consent cookie cleared successfully. Please try logging in again.');
    }
}
