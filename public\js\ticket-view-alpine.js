/**
 * Ticket View Alpine.js Component
 *
 * This file contains the Alpine.js component for the ticket view page.
 * It's loaded directly in the HTML to ensure it's available before Alpine initializes.
 */

// Make sure window.ticketView is defined globally 
(function(global) {
    // Debug: Log when the file is loaded
    console.log('ticket-view-alpine.js loaded');
    
    // Define the ticketView function in the global scope
    global.ticketView = function() {
    return {
        init() {
            console.log('TicketView Alpine component initialized');
            this.scrollToBottom();
            this.setupNotifications();
        },

        scrollToBottom() {
            console.log('Scrolling to bottom');
            const messagesContainer = document.getElementById('messages-container');
            if (!messagesContainer) {
                console.error('Messages container not found');
                return;
            }

            // Force a reflow to ensure the scrollHeight is accurate
            void messagesContainer.offsetHeight;
            messagesContainer.scrollTop = messagesContainer.scrollHeight;
        },

        setupNotifications() {
            console.log('Setting up notifications');
            // Initialize notification sound
            this.notificationSound = document.getElementById('notification-sound');
            if (!this.notificationSound) {
                this.notificationSound = new Audio('/audio/notification.mp3');
            }
        },

        playNotificationSound() {
            console.log('Playing notification sound');
            const soundToggle = document.getElementById('sound-toggle');
            const soundEnabled = soundToggle ? soundToggle.checked : true;

            if (soundEnabled && this.notificationSound) {
                try {
                    this.notificationSound.currentTime = 0;
                    this.notificationSound.play().catch(error => {
                        console.error('Error playing notification sound:', error);
                    });
                } catch (error) {
                    console.error('Error playing sound:', error);
                }
            }
        },

        handleMessageAdded(message) {
            console.log('Message added:', message);

            // Play notification sound
            this.playNotificationSound();

            // Get the messages container
            const messagesContainer = document.getElementById('messages-container');
            if (!messagesContainer) return;

            // Check if we're already at the bottom
            const isAtBottom = messagesContainer.scrollTop >= (messagesContainer.scrollHeight - messagesContainer.clientHeight - 100);

            // Wait for DOM to update with the new message
            setTimeout(() => {
                if (isAtBottom) {
                    // If we were at the bottom, scroll to bottom
                    this.scrollToBottom();
                } else {
                    // Show new message indicator
                    const indicator = document.getElementById('new-message-indicator');
                    if (indicator) {
                        indicator.classList.remove('hidden');
                    }
                }
            }, 100);
        },

        handleAttachmentsReady(messageId, attachments) {
            console.log('Attachments ready for message:', messageId, attachments);
            // This is a simplified version, the full implementation would rebuild UI elements
            setTimeout(() => {
                // Re-initialize video players or other media elements if needed
                if (typeof initCustomVideoPlayers === 'function') {
                    initCustomVideoPlayers();
                }
            }, 100);
        },

        handleTypingUpdate(users) {
            console.log('Typing update:', users);
            const typingIndicator = document.getElementById('typing-indicator');
            if (!typingIndicator) return;

            if (!users || Object.keys(users).length === 0) {
                typingIndicator.classList.add('hidden');
                return;
            }

            // Update UI to show who's typing
            typingIndicator.classList.remove('hidden');
        }
    };
};

console.log('Ticket view Alpine component registered');

})(typeof window !== 'undefined' ? window : this); // Close the IIFE
