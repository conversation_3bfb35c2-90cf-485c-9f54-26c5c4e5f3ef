<?php if (isset($component)) { $__componentOriginal9ac128a9029c0e4701924bd2d73d7f54 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54 = $attributes; } ?>
<?php $component = App\View\Components\AppLayout::resolve([] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('app-layout'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\App\View\Components\AppLayout::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
     <?php $__env->slot('title', null, []); ?> Meine Bewerbungen - MineWache <?php $__env->endSlot(); ?>

    <div class="container mx-auto px-4 py-8">
        <div class="max-w-4xl mx-auto">
            <div class="flex justify-between items-center mb-6">
                <h1 class="text-2xl font-bold">Meine Bewerbungen</h1>
                <?php if (isset($component)) { $__componentOriginal19987cd45c699eefead8b7a8ac222798 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal19987cd45c699eefead8b7a8ac222798 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.modern-button','data' => ['variant' => 'primary','href' => ''.e(route('bewerben')).'']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('modern-button'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['variant' => 'primary','href' => ''.e(route('bewerben')).'']); ?><svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                    </svg>
                    Neue Bewerbung <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal19987cd45c699eefead8b7a8ac222798)): ?>
<?php $attributes = $__attributesOriginal19987cd45c699eefead8b7a8ac222798; ?>
<?php unset($__attributesOriginal19987cd45c699eefead8b7a8ac222798); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal19987cd45c699eefead8b7a8ac222798)): ?>
<?php $component = $__componentOriginal19987cd45c699eefead8b7a8ac222798; ?>
<?php unset($__componentOriginal19987cd45c699eefead8b7a8ac222798); ?>
<?php endif; ?>
            </div>

            <?php if(count($applications) > 0): ?>
            <div class="card bg-base-100 shadow-xl mb-6 overflow-hidden">
                <div class="card-body p-4">
                    <h3 class="card-title text-lg mb-2">Bewerbungsübersicht</h3>
                    <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
                        <div class="stat bg-primary/10 rounded-box p-3 border border-primary/20">
                            <div class="stat-figure text-primary">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-8 w-8" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                                </svg>
                            </div>
                            <div class="stat-title text-xs md:text-sm">Gesamt</div>
                            <div class="stat-value text-primary text-2xl md:text-3xl"><?php echo e(count($applications)); ?></div>
                            <div class="stat-desc text-xs">Bewerbungen</div>
                        </div>

                        <div class="stat bg-warning/10 rounded-box p-3 border border-warning/20">
                            <div class="stat-figure text-warning">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-8 w-8" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                                </svg>
                            </div>
                            <div class="stat-title text-xs md:text-sm">Ausstehend</div>
                            <div class="stat-value text-warning text-2xl md:text-3xl"><?php echo e($applications->where('status', 'pending')->count()); ?></div>
                            <div class="stat-desc text-xs">In Bearbeitung</div>
                        </div>

                        <div class="stat bg-success/10 rounded-box p-3 border border-success/20">
                            <div class="stat-figure text-success">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-8 w-8" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                                </svg>
                            </div>
                            <div class="stat-title text-xs md:text-sm">Angenommen</div>
                            <div class="stat-value text-success text-2xl md:text-3xl"><?php echo e($applications->where('status', 'approved')->count()); ?></div>
                            <div class="stat-desc text-xs">Erfolgreiche</div>
                        </div>

                        <div class="stat bg-error/10 rounded-box p-3 border border-error/20">
                            <div class="stat-figure text-error">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-8 w-8" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 14l2-2m0 0l2-2m-2 2l-2-2m2 2l2 2m7-2a9 9 0 11-18 0 9 9 0 0118 0z" />
                                </svg>
                            </div>
                            <div class="stat-title text-xs md:text-sm">Abgelehnt</div>
                            <div class="stat-value text-error text-2xl md:text-3xl"><?php echo e($applications->where('status', 'rejected')->count()); ?></div>
                            <div class="stat-desc text-xs">Nicht angenommen</div>
                        </div>
                    </div>

                    <div class="mt-4 text-sm text-base-content/70 flex items-center gap-2">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-info" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                        </svg>
                        <span><?php echo e($applications->where('editable', true)->count()); ?> Bewerbung(en) können aktuell bearbeitet werden.</span>
                    </div>
                </div>
            </div>
            <?php endif; ?>

            <?php if(session('success')): ?>
                <div class="alert alert-success mb-6">
                    <div>
                        <svg xmlns="http://www.w3.org/2000/svg" class="stroke-current flex-shrink-0 h-6 w-6" fill="none" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" /></svg>
                        <span><?php echo e(session('success')); ?></span>
                    </div>
                </div>
            <?php endif; ?>

            <?php if(session('error')): ?>
                <div class="alert alert-error mb-6">
                    <div>
                        <svg xmlns="http://www.w3.org/2000/svg" class="stroke-current flex-shrink-0 h-6 w-6" fill="none" viewBox="0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 14l2-2m0 0l2-2m-2 2l-2-2m2 2l2 2m7-2a9 9 0 11-18 0 9 9 0 0118 0z" /></svg>
                        <span><?php echo e(session('error')); ?></span>
                    </div>
                </div>
            <?php endif; ?>

            <?php if(count($applications) > 0): ?>
                <div class="card bg-base-100 shadow-xl mb-6 overflow-hidden">
                    <div class="card-body">
                        <h3 class="card-title text-lg">Hinweise zur Bearbeitung</h3>
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mt-2">
                            <div class="bg-info/10 p-4 rounded-box border border-info/30">
                                <div class="flex items-start gap-3">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-info flex-shrink-0 mt-0.5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                                    </svg>
                                    <div>
                                        <h4 class="font-semibold mb-1">Bearbeitung von Bewerbungen</h4>
                                        <p class="text-sm">Bewerbungen können nur bearbeitet werden, wenn ein Administrator die Bearbeitung freigibt. Du kannst alle Angaben bearbeiten, außer die Tätigkeitsbereiche.</p>
                                    </div>
                                </div>
                            </div>
                            <div class="bg-warning/10 p-4 rounded-box border border-warning/30">
                                <div class="flex items-start gap-3">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-warning flex-shrink-0 mt-0.5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
                                    </svg>
                                    <div>
                                        <h4 class="font-semibold mb-1">Neue Tätigkeitsbereiche</h4>
                                        <p class="text-sm">Tätigkeitsbereiche können nicht nachträglich geändert werden. Für weitere Bereiche <a href="<?php echo e(route('bewerben')); ?>" class="link link-primary">reiche bitte eine neue Bewerbung ein</a>.</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="card bg-base-100 shadow-xl overflow-hidden">
                    <div class="card-body p-0">
                        <div class="overflow-x-auto">
                            <table class="table w-full table-zebra">
                                <thead class="bg-base-200 text-base-content">
                                    <tr>
                                        <th class="hidden md:table-cell">ID</th>
                                        <th>Datum</th>
                                        <th class="hidden lg:table-cell">Bereiche</th>
                                        <th>Status</th>
                                        <th class="hidden md:table-cell">Bearbeitbar</th>
                                        <th class="hidden lg:table-cell">Letzte Aktualisierung</th>
                                        <th>Aktionen</th>
                                    </tr>
                                </thead>
                        <tbody>
                            <?php $__currentLoopData = $applications; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $application): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <tr class="hover:bg-base-300 transition-colors duration-200">
                                    <td class="hidden md:table-cell"><?php echo e($application->id); ?></td>
                                    <td>
                                        <div class="flex flex-col">
                                            <span><?php echo e($application->created_at->format('d.m.Y')); ?></span>
                                            <span class="text-xs opacity-70 md:hidden">ID: <?php echo e($application->id); ?></span>
                                        </div>
                                    </td>
                                    <td class="hidden lg:table-cell">
                                        <div class="flex flex-wrap gap-1">
                                            <?php $__currentLoopData = $application->professions; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $profession): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                <?php switch($profession):
                                                    case ('actor'): ?>
                                                        <span class="badge badge-sm badge-primary">Schauspieler</span>
                                                        <?php break; ?>
                                                    <?php case ('voice_actor'): ?>
                                                        <span class="badge badge-sm badge-primary">Synchronsprecher</span>
                                                        <?php break; ?>
                                                    <?php case ('builder'): ?>
                                                        <span class="badge badge-sm badge-secondary">Builder</span>
                                                        <?php break; ?>
                                                    <?php case ('designer'): ?>
                                                        <span class="badge badge-sm badge-accent">Designer</span>
                                                        <?php break; ?>
                                                    <?php case ('developer'): ?>
                                                        <span class="badge badge-sm badge-info">Entwickler</span>
                                                        <?php break; ?>
                                                    <?php case ('cutter'): ?>
                                                        <span class="badge badge-sm badge-info">Cutter</span>
                                                        <?php break; ?>
                                                    <?php case ('cameraman'): ?>
                                                        <span class="badge badge-sm badge-info">Kameramann</span>
                                                        <?php break; ?>
                                                    <?php case ('modeler'): ?>
                                                        <span class="badge badge-sm badge-accent">3D-Modellierer</span>
                                                        <?php break; ?>
                                                    <?php case ('music_producer'): ?>
                                                        <span class="badge badge-sm badge-primary">Musikproduzent</span>
                                                        <?php break; ?>
                                                    <?php default: ?>
                                                        <span class="badge badge-sm"><?php echo e($profession); ?></span>
                                                <?php endswitch; ?>
                                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                        </div>
                                    </td>
                                    <td>
                                        <?php switch($application->status):
                                            case ('pending'): ?>
                                                <span class="badge badge-warning">In Bearbeitung</span>
                                                <?php break; ?>
                                            <?php case ('approved'): ?>
                                                <span class="badge badge-success">Angenommen</span>
                                                <?php break; ?>
                                            <?php case ('rejected'): ?>
                                                <span class="badge badge-error">Abgelehnt</span>
                                                <?php break; ?>
                                            <?php default: ?>
                                                <span class="badge"><?php echo e($application->status); ?></span>
                                        <?php endswitch; ?>
                                    </td>
                                    <td class="hidden md:table-cell">
                                        <?php if($application->editable): ?>
                                            <span class="badge badge-success gap-1">
                                                <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 11V7a4 4 0 118 0m-4 8v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2z" />
                                                </svg>
                                                Ja
                                            </span>
                                        <?php else: ?>
                                            <span class="badge badge-neutral gap-1">
                                                <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
                                                </svg>
                                                Nein
                                            </span>
                                        <?php endif; ?>
                                    </td>
                                    <td class="hidden lg:table-cell"><?php echo e($application->updated_at->format('d.m.Y H:i')); ?></td>
                                    <td>
                                        <div class="flex flex-col sm:flex-row gap-2">
                                            <a href="<?php echo e(route('my.applications.show', $application->id)); ?>" class="btn btn-sm btn-info">
                                                <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 sm:mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                                                </svg>
                                                <span class="hidden sm:inline">Details</span>
                                            </a>
                                            <?php if($application->editable): ?>
                                                <a href="<?php echo e(route('my.applications.edit', $application->id)); ?>" class="btn btn-sm btn-primary">
                                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 sm:mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                                                    </svg>
                                                    <span class="hidden sm:inline">Bearbeiten</span>
                                                </a>
                                            <?php else: ?>
                                                <div class="tooltip" data-tip="Ein Administrator muss die Bearbeitung freischalten">
                                                    <button class="btn btn-sm btn-disabled">
                                                        <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 sm:mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
                                                        </svg>
                                                        <span class="hidden sm:inline">Gesperrt</span>
                                                    </button>
                                                </div>
                                            <?php endif; ?>

                                            <!-- Mobile: Zeige Bearbeitbarkeit und Update-Zeit -->
                                            <div class="flex flex-col md:hidden text-xs mt-1">
                                                <span class="opacity-70">Bearbeitbar:
                                                    <?php if($application->editable): ?>
                                                        <span class="text-success">Ja</span>
                                                    <?php else: ?>
                                                        <span class="text-error">Nein</span>
                                                    <?php endif; ?>
                                                </span>
                                                <span class="opacity-70">Update: <?php echo e($application->updated_at->format('d.m.Y')); ?></span>
                                            </div>
                                        </div>
                                    </td>
                                </tr>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            <?php else: ?>
                <div class="bg-base-200 p-8 rounded-box text-center">
                    <div class="flex flex-col items-center justify-center space-y-4">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-16 w-16 text-base-content/30" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                        </svg>
                        <h3 class="text-lg font-semibold">Du hast noch keine Bewerbungen eingereicht</h3>
                        <p class="text-base-content/70 max-w-md">Werde Teil des Minewache-Teams! Reiche deine Bewerbung ein und zeige uns deine Fähigkeiten.</p>
                        <div class="alert alert-info shadow-lg max-w-md mt-2">
                            <div>
                                <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" class="stroke-current flex-shrink-0 w-6 h-6"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path></svg>
                                <span>Hinweis: Bewerbungen können nur bearbeitet werden, wenn ein Administrator die Bearbeitung freigibt.</span>
                            </div>
                        </div>
                        <?php if (isset($component)) { $__componentOriginal19987cd45c699eefead8b7a8ac222798 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal19987cd45c699eefead8b7a8ac222798 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.modern-button','data' => ['variant' => 'primary','href' => ''.e(route('bewerben')).'']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('modern-button'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['variant' => 'primary','href' => ''.e(route('bewerben')).'']); ?><svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                            </svg>
                            Jetzt bewerben <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal19987cd45c699eefead8b7a8ac222798)): ?>
<?php $attributes = $__attributesOriginal19987cd45c699eefead8b7a8ac222798; ?>
<?php unset($__attributesOriginal19987cd45c699eefead8b7a8ac222798); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal19987cd45c699eefead8b7a8ac222798)): ?>
<?php $component = $__componentOriginal19987cd45c699eefead8b7a8ac222798; ?>
<?php unset($__componentOriginal19987cd45c699eefead8b7a8ac222798); ?>
<?php endif; ?>
                    </div>
                </div>
            <?php endif; ?>
        </div>
    </div>
 <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54)): ?>
<?php $attributes = $__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54; ?>
<?php unset($__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal9ac128a9029c0e4701924bd2d73d7f54)): ?>
<?php $component = $__componentOriginal9ac128a9029c0e4701924bd2d73d7f54; ?>
<?php unset($__componentOriginal9ac128a9029c0e4701924bd2d73d7f54); ?>
<?php endif; ?><?php /**PATH C:\Users\<USER>\Documents\GitHub\minewache-website\resources\views/applications/my-applications.blade.php ENDPATH**/ ?>