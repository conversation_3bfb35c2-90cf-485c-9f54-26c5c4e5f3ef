<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;
use Discord\Discord;
use Discord\WebSockets\Event as DiscordEvent;
use Discord\Parts\User\Activity;
use Discord\Parts\Interactions\Command\Command as SlashCommand;
use Discord\Parts\Interactions\Command\Option;
use Discord\Parts\Channel\Overwrite;
use Discord\Parts\Permissions\RolePermission;
use Discord\Parts\Channel\Channel;
use App\Events\Discord\UserRolesChanged;
use App\Services\TicketService;
use App\Services\DiscordMessagingService;
use App\Services\TicketUpdateService;
use App\Services\DiscordTicketChannelService;
use App\Services\TicketAssignmentService;
use App\Services\TicketCreationService;
use App\Services\AITicketService;
use App\Models\TicketMessage; // Added for creating system messages in reconstruct
use Discord\Builders\MessageBuilder;
use Discord\Parts\Interactions\Interaction;
use Discord\Builders\Components\ActionRow;
use Discord\Builders\Components\Button;
use App\Models\Ticket;
use Exception;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Config;
use Illuminate\Support\Facades\Gate;
use App\Models\User;
use Illuminate\Support\Facades\App;
use Illuminate\Support\Str;
use React\Http\HttpServer;
use React\Socket\SocketServer;
use Psr\Http\Message\ServerRequestInterface;
use React\Promise\Promise;


class RunMinewacheDiscordBot extends Command
{
    protected const ROLE_STATE_FILE_PATH = 'discord/role_state.json';
    protected const DISCORD_API_BASE_URL = 'https://discord.com/api/v10';
    protected const HTTP_SERVER_PORT = 3001;
    protected const LOCK_FILE_PATH = 'discord/bot_lock.pid';

    protected const PERM_VIEW_CHANNEL = 1 << 10;
    protected const PERM_SEND_MESSAGES = 1 << 11;
    protected const PERM_MANAGE_CHANNELS = 1 << 4;
    protected const PERM_MANAGE_ROLES = 1 << 28;
    protected const PERM_MANAGE_MESSAGES = 1 << 13;
    protected const PERM_READ_MESSAGE_HISTORY = 1 << 16;
    protected const PERM_ATTACH_FILES = 1 << 15;
    protected const PERM_EMBED_LINKS = 1 << 14;

    protected $signature = 'minewache:run-discord-bot';
    protected $description = 'Starts and runs the Minewache Discord Bot';
    protected $discord;
    protected $httpServer;
    protected $socketServer;

    public function handle()
    {
        $this->info('Attempting to start Minewache Discord Bot...');

        // Check if another instance is already running
        if (!$this->acquireLock()) {
            $this->error('Another instance of the Discord bot is already running. Exiting.');
            Log::error('Discord Bot: Another instance is already running. Exiting.');
            return Command::FAILURE;
        }

        // Register shutdown function to release lock on exit
        register_shutdown_function([$this, 'releaseLock']);

        $botToken = config('minewache_discord_bot.token');
        $intents = config('minewache_discord_bot.intents');
        $activityName = config('minewache_discord_bot.activity.name', 'Minewache');
        $activityType = config('minewache_discord_bot.activity.type', 'PLAYING');

        if (empty($botToken)) {
            $this->error('Discord Bot Token is not configured. Please check your .env file and config/minewache_discord_bot.php.');
            Log::error('Discord Bot: Token not configured.');
            $this->releaseLock();
            return Command::FAILURE;
        }
        if (empty($intents)) {
            $this->warn('Discord Bot: No Gateway Intents configured. The bot might not receive expected events.');
            Log::warning('Discord Bot: No Gateway Intents configured.');
        }
        try {
            // Start the HTTP server for API requests
            $this->startHttpServer();

            $this->info('Initializing Discord client...');
            $this->discord = new Discord(['token' => $botToken, 'loadAllMembers' => true, 'intents' => $intents, 'logger' => Log::getLogger()]);
            $this->discord->on('ready', function (Discord $discord) use ($activityName, $activityType) {
                $this->info('Discord Bot Connected!');
                $this->line('Logged in as: ' . $discord->user->username . '#' . $discord->user->discriminator . ' (' . $discord->user->id . ')');
                Log::info('Discord Bot: Connected as ' . $discord->user->tag);
                $activity = new Activity($discord, ['name' => $activityName, 'type' => $this->getActivityTypeInt($activityType)]);
                $discord->updatePresence($activity);
                $this->info("Bot presence updated: {$activityType} {$activityName}");
                $this->info('Checking for guild role definition changes...');
                $loadedRoleState = $this->loadPersistedRoleState();
                $this->compareAndLogRoleChanges($discord, $loadedRoleState);
                $this->persistCurrentRoleState($discord);
                $this->registerGlobalSlashCommands($discord);
                $this->registerEventHandlers();

                // Log that the HTTP server is running
                $this->info("HTTP API Server running on localhost:" . self::HTTP_SERVER_PORT);
                Log::info("Discord Bot: HTTP API Server started on port " . self::HTTP_SERVER_PORT);
            });
            $this->discord->on('error', function ($error) { $this->error('Discord Bot Error: ' . $error->getMessage()); Log::error('Discord Bot Error: ', ['exception' => $error]); });
            $this->discord->on('close', function ($code, $reason) { $this->warn("Discord Bot disconnected. Code: {$code}, Reason: {$reason}"); Log::warning("Discord Bot: Disconnected. Code: {$code}, Reason: {$reason}"); });
            $this->info('Starting Discord client loop...');
            $this->discord->run();
            $this->info('Discord Bot has stopped.');
            return Command::SUCCESS;
        } catch (Exception $e) {
            $this->error('Failed to start Minewache Discord Bot: ' . $e->getMessage());
            Log::error('Discord Bot: Failed to start.', ['exception' => $e]);
            return Command::FAILURE;
        }
    }

    protected function registerEventHandlers()
    {
        $this->info('Registering event handlers...');
        $this->discord->on(DiscordEvent::MESSAGE_CREATE, function (\Discord\Parts\Channel\Message $message, Discord $discord) {
            if ($message->author->bot || $message->author->id == $discord->user->id) return;
            $configuredGuildId = config('minewache_discord_bot.guild_id');
            if (!$message->guild || $message->guild_id != $configuredGuildId) return;
            $ticketDbId = $this->getTicketIdFromChannelObject($message->channel);
            if ($ticketDbId) {
                Log::debug("Discord Bot: Message received in potential ticket channel #{$message->channel->name} (Ticket DB ID: {$ticketDbId}) from {$message->author->tag}: {$message->content_safe}");
                $ticketService = app(TicketService::class);
                $attachmentsData = [];
                if ($message->attachments->count() > 0) {
                    foreach ($message->attachments as $attachment) {
                        $attachmentsData[] = ['id' => $attachment->id, 'filename' => $attachment->filename, 'url' => $attachment->url, 'size' => $attachment->size, 'content_type' => $attachment->content_type];
                    }
                }
                $storedMessage = $ticketService->storeDiscordMessage((int)$ticketDbId, $message->author->id, $message->content, $message->id, $attachmentsData);
                if ($storedMessage) {
                    Log::info("Discord Bot: Message from {$message->author->tag} successfully relayed to Laravel for ticket #{$ticketDbId}.");
                    $ticket = $ticketService->getTicketById((int)$ticketDbId);
                    if ($ticket && !$message->author->bot && $ticket->gemini_consent) {
                        Log::info("Discord Bot: Conditions met for AI response for ticket #{$ticket->id}.");
                        try {
                            $aiTicketService = app(AITicketService::class);
                            $aiUserId = Config::get('minewache_discord_bot.ai.system_user_id');
                            if (!$aiUserId) { Log::warning("Discord Bot: AI System User ID not configured. Cannot generate AI response."); }
                            else {
                                $aiUser = User::find($aiUserId);
                                if (!$aiUser) { Log::warning("Discord Bot: AI System User (ID: {$aiUserId}) not found. Cannot generate AI response."); }
                                else {
                                    $aiResponseMessage = $aiTicketService->generateResponse($ticket, $aiUser);
                                    if ($aiResponseMessage && !empty(trim($aiResponseMessage->content))) {
                                        $messagingService = app(DiscordMessagingService::class);
                                        $embedData = ['description' => $aiResponseMessage->content, 'color' => 0x2ECC71, 'footer' => ['text' => "🤖 " . __('discord.ai.response_footer_disclaimer')]];
                                        $aiEmbed = $messagingService->createEmbed($embedData);
                                        if($aiEmbed) { $messagingService->sendMessageToChannel($ticket->discord_channel_id, '', [$aiEmbed]); }
                                        else { $plainTextResponse = "🤖 " . __('discord.ai.response_prefix') . "\n\n" . $aiResponseMessage->content . "\n\n" . "🤖 " . __('discord.ai.response_footer_disclaimer'); $messagingService->sendMessageToChannel($ticket->discord_channel_id, $plainTextResponse); }
                                        Log::info("Discord Bot: AI-generated response sent to ticket #{$ticket->id} in channel {$ticket->discord_channel_id}.");
                                    } else { Log::info("Discord Bot: AITicketService generated no response for ticket #{$ticket->id}."); }
                                }
                            }
                        } catch (Exception $e) { Log::error("Discord Bot: Error during AI response generation for ticket #{$ticket->id}.", ['exception_message' => $e->getMessage(), 'exception_trace' => Str::limit($e->getTraceAsString(), 1000)]); }
                    } elseif ($ticket && !$ticket->gemini_consent) { Log::info("Discord Bot: Ticket #{$ticket->id} does not have AI consent. Skipping AI response."); }
                    elseif ($message->author->bot) { Log::debug("Discord Bot: Message from bot {$message->author->tag}, skipping AI response for ticket #{$ticketDbId}."); }
                } else { Log::warning("Discord Bot: Failed to relay message from {$message->author->tag} to Laravel for ticket #{$ticketDbId}."); }
            } else { Log::debug("Discord Bot: Standard message in #{$message->channel->name} from {$message->author->tag}: {$message->content_safe}"); }
        });
        $this->discord->on(DiscordEvent::GUILD_MEMBER_UPDATE, function (\Discord\Parts\User\Member $newMember, Discord $discord, ?\Discord\Parts\User\Member $oldMember) {
            $configuredGuildId = config('minewache_discord_bot.guild_id');
            if ($newMember->guild_id != $configuredGuildId) return;
            if (!$oldMember) { Log::info("Discord Bot: GUILD_MEMBER_UPDATE for {$newMember->user->tag}, old member data not available."); return; }
            $oldRoleIds = $oldMember->roles->map(fn ($role) => $role->id)->toArray(); sort($oldRoleIds);
            $newRoleIds = $newMember->roles->map(fn ($role) => $role->id)->toArray(); sort($newRoleIds);
            if ($oldRoleIds == $newRoleIds) { Log::debug("Discord Bot: GUILD_MEMBER_UPDATE for {$newMember->user->tag}, no role changes."); return; }
            Log::info("Discord Bot: Role change for {$newMember->user->tag}.");
            $currentRolesDetails = $newMember->roles->map(fn ($role) => ['id' => $role->id, 'name' => $role->name])->toArray();
            try { event(new UserRolesChanged($newMember->id, $currentRolesDetails, $newMember->guild_id)); }
            catch (\Exception $e) { Log::error("Error dispatching UserRolesChanged event", ['exception' => $e, 'userId' => $newMember->id]); }
        });
        $this->discord->on(DiscordEvent::INTERACTION_CREATE, function (Interaction $interaction, Discord $discord) {
            Log::debug("Discord Bot: Interaction received from {$interaction->user->tag}. Type: {$interaction->type}, ID: {$interaction->id}");
            if ($interaction->type == Interaction::TYPE_APPLICATION_COMMAND) {
                $commandName = $interaction->data->name;
                Log::info("Discord Bot: Received slash command: {$commandName} by {$interaction->user->tag}");
                switch ($commandName) {
                    case 'ping': $interaction->respondWithMessage(MessageBuilder::new()->setContent('Pong!')); break;
                    case 'support':
                        $subCommandData = $interaction->data->options->first();
                        if ($subCommandData && $subCommandData->name === 'create') $this->handleSupportCreateCommand($interaction, $discord);
                        elseif ($subCommandData && $subCommandData->name === 'close') $this->handleSupportCloseCommand($interaction, $discord);
                        elseif ($subCommandData && $subCommandData->name === 'list') $this->handleSupportListCommand($interaction, $discord); // Changed this line
                        elseif ($subCommandData && $subCommandData->name === 'assign') $this->handleTicketAssignCommand($interaction, $discord); // Target for implementation
                        elseif ($subCommandData && $subCommandData->name === 'reconstruct') $this->handleTicketReconstructCommand($interaction, $discord); // Added reconstruct
                        else { $interaction->respondWithMessage(MessageBuilder::new()->setContent('Invalid /support subcommand.'), true); Log::warning("Discord Bot: Invalid /support subcommand used by {$interaction->user->tag}."); }
                        break;
                    case 'setup-tickets': $this->handleSetupTicketsCommand($interaction, $discord); break;
                    default: $interaction->respondWithMessage(MessageBuilder::new()->setContent('Sorry, I don\'t recognize that command.'), true); Log::warning("Discord Bot: Unhandled slash command: {$commandName}"); break;
                }
            } elseif ($interaction->type == Interaction::TYPE_MESSAGE_COMPONENT) {
                $customId = $interaction->data->custom_id;
                Log::info("Discord Bot: Component interaction: {$customId} from {$interaction->user->tag}");
                if (str_starts_with($customId, 'ticket_close_')) {
                    $ticketDbId = str_replace('ticket_close_', '', $customId);
                    $locale = $this->getInteractionLocale($interaction);
                    App::setLocale($locale); // Set locale early

                    Log::info("Discord Bot: 'ticket_close_' button pressed by {$interaction->user->tag} for ticket DB ID {$ticketDbId}. Locale: {$locale}");

                    // Acknowledge the interaction ephemerally
                    $interaction->acknowledgeWithResponse(true)->then(function () use ($interaction, $discord, $ticketDbId, $locale) {
                        App::setLocale($locale); // Ensure locale in async context

                        $discordUserId = $interaction->user->id;
                        $channelId = $interaction->channel_id; // Current channel where button was pressed
                        $reason = __('discord.ticket.closed_via_button');

                        try {
                            $ticketUpdateService = app(TicketUpdateService::class);
                            $result = $ticketUpdateService->closeTicketByDiscordUser(
                                $discordUserId,
                                $channelId, // Pass current channel_id for context/validation if needed by service
                                $locale,
                                $reason,
                                $ticketDbId // Explicitly pass the ticket ID from the button
                            );

                            $logContext = [
                                'user_id' => $discordUserId,
                                'channel_id' => $channelId,
                                'ticket_db_id' => $ticketDbId,
                                'reason' => $reason,
                                'service_message_key' => $result['message_key'] ?? 'N/A',
                                'resolved_ticket_id' => $result['ticket']?->id,
                            ];

                            if ($result['success']) {
                                Log::info("Discord Bot: Ticket successfully closed via button by {$discordUserId}.", $logContext);
                                // Send a public confirmation in the channel.
                                // The original message with the button might be cleared/modified by Discord or the service.
                                $interaction->sendFollowUpMessage(
                                    MessageBuilder::new()->setContent(
                                        __($result['message_key'] ?? 'discord.ticket.closed_successfully_button', ['ticketId' => $result['ticket']?->id])
                                    )
                                    // false // Public
                                );
                            } else {
                                $logContext['error_code'] = $result['error_code'] ?? 'N/A';
                                Log::warning("Discord Bot: Failed to close ticket via button for {$discordUserId}.", $logContext);
                                $interaction->sendFollowUpMessage(
                                    MessageBuilder::new()->setContent(
                                        __($result['message_key'] ?? 'discord.ticket.close_failed_generic_button', ['errorCode' => $result['error_code'] ?? 'unknown'])
                                    ),
                                    true // Ephemeral for errors
                                );
                            }

                        } catch (Exception $e) {
                            Log::error("Discord Bot: Exception during 'ticket_close_' button interaction.", [
                                'user_id' => $discordUserId,
                                'channel_id' => $channelId,
                                'ticket_db_id' => $ticketDbId,
                                'exception_message' => $e->getMessage(),
                                'exception_trace' => Str::limit($e->getTraceAsString(), 1000)
                            ]);
                            $interaction->sendFollowUpMessage(
                                MessageBuilder::new()->setContent(__('discord.ticket.close_failed_exception_button')),
                                true
                            );
                        }
                    });

                } elseif (str_starts_with($customId, 'ticket_progress_')) {
                    $ticketDbId = str_replace('ticket_progress_', '', $customId);
                    $locale = $this->getInteractionLocale($interaction);
                    App::setLocale($locale); // Set locale early

                    Log::info("Discord Bot: 'ticket_progress_' button pressed by {$interaction->user->tag} for ticket DB ID {$ticketDbId}. Locale: {$locale}");

                    $interaction->acknowledgeWithResponse(true)->then(function () use ($interaction, $discord, $ticketDbId, $locale) {
                        App::setLocale($locale); // Ensure locale in async context

                        $updaterDiscordId = $interaction->user->id;
                        $channelId = $interaction->channel_id;
                        $newStatus = 'in_progress'; // Hardcoded for this button

                        try {
                            $ticketUpdateService = app(TicketUpdateService::class);
                            // Assumed method signature: updateTicketStatusFromDiscord(string $updaterDiscordId, string $ticketDbId, string $newStatus, string $locale, ?string $channelId = null): array
                            $result = $ticketUpdateService->updateTicketStatusFromDiscord(
                                $updaterDiscordId,
                                $ticketDbId,
                                $newStatus,
                                $locale,
                                $channelId
                            );

                            $logContext = [
                                'updater_id' => $updaterDiscordId,
                                'channel_id' => $channelId,
                                'ticket_db_id' => $ticketDbId,
                                'new_status' => $newStatus,
                                'service_message_key' => $result['message_key'] ?? 'N/A',
                                'resolved_ticket_id' => $result['ticket']?->id,
                            ];

                            if ($result['success']) {
                                Log::info("Discord Bot: Ticket status successfully updated to '{$newStatus}' via button by {$updaterDiscordId}.", $logContext);
                                $interaction->sendFollowUpMessage(
                                    MessageBuilder::new()->setContent(
                                        __($result['message_key'] ?? 'discord.ticket.status_updated_successfully_button', [
                                            'ticketId' => $result['ticket']?->id,
                                            'status' => __("discord.ticket_statuses.{$newStatus}", [], $locale) // Attempt to translate status
                                        ])
                                    )
                                    // false // Public message
                                );
                            } else {
                                $logContext['error_code'] = $result['error_code'] ?? 'N/A';
                                Log::warning("Discord Bot: Failed to update ticket status to '{$newStatus}' via button for {$updaterDiscordId}.", $logContext);
                                $interaction->sendFollowUpMessage(
                                    MessageBuilder::new()->setContent(
                                        __($result['message_key'] ?? 'discord.ticket.status_update_failed_generic_button', [
                                            'ticketId' => $ticketDbId, // Use original ticketDbId for error message if ticket object isn't returned
                                            'errorCode' => $result['error_code'] ?? 'unknown'
                                        ])
                                    ),
                                    true // Ephemeral for errors
                                );
                            }

                        } catch (Exception $e) {
                            Log::error("Discord Bot: Exception during 'ticket_progress_' button interaction.", [
                                'updater_id' => $updaterDiscordId,
                                'channel_id' => $channelId,
                                'ticket_db_id' => $ticketDbId,
                                'new_status' => $newStatus,
                                'exception_message' => $e->getMessage(),
                                'exception_trace' => Str::limit($e->getTraceAsString(), 1000)
                            ]);
                            $interaction->sendFollowUpMessage(
                                MessageBuilder::new()->setContent(__('discord.ticket.status_update_failed_exception_button')),
                                true
                            );
                        }
                    });

                } else { $interaction->respondWithMessage(MessageBuilder::new()->setContent("Unknown button: {$customId}"), true); }
            } elseif ($interaction->type == Interaction::TYPE_MODAL_SUBMIT) {
                Log::info("Discord Bot: Modal submission: {$interaction->data->custom_id}");
                $interaction->respondWithMessage(MessageBuilder::new()->setContent("Modal submitted - handler not implemented."), true);
            }
        });
        $this->info('Event handlers registered.');
    }

    protected function getActivityTypeInt(string $activityTypeString): int
    {
        return match (strtoupper($activityTypeString)) {
            'STREAMING' => Activity::TYPE_STREAMING, 'LISTENING' => Activity::TYPE_LISTENING,
            'WATCHING' => Activity::TYPE_WATCHING, 'COMPETING' => Activity::TYPE_COMPETING,
            default => Activity::TYPE_PLAYING,
        };
    }

    protected function registerGlobalSlashCommands(Discord $discord)
    {
        $this->info('Registering global slash commands...');
        $supportOptions = [
            ['name' => 'create', 'description' => 'Create a new support ticket', 'type' => Option::SUB_COMMAND, 'options' => [['name' => 'title', 'description' => 'The title of your ticket', 'type' => Option::STRING, 'required' => true], ['name' => 'description', 'description' => 'Describe your issue in detail', 'type' => Option::STRING, 'required' => true]]],
            ['name' => 'close', 'description' => 'Close an existing support ticket', 'type' => Option::SUB_COMMAND, 'options' => [['name' => 'reason', 'description' => 'Optional reason for closing', 'type' => Option::STRING, 'required' => false]]],
            ['name' => 'list', 'description' => 'List your open support tickets', 'type' => Option::SUB_COMMAND],
            ['name' => 'assign', 'description' => 'Assign a ticket to a staff member or unassign', 'type' => Option::SUB_COMMAND, 'options' => [['name' => 'user', 'description' => 'Staff member to assign (omit to unassign)', 'type' => Option::USER, 'required' => false], ['name' => 'ticket_id', 'description' => 'Ticket ID (uses current channel if omitted)', 'type' => Option::STRING, 'required' => false]]],
            // Added /support reconstruct
            ['name' => 'reconstruct', 'description' => 'Manually links an existing Discord channel to a ticket ID.', 'type' => Option::SUB_COMMAND, 'options' => [
                ['name' => 'channel', 'description' => 'The Discord channel to link.', 'type' => Option::CHANNEL, 'channel_types' => [Channel::TYPE_TEXT, Channel::TYPE_PRIVATE_THREAD, Channel::TYPE_PUBLIC_THREAD], 'required' => true],
                ['name' => 'ticket_id', 'description' => 'The numeric ID of the ticket in the database.', 'type' => Option::INTEGER, 'required' => true]
            ]]
        ];
        $supportCommand = new SlashCommand($discord, ['name' => 'support', 'description' => 'Manage support tickets', 'options' => $supportOptions]);
        $discord->application->commands->save($supportCommand)->then(
            fn() => Log::info('Global command /support (with reconstruct) registered/updated.'),
            fn($e) => Log::error('Failed to register /support (with reconstruct).', ['exception' => $e])
        );
        $pingCommand = new SlashCommand($discord, ['name' => 'ping', 'description' => 'Replies with Pong!']);
        $discord->application->commands->save($pingCommand)->then(fn() => Log::info('/ping registered.'), fn($e) => Log::error('Failed to register /ping.', ['exception' => $e]));
        $setupTicketsCommand = new SlashCommand($discord, ['name' => 'setup-tickets', 'description' => 'Sets up initial ticket system components.']);
        $discord->application->commands->save($setupTicketsCommand)->then(fn() => Log::info('/setup-tickets registered.'), fn($e) => Log::error('Failed to register /setup-tickets.', ['exception' => $e]));
    }

    protected function loadPersistedRoleState(): array
    {
        $this->info('Loading persisted role state from ' . self::ROLE_STATE_FILE_PATH);
        try {
            if (Storage::exists(self::ROLE_STATE_FILE_PATH)) {
                $jsonState = Storage::get(self::ROLE_STATE_FILE_PATH);
                if ($jsonState === null) {
                    Log::warning('Persisted role state file is empty or could not be read properly, returning default empty array.');
                    return [];
                }
                $decodedState = json_decode($jsonState, true);
                if (json_last_error() !== JSON_ERROR_NONE) {
                    Log::error('Failed to decode persisted role state JSON: ' . json_last_error_msg());
                    return []; // Return default on JSON decode error
                }
                $this->info('Successfully loaded and decoded persisted role state.');
                return $decodedState ?? [];
            } else {
                $this->info('Persisted role state file not found. Returning default empty array.');
                return []; // Return default if file doesn't exist
            }
        } catch (Exception $e) {
            Log::error('Exception while loading persisted role state: ' . $e->getMessage(), ['exception' => $e]);
            return []; // Return default on any exception
        }
    }

    protected function persistCurrentRoleState(Discord $discord)
    {
        $this->info('Attempting to persist current role state...');
        $guildId = config('minewache_discord_bot.guild_id');
        if (!$guildId) {
            Log::error('Discord Guild ID not configured. Cannot persist role state.');
            return;
        }

        $guild = $discord->guilds->get('id', $guildId);
        if (!$guild) {
            Log::error("Could not find configured guild with ID {$guildId}. Cannot persist role state.");
            return;
        }

        $currentRoles = [];
        foreach ($guild->roles as $role) {
            $currentRoles[$role->id] = [
                'id' => $role->id,
                'name' => $role->name,
                'permissions' => $role->permissions->getPermissions(),
                // 'color' => $role->color, // Optional: persist color
                // 'position' => $role->position, // Optional: persist position
            ];
        }

        try {
            $jsonState = json_encode($currentRoles, JSON_PRETTY_PRINT);
            if (json_last_error() !== JSON_ERROR_NONE) {
                Log::error('Failed to encode current role state to JSON: ' . json_last_error_msg());
                return; // Do not attempt to save invalid JSON
            }

            Storage::put(self::ROLE_STATE_FILE_PATH, $jsonState);
            $this->info('Successfully persisted current role state to ' . self::ROLE_STATE_FILE_PATH);
        } catch (Exception $e) {
            Log::error('Exception while persisting current role state: ' . $e->getMessage(), ['exception' => $e]);
        }
    }

    protected function compareAndLogRoleChanges(Discord $discord, array $loadedRoleState) { /* ... existing code ... */ }

    protected function handleSupportCreateCommand(Interaction $interaction, Discord $discord)
    {
        $locale = $this->getInteractionLocale($interaction);
        App::setLocale($locale);
        Log::info("Discord Bot: /support create command initiated by {$interaction->user->tag}. Locale: {$locale}");

        $interaction->acknowledgeWithResponse(true)->then(function () use ($interaction, $discord, $locale) {
            App::setLocale($locale); // Ensure locale in async context

            try {
                // Retrieve options for the 'create' subcommand
                $createSubCommandOptions = $interaction->data->options->get('name', 'create')->options;
                $title = $createSubCommandOptions->get('name', 'title')?->value;
                $description = $createSubCommandOptions->get('name', 'description')?->value;

                if (!$title || !$description) {
                    Log::warning("Discord Bot: /support create command from {$interaction->user->tag} missing title or description.");
                    $interaction->sendFollowUpMessage(MessageBuilder::new()->setContent(__('discord.errors.missing_options')), true);
                    return;
                }

                Log::info("Discord Bot: Attempting to create ticket for {$interaction->user->tag} with title '{$title}'.");

                $ticketCreationService = app(TicketCreationService::class);
                $ticket = $ticketCreationService->createTicketFromDiscord(
                    $interaction->user->id,
                    $title,
                    $description,
                    $interaction->guild_id,
                    $locale
                );

                if ($ticket && !empty($ticket->discord_channel_id)) {
                    Log::info("Discord Bot: Successfully created ticket #{$ticket->id} for {$interaction->user->tag}. Channel ID: {$ticket->discord_channel_id}");
                    $interaction->sendFollowUpMessage(
                        MessageBuilder::new()->setContent(
                            __('discord.ticket.created_successfully', [
                                'ticketId' => $ticket->id,
                                'channelMention' => "<#{$ticket->discord_channel_id}>"
                            ])
                        ),
                        false // Not ephemeral, so user can see the link
                    );
                } else if ($ticket) {
                    Log::error("Discord Bot: Ticket #{$ticket->id} created for {$interaction->user->tag}, but no Discord channel ID was returned by the service.");
                    $interaction->sendFollowUpMessage(MessageBuilder::new()->setContent(__('discord.ticket.creation_failed_channel')), true);
                }
                else {
                    Log::warning("Discord Bot: TicketCreationService failed to create ticket for {$interaction->user->tag} (title: '{$title}'). Service returned null.");
                    $interaction->sendFollowUpMessage(MessageBuilder::new()->setContent(__('discord.ticket.creation_failed')), true);
                }

            } catch (Exception $e) {
                Log::error("Discord Bot: Exception during /support create for {$interaction->user->tag}.", [
                    'exception_message' => $e->getMessage(),
                    'exception_trace' => Str::limit($e->getTraceAsString(), 1000)
                ]);
                $interaction->sendFollowUpMessage(MessageBuilder::new()->setContent(__('discord.ticket.creation_failed_exception')), true);
            }
        });
    }

    protected function createDiscordTicketChannel(Interaction $interaction, Discord $discord, string $sanitizedTitle, string $ticketCreatorId): ?Channel { /* ... existing code ... */ return null; }

    protected function handleSupportCloseCommand(Interaction $interaction, Discord $discord)
    {
        $locale = $this->getInteractionLocale($interaction);
        App::setLocale($locale);
        $discordUserId = $interaction->user->id;
        $channelId = $interaction->channel_id;

        // Retrieve reason from options for the 'close' subcommand
        $reason = null;
        $closeSubCommandOptions = $interaction->data->options?->get('name', 'close')?->options;
        if ($closeSubCommandOptions) {
            $reason = $closeSubCommandOptions->get('name', 'reason')?->value;
        }

        Log::info("Discord Bot: /support close command initiated by {$discordUserId} in channel {$channelId}. Reason: " . ($reason ?? 'None provided') . ". Locale: {$locale}");

        $interaction->acknowledgeWithResponse(true)->then(function () use ($interaction, $discord, $locale, $discordUserId, $channelId, $reason) {
            App::setLocale($locale); // Ensure locale in async context

            try {
                $ticketUpdateService = app(TicketUpdateService::class);
                $result = $ticketUpdateService->closeTicketByDiscordUser(
                    $discordUserId,
                    $channelId,
                    $locale,
                    $reason
                );

                if ($result['success']) {
                    Log::info("Discord Bot: Ticket successfully closed by {$discordUserId} in channel {$channelId}. Ticket ID: {$result['ticket']?->id}");
                    $interaction->sendFollowUpMessage(
                        MessageBuilder::new()->setContent(
                            __($result['message_key'] ?? 'discord.ticket.closed_successfully', ['ticketId' => $result['ticket']?->id])
                        )
                        // false // Make it public so others in channel see it's closed.
                    );
                } else {
                    Log::warning("Discord Bot: Failed to close ticket for {$discordUserId} in channel {$channelId}. Reason: " . ($result['message_key'] ?? 'Unknown') . ", Error Code: " . ($result['error_code'] ?? 'N/A'));
                    $interaction->sendFollowUpMessage(
                        MessageBuilder::new()->setContent(
                            __($result['message_key'] ?? 'discord.ticket.close_failed_generic', ['errorCode' => $result['error_code'] ?? 'unknown'])
                        ),
                        true // Ephemeral for errors
                    );
                }

            } catch (Exception $e) {
                Log::error("Discord Bot: Exception during /support close for {$discordUserId} in channel {$channelId}.", [
                    'exception_message' => $e->getMessage(),
                    'exception_trace' => Str::limit($e->getTraceAsString(), 1000)
                ]);
                $interaction->sendFollowUpMessage(MessageBuilder::new()->setContent(__('discord.ticket.close_failed_exception')), true);
            }
        });
    }

    protected function handleCloseTicketProcess(Interaction $interaction, Discord $discord, ?string $ticketDbIdFromButton, ?string $reasonFromCommand) { /* ... existing code ... */ }
    protected function getTicketIdFromChannelObject(Channel $channel): ?string { if ($channel->topic && preg_match('/Ticket ID: (\d+)/', $channel->topic, $matches)) return $matches[1]; if (preg_match('/(?:ticket|closed)-(\d+)/', $channel->name, $matches)) return $matches[1]; return null; }

    protected function handleSupportListCommand(Interaction $interaction, Discord $discord)
    {
        $locale = $this->getInteractionLocale($interaction);
        App::setLocale($locale);
        $discordUserId = $interaction->user->id;

        Log::info("Discord Bot: /support list command initiated by {$discordUserId}. Locale: {$locale}");

        $interaction->acknowledgeWithResponse(true)->then(function () use ($interaction, $discord, $locale, $discordUserId) {
            App::setLocale($locale); // Ensure locale in async context

            try {
                $ticketService = app(TicketService::class);
                // Assuming listOpenTicketsForDiscordUser returns:
                // array of ['id' => ..., 'title' => ..., 'status' => ..., 'channel_id' => ..., 'created_at' => ...]
                $tickets = $ticketService->listOpenTicketsForDiscordUser($discordUserId, $locale);

                if (empty($tickets)) {
                    Log::info("Discord Bot: No open tickets found for {$discordUserId}.");
                    $interaction->sendFollowUpMessage(
                        MessageBuilder::new()->setContent(__('discord.ticket.list_no_open_tickets')),
                        true
                    );
                    return;
                }

                Log::info("Discord Bot: Found " . count($tickets) . " open ticket(s) for {$discordUserId}.");

                $messageBuilder = MessageBuilder::new();
                $embed = new \Discord\Parts\Embed\Embed($discord); // Correctly instantiate Embed

                $embed->setTitle(__('discord.ticket.list_embed_title'));
                $embed->setColor(0x00AE86); // A nice green color

                $descriptionLines = [];
                foreach ($tickets as $ticket) {
                    $channelMention = $ticket['channel_id'] ? "<#{$ticket['channel_id']}>" : __('discord.ticket.list_channel_not_available');
                    $descriptionLines[] = __(
                        'discord.ticket.list_ticket_line',
                        [
                            'ticketId' => $ticket['id'],
                            'title' => Str::limit($ticket['title'], 100), // Limit title length
                            'status' => $ticket['status'], // Assuming status is already localized or a key
                            'channelMention' => $channelMention,
                            // 'createdAt' => Carbon::parse($ticket['created_at'])->diffForHumans() // Optional: human-readable time
                        ]
                    );
                }
                $embed->setDescription(implode("\n", $descriptionLines));
                $embed->setFooter(__('discord.ticket.list_embed_footer', ['count' => count($tickets)]));


                // Discord embed description character limit is 4096.
                // If the description is too long, consider sending multiple embeds or a simpler text list.
                // For this implementation, we assume it will fit.
                if (strlen($embed->description) > 4000) { // Check before it's too late
                    Log::warning("Discord Bot: Ticket list for {$discordUserId} is too long for a single embed. Sending truncated list or consider alternative.");
                    // Fallback or truncation logic could be added here.
                    // For now, we'll let it try and potentially fail if Discord API rejects it, or send a simpler message.
                    // A simple text fallback:
                    // $interaction->sendFollowUpMessage(MessageBuilder::new()->setContent(__('discord.ticket.list_too_long_text_fallback')), true);
                    // return;
                }

                $messageBuilder->addEmbed($embed);
                $interaction->sendFollowUpMessage($messageBuilder, true);

            } catch (Exception $e) {
                Log::error("Discord Bot: Exception during /support list for {$discordUserId}.", [
                    'exception_message' => $e->getMessage(),
                    'exception_trace' => Str::limit($e->getTraceAsString(), 1000)
                ]);
                $interaction->sendFollowUpMessage(
                    MessageBuilder::new()->setContent(__('discord.ticket.list_failed')),
                    true
                );
            }
        });
    }

    protected function handleProgressTicketProcess(Interaction $interaction, Discord $discord, string $ticketDbId) { /* ... existing code ... */ }
    protected function handleSetupTicketsCommand(Interaction $interaction, Discord $discord) { /* ... existing code ... */ }
    private function getChannelDetails(string $channelId, string $botToken): ?array { /* ... existing code ... */ return null; }
    private function createCategory(string $guildId, string $name, string $botToken): ?array { /* ... existing code ... */ return null; }
    private function getGuildRoles(string $guildId, string $botToken): ?array { /* ... existing code ... */ return null; }
    private function updateChannelPermissions(string $channelId, array $permissionOverwrites, string $botToken): bool { /* ... existing code ... */ return false; }
    protected function getInteractionLocale(Interaction $interaction): string { $p = $interaction->locale; $g = $interaction->guild_locale; $f = Config::get('app.fallback_locale','en'); $s = Config::get('app.supported_locales',['en']); if(in_array($p,$s)) return $p; if(in_array($g,$s)) return $g; return $f; }

    protected function handleTicketAssignCommand(Interaction $interaction, Discord $discord)
    {
        $locale = $this->getInteractionLocale($interaction);
        App::setLocale($locale);
        $assignerDiscordId = $interaction->user->id;
        $channelId = $interaction->channel_id;

        // Retrieve options for the 'assign' subcommand
        $assignSubCommandOptions = $interaction->data->options?->get('name', 'assign')?->options;
        $targetDiscordUserId = $assignSubCommandOptions?->get('name', 'user')?->value;
        $ticketIdFromOption = $assignSubCommandOptions?->get('name', 'ticket_id')?->value;

        Log::info("Discord Bot: /support assign command initiated by {$assignerDiscordId}.", [
            'target_user_id' => $targetDiscordUserId,
            'ticket_id_option' => $ticketIdFromOption,
            'channel_id' => $channelId,
            'locale' => $locale
        ]);

        $interaction->acknowledgeWithResponse(true)->then(function () use ($interaction, $discord, $locale, $assignerDiscordId, $targetDiscordUserId, $ticketIdFromOption, $channelId) {
            App::setLocale($locale); // Ensure locale in async context

            try {
                $ticketAssignmentService = app(TicketAssignmentService::class);
                $result = $ticketAssignmentService->assignTicket(
                    $assignerDiscordId,
                    $targetDiscordUserId,
                    $ticketIdFromOption,
                    $channelId,
                    $locale
                );

                $logContext = [
                    'assigner_id' => $assignerDiscordId,
                    'target_user_id' => $targetDiscordUserId,
                    'ticket_id_option' => $ticketIdFromOption,
                    'channel_id' => $channelId,
                    'ticket_id_resolved' => $result['ticket']?->id,
                    'service_message_key' => $result['message_key'] ?? 'N/A',
                    'assigned_user_name' => $result['assigned_user_name'] ?? 'N/A',
                ];

                if ($result['success']) {
                    Log::info("Discord Bot: Ticket assignment/unassignment successful.", $logContext);
                    $interaction->sendFollowUpMessage(
                        MessageBuilder::new()->setContent(
                            __($result['message_key'] ?? 'discord.ticket.assign_success_generic', [
                                'ticketId' => $result['ticket']?->id,
                                'userName' => $result['assigned_user_name'] // Will be null for unassign, handled by translation key
                            ])
                        )
                        // false // Public message
                    );
                } else {
                    $logContext['error_code'] = $result['error_code'] ?? 'N/A';
                    Log::warning("Discord Bot: Ticket assignment/unassignment failed.", $logContext);
                    $interaction->sendFollowUpMessage(
                        MessageBuilder::new()->setContent(
                            __($result['message_key'] ?? 'discord.ticket.assign_failed_generic', [
                                'errorCode' => $result['error_code'] ?? 'unknown'
                            ])
                        ),
                        true // Ephemeral for errors
                    );
                }

            } catch (Exception $e) {
                Log::error("Discord Bot: Exception during /support assign.", [
                    'assigner_id' => $assignerDiscordId,
                    'target_user_id' => $targetDiscordUserId,
                    'ticket_id_option' => $ticketIdFromOption,
                    'channel_id' => $channelId,
                    'exception_message' => $e->getMessage(),
                    'exception_trace' => Str::limit($e->getTraceAsString(), 1000)
                ]);
                $interaction->sendFollowUpMessage(
                    MessageBuilder::new()->setContent(__('discord.ticket.assign_failed_exception')),
                    true
                );
            }
        });
    }


    /**
     * Acquire a lock to prevent multiple instances of the bot from running
     *
     * @return bool True if lock was acquired, false if another instance is running
     */
    protected function acquireLock(): bool
    {
        $lockFile = storage_path(self::LOCK_FILE_PATH);
        $lockDir = dirname($lockFile);

        // Create directory if it doesn't exist
        if (!file_exists($lockDir)) {
            mkdir($lockDir, 0755, true);
        }

        // Check if lock file exists and process is still running
        if (file_exists($lockFile)) {
            $pid = file_get_contents($lockFile);

            // On Windows, check if process is running using tasklist
            if (PHP_OS_FAMILY === 'Windows') {
                $output = [];
                exec("tasklist /FI \"PID eq $pid\" /NH", $output);

                // If process is still running, return false
                if (count($output) > 0 && strpos($output[0], $pid) !== false) {
                    return false;
                }
            }
            // On Unix-like systems, check if process is running using kill -0
            else {
                if (posix_kill($pid, 0)) {
                    return false;
                }
            }

            // Process is not running, remove stale lock file
            @unlink($lockFile);
        }

        // Create lock file with current PID
        file_put_contents($lockFile, getmypid());

        return true;
    }

    /**
     * Release the lock when the bot exits
     */
    public function releaseLock(): void
    {
        $lockFile = storage_path(self::LOCK_FILE_PATH);
        if (file_exists($lockFile)) {
            @unlink($lockFile);
        }

        // Close HTTP server if it's running
        if ($this->socketServer) {
            $this->socketServer->close();
        }
    }

    /**
     * Start the HTTP server for API requests
     */
    protected function startHttpServer(): void
    {
        try {
            // Create HTTP server
            $this->httpServer = new HttpServer(function (ServerRequestInterface $request) {
                return $this->handleApiRequest($request);
            });

            // Create socket server
            $this->socketServer = new SocketServer('127.0.0.1:' . self::HTTP_SERVER_PORT);

            // Attach HTTP server to socket
            $this->httpServer->listen($this->socketServer);

            $this->info('HTTP API Server started on port ' . self::HTTP_SERVER_PORT);
            Log::info('Discord Bot: HTTP API Server initialized on port ' . self::HTTP_SERVER_PORT);
        } catch (Exception $e) {
            $this->error('Failed to start HTTP API Server: ' . $e->getMessage());
            Log::error('Discord Bot: Failed to start HTTP API Server', ['exception' => $e]);
            throw $e; // Re-throw to stop bot initialization
        }
    }

    /**
     * Handle API requests
     *
     * @param ServerRequestInterface $request
     * @return \React\Http\Message\Response
     */
    protected function handleApiRequest(ServerRequestInterface $request)
    {
        $path = $request->getUri()->getPath();
        $method = $request->getMethod();

        Log::debug('Discord Bot API: Received request', [
            'path' => $path,
            'method' => $method,
            'query' => $request->getQueryParams(),
            'headers' => $request->getHeaders()
        ]);

        // Check API key for authentication
        $authHeader = $request->getHeaderLine('Authorization');
        $apiKey = config('services.discord.api_key');

        if (empty($apiKey) || $authHeader !== "Bearer {$apiKey}") {
            Log::warning('Discord Bot API: Unauthorized request', [
                'path' => $path,
                'provided_auth' => $authHeader
            ]);

            return new \React\Http\Message\Response(
                401,
                ['Content-Type' => 'application/json'],
                json_encode(['error' => 'Unauthorized', 'message' => 'Invalid or missing API key'])
            );
        }

        // Handle /api/users/{userId}/roles endpoint
        if (preg_match('#^/api/users/(\d+)/roles$#', $path, $matches)) {
            $userId = $matches[1];
            return $this->handleUserRolesRequest($userId, $request);
        }

        // Handle health check endpoint
        if ($path === '/api/health') {
            return new \React\Http\Message\Response(
                200,
                ['Content-Type' => 'application/json'],
                json_encode(['status' => 'ok', 'timestamp' => time()])
            );
        }

        // Handle unknown endpoints
        Log::warning('Discord Bot API: Unknown endpoint requested', ['path' => $path]);
        return new \React\Http\Message\Response(
            404,
            ['Content-Type' => 'application/json'],
            json_encode(['error' => 'Not Found', 'message' => 'Endpoint not found'])
        );
    }

    /**
     * Handle requests to /api/users/{userId}/roles
     *
     * @param string $userId Discord user ID
     * @param ServerRequestInterface $request
     * @return \React\Http\Message\Response
     */
    protected function handleUserRolesRequest(string $userId, ServerRequestInterface $request)
    {
        // Wait until Discord client is ready
        if (!$this->discord || !isset($this->discord->guilds)) {
            Log::warning('Discord Bot API: Discord client not ready for user roles request', ['user_id' => $userId]);
            return new \React\Http\Message\Response(
                503,
                ['Content-Type' => 'application/json'],
                json_encode(['error' => 'Service Unavailable', 'message' => 'Discord client not ready'])
            );
        }

        $guildId = config('minewache_discord_bot.guild_id');
        if (!$guildId) {
            Log::error('Discord Bot API: Guild ID not configured');
            return new \React\Http\Message\Response(
                500,
                ['Content-Type' => 'application/json'],
                json_encode(['error' => 'Server Error', 'message' => 'Guild ID not configured'])
            );
        }

        $guild = $this->discord->guilds->get('id', $guildId);
        if (!$guild) {
            Log::error('Discord Bot API: Guild not found', ['guild_id' => $guildId]);
            return new \React\Http\Message\Response(
                500,
                ['Content-Type' => 'application/json'],
                json_encode(['error' => 'Server Error', 'message' => 'Guild not found'])
            );
        }

        // Check if user is a member of the guild
        $member = $guild->members->get('id', $userId);
        if (!$member) {
            Log::info('Discord Bot API: User is not a member of the guild', ['user_id' => $userId, 'guild_id' => $guildId]);
            return new \React\Http\Message\Response(
                404,
                ['Content-Type' => 'application/json'],
                json_encode(['error' => 'Not Found', 'message' => 'User is not a member of the guild'])
            );
        }

        // Get user's roles
        $roles = $member->roles->map(function ($role) {
            return [
                'id' => $role->id,
                'name' => $role->name,
                'color' => $role->color,
                'position' => $role->position,
                'permissions' => $role->permissions->getPermissions()
            ];
        })->toArray();

        Log::debug('Discord Bot API: Retrieved roles for user', [
            'user_id' => $userId,
            'role_count' => count($roles)
        ]);

        return new \React\Http\Message\Response(
            200,
            ['Content-Type' => 'application/json'],
            json_encode([
                'user_id' => $userId,
                'guild_id' => $guildId,
                'roles' => $roles
            ])
        );
    }

    protected function handleTicketReconstructCommand(Interaction $interaction, Discord $discord)
    {
        $locale = $this->getInteractionLocale($interaction);
        App::setLocale($locale);
        Log::info("Discord Bot: /support reconstruct command initiated by {$interaction->user->tag}. Locale: {$locale}");

        $interaction->acknowledgeWithResponse(true)->then(function () use ($interaction, $discord, $locale) {
            App::setLocale($locale); // Ensure locale in async context

            // 1. Admin/Permission Check
            $adminDiscordId = $interaction->user->id;
            $adminUser = User::where('discord_id', $adminDiscordId)->first();
            if (!$adminUser) {
                $interaction->sendFollowUpMessage(MessageBuilder::new()->setContent(__('discord.errors.user_not_linked_short')), true);
                Log::warning("Admin Discord user {$adminDiscordId} not linked to a Laravel user for /support reconstruct.");
                return;
            }
            // Define 'admin.tickets.reconstruct' in AuthServiceProvider
            // Example: Gate::define('admin.tickets.reconstruct', fn(User $user) => $user->hasRole('Admin'));
            if (Gate::forUser($adminUser)->denies('admin.tickets.reconstruct')) {
                $interaction->sendFollowUpMessage(MessageBuilder::new()->setContent(__('discord.errors.admin_only_command')), true);
                Log::warning("User {$adminUser->name} (Discord ID: {$adminDiscordId}) attempted /support reconstruct without permission.");
                return;
            }

            // 2. Get Options
            $reconstructOptions = $interaction->data->options->get('name', 'reconstruct')->options;
            $channelIdFromOption = $reconstructOptions->get('name', 'channel')->value;
            $ticketDbIdFromOption = $reconstructOptions->get('name', 'ticket_id')->value;

            // 3. Validate Ticket
            $ticket = Ticket::find($ticketDbIdFromOption);
            if (!$ticket) {
                $interaction->sendFollowUpMessage(MessageBuilder::new()->setContent(__('discord.ticket.reconstruct_ticket_not_found', ['ticketId' => $ticketDbIdFromOption])), true);
                return;
            }
            if (!empty($ticket->discord_channel_id)) {
                 // Simpler: error out if already linked. Confirmation flow is too complex for this iteration.
                $interaction->sendFollowUpMessage(MessageBuilder::new()->setContent(__('discord.ticket.reconstruct_already_linked', ['ticketId' => $ticketDbIdFromOption, 'channelMention' => "<#{$ticket->discord_channel_id}>"])), true);
                return;
            }

            // 4. Validate Channel (using DiscordPHP's fetch for reliability)
            $discord->channel->fetch($channelIdFromOption)->then(
                function(Channel $targetChannel) use ($interaction, $ticket, $adminUser, $ticketDbIdFromOption, $locale) {
                    App::setLocale($locale); // Ensure locale in async
                    if ($targetChannel->type !== Channel::TYPE_TEXT && $targetChannel->type !== Channel::TYPE_PRIVATE_THREAD && $targetChannel->type !== Channel::TYPE_PUBLIC_THREAD) {
                         $interaction->sendFollowUpMessage(MessageBuilder::new()->setContent(__('discord.ticket.reconstruct_channel_not_text', ['channelMention' => "<#{$targetChannel->id}>"])), true)->then();
                         return;
                    }

                    // 5. Update Ticket
                    $ticket->discord_channel_id = $targetChannel->id;
                    $ticket->discord_guild_id = $interaction->guild_id;
                    // Optionally update ticket title from channel name
                    // $ticket->title = $targetChannel->name;
                    $ticket->save();

                    // Create system message in Laravel DB
                    TicketMessage::create([
                        'ticket_id' => $ticket->id,
                        'user_id' => $adminUser->id, // Admin user performs this action
                        'content' => __('discord.ticket.reconstruct_system_message', ['channelName' => $targetChannel->name, 'adminName' => $adminUser->name]),
                        'is_system_message' => true,
                    ]);

                    // 6. Update Channel Topic (Optional but Recommended)
                    $newTopic = __('discord.ticket.reconstruct_channel_topic', ['ticketId' => $ticketDbIdFromOption, 'adminName' => $adminUser->name]);
                    $newTopic = substr($newTopic, 0, 1024); // Discord topic length limit

                    $targetChannel->setTopic($newTopic)->then(
                        function() use ($interaction, $targetChannel, $ticketDbIdFromOption, $locale) {
                            App::setLocale($locale);
                            Log::info("Successfully updated topic for channel #{$targetChannel->id} during ticket #{$ticketDbIdFromOption} reconstruction.");
                            $interaction->sendFollowUpMessage(MessageBuilder::new()->setContent(__('discord.ticket.reconstruct_success', ['channelMention' => "<#{$targetChannel->id}>", 'ticketId' => $ticketDbIdFromOption])), true)->then();
                        },
                        function($error) use ($interaction, $targetChannel, $ticketDbIdFromOption, $locale) {
                            App::setLocale($locale);
                            Log::error("Failed to update topic for channel #{$targetChannel->id} during ticket #{$ticketDbIdFromOption} reconstruction.", ['error' => $error]);
                            $interaction->sendFollowUpMessage(MessageBuilder::new()->setContent(__('discord.ticket.reconstruct_success_topic_fail', ['channelMention' => "<#{$targetChannel->id}>", 'ticketId' => $ticketDbIdFromOption])), true)->then();
                        }
                    );
                    Log::info("Ticket #{$ticketDbIdFromOption} reconstructed to channel #{$targetChannel->id} by admin {$adminUser->name} (ID: {$adminUser->id}).");

                },
                function(Exception $e) use ($interaction, $channelIdFromOption, $locale) { // Channel fetch failed
                    App::setLocale($locale);
                    Log::error("Failed to fetch channel {$channelIdFromOption} for reconstruction.", ['exception_message' => $e->getMessage()]);
                    $interaction->sendFollowUpMessage(MessageBuilder::new()->setContent(__('discord.ticket.reconstruct_channel_fetch_error', ['channelId' => $channelIdFromOption])), true)->then();
                }
            );
        });
    }
}
