/**
 * <PERSON><PERSON> wird ausgeführt, bevor die Entwicklungsumgebung gestartet wird.
 * <PERSON><PERSON> stellt sicher, dass alle Prozesse ordnungsgemäß vorbereitet sind.
 */
import { execSync } from 'child_process';
import { fileURLToPath } from 'url';
import path from 'path';

console.log('🚀 Bereite Entwicklungsumgebung vor...');

// Pfad zum aktuellen Verzeichnis (ES Module Ersatz für __dirname)
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

try {
  // Prüfen, ob der PHP Discord-Bot bereits läuft und ggf. stoppen
  console.log('🔍 Prüfe, ob der PHP Discord-Bot bereits läuft...');
  try {
    const isWindows = process.platform === 'win32';
    if (isWindows) {
      // Auf Windows, prüfe mit tasklist
      const result = execSync('tasklist /FI "WINDOWTITLE eq php artisan minewache:run-discord-bot*" /NH', { stdio: 'pipe' });
      const isRunning = !result.toString().includes('No tasks');

      if (isRunning) {
        console.log('🤖 PHP Discord-Bot läuft bereits. Stoppe ihn...');
        execSync('stop-discord.bat', { stdio: 'inherit' });
      }
    } else {
      // Auf Linux/Mac, prüfe mit ps
      const result = execSync("ps aux | grep '[p]hp artisan minewache:run-discord-bot'", { stdio: 'pipe' });
      const isRunning = result.toString().trim() !== '';

      if (isRunning) {
        console.log('🤖 PHP Discord-Bot läuft bereits. Stoppe ihn...');
        execSync("pkill -f 'php artisan minewache:run-discord-bot'", { stdio: 'inherit' });
      }
    }

    if (!isWindows || !isRunning) {
      console.log('ℹ️ PHP Discord-Bot läuft nicht. Das ist in Ordnung.');
    }
  } catch (error) {
    // Fehler beim Prüfen oder Stoppen, wahrscheinlich läuft der Bot nicht
    console.log('ℹ️ PHP Discord-Bot läuft nicht oder konnte nicht geprüft werden.');
  }
} catch (error) {
  console.error('❌ Fehler bei der Vorbereitung:', error.message);
  process.exit(1);
}

console.log('✅ Entwicklungsumgebung erfolgreich vorbereitet!');
