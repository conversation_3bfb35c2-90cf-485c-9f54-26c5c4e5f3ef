<?php

namespace Database\Seeders;

use App\Models\RegisterYoutubeLink;
use Carbon\Carbon;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\File;
use Illuminate\Support\Facades\Log;

class YouTubeCSVSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $csvPath = storage_path('app/mw.csv');
        
        // Check if the CSV file exists
        if (!File::exists($csvPath)) {
            $this->command->error('CSV file not found at: ' . $csvPath);
            return;
        }
        
        $this->command->info('Importing YouTube data from CSV...');
        
        // Read the CSV file
        $csvData = File::get($csvPath);
        $lines = explode(PHP_EOL, $csvData);
        
        // Remove empty lines
        $lines = array_filter($lines);
        
        $this->command->info('Found ' . count($lines) . ' entries in the CSV file.');
        
        $importCount = 0;
        $updateCount = 0;
        $errorCount = 0;
        
        foreach ($lines as $line) {
            try {
                // Parse the CSV line
                $data = str_getcsv($line);
                
                // Skip if we don't have enough data
                if (count($data) < 12) {
                    $this->command->warn("Skipping line with insufficient data: " . $line);
                    $errorCount++;
                    continue;
                }
                
                // Map CSV columns to database fields
                $videoData = [
                    'id' => $data[0],
                    'season' => $data[1],
                    'episode' => $data[2],
                    'link' => $data[3],
                    'created_at' => $data[4],
                    'updated_at' => $data[5],
                    'title' => $data[6],
                    'description' => $data[7],
                    'thumbnail_url' => $data[8],
                    'published_at' => $data[9],
                    'duration_seconds' => $data[10],
                    'view_count' => $data[11],
                    'is_featured' => isset($data[12]) ? (bool)$data[12] : false,
                    'status' => isset($data[13]) ? $data[13] : 'active',
                ];
                
                // Convert published_at to a datetime if it exists
                if (isset($videoData['published_at']) && $videoData['published_at']) {
                    try {
                        $videoData['published_at'] = Carbon::parse($videoData['published_at']);
                    } catch (\Exception $e) {
                        $this->command->warn("Could not parse date: {$videoData['published_at']} for video {$videoData['title']}");
                        $videoData['published_at'] = null;
                        $errorCount++;
                    }
                }
                
                // Check if the video already exists
                $existingVideo = RegisterYoutubeLink::where('season', $videoData['season'])
                    ->where('episode', $videoData['episode'])
                    ->first();
                    
                if ($existingVideo) {
                    // Update existing video
                    $existingVideo->update($videoData);
                    $updateCount++;
                } else {
                    // Create new video
                    RegisterYoutubeLink::create($videoData);
                    $importCount++;
                }
            } catch (\Exception $e) {
                $this->command->error("Error processing line: " . $line);
                $this->command->error("Error message: " . $e->getMessage());
                $errorCount++;
                continue;
            }
        }
        
        $this->command->info("Import completed: {$importCount} new videos, {$updateCount} updated, {$errorCount} errors.");
    }
}