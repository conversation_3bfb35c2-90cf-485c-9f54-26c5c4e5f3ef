<?php

namespace App\Http\Controllers;

use App\Enums\Role;
use App\Jobs\ProcessTicketMediaJob;
use App\Models\Ticket;
use App\Models\TicketAttachment; // Keep this for downloadAttachment
use App\Models\TicketMessage;
use App\Models\User;
use App\Services\DiscordService;
use App\Services\TicketAttachmentService; // Add this
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Gate;
use Illuminate\Support\Facades\Storage;

class TicketController extends Controller
{
    private DiscordService $discordService;
    private TicketAttachmentService $ticketAttachmentService; // Add this

    public function __construct(
        DiscordService $discordService,
        TicketAttachmentService $ticketAttachmentService // Add this
    ) {
        $this->discordService = $discordService;
        $this->ticketAttachmentService = $ticketAttachmentService; // Add this
    }

    /**
     * Display a listing of the tickets.
     */
    public function index(Request $request)
    {
        $user = $request->user();

        // If user is a supporter, show all tickets
        if ($user->hasRole(Role::MINEWACHE_TEAM)) {
            $tickets = Ticket::with('user', 'assignedTo')
                ->latest()
                ->paginate(15);
        } else {
            // Otherwise, show only user's tickets
            $tickets = $user->tickets()
                ->with('assignedTo')
                ->latest()
                ->paginate(15);
        }

        return view('tickets.index', compact('tickets'));
    }

    /**
     * Show the form for creating a new ticket.
     */
    public function create()
    {
        return view('tickets.create');
    }

    /**
     * Store a newly created ticket.
     */
    public function store(Request $request)
    {
        $validated = $request->validate([
            'title' => 'required|string|max:255',
            'description' => 'required|string',
        ]);

        $ticket = new Ticket([
            'user_id' => $request->user()->id,
            'title' => $validated['title'],
            'description' => $validated['description'],
            'status' => 'open',
        ]);

        $ticket->save();

        // Create a Discord channel for this ticket if Discord integration is enabled
        if (config('services.discord.enabled', false)) {
            $channelId = $this->discordService->createTicketChannel($ticket);
            if ($channelId) {
                $ticket->discord_channel_id = $channelId;
                $ticket->save();
            }
        }

        return redirect()->route('tickets.show', $ticket)
            ->with('success', __('tickets.created_successfully'));
    }

    /**
     * Display the specified ticket.
     */
    public function show(Ticket $ticket)
    {
        Gate::authorize('view', $ticket);

        $ticket->load(['messages.user', 'user', 'assignedTo']);

        return view('tickets.show', compact('ticket'));
    }

    /**
     * Add a reply to a ticket.
     */
    public function reply(Request $request, Ticket $ticket)
    {
        Gate::authorize('reply', $ticket);

        $validated = $request->validate([
            'message' => 'required|string',
            'attachments.*' => 'nullable|file|max:10240', // 10MB max per file
        ]);

        $message = new TicketMessage([
            'ticket_id' => $ticket->id,
            'user_id' => $request->user()->id,
            'message' => $validated['message'],
        ]);

        $message->save();

        // Handle file attachments using the service
        $this->ticketAttachmentService->handleUploads($request, $message);

        // Send the message to Discord if integration is enabled
        if (config('services.discord.enabled', false) && $ticket->discord_channel_id) {
            $this->discordService->sendTicketMessage($message);
        }

        return redirect()->route('tickets.show', $ticket)
            ->with('success', __('tickets.reply_added'));
    }

    /**
     * Update the ticket status.
     */
    public function updateStatus(Request $request, Ticket $ticket)
    {
        Gate::authorize('changeStatus', $ticket);

        $validated = $request->validate([
            'status' => 'required|in:open,in_progress,closed',
        ]);

        $ticket->status = $validated['status'];
        $ticket->save();

        // Update the status on Discord if integration is enabled
        if (config('services.discord.enabled', false) && $ticket->discord_channel_id) {
            $this->discordService->updateTicketStatus($ticket);
        }

        return redirect()->route('tickets.show', $ticket)
            ->with('success', __('tickets.status_updated'));
    }

    /**
     * Assign the ticket to a supporter.
     */
    public function assign(Request $request, Ticket $ticket)
    {
        Gate::authorize('assign', $ticket);

        $validated = $request->validate([
            'assigned_to' => 'nullable|string|exists:users,id',
        ]);

        $ticket->assigned_to = $validated['assigned_to'];
        $ticket->save();

        // Update the assignment on Discord if integration is enabled
        if (config('services.discord.enabled', false) && $ticket->discord_channel_id) {
            $this->discordService->updateTicketAssignment($ticket);
        }

        return redirect()->route('tickets.show', $ticket)
            ->with('success', __('tickets.assigned_successfully'));
    }

    /**
     * Download a ticket attachment.
     */
    public function downloadAttachment(TicketAttachment $attachment)
    {
        $ticket = $attachment->message->ticket;

        Gate::authorize('downloadAttachment', $ticket);

        // Determine which file to serve - use processed file if available and completed
        $filePath = $attachment->file_path;
        $mimeType = $attachment->mime_type;
        $filename = $attachment->original_filename;

        // If the attachment has been processed successfully and has a processed file, use that instead
        if ($attachment->processing_status === 'completed' && $attachment->processed_file_path && Storage::exists($attachment->processed_file_path)) {
            $filePath = $attachment->processed_file_path;

            // Update mime type based on the processed file extension
            $extension = pathinfo($filePath, PATHINFO_EXTENSION);
            if ($extension === 'mp4') {
                $mimeType = 'video/mp4';
            } elseif ($extension === 'mp3') {
                $mimeType = 'audio/mpeg';
            } elseif ($extension === 'jpg' || $extension === 'jpeg') {
                $mimeType = 'image/jpeg';
            }

            // Update filename to include the correct extension
            $originalExtension = pathinfo($filename, PATHINFO_EXTENSION);
            if (strtolower($originalExtension) !== strtolower($extension) && !empty($extension)) {
                $filename = pathinfo($filename, PATHINFO_FILENAME) . '.' . $extension;
            }
        }

        if (!Storage::exists($filePath)) {
            abort(404, __('tickets.attachment_not_found'));
        }

        return Storage::download(
            $filePath,
            $filename,
            ['Content-Type' => $mimeType]
        );
    }
}
