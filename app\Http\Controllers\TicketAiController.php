<?php

namespace App\Http\Controllers;

use App\Contracts\LanguageModelInterface;
use App\Models\Ticket;
use App\Models\TicketMessage;
use App\Models\UserAiConsent;
use App\Services\AITicketService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Gate;
use Illuminate\Support\Facades\Log;

class TicketAiController extends Controller
{
    /**
     * The language model service.
     *
     * @var \App\Contracts\LanguageModelInterface
     */
    protected $languageModel;

    /**
     * The AI ticket service.
     *
     * @var \App\Services\AITicketService
     */
    protected $aiTicketService;

    /**
     * Create a new controller instance.
     *
     * @param \App\Contracts\LanguageModelInterface $languageModel
     * @param \App\Services\AITicketService $aiTicketService
     * @return void
     */
    public function __construct(LanguageModelInterface $languageModel, AITicketService $aiTicketService)
    {
        $this->languageModel = $languageModel;
        $this->aiTicketService = $aiTicketService;
    }

    /**
     * Generate an AI response for a ticket.
     *
     * @param \Illuminate\Http\Request $request
     * @param \App\Models\Ticket $ticket
     * @return \Illuminate\Http\Response
     */
    public function generateResponse(Request $request, Ticket $ticket)
    {
        Gate::authorize('reply', $ticket);

        $user = $request->user();

        // Check if AI is enabled
        if (!config('services.gemini.enabled', true)) {
            return response()->json([
                'success' => false,
                'message' => __('tickets.ai_disabled'),
            ]);
        }

        // Check if user has consented to AI
        if (!$user->hasConsentedToAi()) {
            return response()->json([
                'success' => false,
                'message' => __('tickets.ai_consent_required'),
                'requiresConsent' => true,
            ]);
        }

        try {
            // Generate the response
            $response = $this->languageModel->generateTicketResponse($ticket, $user);

            return response()->json([
                'success' => true,
                'response' => $response,
            ]);
        } catch (\Exception $e) {
            report($e);
            return response()->json([
                'success' => false,
                'message' => __('tickets.ai_error'),
            ], 500);
        }
    }

    /**
     * Add an AI-generated response to a ticket.
     *
     * @param \Illuminate\Http\Request $request
     * @param \App\Models\Ticket $ticket
     * @return \Illuminate\Http\Response
     */
    public function addAiResponse(Request $request, Ticket $ticket)
    {
        Gate::authorize('reply', $ticket);

        $validated = $request->validate([
            'message' => 'required|string',
        ]);

        $user = $request->user();

        // Create a new ticket message
        $message = new TicketMessage([
            'ticket_id' => $ticket->id,
            'user_id' => $user->id,
            'message' => $validated['message'],
            'is_system_message' => true,
        ]);

        $message->save();

        return redirect()->route('tickets.show', $ticket)
            ->with('success', __('tickets.ai_response_added'));
    }

    /**
     * Update the user's AI consent.
     *
     * @param \Illuminate\Http\Request $request
     * @return \Illuminate\Http\Response
     */
    public function updateConsent(Request $request)
    {
        $validated = $request->validate([
            'has_consented' => 'required|boolean',
        ]);

        $user = $request->user();

        // Get or create the consent record
        $consent = $user->aiConsent ?? new UserAiConsent(['user_id' => $user->id]);

        // Update the consent
        $consent->has_consented = $validated['has_consented'];

        if ($validated['has_consented']) {
            $consent->consent_given_at = now();
        }

        $consent->save();

        return response()->json([
            'success' => true,
            'has_consented' => $consent->has_consented,
        ]);
    }

    /**
     * Update the Gemini consent for a ticket.
     *
     * @param \Illuminate\Http\Request $request
     * @param \App\Models\Ticket $ticket
     * @return \Illuminate\Http\JsonResponse
     */
    public function updateTicketConsent(Request $request, Ticket $ticket)
    {
        $request->validate([
            'gemini_consent' => 'required|boolean',
        ]);

        $ticket->gemini_consent = $request->gemini_consent;
        $ticket->gemini_consent_at = now();
        $ticket->save();

        Log::info('Ticket Gemini consent updated via API', [
            'ticket_id' => $ticket->id,
            'consent' => $request->gemini_consent,
        ]);

        return response()->json([
            'success' => true,
            'ticket' => $ticket,
        ]);
    }

    /**
     * Generate an AI response for a ticket via API.
     *
     * @param \Illuminate\Http\Request $request
     * @param \App\Models\Ticket $ticket
     * @return \Illuminate\Http\JsonResponse
     */
    public function generateAiResponseApi(Request $request, Ticket $ticket)
    {
        // Check if AI assistance is available
        if (!$this->aiTicketService->isAIAssistanceAvailable($ticket)) {
            return response()->json([
                'success' => false,
                'message' => 'AI assistance is not available for this ticket.',
            ]);
        }

        try {
            // Generate the AI response
            $message = $this->aiTicketService->generateResponse($ticket);

            if ($message) {
                return response()->json([
                    'success' => true,
                    'message' => $message,
                ]);
            } else {
                return response()->json([
                    'success' => false,
                    'message' => 'Failed to generate AI response.',
                ]);
            }
        } catch (\Exception $e) {
            Log::error('Error generating AI response via API', [
                'ticket_id' => $ticket->id,
                'error' => $e->getMessage(),
            ]);

            return response()->json([
                'success' => false,
                'message' => 'An error occurred while generating the AI response.',
            ], 500);
        }
    }

    /**
     * Get a ticket by Discord channel ID.
     *
     * @param \Illuminate\Http\Request $request
     * @param string $channelId
     * @return \Illuminate\Http\JsonResponse
     */
    public function getTicketByChannel(Request $request, $channelId)
    {
        $ticket = Ticket::where('discord_channel_id', $channelId)->first();

        if (!$ticket) {
            return response()->json([
                'success' => false,
                'message' => 'Ticket not found for this channel.',
            ], 404);
        }

        return response()->json([
            'success' => true,
            'ticket' => $ticket,
        ]);
    }
}
