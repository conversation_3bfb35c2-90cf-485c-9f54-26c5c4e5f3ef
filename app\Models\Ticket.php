<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Ticket extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id',
        'title',
        'description',
        'status',
        'discord_channel_id',
        'assigned_to',
        'is_reconstructed',
        'gemini_consent',
        'gemini_consent_at',
    ];

    protected $casts = [
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
        'is_reconstructed' => 'boolean',
        'gemini_consent' => 'boolean',
        'gemini_consent_at' => 'datetime',
    ];

    /**
     * Relationship to the user who created the ticket
     */
    public function user()
    {
        return $this->belongsTo(User::class, 'user_id');
    }

    /**
     * Relationship to the supporter assigned to the ticket
     */
    public function assignedTo()
    {
        return $this->belongsTo(User::class, 'assigned_to');
    }

    /**
     * Relationship to ticket messages
     */
    public function messages()
    {
        return $this->hasMany(TicketMessage::class);
    }

    /**
     * Get the status label
     */
    public function getStatusLabelAttribute()
    {
        return match ($this->status) {
            'open' => __('tickets.status_open'),
            'in_progress' => __('tickets.status_in_progress'),
            'closed' => __('tickets.status_closed'),
            default => $this->status,
        };
    }

    /**
     * Get the status color class
     */
    public function getStatusColorAttribute()
    {
        return match ($this->status) {
            'open' => 'badge-warning',
            'in_progress' => 'badge-info',
            'closed' => 'badge-success',
            default => 'badge-secondary',
        };
    }
}
