<?php $attributes ??= new \Illuminate\View\ComponentAttributeBag;

$__newAttributes = [];
$__propNames = \Illuminate\View\ComponentAttributeBag::extractPropNames((['field' => '']));

foreach ($attributes->all() as $__key => $__value) {
    if (in_array($__key, $__propNames)) {
        $$__key = $$__key ?? $__value;
    } else {
        $__newAttributes[$__key] = $__value;
    }
}

$attributes = new \Illuminate\View\ComponentAttributeBag($__newAttributes);

unset($__propNames);
unset($__newAttributes);

foreach (array_filter((['field' => '']), 'is_string', ARRAY_FILTER_USE_KEY) as $__key => $__value) {
    $$__key = $$__key ?? $__value;
}

$__defined_vars = get_defined_vars();

foreach ($attributes->all() as $__key => $__value) {
    if (array_key_exists($__key, $__defined_vars)) unset($$__key);
}

unset($__defined_vars); ?>

<div class="text-xs text-base-content/70 mt-1">
    <!--[if BLOCK]><![endif]--><?php if($field && __('gdpr.field_explanation.' . $field) !== 'gdpr.field_explanation.' . $field): ?>
        <?php echo e(__('gdpr.field_explanation.' . $field)); ?>

    <?php else: ?>
        <?php echo e(__('gdpr.field_explanation.default')); ?>

    <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
</div>
<?php /**PATH C:\Users\<USER>\Documents\GitHub\minewache-website\resources\views/components/gdpr-field-explanation.blade.php ENDPATH**/ ?>