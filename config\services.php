<?php

return [
    /*
    |--------------------------------------------------------------------------
    | Third Party Services
    |--------------------------------------------------------------------------
    |
    | This file is for storing the credentials for third party services such
    | as Mailgun, Postmark, AWS and more. This file provides the de facto
    | location for this type of information, allowing packages to have
    | a conventional file to locate the various service credentials.
    |
    */

    'postmark' => [
        'token' => env('POSTMARK_TOKEN'),
    ],

    'ses' => [
        'key' => env('AWS_ACCESS_KEY_ID'),
        'secret' => env('AWS_SECRET_ACCESS_KEY'),
        'region' => env('AWS_DEFAULT_REGION', 'us-east-1'),
    ],

    'slack' => [
        'notifications' => [
            'bot_user_oauth_token' => env('SLACK_BOT_USER_OAUTH_TOKEN'),
            'channel' => env('SLACK_BOT_USER_DEFAULT_CHANNEL'),
        ],
    ],

    'discord' => [
        'enabled' => env('DISCORD_ENABLED', true),
        'api_key' => env('DISCORD_API_KEY', ''),
        'guild_id' => env('LARASCORD_GUILD_ID', '1031202173826109591'),
        'client_id' => env('LARASCORD_CLIENT_ID', '1350931744593019002'),
        'client_secret' => env('LARASCORD_CLIENT_SECRET', ''),
        'redirect' => env('LARASCORD_REDIRECT_URI', 'http://localhost:8000/larascord/callback'),
        'bot_url' => env('DISCORD_BOT_API_URL', 'http://localhost:' . env('DISCORD_BOT_PORT', '3001')),
    ],

    'youtube' => [
        'api_key' => env('YOUTUBE_API_KEY', ''),
        'channel_id' => env('YOUTUBE_CHANNEL_ID', 'Die-Minewache'),
    ],

    'gemini' => [
        'api_key' => env('GEMINI_API_KEY', ''),
        'enabled' => env('GEMINI_ENABLED', true),
        'system_user_id' => env('GEMINI_SYSTEM_USER_ID', ''),
        'model_name' => env('GEMINI_MODEL_NAME', 'gemini-1.5-flash-001'),
        'project_id' => env('GEMINI_PROJECT_ID', ''),
        'location' => env('GEMINI_LOCATION', 'us-central1'),
        'rate_limit' => [
            'per_minute' => env('GEMINI_RATE_LIMIT_PER_MINUTE', 10),
            'per_hour' => env('GEMINI_RATE_LIMIT_PER_HOUR', 100),
            'per_day' => env('GEMINI_RATE_LIMIT_PER_DAY', 1000),
            'per_ticket' => env('GEMINI_RATE_LIMIT_PER_TICKET', 5),
        ],
    ],
];
