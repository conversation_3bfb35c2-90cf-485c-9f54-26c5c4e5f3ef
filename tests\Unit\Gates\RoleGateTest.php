<?php

namespace Tests\Unit\Gates;

use App\Enums\Role;
use App\Models\User;
use Illuminate\Auth\Access\Response;
use Illuminate\Support\Facades\Gate;
use Tests\TestCase;

class RoleGateTest extends TestCase
{
    protected function setUp(): void
    {
        parent::setUp();

        // Register the gate for testing
        Gate::define('test-role', function (User $user, $role) {
            // Convert string representation to Role enum if needed
            if (is_string($role) && str_contains($role, 'App\Enums\Role::')) {
                $roleName = substr($role, strrpos($role, ':') + 1);
                $role = constant("App\Enums\Role::$roleName");
            }

            return $user->hasRole($role)
                ? Response::allow()
                : Response::deny("Du benötigst die {$role->label()} Rolle.");
        });
    }

    /** @test */
    public function it_accepts_role_enum()
    {
        // Create a mock user with MINEWACHE_TEAM permissions
        $user = $this->createMock(User::class);
        $user->method('hasRole')->willReturn(true);

        // Test with Role enum
        $response = Gate::forUser($user)->check('test-role', Role::MINEWACHE_TEAM);

        $this->assertTrue($response);
    }

    /** @test */
    public function it_accepts_string_representation_of_role()
    {
        // Create a mock user with MINEWACHE_TEAM permissions
        $user = $this->createMock(User::class);
        $user->method('hasRole')->willReturn(true);

        // Test with string representation of Role enum
        $response = Gate::forUser($user)->check('test-role', 'App\Enums\Role::MINEWACHE_TEAM');

        $this->assertTrue($response);
    }

    /** @test */
    public function it_denies_access_when_user_doesnt_have_role()
    {
        // Create a mock user without MINEWACHE_TEAM permissions
        $user = $this->createMock(User::class);
        $user->method('hasRole')->willReturn(false);

        // Test with Role enum
        $response = Gate::forUser($user)->check('test-role', Role::MINEWACHE_TEAM);

        $this->assertFalse($response);
    }
}
