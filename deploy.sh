#!/bin/bash

# MineWache Website - Production Deployment Script for Ubuntu 24.0.1
# Place this file in the root directory of your Laravel project

# --- Configuration Variables ---
# Define paths and users here for easier modification
readonly LOCK_FILE="/tmp/minewache-deploy.lock"
readonly LOG_FILE="/var/log/minewache-deploy.log"
readonly WEB_USER="www-data"
readonly WEB_GROUP="www-data"
readonly PROJECT_DIR=$(pwd) # Assuming script is run from project root
readonly BOT_DIR="${PROJECT_DIR}/discord-bot"
readonly REVERB_SERVICE_NAME="minewache-reverb"
readonly QUEUE_SERVICE_NAME="minewache-queue"
readonly DISCORD_ROLE_SERVICE_NAME="minewache-discord-role"
readonly DISCORD_TICKET_SERVICE_NAME="minewache-discord-ticket"
# --- End Configuration Variables ---

# Exit immediately if a command exits with a non-zero status
set -euo pipefail

# Ensure script is not sourced
if [[ "${BASH_SOURCE[0]}" != "${0}" ]]; then
    echo "Script must not be sourced"
    exit 1
fi

# Lock file to prevent multiple simultaneous deployments
LOCK_FD=200

# Function to release lock
function release_lock() {
    flock -u "$LOCK_FD"
    rm -f "$LOCK_FILE"
}

# Try to acquire lock
exec 200>"$LOCK_FILE"
if ! flock -n "$LOCK_FD"; then
    echo "Another deployment is already running. Please wait."
    exit 1
fi

# Ensure lock is released on script exit
trap release_lock EXIT

# Function to display colored messages
function echo_color() {
    local color=$1
    local message=$2

    case $color in
        "green") echo -e "\e[32m$message\e[0m" ;;
        "yellow") echo -e "\e[33m$message\e[0m" ;;
        "red") echo -e "\e[31m$message\e[0m" ;;
        "blue") echo -e "\e[34m$message\e[0m" ;;
        *) echo "$message" ;;
    esac
}

# Function to log messages
function log_message() {
    local level=$1
    local message=$2
    local log_dir=$(dirname "$LOG_FILE")

    # Create log directory and file with proper permissions if they don't exist
    if [ ! -d "$log_dir" ]; then
        if command -v sudo >/dev/null 2>&1 && sudo -n true 2>/dev/null; then
            sudo mkdir -p "$log_dir"
            sudo chown "$WEB_USER:$WEB_GROUP" "$log_dir"
            sudo chmod 775 "$log_dir"
        else
            echo_color "red" "Cannot create log directory $log_dir without sudo."
            # Fallback or exit? For now, continue, logging might fail.
        fi
    fi

    if [ ! -f "$LOG_FILE" ]; then
        if command -v sudo >/dev/null 2>&1 && sudo -n true 2>/dev/null; then
            sudo touch "$LOG_FILE"
            sudo chown "$WEB_USER:$WEB_GROUP" "$LOG_FILE"
            sudo chmod 664 "$LOG_FILE"
        else
            # Fallback to logging in the current directory if no sudo rights
            # This might not be ideal if script runs as root initially
            local fallback_log="./deploy.log"
            touch "$fallback_log"
            echo_color "yellow" "Cannot create $LOG_FILE without sudo. Logging to $fallback_log instead."
            LOG_FILE="$fallback_log" # Update variable for this run
        fi
    fi

    # Write to log file if writable, otherwise just display to console
    if [ -w "$LOG_FILE" ]; then
        echo "[$(date '+%Y-%m-%d %H:%M:%S')] [$level] $message" >> "$LOG_FILE"
    else
         echo_color "yellow" "Warning: Cannot write to log file $LOG_FILE."
    fi

    # Always display to console
    echo_color "$level" "$message"
}

# Function to check and install system dependencies
function check_and_install_dependencies() {
    log_message "blue" "Checking system dependencies..."
    local missing_deps=()

    # Check for Composer
    if ! command -v composer &> /dev/null; then
        log_message "yellow" "Composer not found. Attempting to install..."
        if command -v sudo >/dev/null 2>&1 && sudo -n true 2>/dev/null; then
            sudo apt-get update && sudo apt-get install -y composer
            if ! command -v composer &> /dev/null; then
                missing_deps+=("Composer")
            else
                log_message "green" "Composer installed successfully."
            fi
        else
            missing_deps+=("Composer (sudo required for installation)")
        fi
    else
        log_message "green" "Composer found: $(composer --version)"
    fi

    # Check for Node.js and npm
    if ! command -v node &> /dev/null || ! command -v npm &> /dev/null; then
        log_message "yellow" "Node.js or npm not found. Attempting to install Node.js (which includes npm)..."
        if command -v sudo >/dev/null 2>&1 && sudo -n true 2>/dev/null; then
            # Install Node.js (e.g., LTS version using NodeSource setup)
            # Check if curl is installed
            if ! command -v curl &> /dev/null; then
                sudo apt-get update && sudo apt-get install -y curl
            fi
            # Security Note: Piping curl to bash executes remote code. Ensure you trust the source.
            curl -fsSL https://deb.nodesource.com/setup_lts.x | sudo -E bash -
            sudo apt-get install -y nodejs
            if ! command -v node &> /dev/null || ! command -v npm &> /dev/null; then
                missing_deps+=("Node.js/npm")
            else
                log_message "green" "Node.js and npm installed successfully."
            fi
        else
            missing_deps+=("Node.js/npm (sudo required for installation)")
        fi
    else
        log_message "green" "Node.js found: $(node -v)"
        log_message "green" "npm found: $(npm -v)"
    fi

    # Check for FFmpeg
    if ! command -v ffmpeg &> /dev/null; then
        log_message "yellow" "FFmpeg not found. Attempting to install..."
        if command -v sudo >/dev/null 2>&1 && sudo -n true 2>/dev/null; then
            sudo apt-get update && sudo apt-get install -y ffmpeg
            if ! command -v ffmpeg &> /dev/null; then
                missing_deps+=("FFmpeg")
            else
                log_message "green" "FFmpeg installed successfully."
            fi
        else
            missing_deps+=("FFmpeg (sudo required for installation)")
        fi
    else
        log_message "green" "FFmpeg found."
    fi

    # Check for FFprobe (usually installed with FFmpeg)
    if ! command -v ffprobe &> /dev/null; then
        log_message "yellow" "FFprobe not found. Attempting to install (usually comes with ffmpeg)..."
        if command -v sudo >/dev/null 2>&1 && sudo -n true 2>/dev/null; then
            sudo apt-get update && sudo apt-get install -y ffmpeg # Re-attempt ffmpeg install
            if ! command -v ffprobe &> /dev/null; then
                missing_deps+=("FFprobe")
            else
                log_message "green" "FFprobe installed successfully."
            fi
        else
            missing_deps+=("FFprobe (sudo required for installation)")
        fi
    else
        log_message "green" "FFprobe found."
    fi

    # Check for Ghostscript (gs)
    if ! command -v gs &> /dev/null; then
        log_message "yellow" "Ghostscript (gs) not found. Attempting to install..."
        if command -v sudo >/dev/null 2>&1 && sudo -n true 2>/dev/null; then
            sudo apt-get update && sudo apt-get install -y ghostscript
            if ! command -v gs &> /dev/null; then
                missing_deps+=("Ghostscript")
            else
                log_message "green" "Ghostscript installed successfully."
            fi
        else
            missing_deps+=("Ghostscript (sudo required for installation)")
        fi
    else
        log_message "green" "Ghostscript found."
    fi

    # Report missing dependencies
    if [ ${#missing_deps[@]} -ne 0 ]; then
        log_message "red" "The following dependencies could not be found or installed: ${missing_deps[*]}"
        log_message "red" "Please install them manually and re-run the script."
        exit 1
    else
        log_message "green" "All required system dependencies are present."
    fi
}

# Function to check system requirements
function check_system_requirements() {
    log_message "blue" "Checking system requirements..."

    # Check disk space
    local free_space_kb=$(df -k . | awk 'NR==2 {print $4}')
    local free_space_mb=$((free_space_kb / 1024))
    if [ "$free_space_mb" -lt 1000 ]; then
        log_message "red" "Insufficient disk space. At least 1GB required."
        exit 1
    fi

    # Check memory
    local free_memory=$(free -m | awk '/^Mem:/ {print $4}')
    if [ "$free_memory" -lt 512 ]; then
        log_message "yellow" "Warning: Low memory available ($free_memory MB)"
    fi

    # Check PHP memory limit
    local php_memory_limit=$(php -r 'echo ini_get("memory_limit");')
    # Remove 'M' from memory limit and compare
    php_memory_limit_mb=$(echo "$php_memory_limit" | sed 's/[MmGg]//') # Handle M or G
    local php_memory_limit_unit=$(echo "$php_memory_limit" | sed 's/[0-9]//g')

    if [[ "$php_memory_limit" != "-1" ]]; then
        if [[ "$php_memory_limit_unit" == "G" || "$php_memory_limit_unit" == "g" ]]; then
            php_memory_limit_mb=$((php_memory_limit_mb * 1024))
        fi
        if [[ "$php_memory_limit_mb" -lt 256 ]]; then
            log_message "yellow" "Warning: PHP memory limit might be too low ($php_memory_limit)"
        fi
    fi
}

# Function to validate environment
function validate_environment() {
    # Removed environment validation
    return 0
}

# Function to check database connection
function check_database() {
    log_message "yellow" "Checking database configuration and connection..."

    # Get default connection and SQLite path using php -r
    local default_connection=$(php -r "require __DIR__.'/bootstrap/app.php'; echo config('database.default');")
    local sqlite_db_path_raw=$(php -r "require __DIR__.'/bootstrap/app.php'; echo config('database.connections.sqlite.database');")

    # Check if commands were successful
    if [ -z "$default_connection" ]; then
        log_message "red" "Failed to get default database connection from Laravel config."
        exit 1
    fi
     if [ -z "$sqlite_db_path_raw" ]; then
        log_message "yellow" "Could not retrieve SQLite database path (maybe not configured or using different driver)."
        # Continue if not using sqlite, otherwise this might be an issue later
    fi


    # Check if using SQLite and if the file exists
    if [[ "$default_connection" == "sqlite" ]]; then
        log_message "blue" "Default connection is SQLite. Checking database file: $sqlite_db_path_raw"
        local sqlite_db_path="$sqlite_db_path_raw" # Use the retrieved path
        # Ensure the path is absolute or relative to the project root
        if [[ "$sqlite_db_path" != /* ]]; then
            sqlite_db_path="${PROJECT_DIR}/${sqlite_db_path}"
        fi

        # ... rest of the SQLite file/directory check logic ...
        local db_dir=$(dirname "$sqlite_db_path")

        # Check if the directory exists, create if not
        if [ ! -d "$db_dir" ]; then
            log_message "yellow" "Database directory does not exist. Creating: $db_dir"
            # Attempt creation without sudo first
            mkdir -p "$db_dir" || {
                # If failed, try with sudo
                if command -v sudo >/dev/null 2>&1 && sudo -n true 2>/dev/null; then
                    log_message "yellow" "Retrying directory creation with sudo..."
                    sudo mkdir -p "$db_dir"
                    sudo chown "$WEB_USER:$WEB_GROUP" "$db_dir" # Set ownership if created with sudo
                    sudo chmod 775 "$db_dir" # Set permissions for directory
                else
                    log_message "red" "Failed to create database directory $db_dir. Sudo might be required."
                    exit 1
                fi
            }
            # Ensure permissions even if created without sudo initially
            if command -v sudo >/dev/null 2>&1 && sudo -n true 2>/dev/null; then
                 sudo chown "$WEB_USER:$WEB_GROUP" "$db_dir"
                 sudo chmod 775 "$db_dir"
            fi
        else
             # Ensure correct permissions even if directory exists
             if command -v sudo >/dev/null 2>&1 && sudo -n true 2>/dev/null; then
                 sudo chown "$WEB_USER:$WEB_GROUP" "$db_dir"
                 sudo chmod 775 "$db_dir"
             else
                 log_message "yellow" "Cannot verify/set permissions for existing database directory without sudo."
             fi
        fi

        # Check if the database file exists, create if not
        if [ ! -f "$sqlite_db_path" ]; then
            log_message "yellow" "SQLite database file does not exist. Creating: $sqlite_db_path"
            # Attempt creation without sudo first
            touch "$sqlite_db_path" && chmod 664 "$sqlite_db_path" || {
                # If failed, try with sudo
                if command -v sudo >/dev/null 2>&1 && sudo -n true 2>/dev/null; then
                    log_message "yellow" "Retrying file creation with sudo..."
                    sudo touch "$sqlite_db_path"
                    sudo chown "$WEB_USER:$WEB_GROUP" "$sqlite_db_path" # Set ownership
                    sudo chmod 664 "$sqlite_db_path" # Set permissions
                else
                    log_message "red" "Failed to create SQLite file $sqlite_db_path. Sudo might be required."
                    exit 1
                fi
            }
             # Ensure permissions even if created without sudo initially
            if command -v sudo >/dev/null 2>&1 && sudo -n true 2>/dev/null; then
                 sudo chown "$WEB_USER:$WEB_GROUP" "$sqlite_db_path"
                 sudo chmod 664 "$sqlite_db_path"
            fi
            log_message "green" "SQLite database file created."
        else
             log_message "green" "SQLite database file found."
             # Ensure correct permissions even if file exists
             if command -v sudo >/dev/null 2>&1 && sudo -n true 2>/dev/null; then
                 sudo chown "$WEB_USER:$WEB_GROUP" "$sqlite_db_path"
                 sudo chmod 664 "$sqlite_db_path"
             else
                 log_message "yellow" "Cannot verify/set permissions for existing SQLite file without sudo."
             fi
        fi
    else
        log_message "blue" "Default connection is '$default_connection'. Assuming database exists and permissions are correct."
    fi

    # Now, test the actual connection using Laravel
    log_message "yellow" "Testing database connection via Laravel..."
    if ! php artisan db:show --json > /dev/null 2>&1; then
        log_message "red" "Database connection failed using Laravel configuration."
        log_message "yellow" "Possible solutions:"
        echo "1. Check database credentials in .env"
        echo "2. Ensure database server is running (for MySQL/MariaDB etc.)"
        echo "3. Check network connectivity to database server"
        echo "4. Ensure the database user has correct permissions"
        if [[ "$default_connection" == "sqlite" ]]; then
             echo "5. For SQLite, check file permissions for '$sqlite_db_path'"
        fi
        exit 1
    else
        log_message "green" "Database connection successful."
    fi
}

# Function to set correct file permissions and ownership
function set_permissions() {
    log_message "blue" "Setting file permissions and ownership..."

    # Check for sudo access
    if ! command -v sudo >/dev/null 2>&1 || ! sudo -n true 2>/dev/null; then
        log_message "red" "Sudo access required to set permissions correctly. Skipping."
        log_message "yellow" "Please ensure storage/, bootstrap/cache/, and potentially the SQLite file are writable by the web server user (e.g., $WEB_USER)."
        return 1 # Indicate potential failure
    fi

    # Set ownership for storage and bootstrap/cache
    log_message "yellow" "Setting ownership for storage and bootstrap/cache to ${WEB_USER}:${WEB_GROUP}..."
    sudo chown -R "${WEB_USER}:${WEB_GROUP}" "${PROJECT_DIR}/storage" "${PROJECT_DIR}/bootstrap/cache"

    # Set ownership for database directory (if it exists)
    if [ -d "${PROJECT_DIR}/database" ]; then
        log_message "yellow" "Setting ownership for database directory to ${WEB_USER}:${WEB_GROUP}..."
        sudo chown -R "${WEB_USER}:${WEB_GROUP}" "${PROJECT_DIR}/database"
    fi

    # Set general directory permissions (755) - Find might be slow on large projects
    log_message "yellow" "Setting directory permissions to 755..."
    sudo find "$PROJECT_DIR" -type d -print0 | sudo xargs -0 chmod 755

    # Set general file permissions (644)
    log_message "yellow" "Setting file permissions to 644..."
    sudo find "$PROJECT_DIR" -type f -print0 | sudo xargs -0 chmod 644

    # Set specific writable permissions for storage and bootstrap/cache (775 for dirs, 664 for files)
    log_message "yellow" "Setting writable permissions for storage and bootstrap/cache..."
    sudo find "${PROJECT_DIR}/storage" -type d -print0 | sudo xargs -0 chmod 775
    sudo find "${PROJECT_DIR}/bootstrap/cache" -type d -print0 | sudo xargs -0 chmod 775
    sudo find "${PROJECT_DIR}/storage" -type f -print0 | sudo xargs -0 chmod 664
    sudo find "${PROJECT_DIR}/bootstrap/cache" -type f -print0 | sudo xargs -0 chmod 664

    # Set specific writable permissions for database directory and file (if they exist)
    if [ -d "${PROJECT_DIR}/database" ]; then
        log_message "yellow" "Setting writable permissions for database directory..."
        sudo find "${PROJECT_DIR}/database" -type d -print0 | sudo xargs -0 chmod 775
        # Set write permission for the sqlite file itself
        if [ -f "${PROJECT_DIR}/database/database.sqlite" ]; then
             log_message "yellow" "Setting writable permissions for database/database.sqlite..."
             sudo chmod 664 "${PROJECT_DIR}/database/database.sqlite"
        fi
        # Set write permission for any journal files SQLite might create
        sudo find "${PROJECT_DIR}/database" -name 'database.sqlite-journal' -type f -print0 | sudo xargs -0 chmod 664
        sudo find "${PROJECT_DIR}/database" -name 'database.sqlite-wal' -type f -print0 | sudo xargs -0 chmod 664
        sudo find "${PROJECT_DIR}/database" -name 'database.sqlite-shm' -type f -print0 | sudo xargs -0 chmod 664
    fi

    # Ensure deploy script and related scripts remain executable by the user running them
    # (Assuming the user running deploy.sh needs to execute stop-app.sh etc.)
    chmod +x "${PROJECT_DIR}/deploy.sh"
    chmod +x "${PROJECT_DIR}/stop-app.sh"
    # Add other scripts if needed

    log_message "green" "File permissions and ownership set."
    return 0
}

# Function to check and fix common Laravel issues
function check_laravel_health() {
    log_message "blue" "Checking Laravel health..."
    # Check storage link
    if [ ! -L "${PROJECT_DIR}/public/storage" ]; then
        log_message "yellow" "Storage symlink missing. Creating..."
        # Run as web user to ensure correct ownership if link needs creation within web root
        if command -v sudo >/dev/null 2>&1 && sudo -n true 2>/dev/null; then
            sudo -u "$WEB_USER" php artisan storage:link
        else
            log_message "yellow" "Cannot run storage:link as $WEB_USER without sudo. Running as current user."
            php artisan storage:link
        fi
    fi

    # Check Laravel logs for errors (optional, can be noisy)
    # if [ -f "${PROJECT_DIR}/storage/logs/laravel.log" ]; then
    #     local error_count=$(grep -c "ERROR" "${PROJECT_DIR}/storage/logs/laravel.log")
    #     if [ "$error_count" -gt 0 ]; then
    #         log_message "yellow" "Warning: $error_count errors found in Laravel log"
    #     fi
    # fi

    # Check Reverb configuration
    log_message "yellow" "Checking Reverb configuration in .env..."
    local env_file="${PROJECT_DIR}/.env"

    if [ ! -f "$env_file" ]; then
        log_message "red" ".env file not found. Cannot check or update configuration."
        return 1
    fi

    # Check BROADCAST_CONNECTION
    if ! grep -q "^BROADCAST_CONNECTION=reverb" "$env_file"; then
        log_message "yellow" "Setting BROADCAST_CONNECTION to reverb in .env (if it exists)..."
        # Use sed to replace if line exists, otherwise append (safer than simple replace)
        if grep -q "^BROADCAST_CONNECTION=" "$env_file"; then
            sed -i 's/^BROADCAST_CONNECTION=.*/BROADCAST_CONNECTION=reverb/' "$env_file"
        else
            echo "BROADCAST_CONNECTION=reverb" >> "$env_file"
        fi
    fi

    # Check if Reverb environment variables exist (APP_ID only as a marker)
    if ! grep -q "^REVERB_APP_ID=" "$env_file"; then
        log_message "yellow" "Adding Reverb configuration placeholders to .env"
        log_message "red" "SECURITY WARNING: Default Reverb secrets added. Replace these with secure, generated values!"
        # Add placeholders - user MUST replace these
        cat >> "$env_file" << EOL

# Reverb-Konfiguration (PLACEHOLDERS - PLEASE REPLACE WITH SECURE VALUES)
REVERB_APP_ID="minewache_app_placeholder"
REVERB_APP_KEY="minewache_key_placeholder"
REVERB_APP_SECRET="minewache_secret_placeholder"
REVERB_HOST="localhost"
REVERB_PORT=8080
REVERB_SCHEME="http"

# Reverb Server Konfiguration
REVERB_SERVER_HOST="127.0.0.1"
REVERB_SERVER_PORT=6001
EOL
    fi

    # Check if Vite Reverb environment variables exist (VITE_REVERB_APP_KEY as marker)
    if ! grep -q "^VITE_REVERB_APP_KEY=" "$env_file"; then
        log_message "yellow" "Adding Vite Reverb configuration to .env"
        cat >> "$env_file" << EOL

# Vite Reverb Konfiguration
VITE_REVERB_APP_KEY="\${REVERB_APP_KEY}"
VITE_REVERB_HOST="\${REVERB_HOST}"
VITE_REVERB_PORT="\${REVERB_PORT}"
VITE_REVERB_SCHEME="\${REVERB_SCHEME}"
EOL
    fi
}

# Function to setup and manage systemd services
function setup_systemd_service() {
    local service_name=$1
    local description=$2
    local user=$3 # Should be WEB_USER
    local group=$4 # Should be WEB_GROUP
    local working_dir=$5 # Should be PROJECT_DIR or BOT_DIR
    local exec_start=$6
    local service_file_path="/etc/systemd/system/${service_name}.service"
    local service_log_file="/var/log/${service_name}.log"
    local service_error_log_file="/var/log/${service_name}-error.log"

    log_message "blue" "Setting up systemd service: ${service_name}..."

    # Check for sudo access
    if ! command -v sudo >/dev/null 2>&1 || ! sudo -n true 2>/dev/null; then
        log_message "red" "Sudo access required to manage systemd services. Skipping ${service_name}."
        return 1
    fi

    # Create service file content
    # Note: Consider adding EnvironmentFile=/path/to/your/project/.env if services need it
    local service_content="[Unit]
Description=${description}
After=network.target mysql.service # Add other dependencies like redis if needed

[Service]
User=${user}
Group=${group}
WorkingDirectory=${working_dir}
ExecStart=${exec_start}
Restart=always
RestartSec=5
TimeoutStopSec=60
StandardOutput=append:${service_log_file}
StandardError=append:${service_error_log_file}
# Consider adding EnvironmentFile=${PROJECT_DIR}/.env if needed

[Install]
WantedBy=multi-user.target"

    # Write service file using sudo tee
    echo -e "$service_content" | sudo tee "$service_file_path" > /dev/null

    # Ensure log files exist and have correct permissions
    sudo touch "$service_log_file" "$service_error_log_file"
    sudo chown "$user:$group" "$service_log_file" "$service_error_log_file"
    sudo chmod 664 "$service_log_file" "$service_error_log_file"

    # Reload systemd, enable and restart the service
    log_message "yellow" "Reloading systemd daemon..."
    sudo systemctl daemon-reload

    log_message "yellow" "Enabling ${service_name} service..."
    sudo systemctl enable "${service_name}.service"

    log_message "yellow" "Restarting ${service_name} service..."
    sudo systemctl restart "${service_name}.service"

    # Verify service status
    sleep 2 # Give the service a moment to start
    if ! sudo systemctl is-active --quiet "${service_name}.service"; then
        log_message "red" "${service_name} service failed to start."
        log_message "yellow" "Check logs with: sudo journalctl -u ${service_name} -n 50 --no-pager or check ${service_log_file} / ${service_error_log_file}"
        # Consider adding rollback or exit here if critical
        return 1
    else
        log_message "green" "${service_name} service started successfully."
    fi
    return 0
}

# Function to handle Discord bot deployment
function deploy_discord_bot() {
    log_message "blue" "Deploying Discord bot..."

    if [ ! -d "$BOT_DIR" ]; then
        log_message "red" "Discord bot directory not found at $BOT_DIR"
        return 1
    fi

    # Check Node.js version compatibility (ensure node is available)
    if ! command -v node >/dev/null 2>&1; then
         log_message "red" "Node.js is required but not found."
         return 1
    fi
    local required_node_version="18.0.0"
    local current_node_version=$(node -v | sed 's/v//')
    if [[ "$(printf '%s\n' "$required_node_version" "$current_node_version" | sort -V | head -n1)" != "$required_node_version" ]]; then
        log_message "red" "Node.js $required_node_version or higher required (found v$current_node_version)"
        return 1
    fi

    # Backup existing Discord bot configuration
    if [ -f "$BOT_DIR/.env" ]; then
        cp "$BOT_DIR/.env" "$BOT_DIR/.env.backup.$(date +%Y%m%d_%H%M%S)"
        log_message "green" "Backed up existing discord-bot/.env"
    fi

    # Install dependencies as the web user
    log_message "yellow" "Installing Discord bot dependencies as user $WEB_USER..."
    cd "$BOT_DIR"
    # Ensure the directory and its contents are owned by WEB_USER first
    if command -v sudo >/dev/null 2>&1 && sudo -n true 2>/dev/null; then
        sudo chown -R "$WEB_USER:$WEB_GROUP" "$BOT_DIR"
        if ! sudo -u "$WEB_USER" npm ci --only=production; then
            log_message "red" "Failed to install Discord bot dependencies as $WEB_USER"
            cd ..
            return 1
        fi
    else
        log_message "yellow" "Cannot run npm ci as $WEB_USER without sudo. Running as current user."
        if ! npm ci --only=production; then
            log_message "red" "Failed to install Discord bot dependencies"
            cd ..
            return 1
        fi
    fi

    # Sync environment variables (run as current user, assumes it reads project .env)
    log_message "yellow" "Syncing Discord bot environment..."
    if ! node sync-env.js; then
        log_message "red" "Failed to sync Discord bot environment"
        cd ..
        return 1
    fi
    cd .. # Return to project root

    # Setup systemd services for both Discord bot components
    local node_path=$(command -v node)

    # Setup Role Bot service
    local role_bot_script="$BOT_DIR/index.js"
    if ! setup_systemd_service "$DISCORD_ROLE_SERVICE_NAME" "MineWache Discord Role Bot" "$WEB_USER" "$WEB_GROUP" "$BOT_DIR" "$node_path $role_bot_script"; then
        log_message "red" "Failed to setup/start Discord Role Bot service."
        return 1
    fi

    # Setup Ticket Bot service
    local ticket_bot_script="$BOT_DIR/index-tickets.js"
    if ! setup_systemd_service "$DISCORD_TICKET_SERVICE_NAME" "MineWache Discord Ticket Bot" "$WEB_USER" "$WEB_GROUP" "$BOT_DIR" "$node_path $ticket_bot_script"; then
        log_message "red" "Failed to setup/start Discord Ticket Bot service."
        return 1
    fi

    return 0
}

# Function to handle backup (Currently Disabled)
# function create_backup() {
#     # NOTE: This function is disabled.
#     # If re-enabled, ensure it performs a DATA backup, not just schema.
#     # For SQLite: Use `sqlite3 database.sqlite .dump | gzip > backup.sql.gz`
#     # For MySQL: Use `mysqldump ... | gzip > backup.sql.gz`
#     log_message "blue" "Creating backup..."
#     local backup_dir="/var/backups/minewache"
#     local timestamp=$(date +%Y%m%d_%H%M%S)
#
#     # Create backup directory if it doesn't exist
#     sudo mkdir -p "$backup_dir"
#     sudo chown "$WEB_USER:$WEB_GROUP" "$backup_dir"
#     sudo chmod 775 "$backup_dir"
#
#     # Database backup (SCHEMA ONLY - NEEDS FIXING FOR DATA)
#     log_message "yellow" "Creating database SCHEMA backup (DATA backup needed)..."
#
#     # Check if sqlite3 command is available
#     if command -v sqlite3 &> /dev/null; then
#         # Use Laravel's built-in database dump command (SCHEMA ONLY)
#         sudo mkdir -p "$backup_dir/schema"
#         sudo chown "$WEB_USER:$WEB_GROUP" "$backup_dir/schema"
#         sudo chmod 775 "$backup_dir/schema"
#         php artisan schema:dump --path="$backup_dir/schema" --prune
#     else
#         log_message "yellow" "sqlite3 command not found. Skipping database schema dump."
#         log_message "yellow" "Consider installing sqlite3 with: sudo apt-get install sqlite3"
#         touch "$backup_dir/db_backup_$timestamp.txt"
#         echo "Database schema backup skipped - sqlite3 not installed" > "$backup_dir/db_backup_$timestamp.txt"
#     fi
#
#     # Files backup
#     log_message "yellow" "Creating files backup..."
#     tar -czf "$backup_dir/files_backup_$timestamp.tar.gz" \
#         --exclude="./node_modules" \
#         --exclude="./vendor" \
#         --exclude="./.git" \
#         --exclude="./storage/logs" \
#         .
#
#     log_message "green" "Backup created successfully in $backup_dir"
# }

# Function to handle rollback
function rollback() {
    log_message "red" "Deployment failed, initiating rollback..."

    # Restore database (COMMENTED OUT - migrate:fresh is DESTRUCTIVE)
    # A real rollback requires restoring from a proper data backup.
    # if [ -f "$DB_BACKUP" ]; then
    #     log_message "yellow" "Attempting database rollback (using migrate:fresh - DATA LOSS WARNING)..."
    #     # php artisan migrate:fresh --force # DANGEROUS - Clears all data
    #     log_message "red" "Database rollback skipped. Manual restore from backup required."
    # fi

    # Restore code from git if available
    if [ -n "${PREVIOUS_COMMIT:-}" ]; then # Check if PREVIOUS_COMMIT is set
        log_message "yellow" "Rolling back code to previous commit: $PREVIOUS_COMMIT"
        git reset --hard "$PREVIOUS_COMMIT"
        # Re-run composer/npm install and build if necessary after reset? Maybe too complex for basic rollback.
        log_message "yellow" "Code rolled back. Dependencies might need reinstalling (composer install, npm ci, npm run build)."
    else
        log_message "yellow" "No previous commit recorded, cannot automatically roll back code."
    fi

    # Restore Discord bot configuration
    # Find the latest backup
    local latest_bot_env_backup=$(ls -t "$BOT_DIR/.env.backup."* 2>/dev/null | head -n 1)
    if [ -f "$latest_bot_env_backup" ]; then
        log_message "yellow" "Restoring Discord bot .env from $latest_bot_env_backup"
        mv "$latest_bot_env_backup" "$BOT_DIR/.env"
    fi

    # Restart services to reflect rolled-back code/config
    log_message "yellow" "Attempting to restart services with potentially rolled-back code..."
    if command -v sudo >/dev/null 2>&1 && sudo -n true 2>/dev/null; then
        sudo systemctl restart "$REVERB_SERVICE_NAME" || log_message "yellow" "Failed to restart $REVERB_SERVICE_NAME (might not exist yet)"
        sudo systemctl restart "$QUEUE_SERVICE_NAME" || log_message "yellow" "Failed to restart $QUEUE_SERVICE_NAME (might not exist yet)"
        sudo systemctl restart "$DISCORD_ROLE_SERVICE_NAME" || log_message "yellow" "Failed to restart $DISCORD_ROLE_SERVICE_NAME (might not exist yet)"
        sudo systemctl restart "$DISCORD_TICKET_SERVICE_NAME" || log_message "yellow" "Failed to restart $DISCORD_TICKET_SERVICE_NAME (might not exist yet)"
    else
        log_message "yellow" "Cannot restart services without sudo."
    fi

    log_message "red" "Rollback attempted. Manual verification required."
    # NOTE: A more robust rollback strategy involves deploying to separate directories
    # and switching a symlink (e.g., using Capistrano, Deployer, or custom scripts).
    # This allows for instant rollbacks without destructive operations.
}


# Main deployment logic
function main() {
    log_message "blue" "Starting deployment process for project in $PROJECT_DIR..."

    # Store initial state for potential rollback
    PREVIOUS_COMMIT=$(git rev-parse HEAD)
    # DB_BACKUP variable is defined but backup function is disabled
    # DB_BACKUP="/tmp/db_backup_$(date +%Y%m%d_%H%M%S).sql"

    # Create backup (DISABLED)
    # create_backup

    # Run checks
    check_system_requirements
    check_and_install_dependencies
    # validate_environment # Consider if this is still needed
    check_database
    check_laravel_health

    # Pull latest changes
    log_message "yellow" "Pulling latest changes from git..."
    git pull

    # Install/update Composer dependencies as web user
    log_message "yellow" "Installing Composer dependencies as user $WEB_USER..."
    if command -v sudo >/dev/null 2>&1 && sudo -n true 2>/dev/null; then
        # Ensure vendor dir is writable by web user if it exists
        if [ -d "${PROJECT_DIR}/vendor" ]; then
             sudo chown -R "$WEB_USER:$WEB_GROUP" "${PROJECT_DIR}/vendor"
        fi
        sudo -u "$WEB_USER" composer install --no-interaction --prefer-dist --optimize-autoloader --no-dev
    else
        log_message "yellow" "Cannot run composer install as $WEB_USER without sudo. Running as current user."
        composer install --no-interaction --prefer-dist --optimize-autoloader --no-dev
    fi


    # Install/update NPM dependencies as web user
    log_message "yellow" "Installing NPM dependencies as user $WEB_USER..."
     if command -v sudo >/dev/null 2>&1 && sudo -n true 2>/dev/null; then
         # Ensure node_modules dir is writable by web user if it exists
         if [ -d "${PROJECT_DIR}/node_modules" ]; then
             sudo chown -R "$WEB_USER:$WEB_GROUP" "${PROJECT_DIR}/node_modules"
         fi
         # Ensure package-lock.json is readable
         if [ -f "${PROJECT_DIR}/package-lock.json" ]; then
             sudo chown "$WEB_USER:$WEB_GROUP" "${PROJECT_DIR}/package-lock.json"
         fi
         sudo -u "$WEB_USER" npm ci
     else
        log_message "yellow" "Cannot run npm ci as $WEB_USER without sudo. Running as current user."
        npm ci
     fi

    # Build frontend assets as the CURRENT user (not www-data)
    log_message "yellow" "Building frontend assets..."
    # Removed sudo -u "$WEB_USER" to run as the user executing the script
    if ! npm run build; then
        log_message "red" "Failed to build frontend assets."
        rollback
        exit 1
    fi

    # Set permissions AFTER pulling code and installing dependencies AND building assets
    # This ensures generated files (like vendor/, node_modules/, public/build/) have correct permissions.
    if ! set_permissions; then
        log_message "red" "Failed to set permissions. Aborting deployment."
        # Optionally attempt rollback or just exit
        rollback # Attempt rollback on permission failure
        exit 1
    fi

    # Run database migrations
    log_message "yellow" "Running database migrations..."
    log_message "yellow" "Ensure you have a database backup before running migrations in production!"
    php artisan migrate --force

    # Clear caches
    log_message "yellow" "Clearing application caches..."
    php artisan optimize:clear
    # Cache config and routes for production performance
    log_message "yellow" "Caching configuration and routes..."
    php artisan config:cache
    php artisan route:cache
    # Allow view:cache to fail without stopping the script
    log_message "yellow" "Caching views (allowing potential component errors)..."
    php artisan view:cache || {
        log_message "yellow" "View caching failed (likely missing component), continuing deployment..."
        true # Ensure this block returns success
    }

    # Setup/Restart Reverb service
    local php_path=$(command -v php)
    if ! setup_systemd_service "$REVERB_SERVICE_NAME" "MineWache Reverb WebSocket Server" "$WEB_USER" "$WEB_GROUP" "$PROJECT_DIR" "$php_path artisan reverb:start --host=127.0.0.1 --port=6001"; then
        log_message "red" "Reverb service setup/restart failed."
        rollback
        exit 1
    fi

    # Setup/Restart Queue Worker service
    # Adjust --queue, --sleep, --tries, --timeout as needed
    if ! setup_systemd_service "$QUEUE_SERVICE_NAME" "MineWache Laravel Queue Worker" "$WEB_USER" "$WEB_GROUP" "$PROJECT_DIR" "$php_path artisan queue:work --queue=default,media --sleep=3 --tries=3 --timeout=3600"; then
        log_message "red" "Queue worker service setup/restart failed."
        rollback
        exit 1
    fi

    # Deploy Discord bot (which now also uses setup_systemd_service)
    if ! deploy_discord_bot; then
        log_message "red" "Discord bot deployment failed"
        rollback
        exit 1
    fi

    log_message "green" "Deployment completed successfully!"
    log_message "blue" "Services running: $REVERB_SERVICE_NAME, $QUEUE_SERVICE_NAME, $DISCORD_ROLE_SERVICE_NAME, $DISCORD_TICKET_SERVICE_NAME"
    log_message "blue" "You can check their status with: sudo systemctl status <service_name>"
    log_message "blue" "Discord bot logs are available at: /var/log/$DISCORD_ROLE_SERVICE_NAME.log and /var/log/$DISCORD_TICKET_SERVICE_NAME.log"
}

# Error handling
function handle_error() {
    local exit_code=$?
    local line_number=$1

    log_message "red" "Error occurred at line $line_number (Exit code: $exit_code)"

    # Note: Relying solely on exit codes for specific error types can be unreliable.
    # More robust error handling would involve checking command output directly where possible.
    case $exit_code in
        1)
            # General error, could be git, permissions, etc.
            # Git conflict check (already present, good)
            if [[ -n $(git status --porcelain) ]]; then
                log_message "yellow" "Potential Git conflict or uncommitted changes detected."
                # Attempting stash/pull/pop is risky automatically. Manual intervention preferred.
                log_message "red" "Please resolve git issues manually and retry deployment."
                # rollback # Optionally rollback on git issues
                exit 1
            fi
            log_message "red" "A command failed with exit code 1. Check logs above."
            ;;
        13)
            # Permission errors
            log_message "red" "Permission denied error detected (Exit Code 13)."
            log_message "yellow" "Check file/directory permissions and sudo access."
            # Suggesting commands might be helpful but could also be wrong context.
            # echo "Consider checking ownership with: ls -ld <path>"
            # echo "Consider checking permissions with: ls -l <path>"
            ;;
        60)
            # SSL certificate errors (less common for internal scripts)
            log_message "red" "SSL certificate verification failed (Exit Code 60)"
            log_message "yellow" "Check SSL certificates and connectivity if accessing external resources via https."
            ;;
        127)
            # Command not found
             log_message "red" "Command not found (Exit Code 127). A required program might be missing or not in PATH."
             log_message "yellow" "Check dependencies (e.g., php, composer, node, git, sudo, find, xargs, systemctl)."
            ;;
        255)
            # SSH/Network errors (less common for local deployment steps)
            log_message "red" "Network or SSH connection failed (Exit Code 255)"
            log_message "yellow" "Check network connection and SSH configuration if interacting with remote systems."
            ;;
        *)
            log_message "red" "Unhandled error occurred (Exit Code: $exit_code)"
            ;;
    esac

    # Attempt rollback on any error caught by the trap
    rollback
    exit $exit_code # Exit with the original error code
}

# Set up error handling
trap 'handle_error $LINENO' ERR

# Start deployment
main

# Explicitly exit successfully if main completes without error trap
exit 0
