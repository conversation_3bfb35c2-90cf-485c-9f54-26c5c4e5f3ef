/**
 * Ticket View Media Handler
 * Prevents media from reloading on Livewire updates
 */

document.addEventListener('DOMContentLoaded', function() {
    // Initialize the media handler
    initMediaHandler();
    
    // Listen for Livewire updates
    document.addEventListener('livewire:load', function() {
        Livewire.hook('message.processed', (message, component) => {
            if (component.fingerprint.name === 'ticket-view') {
                // Restore media elements after Livewire updates
                restoreMediaElements();
            }
        });
    });
});

// Store for media elements
const mediaStore = {
    images: {},
    videos: {},
    audios: {},
    iframes: {}
};

/**
 * Initialize the media handler
 */
function initMediaHandler() {
    // Save the initial state of all media elements
    saveMediaElements();
    
    // Set up a mutation observer to detect when Livewire updates the DOM
    const messagesContainer = document.getElementById('messages-container');
    if (messagesContainer) {
        const observer = new MutationObserver(function(mutations) {
            mutations.forEach(function(mutation) {
                if (mutation.type === 'childList' && mutation.addedNodes.length > 0) {
                    // Save any new media elements
                    saveMediaElements();
                }
            });
        });
        
        observer.observe(messagesContainer, { 
            childList: true, 
            subtree: true 
        });
    }
}

/**
 * Save all media elements to the store
 */
function saveMediaElements() {
    // Save images
    document.querySelectorAll('#messages-container img:not([data-saved])').forEach(img => {
        const id = generateUniqueId(img);
        img.dataset.mediaId = id;
        img.dataset.saved = 'true';
        mediaStore.images[id] = {
            src: img.src,
            loaded: img.complete,
            naturalWidth: img.naturalWidth,
            naturalHeight: img.naturalHeight
        };
    });
    
    // Save videos
    document.querySelectorAll('#messages-container video:not([data-saved])').forEach(video => {
        const id = generateUniqueId(video);
        video.dataset.mediaId = id;
        video.dataset.saved = 'true';
        
        // Save the current time, volume, and playback state
        mediaStore.videos[id] = {
            sources: Array.from(video.querySelectorAll('source')).map(source => ({
                src: source.src,
                type: source.type
            })),
            currentTime: video.currentTime,
            volume: video.volume,
            muted: video.muted,
            paused: video.paused
        };
    });
    
    // Save audios
    document.querySelectorAll('#messages-container audio:not([data-saved])').forEach(audio => {
        const id = generateUniqueId(audio);
        audio.dataset.mediaId = id;
        audio.dataset.saved = 'true';
        
        // Save the current time, volume, and playback state
        mediaStore.audios[id] = {
            sources: Array.from(audio.querySelectorAll('source')).map(source => ({
                src: source.src,
                type: source.type
            })),
            currentTime: audio.currentTime,
            volume: audio.volume,
            muted: audio.muted,
            paused: audio.paused
        };
    });
    
    // Save iframes (PDFs)
    document.querySelectorAll('#messages-container iframe:not([data-saved])').forEach(iframe => {
        const id = generateUniqueId(iframe);
        iframe.dataset.mediaId = id;
        iframe.dataset.saved = 'true';
        
        mediaStore.iframes[id] = {
            src: iframe.src
        };
    });
}

/**
 * Restore media elements after a Livewire update
 */
function restoreMediaElements() {
    // Restore images
    document.querySelectorAll('#messages-container img[data-media-id]').forEach(img => {
        const id = img.dataset.mediaId;
        const savedImg = mediaStore.images[id];
        
        if (savedImg && img.src !== savedImg.src) {
            // Only update if the source has changed
            img.src = savedImg.src;
        }
    });
    
    // Restore videos
    document.querySelectorAll('#messages-container video[data-media-id]').forEach(video => {
        const id = video.dataset.mediaId;
        const savedVideo = mediaStore.videos[id];
        
        if (savedVideo) {
            // Restore sources if they've changed
            const currentSources = Array.from(video.querySelectorAll('source')).map(source => source.src);
            const savedSources = savedVideo.sources.map(source => source.src);
            
            if (!arraysEqual(currentSources, savedSources)) {
                // Clear existing sources
                while (video.firstChild) {
                    video.removeChild(video.firstChild);
                }
                
                // Add saved sources
                savedVideo.sources.forEach(source => {
                    const sourceElement = document.createElement('source');
                    sourceElement.src = source.src;
                    sourceElement.type = source.type;
                    video.appendChild(sourceElement);
                });
                
                // Reload the video
                video.load();
            }
            
            // Restore playback state
            if (!video.paused !== !savedVideo.paused) {
                if (savedVideo.paused) {
                    video.pause();
                } else {
                    video.play().catch(() => {});
                }
            }
            
            // Restore current time if it's significantly different
            if (Math.abs(video.currentTime - savedVideo.currentTime) > 1) {
                video.currentTime = savedVideo.currentTime;
            }
            
            // Restore volume and muted state
            video.volume = savedVideo.volume;
            video.muted = savedVideo.muted;
        }
    });
    
    // Restore audios
    document.querySelectorAll('#messages-container audio[data-media-id]').forEach(audio => {
        const id = audio.dataset.mediaId;
        const savedAudio = mediaStore.audios[id];
        
        if (savedAudio) {
            // Restore sources if they've changed
            const currentSources = Array.from(audio.querySelectorAll('source')).map(source => source.src);
            const savedSources = savedAudio.sources.map(source => source.src);
            
            if (!arraysEqual(currentSources, savedSources)) {
                // Clear existing sources
                while (audio.firstChild) {
                    audio.removeChild(audio.firstChild);
                }
                
                // Add saved sources
                savedAudio.sources.forEach(source => {
                    const sourceElement = document.createElement('source');
                    sourceElement.src = source.src;
                    sourceElement.type = source.type;
                    audio.appendChild(sourceElement);
                });
                
                // Reload the audio
                audio.load();
            }
            
            // Restore playback state
            if (!audio.paused !== !savedAudio.paused) {
                if (savedAudio.paused) {
                    audio.pause();
                } else {
                    audio.play().catch(() => {});
                }
            }
            
            // Restore current time if it's significantly different
            if (Math.abs(audio.currentTime - savedAudio.currentTime) > 1) {
                audio.currentTime = savedAudio.currentTime;
            }
            
            // Restore volume and muted state
            audio.volume = savedAudio.volume;
            audio.muted = savedAudio.muted;
        }
    });
    
    // Restore iframes
    document.querySelectorAll('#messages-container iframe[data-media-id]').forEach(iframe => {
        const id = iframe.dataset.mediaId;
        const savedIframe = mediaStore.iframes[id];
        
        if (savedIframe && iframe.src !== savedIframe.src) {
            iframe.src = savedIframe.src;
        }
    });
}

/**
 * Generate a unique ID for a media element
 * @param {HTMLElement} element - The media element
 * @returns {string} - A unique ID
 */
function generateUniqueId(element) {
    // Try to use existing IDs in the DOM hierarchy
    const messageId = element.closest('[data-message-id]')?.dataset.messageId;
    const attachmentId = element.closest('[data-attachment-id]')?.dataset.attachmentId;
    
    if (messageId && attachmentId) {
        return `msg-${messageId}-att-${attachmentId}`;
    } else if (messageId) {
        // Use the element's position within the message as part of the ID
        const parent = element.parentElement;
        const index = Array.from(parent.children).indexOf(element);
        return `msg-${messageId}-${element.tagName.toLowerCase()}-${index}`;
    } else {
        // Fallback to a random ID
        return `media-${Math.random().toString(36).substring(2, 15)}`;
    }
}

/**
 * Compare two arrays for equality
 * @param {Array} arr1 - First array
 * @param {Array} arr2 - Second array
 * @returns {boolean} - Whether the arrays are equal
 */
function arraysEqual(arr1, arr2) {
    if (arr1.length !== arr2.length) return false;
    for (let i = 0; i < arr1.length; i++) {
        if (arr1[i] !== arr2[i]) return false;
    }
    return true;
}
