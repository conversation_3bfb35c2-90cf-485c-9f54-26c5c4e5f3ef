# Layout Guide für Minewache-Website

Dieses Dokument beschreibt die Gestaltungsrichtlinien und Layout-Prinzipien für die Minewache-Website. Der Guide soll als Referenz für Entwickler dienen, um ein konsistentes Erscheinungsbild über das gesamte Projekt hinweg zu gewährleisten.

## Inhaltsverzeichnis

1. [Design-Philosophie](#design-philosophie)
2. [Farbpalette](#farbpalette)
3. [Typografie](#typografie)
4. [Komponenten](#komponenten)
   - [Karten & Container](#karten--container)
   - [Formulare](#formulare)
   - [Tabellen](#tabellen)
   - [Badges & Labels](#badges--labels)
   - [Buttons](#buttons)
5. [Layout-Struktur](#layout-struktur)
   - [Header](#header)
   - [Content-Bereich](#content-bereich)
   - [Footer](#footer)
6. [Responsive Design](#responsive-design)
7. [Animationen](#animationen)
8. [Best Practices](#best-practices)

## Design-Philosophie

Die Minewache-Website folgt einer klaren, modernen und benutzerfreundlichen Design-Philosophie. Die Kernprinzipien sind:

- **Klarheit**: Informationen werden übersichtlich und leicht verständlich präsentiert
- **Funktionalität**: Benutzeroberflächen sind intuitiv und aufgabenorientiert gestaltet
- **Konsistenz**: Einheitliches Erscheinungsbild und konsistentes Verhalten auf allen Seiten
- **Zugänglichkeit**: Gute Lesbarkeit und einfache Navigation für alle Nutzer

Die visuelle Sprache orientiert sich am Minecraft-Universum, behält dabei jedoch eine professionelle und moderne Ästhetik.

## Farbpalette

Die Website verwendet ein konsistentes Farbschema, das auf dem DaisyUI-Framework basiert und für die Minewache angepasst wurde:

```css
:root {
  --color-primary: oklch(58% 0.169 237.323);       /* Hauptfarbe, für wichtige Elemente */
  --color-primary-content: oklch(100% 0.002 247.839); /* Text auf Primärfarbe */
  
  --color-secondary: oklch(64% 0.246 16.439);      /* Akzentfarbe, für Call-to-Actions */
  --color-secondary-content: oklch(98% 0.016 73.684); /* Text auf Sekundärfarbe */
  
  --color-accent: oklch(20% 0.042 265.755);        /* Akzentfarbe für Hervorhebungen */
  --color-neutral: oklch(64% 0.222 41.116);        /* Neutrale Elemente */
  
  --color-info: oklch(68% 0.169 237.323);          /* Informationsmeldungen */
  --color-success: oklch(69% 0.17 162.48);         /* Erfolgsmeldungen */
  --color-warning: oklch(79% 0.184 86.047);        /* Warnungen */
  --color-error: oklch(65% 0.2 22.216);            /* Fehler */
}
```

### Verwendung der Farben

- **Primärfarbe**: Hauptnavigation, wichtige Buttons, Hervorhebungen
- **Sekundärfarbe**: Call-to-Action Elemente, wichtige interaktive Komponenten
- **Akzent**: Sparsam für Hover-Effekte und kleinere Highlights
- **Info/Success/Warning/Error**: Status-Anzeigen, Benachrichtigungen, Badges

## Typografie

Die Website verwendet eine sorgfältig ausgewählte Schrifthierarchie:

```css
font-family: {
  geometric: ['Sora', ...defaultTheme.fontFamily.sans],
  display: ['Anona', 'Stanley', ...defaultTheme.fontFamily.sans],
}
```

### Text-Größen und -Gewichte

- **Überschriften**:
  - H1: 2.6rem (text-title), bold, Primärfarbe
  - H2: 1.5rem, font-semibold
  - H3: 1.25rem, font-medium (für Karten-Titel)
  
- **Text**:
  - Standard: 1rem
  - Paragraph: 1.2rem (text-paragraph)
  - Klein: 0.875rem (für Beschreibungen, Meta-Informationen)
  - Micro: 0.75rem (für Fußzeilen, Hinweise)

## Komponenten

### Karten & Container

Karten sind ein zentrales Element im Layout und folgen diesem Aufbau:

```html
<div class="card bg-base-200 shadow-md">
    <div class="card-body">
        <h3 class="card-title flex items-center gap-2">
            <x-heroicon-o-icon class="w-5 h-5 text-primary" />
            Kartentitel
        </h3>
        <div class="space-y-3 mt-2">
            <!-- Karteninhalt -->
        </div>
    </div>
</div>
```

**Varianten:**
- Standard: `bg-base-200 shadow-md`
- Hervorgehoben: `bg-base-100 shadow-lg`
- Informativ: `bg-info/10 border border-info`

### Formulare

Formularelemente haben ein konsistentes Erscheinungsbild:

```html
<div class="form-control w-full">
    <label class="label">
        <span class="label-text font-medium">Feldbezeichnung</span>
    </label>
    <input type="text" class="input input-bordered w-full" />
    <label class="label">
        <span class="label-text-alt text-error">Fehlermeldung</span>
    </label>
</div>
```

**Spezielle Formularelemente:**

- **Checkboxen**:
```html
<label class="label cursor-pointer justify-start gap-2">
    <input type="checkbox" class="checkbox checkbox-primary" />
    <span class="label-text">Optionstext</span>
</label>
```

- **Radio-Buttons mit visuellen Karten**:
```html
<label class="label cursor-pointer flex flex-col items-center bg-base-100 p-4 rounded-lg border-2 border-base-300 hover:bg-base-300/30 transition-all duration-200">
    <input type="radio" name="status" value="option" class="radio radio-primary" />
    <x-heroicon-o-icon class="w-8 h-8 mt-2 mb-2 text-primary" />
    <span class="label-text font-medium">Optionstext</span>
</label>
```

### Tabellen

Tabellen für Datenübersichten verwenden dieses Grundlayout:

```html
<div class="bg-white dark:bg-base-100 rounded-xl shadow-lg overflow-hidden">
    <div class="overflow-x-auto">
        <table class="table w-full">
            <thead>
                <tr>
                    <th class="cursor-pointer">
                        <div class="flex items-center">
                            Spaltenname
                            <!-- Optional: Sortier-Icon -->
                            <x-heroicon-o-chevron-up class="w-4 h-4 ml-1" />
                        </div>
                    </th>
                    <!-- Weitere Spalten -->
                </tr>
            </thead>
            <tbody>
                <tr class="hover">
                    <td>Zellinhalt</td>
                    <!-- Weitere Zellen -->
                </tr>
                <!-- Weitere Zeilen -->
            </tbody>
        </table>
    </div>
    <!-- Pagination -->
    <div class="p-4">
        {{ $items->links() }}
    </div>
</div>
```

### Badges & Labels

Badges dienen zur Status- und Kategorie-Kennzeichnung:

```html
<!-- Status-Badge -->
<div class="badge badge-success gap-1">
    <x-heroicon-o-check class="w-4 h-4" />
    Angenommen
</div>

<!-- Kategorie-Badge -->
<span class="badge badge-primary badge-sm">
    Kategoriename
</span>

<!-- Großes Badge für Hauptstatus -->
<div class="badge badge-lg badge-success gap-1 p-3">
    <x-heroicon-o-check-circle class="w-5 h-5" />
    Angenommen
</div>
```

### Buttons

Die Website verwendet mehrere Button-Stile für unterschiedliche Aktionen:

```html
<!-- Primäraktionen -->
<button class="btn btn-primary gap-2">
    <x-heroicon-o-icon class="w-5 h-5" />
    Primäraktion
</button>

<!-- Sekundäraktionen -->
<button class="btn btn-secondary gap-2">
    <x-heroicon-o-icon class="w-5 h-5" />
    Sekundäraktion
</button>

<!-- Neutrale/Abbruch-Aktionen -->
<button class="btn btn-ghost gap-2">
    <x-heroicon-o-icon class="w-5 h-5" />
    Neutrale Aktion
</button>

<!-- Icon-Buttons für Tabellen -->
<button class="btn btn-sm btn-circle btn-ghost tooltip" data-tip="Tooltip-Text">
    <x-heroicon-o-icon class="w-5 h-5 text-primary" />
</button>
```

## Layout-Struktur

### Header

Der Seitenheader verwendet ein konsistentes Layout:

```html
<header class="bg-base-100 shadow-md">
    <div class="container mx-auto px-4 py-3">
        <div class="flex justify-between items-center">
            <div class="flex items-center gap-4">
                <img src="/favicon.svg" alt="Minewache Logo" class="w-10 h-10" />
                <h1 class="text-xl font-bold">Seitentitel</h1>
            </div>
            
            <!-- Navigation -->
            <nav class="hidden md:flex gap-4">
                <!-- Navigationselemente -->
            </nav>
            
            <!-- Mobile Menu Button -->
            <button class="md:hidden btn btn-ghost btn-circle">
                <x-heroicon-o-bars-3 class="w-6 h-6" />
            </button>
        </div>
    </div>
</header>
```

### Content-Bereich

Der Hauptinhalt folgt dieser Struktur:

```html
<main class="container mx-auto px-4 py-6">
    <!-- Dashboard Header mit Statistiken -->
    <div class="mb-8">
        <h1 class="text-3xl font-bold mb-6 text-primary">Seitentitel</h1>
        
        <!-- Optional: Statistik-Kacheln -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <div class="stat bg-white dark:bg-base-100 shadow-lg rounded-lg">
                <!-- Statistik-Inhalt -->
            </div>
            <!-- Weitere Statistik-Kacheln -->
        </div>
    </div>
    
    <!-- Hauptinhalt -->
    <div class="bg-white dark:bg-base-100 rounded-xl shadow-lg p-6">
        <!-- Inhalt -->
    </div>
</main>
```

### Footer

Der Footer bleibt einfach und informativ:

```html
<footer class="bg-base-200 mt-auto">
    <div class="container mx-auto px-4 py-8">
        <div class="flex flex-col md:flex-row justify-between items-center">
            <div class="flex items-center gap-2 mb-4 md:mb-0">
                <img src="/favicon.svg" alt="Minewache Logo" class="w-8 h-8" />
                <span class="text-sm font-medium">© Minewache 2025</span>
            </div>
            
            <div class="flex gap-4">
                <a href="/impressum" class="text-sm hover:text-primary">Impressum</a>
                <a href="/datenschutz" class="text-sm hover:text-primary">Datenschutz</a>
                <a href="/links" class="text-sm hover:text-primary">Links</a>
            </div>
        </div>
    </div>
</footer>
```

## Responsive Design

Die Website verwendet einen mobile-first Ansatz mit verschiedenen Breakpoints:

- **Mobile**: < 640px (Standard)
- **Tablet**: 640px - 1023px (`md:` Prefix)
- **Desktop**: 1024px+ (`lg:` Prefix)

### Wichtige responsive Muster:

```html
<!-- Responsives Grid -->
<div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
    <!-- Grid-Elemente -->
</div>

<!-- Responsiver Flex-Container -->
<div class="flex flex-col md:flex-row gap-4">
    <!-- Flex-Elemente -->
</div>

<!-- Responsive sichtbare/versteckte Elemente -->
<div class="hidden md:block">Nur auf größeren Bildschirmen sichtbar</div>
<div class="block md:hidden">Nur auf mobilen Geräten sichtbar</div>
```

## Animationen

Die Website verwendet subtile Animationen zur Verbesserung der Benutzererfahrung. Die vollständige Dokumentation ist in [animations.md](animations.md) zu finden.

Häufig verwendete Animationen:

```html
<!-- Fade-In für neue Elemente -->
<div class="animate-fade-in">Element faded ein</div>

<!-- Slide-Up für Inhalte -->
<div class="animate-slide-up">Element gleitet von unten ins Sichtfeld</div>

<!-- Hover-Effekt für interaktive Elemente -->
<button class="btn-hover">Hover-Effekt</button>
```

## Best Practices

1. **Konsistenz wahren**: Verwende die vordefinierten Komponenten und Stile
2. **Icons mit Text kombinieren**: Für bessere Zugänglichkeit und Verständlichkeit
3. **Sinnvolle Hierarchie**: Wichtige Informationen hervorheben, Details einklappbar machen
4. **Feedback geben**: Interaktionen durch visuelles Feedback bestätigen (Hover-Effekte, Statusänderungen)
5. **Ladezeiten berücksichtigen**: Lazy-Loading für Bilder und komplexe Komponenten verwenden
6. **Erscheinungsbild testen**: Designs auf verschiedenen Geräten und Bildschirmgrößen überprüfen

### Praktische Beispiele

#### Bewerbungsmanager-Layout

```html
<div class="container mx-auto px-4 py-6">
    <!-- Überschrift und Statistik -->
    <div class="mb-8">
        <h1 class="text-3xl font-bold mb-6 text-primary">Bewerbungsübersicht</h1>
        <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
            <!-- Statistik-Kacheln -->
        </div>
    </div>
    
    <!-- Filter-Bereich -->
    <div class="bg-white dark:bg-base-100 p-6 rounded-xl shadow-lg mb-6">
        <div class="flex flex-col md:flex-row gap-4">
            <!-- Filter-Komponenten -->
        </div>
    </div>
    
    <!-- Tabellen-Ansicht -->
    <div class="bg-white dark:bg-base-100 rounded-xl shadow-lg overflow-hidden">
        <div class="overflow-x-auto">
            <table class="table w-full">
                <!-- Tabelle mit Bewerbungen -->
            </table>
        </div>
    </div>
</div>
```

#### Detail-Ansicht

```html
<div class="bg-white dark:bg-base-100 rounded-xl shadow-lg p-6">
    <!-- Header mit Navigation und Status -->
    <div class="flex justify-between items-center mb-6">
        <!-- Zurück-Button und Aktionen -->
    </div>
    
    <!-- Bewerbungsstatus und Metadaten -->
    <div class="flex flex-col md:flex-row justify-between items-start md:items-center mb-6">
        <!-- Titel und Status-Badge -->
    </div>
    
    <!-- Bewerbungsdaten in Grid-Layout -->
    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-8">
        <!-- Persönliche Daten, Fähigkeiten, technische Details -->
    </div>
    
    <!-- Bewerbungstext in vollerBreite -->
    <div class="grid grid-cols-1 gap-6 mb-8">
        <!-- Über den Bewerber, Portfolio, etc. -->
    </div>
</div>
```