## AI Video Tag Generator

The AI Video Tag Generator is a tool for admin users to automatically generate SEO-optimized tags, keywords, and summaries for video content. It leverages the Google Gemini AI model to analyze uploaded videos.

### Accessing the Tag Generator

The Tag Generator can be accessed by administrators at the following URL:

`/admin/tag-generator`

It should also be available via a link in the admin panel navigation if one was added.

### Configuration

To use the AI Tag Generator, you must configure your Google Gemini API key and other related settings.

1.  **Environment Variables (`.env` file):**
    Add the following variables to your `.env` file:

    ```env
    GEMINI_API_KEY="YOUR_GEMINI_API_KEY"
    GEMINI_MODEL_NAME="gemini-1.5-flash-001" # Or your preferred model like gemini-pro-vision
    # If using Vertex AI SDK directly in the future, you might also need:
    # GEMINI_PROJECT_ID="YOUR_GCP_PROJECT_ID"
    # GEMINI_LOCATION="us-central1"
    ```
    Replace `"YOUR_GEMINI_API_KEY"` with your actual API key.

2.  **Service Configuration (`config/services.php`):**
    Ensure the following configuration exists in your `config/services.php` file:

    ```php
    'gemini' => [
        'api_key' => env('GEMINI_API_KEY'),
        'model_name' => env('GEMINI_MODEL_NAME', 'gemini-1.5-flash-001'),
        // For Vertex AI SDK (if used directly in GeminiTagService later)
        // 'project_id' => env('GEMINI_PROJECT_ID'),
        // 'location' => env('GEMINI_LOCATION', 'us-central1'),
    ],
    ```

### Video File Uploads

-   **Supported Formats:** MP4, MOV, WEBM, MKV, AVI.
-   **Maximum File Size:** The default maximum file size is currently set to 50MB. This is configurable within the `App\Livewire\Admin\TagGenerator` component (`MAX_FILE_SIZE_MB` constant) and should also align with your server's PHP settings (`upload_max_filesize` and `post_max_size`). Large files may take a significant time to upload and process.

---
