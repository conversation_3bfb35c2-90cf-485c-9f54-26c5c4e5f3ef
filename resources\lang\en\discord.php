<?php

return [
    // General errors
    'errors.admin_only_command' => 'You do not have permission to run this command.',
    'errors.generic_error_admin_contact' => 'An unexpected error occurred. Please contact an administrator.',
    'errors.user_not_linked_short' => 'Your Discord account is not linked to a Minewache user account.',

    // Ticket system related
    'ticket.db_error_channel_removed' => 'Sorry, there was an error saving your ticket to the database. The created channel will be removed.',
    'ticket.embed_title' => 'Ticket #:id: :title',
    'ticket.embed_field_created_by' => 'Created By',
    'ticket.embed_field_status' => 'Status',
    'ticket.status_open' => 'Open',
    'ticket.embed_footer' => 'Minewache Support System',
    'ticket.welcome_message' => 'Welcome :userMention! Your ticket has been created. A team member will assist you shortly.',
    'ticket.embed_creation_error' => '*Could not generate embed preview.*',
    'ticket.created_success_in_channel' => 'Ticket #:id created successfully in :channelMention!',
    'ticket.channel_creation_error' => 'Sorry, there was an error creating the ticket channel. Please try again later.',
    'ticket.unexpected_error_admin_contact' => 'An unexpected error occurred while processing your ticket request. Please contact an administrator.',
    
    'ticket.closed_by_user_default_reason' => 'Closed by :userTag',
    'ticket.close_not_in_ticket_channel' => 'This command can only be used in an active ticket channel, or use the button on a ticket message.',
    'ticket.close_no_context' => 'Could not determine the ticket context for closing.',
    'ticket.closed_success' => 'Ticket #:id has been closed.',
    'ticket.close_error' => 'Could not close ticket #:id. Please check logs or ticket status.',

    'ticket.progress_message' => 'Ticket #:id is now being worked on by :userTag.',
    'ticket.progress_embed_title' => 'Ticket In Progress',
    'ticket.progress_success_ephemeral' => 'Ticket #:id marked as "In Progress".', 
    'ticket.progress_error' => 'Could not mark ticket #:id as "In Progress". Please check logs.',

    'ticket.invalid_id_format' => 'The provided ticket ID is not in a valid numeric format.',
    'ticket.command_guild_only' => 'This command can only be used in a server channel.',


    // Ticket Assignment
    'ticket.assign_not_in_ticket_channel' => 'Ticket ID must be provided or command used in a ticket channel.',
    'ticket.assign_target_not_linked' => 'The target user :userMention is not linked to a Minewache account.',
    'ticket.assigned_success_confirmation' => 'Ticket #:id successfully assigned to :assigneeName.',
    'ticket.unassigned_success_confirmation' => 'Ticket #:id successfully unassigned.',
    'ticket.assign_error_generic' => 'Failed to update ticket assignment. Error: :message',
    
    'ticket.assigned_to_user_channel' => 'This ticket has been assigned to :assigneeMention by :assignerName.',
    'ticket.unassigned_channel' => 'This ticket has been unassigned by :assignerName.',
    'ticket.assignment_update_title' => 'Ticket Assignment Updated',
    'ticket.assigned_dm_notification' => "You have been assigned Ticket #:ticketId - ':ticketTitle' by :assignerName. View it in :channelMention.",
    'ticket.assigned_dm_title' => 'You have been assigned a Ticket',

    // AI Responses
    'ai.response_prefix' => 'AI Assistant Suggestion:',
    'ai.response_footer_disclaimer' => 'This is an AI-generated response. Please verify critical information.',
    'ai.response_author_name' => 'AI Assistant',


    // Ticket Reconstruct
    'ticket.reconstruct_ticket_not_found' => 'Ticket with ID :ticketId not found in the database.',
    'ticket.reconstruct_already_linked' => 'Ticket #:ticketId is already linked to Discord channel :channelMention. Unlink it first if you want to change it.',
    'ticket.reconstruct_channel_not_text' => 'The selected channel :channelMention is not a valid text channel or thread.',
    'ticket.reconstruct_channel_fetch_error' => 'Could not fetch Discord channel details for ID :channelId. Ensure the bot has access and the channel exists.',
    'ticket.reconstruct_system_message' => 'Ticket manually linked to Discord channel #:channelName by :adminName.',
    'ticket.reconstruct_channel_topic' => 'Ticket ID: :ticketId | Linked by: :adminName',
    'ticket.reconstruct_success' => 'Successfully linked Discord channel :channelMention to Ticket ID :ticketId.',
    'ticket.reconstruct_success_topic_fail' => 'Successfully linked Discord channel :channelMention to Ticket ID :ticketId, but failed to update the channel topic.',


    // Buttons
    'buttons.close_ticket' => 'Close Ticket',
    'buttons.mark_in_progress' => 'Mark In Progress',

    // Setup command related
    'setup.error_user_not_linked' => 'Your Discord account is not linked to a Minewache user account. Please link your account first.',
    'setup.log_permission_granted' => 'User: :userName (Discord: :userTag, ID: :userId) initiated setup with proper permissions.',
    'setup.error_guild_id_not_configured' => 'Discord Guild ID is not configured in `config/minewache_discord_bot.php` or .env.',
    'setup.error_bot_token_not_configured' => 'Discord Bot Token is not configured.',
    'setup.error_fetch_guild_details' => 'Failed to fetch Guild details for ID: :guildId. Ensure the bot is a member of this guild.',
    'setup.log_category_found' => 'Existing ticket category found: \':categoryName\' (ID: :categoryId).',
    'setup.log_category_not_found_or_invalid' => 'Configured ticket category ID \':categoryId\' not found or is not a category. Will attempt to create a new one.',
    'setup.default_category_name' => 'Support Tickets', 
    'setup.log_category_created' => 'Successfully created new ticket category: \':categoryName\' (ID: :categoryId).',
    'setup.error_category_creation_failed' => 'Failed to create a new ticket category named \':categoryName\'.',
    'setup.log_no_support_roles_configured' => 'No support role IDs configured. Skipping role permission setup for support staff.',
    'setup.error_failed_fetch_guild_roles' => 'Failed to fetch guild roles. Cannot verify configured support roles.',
    'setup.log_support_role_found' => 'Found configured support role: \':roleName\' (ID: :roleId).',
    'setup.error_support_role_not_found' => 'Configured support role ID \':roleId\' not found in the guild. Please create it or update the configuration.',
    'setup.log_no_valid_support_roles_found' => 'No configured support roles were found in the guild. Specific permissions for support staff will be skipped.',
    'setup.log_permissions_applied' => 'Successfully applied permissions to category \':categoryName\'.',
    'setup.error_permissions_apply_failed' => 'Failed to apply permissions to category \':categoryName\'.',
    'setup.error_category_not_available_for_perms' => 'Skipping category permission setup as category was not found or created.',
    'setup.summary_header' => 'Ticket Setup Process Completed:',
    'setup.summary_log_title' => 'Log',
    'setup.summary_errors_title' => 'Errors/Warnings',
    'setup.summary_action_required_title' => 'IMPORTANT ACTION REQUIRED',
    'setup.summary_env_update_instructions' => 'A new ticket category was created. Please update your `.env` file with `DISCORD_TICKET_CATEGORY_ID=:categoryId` and then run `php artisan config:cache` (if in production).',
    'setup.summary_complete_no_action' => 'Setup seems complete. Please review the log above.',
    'setup.summary_complete_action_needed' => 'Setup completed with actions required. Please review the log and instructions above.',

];
