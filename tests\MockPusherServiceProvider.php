<?php

namespace Tests;

use Illuminate\Support\ServiceProvider;
use Illuminate\Support\Facades\Broadcast;
use Illuminate\Broadcasting\BroadcastManager;

class MockPusherServiceProvider extends ServiceProvider
{
    public function register()
    {
        $this->app->singleton('pusher', function () {
            return new MockPusher();
        });
    }

    public function boot()
    {
        // Replace the Pusher driver with our mock
        $broadcastManager = $this->app->make(BroadcastManager::class);
        $broadcastManager->extend('pusher', function ($app, $config) {
            return new MockBroadcaster();
        });
    }
}

class MockPusher
{
    public function trigger($channels, $event, $data, $params = [])
    {
        // Do nothing
        return true;
    }
}

class MockBroadcaster
{
    public function auth($request)
    {
        return ['auth' => 'mock_auth_token'];
    }

    public function validAuthenticationResponse($request, $result)
    {
        return $result;
    }

    public function broadcast(array $channels, $event, array $payload = [])
    {
        // Do nothing
        return true;
    }
}
