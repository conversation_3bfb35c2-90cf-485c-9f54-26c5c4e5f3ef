<?php

namespace Database\Seeders;

use App\Models\RegisterYoutubeLink;
use Carbon\Carbon;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\File;
use Illuminate\Support\Facades\Log;

class YouTubeLinkCSVSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $csvPath = storage_path('app/mw.csv');
        
        // Check if the CSV file exists
        if (!File::exists($csvPath)) {
            $this->command->error('CSV file not found at: ' . $csvPath);
            $this->command->info('Please place the mw.csv file in the storage/app directory.');
            return;
        }
        
        $this->command->info('Importing YouTube data from CSV...');
        
        // Read the CSV file line by line
        $fileHandle = fopen($csvPath, 'r');
        if (!$fileHandle) {
            $this->command->error('Unable to open the CSV file.');
            return;
        }
        
        $importCount = 0;
        $updateCount = 0;
        $errorCount = 0;
        $lineCount = 0;
        
        // Process each line
        while (($data = fgetcsv($fileHandle, 0, ',')) !== false) {
            $lineCount++;
            
            try {
                // Ensure we have enough columns for a valid entry
                if (count($data) < 12) {
                    $this->command->warn("Line {$lineCount}: Skipping line with insufficient data - needs at least 12 columns");
                    $errorCount++;
                    continue;
                }
                
                // Map CSV columns to database fields
                $videoData = [
                    'season' => (int)$data[1],
                    'episode' => (int)$data[2],
                    'link' => trim($data[3]),
                    'title' => $data[6],
                    'description' => $data[7],
                    'thumbnail_url' => $data[8],
                    'published_at' => $data[9],
                    'duration_seconds' => (int)$data[10],
                    'view_count' => (int)$data[11],
                    'is_featured' => isset($data[12]) ? (bool)$data[12] : false,
                    'status' => isset($data[13]) ? $data[13] : 'active',
                ];
                
                // Only set timestamps if they are valid
                if (!empty($data[4]) && $this->isValidTimestamp($data[4])) {
                    $videoData['created_at'] = $data[4];
                }
                
                if (!empty($data[5]) && $this->isValidTimestamp($data[5])) {
                    $videoData['updated_at'] = $data[5];
                }
                
                // Basic validation
                if (empty($videoData['link']) || strlen($videoData['link']) < 5) {
                    $this->command->warn("Line {$lineCount}: Invalid YouTube link: {$videoData['link']}");
                    $errorCount++;
                    continue;
                }
                
                // Convert published_at to a datetime if it exists
                if (!empty($videoData['published_at']) && $videoData['published_at'] !== 'NULL') {
                    try {
                        $videoData['published_at'] = Carbon::parse($videoData['published_at']);
                    } catch (\Exception $e) {
                        $this->command->warn("Line {$lineCount}: Could not parse date: {$videoData['published_at']}");
                        $videoData['published_at'] = null;
                    }
                } else {
                    $videoData['published_at'] = null;
                }
                
                // Check if the video already exists
                $existingVideo = RegisterYoutubeLink::where('link', $videoData['link'])
                    ->orWhere(function ($query) use ($videoData) {
                        $query->where('season', $videoData['season'])
                              ->where('episode', $videoData['episode']);
                    })
                    ->first();
                    
                if ($existingVideo) {
                    // Update existing video
                    $existingVideo->update($videoData);
                    $updateCount++;
                } else {
                    // Create new video
                    RegisterYoutubeLink::create($videoData);
                    $importCount++;
                }
                
                if (($importCount + $updateCount) % 10 === 0) {
                    $this->command->info("Processed {$importCount} new, {$updateCount} updated, {$errorCount} errors...");
                }
                
            } catch (\Exception $e) {
                $this->command->error("Line {$lineCount}: Error: " . $e->getMessage());
                $errorCount++;
                continue;
            }
        }
        
        fclose($fileHandle);
        
        $this->command->info("Import completed: {$importCount} new videos, {$updateCount} updated, {$errorCount} errors.");
    }
    
    /**
     * Check if a timestamp string is valid
     * 
     * @param string $timestamp
     * @return bool
     */
    private function isValidTimestamp(string $timestamp): bool
    {
        try {
            Carbon::parse($timestamp);
            return true;
        } catch (\Exception $e) {
            return false;
        }
    }
}