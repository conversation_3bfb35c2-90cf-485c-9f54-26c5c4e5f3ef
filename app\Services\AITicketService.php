<?php

namespace App\Services;

use App\Contracts\LanguageModelInterface;
use App\Events\TicketAIResponseGenerated;
use App\Events\TicketMessageCreated;
use App\Models\Ticket;
use App\Models\TicketMessage;
use App\Models\User;
use Illuminate\Support\Facades\Log;
use App\Services\GeminiRateLimiter;

class AITicketService
{
    /**
     * The language model service.
     *
     * @var \App\Contracts\LanguageModelInterface
     */
    protected $languageModel;

    /**
     * The rate limiter service.
     *
     * @var GeminiRateLimiter
     */
    protected $rateLimiter;

    /**
     * Create a new service instance.
     *
     * @param \App\Contracts\LanguageModelInterface $languageModel
     * @param GeminiRateLimiter|null $rateLimiter
     * @return void
     */
    public function __construct(LanguageModelInterface $languageModel, ?GeminiRateLimiter $rateLimiter = null)
    {
        $this->languageModel = $languageModel;
        $this->rateLimiter = $rateLimiter ?? new GeminiRateLimiter();
    }

    /**
     * Check if AI assistance is available for a ticket.
     *
     * @param Ticket $ticket
     * @return bool
     */
    public function isAIAssistanceAvailable(Ticket $ticket): bool
    {
        // Check if Gemini is enabled in the config
        if (!config('services.gemini.enabled', true)) {
            Log::debug('AI assistance not available: Gemini is disabled in config', [
                'ticket_id' => $ticket->id,
                'gemini_enabled' => config('services.gemini.enabled', true)
            ]);
            return false;
        }

        // Check if the ticket has consent for Gemini
        if (!$ticket->gemini_consent) {
            Log::debug('AI assistance not available: No consent for this ticket', [
                'ticket_id' => $ticket->id,
                'gemini_consent' => $ticket->gemini_consent
            ]);
            return false;
        }

        // Check if a human supporter is assigned
        if ($ticket->assigned_to) {
            Log::debug('AI assistance not available: Human supporter assigned', [
                'ticket_id' => $ticket->id,
                'assigned_to' => $ticket->assigned_to
            ]);
            return false;
        }

        // Check if the ticket is closed
        if ($ticket->status === 'closed') {
            Log::debug('AI assistance not available: Ticket is closed', [
                'ticket_id' => $ticket->id,
                'status' => $ticket->status
            ]);
            return false;
        }

        Log::debug('AI assistance is available for ticket', [
            'ticket_id' => $ticket->id
        ]);
        return true;
    }

    /**
     * Generate and save an AI response for a ticket.
     *
     * @param Ticket $ticket
     * @param User|null $aiUser The user account to use for AI responses
     * @return TicketMessage|null
     */
    public function generateResponse(Ticket $ticket, ?User $aiUser = null): ?TicketMessage
    {
        Log::debug('Attempting to generate AI response for ticket', [
            'ticket_id' => $ticket->id
        ]);

        if (!$this->isAIAssistanceAvailable($ticket)) {
            Log::info('AI assistance not available for ticket', [
                'ticket_id' => $ticket->id,
                'gemini_consent' => $ticket->gemini_consent,
                'assigned_to' => $ticket->assigned_to,
                'status' => $ticket->status,
            ]);
            return null;
        }

        // Check rate limiting
        if ($this->rateLimiter->shouldRateLimit($ticket->id)) {
            Log::warning('Rate limit exceeded for Gemini API', [
                'ticket_id' => $ticket->id
            ]);
            return null;
        }

        try {
            // Get the AI user or use a default system user
            $user = $aiUser ?? $this->getAIUser();

            if (!$user) {
                Log::error('AI user not found', [
                    'system_user_id' => config('services.gemini.system_user_id')
                ]);
                return null;
            }

            Log::debug('Using AI user for response', [
                'user_id' => $user->id,
                'username' => $user->username
            ]);

            // Generate the response using the language model
            $systemPrompt = $this->buildSystemPrompt($ticket);
            Log::debug('Generated system prompt for AI', [
                'ticket_id' => $ticket->id,
                'prompt_length' => strlen($systemPrompt),
                'prompt_preview' => substr($systemPrompt, 0, 100) . '...'
            ]);

            Log::debug('Calling language model to generate response', [
                'ticket_id' => $ticket->id,
                'language_model' => get_class($this->languageModel)
            ]);

            $response = $this->languageModel->generateTicketResponse($ticket, $user, $systemPrompt);

            Log::debug('Received response from language model', [
                'ticket_id' => $ticket->id,
                'response_length' => strlen($response),
                'response_preview' => substr($response, 0, 100) . '...'
            ]);

            // Create a new ticket message with the AI response
            $message = new TicketMessage([
                'ticket_id' => $ticket->id,
                'user_id' => $user->id,
                'message' => $response,
                'is_system_message' => false,
                'message_source' => 'ai',
            ]);

            Log::debug('Saving AI message to database', [
                'ticket_id' => $ticket->id,
                'user_id' => $user->id,
                'message_source' => 'ai'
            ]);

            $message->save();

            // Broadcast the message creation event
            Log::debug('Broadcasting TicketMessageCreated event', [
                'ticket_id' => $ticket->id,
                'message_id' => $message->id
            ]);
            event(new TicketMessageCreated($message));

            // Broadcast the AI response generated event
            Log::debug('Broadcasting TicketAIResponseGenerated event', [
                'ticket_id' => $ticket->id,
                'message_id' => $message->id
            ]);
            event(new TicketAIResponseGenerated($message));

            Log::info('AI response generated for ticket', [
                'ticket_id' => $ticket->id,
                'message_id' => $message->id,
            ]);

            return $message;
        } catch (\Exception $e) {
            Log::error('Error generating AI response', [
                'ticket_id' => $ticket->id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return null;
        }
    }

    /**
     * Build the system prompt for the AI.
     *
     * @param Ticket $ticket
     * @return string
     */
    protected function buildSystemPrompt(Ticket $ticket): string
    {
        $locale = app()->getLocale();

        if ($locale === 'de') {
            return "Du bist ein Support-Assistent für das Minewache-Ticketsystem. Deine Aufgabe ist es, Benutzern bei ihren Anfragen zu helfen.

Wichtige Anweisungen:
1. Antworte höflich, präzise und hilfreich.
2. Beziehe dich nur auf Informationen, die im Ticket enthalten sind.
3. Wenn du ein Problem nicht lösen kannst, sage klar: 'Für dieses spezifische Problem muss ich dich mit einem menschlichen Support-Mitarbeiter verbinden.'
4. Unterteile komplexe Lösungen in nummerierte Schritte.
5. Halte Antworten unter 250 Wörtern, es sei denn, detaillierte technische Anweisungen sind erforderlich.
6. Antworte auf Deutsch.

Du bist ein KI-Assistent und kein menschlicher Mitarbeiter. Stelle dies in deinen Antworten klar.";
        } else {
            return "You are a support assistant for the Minewache ticket system. Your task is to help users with their inquiries.

Important instructions:
1. Maintain a helpful and professional tone.
2. Reference only information provided in the ticket history.
3. If you cannot resolve an issue, clearly state: 'For this specific issue, I'll need to connect you with a human support agent.'
4. Break down complex solutions into numbered steps when appropriate.
5. Keep responses under 250 words unless detailed technical instructions are required.
6. Respond in English.

You are an AI assistant, not a human staff member. Make this clear in your responses.";
        }
    }

    /**
     * Get the AI user account.
     *
     * @return User|null
     */
    protected function getAIUser(): ?User
    {
        Log::debug('Looking for AI user');

        // Try to find the AI user by username
        $aiUser = User::where('username', 'AI Support')->first();

        if ($aiUser) {
            Log::debug('Found AI user by username', [
                'user_id' => $aiUser->id,
                'username' => $aiUser->username
            ]);
            return $aiUser;
        }

        // If no AI user exists by username, try to find by system user ID
        $systemUserId = config('services.gemini.system_user_id');
        Log::debug('AI user not found by username, trying system user ID', [
            'system_user_id' => $systemUserId
        ]);

        if ($systemUserId) {
            $aiUser = User::where('id', $systemUserId)->first();

            if ($aiUser) {
                Log::debug('Found AI user by system user ID', [
                    'user_id' => $aiUser->id,
                    'username' => $aiUser->username
                ]);
                return $aiUser;
            }
        }

        // If still no AI user exists, log a warning
        Log::warning('AI user not found by username or system user ID', [
            'username_search' => 'AI Support',
            'system_user_id' => $systemUserId
        ]);

        return null;
    }
}
