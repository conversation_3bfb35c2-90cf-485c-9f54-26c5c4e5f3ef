<?php

namespace App\Services;

use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;

class GeminiRateLimiter
{
    /**
     * The maximum number of requests allowed per minute.
     *
     * @var int
     */
    protected $maxRequestsPerMinute;

    /**
     * The maximum number of requests allowed per hour.
     *
     * @var int
     */
    protected $maxRequestsPerHour;

    /**
     * The maximum number of requests allowed per day.
     *
     * @var int
     */
    protected $maxRequestsPerDay;

    /**
     * Create a new rate limiter instance.
     */
    public function __construct()
    {
        $this->maxRequestsPerMinute = config('services.gemini.rate_limit.per_minute', 10);
        $this->maxRequestsPerHour = config('services.gemini.rate_limit.per_hour', 100);
        $this->maxRequestsPerDay = config('services.gemini.rate_limit.per_day', 1000);
    }

    /**
     * Determine if the request should be rate limited.
     *
     * @param string $ticketId The ID of the ticket making the request
     * @return bool
     */
    public function shouldRateLimit(string $ticketId): bool
    {
        $minuteKey = 'gemini_rate_limit:minute:' . now()->format('Y-m-d-H-i');
        $hourKey = 'gemini_rate_limit:hour:' . now()->format('Y-m-d-H');
        $dayKey = 'gemini_rate_limit:day:' . now()->format('Y-m-d');
        $ticketKey = 'gemini_rate_limit:ticket:' . $ticketId . ':' . now()->format('Y-m-d-H');

        // Increment the counters
        $minuteCount = Cache::increment($minuteKey);
        $hourCount = Cache::increment($hourKey);
        $dayCount = Cache::increment($dayKey);
        $ticketCount = Cache::increment($ticketKey);

        // Set expiration for the cache keys if they were just created
        if ($minuteCount === 1) {
            Cache::put($minuteKey, 1, now()->addMinutes(2)); // Keep for 2 minutes to ensure overlap
        }
        
        if ($hourCount === 1) {
            Cache::put($hourKey, 1, now()->addHours(2)); // Keep for 2 hours to ensure overlap
        }
        
        if ($dayCount === 1) {
            Cache::put($dayKey, 1, now()->addDays(2)); // Keep for 2 days to ensure overlap
        }
        
        if ($ticketCount === 1) {
            Cache::put($ticketKey, 1, now()->addHours(2)); // Keep for 2 hours
        }

        // Check if any of the limits have been exceeded
        if ($minuteCount > $this->maxRequestsPerMinute) {
            Log::warning('Gemini API rate limit exceeded (per minute)', [
                'count' => $minuteCount,
                'limit' => $this->maxRequestsPerMinute,
                'ticket_id' => $ticketId
            ]);
            return true;
        }

        if ($hourCount > $this->maxRequestsPerHour) {
            Log::warning('Gemini API rate limit exceeded (per hour)', [
                'count' => $hourCount,
                'limit' => $this->maxRequestsPerHour,
                'ticket_id' => $ticketId
            ]);
            return true;
        }

        if ($dayCount > $this->maxRequestsPerDay) {
            Log::warning('Gemini API rate limit exceeded (per day)', [
                'count' => $dayCount,
                'limit' => $this->maxRequestsPerDay,
                'ticket_id' => $ticketId
            ]);
            return true;
        }

        // Limit per ticket (5 requests per hour per ticket)
        $maxRequestsPerTicket = config('services.gemini.rate_limit.per_ticket', 5);
        if ($ticketCount > $maxRequestsPerTicket) {
            Log::warning('Gemini API rate limit exceeded (per ticket)', [
                'count' => $ticketCount,
                'limit' => $maxRequestsPerTicket,
                'ticket_id' => $ticketId
            ]);
            return true;
        }

        return false;
    }

    /**
     * Get the current usage statistics.
     *
     * @return array
     */
    public function getUsageStats(): array
    {
        $minuteKey = 'gemini_rate_limit:minute:' . now()->format('Y-m-d-H-i');
        $hourKey = 'gemini_rate_limit:hour:' . now()->format('Y-m-d-H');
        $dayKey = 'gemini_rate_limit:day:' . now()->format('Y-m-d');

        return [
            'minute' => [
                'count' => Cache::get($minuteKey, 0),
                'limit' => $this->maxRequestsPerMinute,
                'remaining' => max(0, $this->maxRequestsPerMinute - Cache::get($minuteKey, 0)),
            ],
            'hour' => [
                'count' => Cache::get($hourKey, 0),
                'limit' => $this->maxRequestsPerHour,
                'remaining' => max(0, $this->maxRequestsPerHour - Cache::get($hourKey, 0)),
            ],
            'day' => [
                'count' => Cache::get($dayKey, 0),
                'limit' => $this->maxRequestsPerDay,
                'remaining' => max(0, $this->maxRequestsPerDay - Cache::get($dayKey, 0)),
            ],
        ];
    }
}
