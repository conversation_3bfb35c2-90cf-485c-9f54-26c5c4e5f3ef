<?php

return [
    // From lang/en/tickets.php (general ticket terms)
    'tickets' => 'Tickets',
    'ticket' => 'Ticket',
    'create_ticket' => 'Create Ticket',
    'my_tickets' => 'My Tickets',
    'all_tickets' => 'All Tickets',
    'ticket_details' => 'Ticket Details',
    'ticket_id' => 'Ticket ID',
    'title' => 'Title',
    'description' => 'Description',
    'status' => 'Status',
    'created_at' => 'Created At',
    'updated_at' => 'Updated At',
    'assigned_to' => 'Assigned To',
    'created_by' => 'Created By',
    'message_count' => 'Messages',
    'details' => 'Details',
    'activity' => 'Activity',
    'messages' => 'Messages',
    'attachments' => 'Attachments',
    'no_messages' => 'No messages yet. Be the first to reply!',
    'unassigned' => 'Unassigned',
    'open' => 'Open',
    'in_progress' => 'In Progress',
    'closed' => 'Closed',
    'status_changed_to' => 'Status changed to',
    'ticket_created' => 'Ticket Created',
    'latest_message' => 'Latest Message',
    'from' => 'From',
    'submit' => 'Submit',
    'cancel' => 'Cancel',
    'save' => 'Save',
    'close' => 'Close',
    'reopen' => 'Reopen',
    'assign' => 'Assign',
    'assign_to_me' => 'Assign to me',
    'reply' => 'Reply',
    'quote' => 'Quote',
    'copy' => 'Copy',
    'delete' => 'Delete',
    'download' => 'Download',
    'fullscreen' => 'Fullscreen',
    'dismiss' => 'Dismiss',
    'try_again' => 'Try Again',
    'preview' => 'Preview',
    'your_reply' => 'Your Reply',
    'reply_placeholder' => 'Type your message here...',
    'add_emoji' => 'Add Emoji',
    'templates' => 'Templates',
    'template_greeting' => 'Greeting',
    'template_closing' => 'Closing',
    'template_status_update' => 'Status Update',
    'template_request_info' => 'Request Information',
    'to_send' => 'to send',
    'delivered' => 'Delivered',
    'synced_with_discord' => 'Synced with Discord',
    'sent_from_discord' => 'Sent from Discord',
    'sync_failed' => 'Discord sync failed',
    'from_discord' => 'Discord',
    'scroll_to_bottom' => 'Scroll to bottom',
    'new_message_received' => 'New message received',
    'typing' => 'Typing',
    'copied_to_clipboard' => 'Copied to clipboard',
    'uploading_files' => 'Uploading files',
    'please_wait_while_uploading' => 'Please wait while your files are being uploaded',
    'do_not_close_browser' => 'Do not close your browser',
    'processing_files' => 'Processing files...',
    'upload_error' => 'Upload Error',
    'upload_error_occurred' => 'An error occurred while uploading your file',
    'uploads_in_progress' => 'Please wait for uploads to complete before sending your message',
    'check_file_size' => 'Check if the file size is within limits (max 10MB)',
    'check_file_type' => 'Make sure the file type is allowed',
    'try_again_later' => 'Try again later if the problem persists',
    'processing_failed' => 'Processing failed',
    'video_not_supported' => 'Your browser does not support the video tag.',
    'audio_not_supported' => 'Your browser does not support the audio element.',
    'new_message' => 'New Message',
    'new_message_in_ticket' => 'New message in ticket',
    'ticket_closed_no_replies' => 'This ticket is closed. No new replies can be added.',
    'back_to_tickets' => 'Back to Tickets',
    'submitting' => 'Submitting...',
    'view' => 'View',
    'manage' => 'Manage',
    'mark_in_progress' => 'Mark In Progress',
    'mark_closed' => 'Mark Closed',
    'new_ticket' => 'New Ticket',
    'create_first_ticket' => 'Create First Ticket',
    'no_tickets' => 'No Tickets Found',
    'no_tickets_description' => 'You haven\'t created any tickets yet. Create your first ticket to get started.',
    'ticket_closed' => 'Ticket Closed',
    'ticket_closed_reply' => 'This ticket is closed and no longer accepts new replies.',
    'title_placeholder' => 'Enter a descriptive title for your ticket...',
    'description_placeholder' => 'Describe your issue or question in detail...',
    'write_reply_placeholder' => 'Type your reply here...',
    'actions' => 'Actions',

    // AI support strings (previously existing in resources/lang/en/tickets.php)
    'ai_assistant' => 'Support Bot',
    'powered_by' => 'Powered by',
    'generate_response' => 'Generate Response',
    'use_response' => 'Use Response',
    'discard' => 'Discard', // Note: 'discard' is general, 'dismiss' from lang/en/tickets.php might be for specific UI elements. Keeping both for now.
    'ai_response_disclaimer' => 'This response was automatically generated. Please review it before sending.',
    'ai_error' => 'An error occurred while generating the response. Please try again later.', // Kept this version
    'ai_disabled' => 'AI support is currently disabled.', // Kept this version
    'ai_consent_required' => 'You need to consent to the use of the Support Bot before using this feature.', // Kept this version
    'ai_consent_title' => 'Enable Support Bot',
    'ai_consent_description' => 'To receive Support Bot-generated responses, you need to consent to the processing of your ticket data by our AI service.',
    'ai_consent_data_usage' => 'Your ticket data will only be used to generate responses and will not be stored for other purposes.',
    'ai_consent_accept' => 'Accept',
    'ai_consent_decline' => 'Decline',
    'ai_response_added' => 'AI-generated response has been added.',

    // Gemini consent modal (previously existing in resources/lang/en/tickets.php)
    'gemini_consent_title' => 'Enable AI Support',
    'gemini_consent_description' => 'Would you like to enable AI support for this ticket?',
    'gemini_consent_explanation' => 'If you consent, our AI assistant (powered by Google Gemini) can automatically respond to your messages when no human supporter is available.',
    'gemini_consent_data_usage' => 'Your ticket data will only be used to generate responses and will not be stored for other purposes.',
    'gemini_consent_accept' => 'Enable AI Support', // This is more specific than the general 'Accept'
    'gemini_consent_decline' => 'No thanks', // This is more specific than the general 'Decline'
    'gemini_consent_later' => 'Decide later',
    'gemini_consent_success' => 'AI support has been enabled.',
    'gemini_consent_error' => 'Error enabling AI support.',
    'gemini_response_pending' => 'AI response is being generated...',
    'gemini_badge_text' => 'AI Assistant',

    // User data consent (previously existing in resources/lang/en/tickets.php)
    'data_consent_title' => 'Data Sharing for AI Assistant',
    'data_consent_description' => 'The AI assistant would like to access your :dataType to better assist you.',
    'data_consent_explanation' => 'If you consent, the AI assistant can use this information to provide you with personalized responses.',
    'data_consent_privacy_note' => 'Your data will only be used for this ticket and will not be permanently stored.',
    'data_consent_grant' => 'Allow Access',
    'data_consent_deny' => 'Deny Access',

    // Data types (previously existing in resources/lang/en/tickets.php)
    'data_type_username' => 'username',
    'data_type_applications' => 'application data',
    'data_type_tickets' => 'ticket history',
    'data_type_discord_info' => 'Discord information',

    // Merged from lang/en/ai.php under 'discord_ai' group
    'discord_ai' => [
        'consent_title' => 'AI Support Assistance',
        'consent_question' => 'To help resolve your issue faster, may we send this conversation to our AI assistant, powered by Google Gemini?',
        'consent_details' => 'If you consent:',
        'consent_detail_1' => 'Your ticket details and messages will be sent to Google Gemini AI',
        'consent_detail_2' => 'The AI will generate responses to help with your inquiry',
        'consent_detail_3' => 'A human supporter can still take over at any time',
        'consent_note' => 'You can change your mind at any time. This consent applies only to this specific ticket.',
        'consent_yes' => 'Yes, I Consent',
        'consent_no' => 'No, Thanks',

        'ai_badge' => 'AI',
        'ai_generating' => 'AI is generating a response...',
        'ai_enabled' => 'AI Support Assistant',
        'ai_enabled_description' => 'AI assistance has been enabled for this ticket. The AI assistant may now respond when no human supporter is available.',

        'discord_command_description' => 'Manage AI assistant consent for the current ticket',
        'discord_not_ticket_channel' => '⚠️ This command can only be used in ticket channels.',
        'discord_ticket_not_found' => '⚠️ Could not find a ticket associated with this channel.',
        'discord_consent_enabled_title' => 'AI Assistance Enabled',
        'discord_consent_enabled_description' => 'You have given consent for AI to assist with this ticket. The AI assistant may now respond when no human supporter is available.',
        'discord_consent_disabled_title' => 'AI Assistance Disabled',
        'discord_consent_disabled_description' => 'You have declined AI assistance for this ticket. Only human supporters will respond to your messages.',
        'discord_error' => '❌ An error occurred while updating AI consent. Please try again later.',

        'ai_not_available' => 'AI assistance is not available for this ticket.',
        'ai_failed' => 'Failed to generate AI response.',
    ],
];
