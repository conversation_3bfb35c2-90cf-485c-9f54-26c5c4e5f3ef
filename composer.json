{"name": "laravel/laravel", "type": "project", "description": "The skeleton application for the Laravel framework.", "keywords": ["laravel", "framework"], "license": "MIT", "require": {"php": "^8.2", "blade-ui-kit/blade-heroicons": "^2.6", "google/apiclient": "^2.18", "jakyeru/larascord": "^6.0", "laravel/framework": "^11.0", "laravel/pulse": "^1.4", "laravel/reverb": "^1.5", "laravel/telescope": "^5.6", "laravel/tinker": "^2.9", "livewire/livewire": "^3.6", "pbmedia/laravel-ffmpeg": "^8.7", "spatie/pdf-to-image": "^1.2", "team-reflex/discord-php": "^10.10"}, "require-dev": {"fakerphp/faker": "^1.23", "laravel/pint": "^1.13", "laravel/sail": "^1.26", "mockery/mockery": "^1.6", "nunomaduro/collision": "^8.0", "phpunit/phpunit": "^10.5", "spatie/laravel-ignition": "^2.4"}, "autoload": {"psr-4": {"App\\": "app/", "Database\\Factories\\": "database/factories/", "Database\\Seeders\\": "database/seeders/"}, "files": ["app/Helpers/functions.php"]}, "autoload-dev": {"psr-4": {"Tests\\": "tests/"}}, "scripts": {"dev": ["@php artisan serve", "@php artisan queue:work --queue=default --timeout=0", "npm run dev"], "post-autoload-dump": ["Illuminate\\Foundation\\ComposerScripts::postAutoloadDump", "@php artisan package:discover --ansi"], "post-update-cmd": ["@php artisan vendor:publish --tag=laravel-assets --ansi --force"], "post-root-package-install": ["@php -r \"file_exists('.env') || copy('.env.example', '.env');\""], "post-create-project-cmd": ["@php artisan key:generate --ansi", "@php -r \"file_exists('database/database.sqlite') || touch('database/database.sqlite');\"", "@php artisan migrate --ansi"]}, "extra": {"branch-alias": {"dev-master": "11.x-dev"}, "laravel": {"dont-discover": []}}, "config": {"optimize-autoloader": true, "preferred-install": "dist", "sort-packages": true, "allow-plugins": {"pestphp/pest-plugin": true, "php-http/discovery": true}}, "minimum-stability": "stable", "prefer-stable": true}