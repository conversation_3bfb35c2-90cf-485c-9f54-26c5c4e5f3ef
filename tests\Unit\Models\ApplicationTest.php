<?php

namespace Tests\Unit\Models;

use App\Models\Application;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class ApplicationTest extends TestCase
{
    use RefreshDatabase;

    /** @test */
    public function it_has_fillable_attributes()
    {
        $fillable = [
            'discord_id',
            'user_id',
            'parent_id',
            'name',
            'age',
            'gender',
            'pronouns',
            'professions',
            'checkboxQuestions',
            'about_you',
            'strengths_weaknesses',
            'final_words',
            'voice_type',
            'ram',
            'fps',
            'desired_role',
            'portfolio',
            'microphone',
            'daw',
            'program',
            'design_style',
            'favorite_design',
            'gpu',
            'languages',
            'ide',
            'status',
            'team_comment',
            'reviewer_id',
            'reviewed_at',
            'editable',
        ];

        $application = new Application();
        $this->assertEquals($fillable, $application->getFillable());
    }

    /** @test */
    public function it_casts_attributes_correctly()
    {
        $application = new Application();
        $casts = $application->getCasts();

        $this->assertArrayHasKey('professions', $casts);
        $this->assertEquals('array', $casts['professions']);

        $this->assertArrayHasKey('checkboxQuestions', $casts);
        $this->assertEquals('array', $casts['checkboxQuestions']);

        $this->assertArrayHasKey('reviewed_at', $casts);
        $this->assertEquals('datetime', $casts['reviewed_at']);
    }

    /** @test */
    public function it_belongs_to_a_reviewer()
    {
        // Create a user who will be the reviewer
        $reviewer = User::factory()->create();

        // Create an application with the reviewer
        $application = Application::factory()->create([
            'reviewer_id' => $reviewer->id
        ]);

        // Assert the relationship works correctly
        $this->assertInstanceOf(User::class, $application->reviewer);
        $this->assertEquals($reviewer->id, $application->reviewer->id);
    }

    /** @test */
    public function it_can_store_and_retrieve_array_data()
    {
        $professions = ['actor', 'developer', 'designer'];
        $checkboxQuestions = ['question1' => true, 'question2' => false];

        $application = Application::factory()->create([
            'professions' => $professions,
            'checkboxQuestions' => $checkboxQuestions
        ]);

        // Refresh the model from the database
        $application = $application->fresh();

        // Assert arrays are properly stored and retrieved
        $this->assertEquals($professions, $application->professions);
        $this->assertEquals($checkboxQuestions, $application->checkboxQuestions);
    }

    /** @test */
    public function it_has_default_status_of_pending()
    {
        $application = Application::factory()->create();

        // Set status to pending in the factory
        $this->assertEquals('pending', $application->status);
    }
}
