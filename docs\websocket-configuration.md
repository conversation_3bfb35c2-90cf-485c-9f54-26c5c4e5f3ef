# WebSocket Configuration Guide

This document provides detailed information about the WebSocket configuration in the Minewache website, focusing on Laravel Reverb integration for real-time communication.

## Table of Contents

1. [Overview](#overview)
2. [Configuration](#configuration)
3. [Environment Variables](#environment-variables)
4. [Client-Side Setup](#client-side-setup)
5. [Server-Side Setup](#server-side-setup)
6. [Channel Authorization](#channel-authorization)
7. [Troubleshooting](#troubleshooting)
8. [Diagnostic Tools](#diagnostic-tools)

## Overview

The Minewache website uses Laravel Reverb for WebSocket communication, enabling real-time updates for features like the ticket system. This allows for instant message delivery, typing indicators, and attachment status updates without page refreshes.

## Configuration

### Environment Variables

The following environment variables control the WebSocket configuration:

```env
# Broadcasting Configuration
# Options: pusher, ably, redis, log, null, reverb
BROADCAST_CONNECTION=reverb
BROADCAST_DRIVER=pusher

# Reverb Configuration
REVERB_APP_ID="minewache_app"
REVERB_APP_KEY="minewache_key"
REVERB_APP_SECRET="minewache_secret"
REVERB_HOST="localhost"
REVERB_PORT=8080
REVERB_SCHEME="http"

# Reverb Server Configuration
REVERB_SERVER_HOST="127.0.0.1"
REVERB_SERVER_PORT=6001

# Vite Reverb Configuration
VITE_REVERB_APP_KEY="${REVERB_APP_KEY}"
VITE_REVERB_HOST="${REVERB_HOST}"
VITE_REVERB_PORT="${REVERB_PORT}"
VITE_REVERB_SCHEME="${REVERB_SCHEME}"
```

### Server Configuration

The Reverb server configuration is defined in `config/reverb.php`:

```php
'reverb' => [
    'host' => env('REVERB_SERVER_HOST', '0.0.0.0'),
    'port' => env('REVERB_SERVER_PORT', 8080),
    'path' => env('REVERB_SERVER_PATH', ''),
    'hostname' => env('REVERB_HOST'),
    'options' => [
        'tls' => [],
    ],
    'max_request_size' => env('REVERB_MAX_REQUEST_SIZE', 10_000),
    'scaling' => [
        'enabled' => env('REVERB_SCALING_ENABLED', false),
        'channel' => env('REVERB_SCALING_CHANNEL', 'reverb'),
        'server' => [
            'url' => env('REDIS_URL'),
            'host' => env('REDIS_HOST', '127.0.0.1'),
            'port' => env('REDIS_PORT', '6379'),
            'username' => env('REDIS_USERNAME'),
            'password' => env('REDIS_PASSWORD'),
            'database' => env('REDIS_DB', '0'),
            'timeout' => env('REDIS_TIMEOUT', 60),
        ],
    ],
],
```

### Broadcasting Configuration

The broadcasting configuration is defined in `config/broadcasting.php`:

```php
'reverb' => [
    'driver' => 'reverb',
    'app_id' => env('REVERB_APP_ID', '921296'),
    'key' => env('REVERB_APP_KEY', '6wnubnahcggubcu5vmds'),
    'secret' => env('REVERB_APP_SECRET', '9htbjury2mv0qlfy1nh0'),
    'options' => [
        'host' => env('REVERB_HOST', 'localhost'),
        'port' => env('REVERB_PORT', 8080),
        'scheme' => env('REVERB_SCHEME', 'http'),
        'useTLS' => env('REVERB_SCHEME', 'http') === 'https',
        'debug' => true,
        'curl_options' => [
            CURLOPT_SSL_VERIFYHOST => 0,
            CURLOPT_SSL_VERIFYPEER => 0,
        ],
    ],
],
```

## Client-Side Setup

The client-side WebSocket configuration is defined in `resources/js/bootstrap.js`:

```javascript
// Konfiguration für Reverb
const reverbConfig = {
    broadcaster: 'reverb',
    key: import.meta.env.VITE_REVERB_APP_KEY || 'minewache_key',
    enabledTransports: ['ws', 'wss'],
    disableStats: true,
};

// Determine environment and set appropriate WebSocket configuration
const isLocalDevelopment = window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1';
const isHttps = window.location.protocol === 'https:';

if (isLocalDevelopment) {
    // Local development configuration
    reverbConfig.wsHost = import.meta.env.VITE_REVERB_HOST || 'localhost';
    reverbConfig.wsPort = parseInt(import.meta.env.VITE_REVERB_PORT || '8080');
    reverbConfig.wssPort = parseInt(import.meta.env.VITE_REVERB_PORT || '8080');
    reverbConfig.forceTLS = (import.meta.env.VITE_REVERB_SCHEME || 'http') === 'https';
} else {
    // Production configuration
    reverbConfig.wsHost = window.location.hostname;

    // Set appropriate ports and TLS based on protocol
    if (isHttps) {
        reverbConfig.forceTLS = true;
        const port = import.meta.env.VITE_REVERB_PORT ? parseInt(import.meta.env.VITE_REVERB_PORT) : 443;
        reverbConfig.wsPort = port;
        reverbConfig.wssPort = port;
    } else {
        reverbConfig.forceTLS = false;
        const port = import.meta.env.VITE_REVERB_PORT ? parseInt(import.meta.env.VITE_REVERB_PORT) : 80;
        reverbConfig.wsPort = port;
    }
}

// Echo initialisieren
window.Echo = new Echo(reverbConfig);
```

This configuration ensures that:

1. In local development, the WebSocket connection uses the environment variables
2. In production, the WebSocket connection uses the current hostname
3. The appropriate port and TLS settings are used based on the protocol (HTTP/HTTPS)

## Server-Side Setup

### Starting the Reverb Server

The Reverb server can be started using the following command:

```bash
php artisan reverb:start
```

For production environments, it's recommended to run Reverb as a systemd service:

```
[Unit]
Description=Minewache Reverb WebSocket Server
After=network.target

[Service]
User=www-data
Group=www-data
WorkingDirectory=/var/www/minewache
ExecStart=/usr/bin/php artisan reverb:start
Restart=always
RestartSec=3
StandardOutput=journal
StandardError=journal
SyslogIdentifier=minewache-reverb
Environment=LARAVEL_ENV=production

[Install]
WantedBy=multi-user.target
```

### Broadcasting Events

To broadcast events, implement the `ShouldBroadcast` interface and define the `broadcastOn` method:

```php
class TicketMessageCreated implements ShouldBroadcast
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    public $message;
    public $ticketId;

    public function __construct(TicketMessage $message)
    {
        $this->message = $message;
        $this->ticketId = $message->ticket_id;
    }

    public function broadcastOn(): Channel
    {
        return new PrivateChannel('tickets.' . $this->ticketId);
    }

    public function broadcastAs(): string
    {
        return 'new-message';
    }

    public function broadcastWith(): array
    {
        return [
            'message' => $this->message->load('user'),
        ];
    }

    public function broadcastVia(): string
    {
        return 'reverb';
    }
}
```

## Channel Authorization

Channel authorization is defined in `routes/channels.php`:

```php
// Autorisierung für private Ticket-Kanäle
Broadcast::channel('tickets.{ticketId}', function (User $user, int $ticketId) {
    $ticket = Ticket::find($ticketId);

    // Benutzer kann den Kanal abonnieren, wenn er der Ersteller des Tickets ist
    // oder wenn er ein Supporter ist
    return $ticket && ($user->id === $ticket->user_id || $user->isSupporter());
});
```

## Troubleshooting

### Common Issues

#### 1. WebSocket Connection Fails

**Symptoms:**
- Console errors like "WebSocket connection failed"
- Real-time updates not working

**Possible Causes:**
- Reverb server not running
- Incorrect hostname or port configuration
- Firewall blocking WebSocket connections
- CORS issues

**Solutions:**
- Ensure the Reverb server is running: `php artisan reverb:start`
- Check environment variables for correct configuration
- Check firewall settings to allow WebSocket connections
- Verify CORS configuration in `config/cors.php`

#### 2. Authentication Issues

**Symptoms:**
- Console errors like "Invalid auth response"
- Channel subscription failures

**Possible Causes:**
- CSRF token mismatch
- Session issues
- Incorrect channel authorization

**Solutions:**
- Ensure CSRF token is included in the page
- Check session configuration
- Verify channel authorization in `routes/channels.php`

#### 3. Events Not Broadcasting

**Symptoms:**
- No errors, but events are not received by clients

**Possible Causes:**
- Event not implementing `ShouldBroadcast`
- Incorrect channel name
- Queue worker not running (if using queued broadcasting)

**Solutions:**
- Ensure the event implements `ShouldBroadcast`
- Check the channel name in `broadcastOn`
- Start the queue worker: `php artisan queue:work`

#### 4. Production Environment Issues

**Symptoms:**
- Works in development but not in production

**Possible Causes:**
- Incorrect hostname configuration
- SSL/TLS issues
- Port configuration issues

**Solutions:**
- Ensure the hostname is correctly set in production
- Check SSL/TLS configuration
- Verify port configuration for production environment

## Diagnostic Tools

### WebSocket Debug Page

The application includes a WebSocket diagnostic page at `/debug/websocket-debug` that provides detailed information about the WebSocket connection:

- Connection status
- Configuration details
- Environment variables
- Event logging

This page is accessible to team members and provides tools for testing the WebSocket connection and diagnosing issues.

### Console Logging

The client-side configuration includes detailed logging to help diagnose WebSocket issues:

```javascript
// Log environment detection
console.log('Environment detection:', {
    hostname: window.location.hostname,
    protocol: window.location.protocol,
    isLocalDevelopment: isLocalDevelopment,
    isHttps: isHttps,
    envHost: import.meta.env.VITE_REVERB_HOST,
    envPort: import.meta.env.VITE_REVERB_PORT,
    envScheme: import.meta.env.VITE_REVERB_SCHEME
});

// Log Echo initialization
console.log('Echo initialized with Reverb config:', {
    broadcaster: reverbConfig.broadcaster,
    key: reverbConfig.key,
    wsHost: reverbConfig.wsHost,
    wsPort: reverbConfig.wsPort,
    wssPort: reverbConfig.wssPort,
    forceTLS: reverbConfig.forceTLS,
    enabledTransports: reverbConfig.enabledTransports,
    hostname: window.location.hostname,
    protocol: window.location.protocol,
    environment: import.meta.env.MODE || 'unknown'
});
```

### Server-Side Logging

The server-side configuration includes logging to help diagnose WebSocket issues:

```php
// Log the data being broadcast
Log::debug('TicketTypingEvent broadcast data', [
    'data' => $data
]);

// Log the broadcasting connection
Log::info('Broadcasting TicketTypingEvent via reverb', ['ticket_id' => $this->ticketId]);
```

## Conclusion

The WebSocket configuration in the Minewache website is designed to provide a seamless real-time experience for users. By following the guidelines in this document, you can ensure that the WebSocket connection is properly configured and troubleshoot any issues that may arise.
