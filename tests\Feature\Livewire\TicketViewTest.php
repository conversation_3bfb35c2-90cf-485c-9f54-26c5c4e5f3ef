<?php

namespace Tests\Feature\Livewire;

use Tests\TestCase;
use Illuminate\Foundation\Testing\RefreshDatabase;
use App\Models\User;
use App\Models\Ticket;
use App\Models\TicketMessage;
use App\Services\DiscordService;
use App\Http\Livewire\TicketView;
use Livewire\Livewire;
use Mockery;

class TicketViewTest extends TestCase
{
    use RefreshDatabase;

    protected function tearDown(): void
    {
        Mockery::close();
        parent::tearDown();
    }

    /** @test */
    public function view_ticket_displays_ticket_details_and_messages()
    {
        $user = User::factory()->create();
        $ticket = Ticket::factory()->create(['user_id' => $user->id, 'title' => 'Test Ticket']);
        TicketMessage::factory()->create([
            'ticket_id' => $ticket->id,
            'user_id'   => $user->id,
            'message'   => 'Hello world'
        ]);

        $this->actingAs($user);

        Livewire::test(TicketView::class, ['ticket' => $ticket])
            ->assertSee('Test Ticket')
            ->assertSee('Hello world')
            ->assertSet('ticket.id', $ticket->id)
            ->assertSet('ticket.title', 'Test Ticket');
    }

    /** @test */
    public function add_reply_calls_discord_service_and_shows_new_message()
    {
        $user = User::factory()->create();
        $ticket = Ticket::factory()->create(['user_id' => $user->id]);
        $this->actingAs($user);

        $discordMock = Mockery::spy(DiscordService::class);
        $this->app->instance(DiscordService::class, $discordMock);

        Livewire::test(TicketView::class, ['ticket' => $ticket])
            ->set('form.message', 'This is a reply')
            ->call('addReply')
            ->assertSee('This is a reply');

        $discordMock->shouldHaveReceived('sendTicketMessage')->once();
    }

    /** @test */
    public function set_status_and_update_calls_discord_service()
    {
        $supporter = User::factory()->create(['is_supporter' => true]);
        $ticket = Ticket::factory()->create([
            'user_id' => $supporter->id,
            'status'  => 'open'
        ]);
        $this->actingAs($supporter);

        $discordMock = Mockery::spy(DiscordService::class);
        $this->app->instance(DiscordService::class, $discordMock);

        Livewire::test(TicketView::class, ['ticket' => $ticket])
            ->call('setStatusAndUpdate', 'closed')
            ->assertSet('ticket.status', 'closed');

        $discordMock->shouldHaveReceived('updateTicketStatus')->once()->withArgs(function ($arg) use ($ticket) {
            return $arg->id === $ticket->id && $arg->status === 'closed';
        });
    }

    /** @test */
    public function set_assignment_and_update_calls_discord_service()
    {
        $supporter = User::factory()->create(['is_supporter' => true]);
        $ticket = Ticket::factory()->create(['user_id' => $supporter->id]);
        $assignee = User::factory()->create();
        $this->actingAs($supporter);

        $discordMock = Mockery::spy(DiscordService::class);
        $this->app->instance(DiscordService::class, $discordMock);

        Livewire::test(TicketView::class, ['ticket' => $ticket])
            ->call('setAssignmentAndUpdate', $assignee->id)
            ->assertSet('ticket.assigned_to', $assignee->id);

        $discordMock->shouldHaveReceived('updateTicketAssignment')->once()->withArgs(function ($arg) use ($ticket) {
            return $arg->id === $ticket->id;
        });
    }
}