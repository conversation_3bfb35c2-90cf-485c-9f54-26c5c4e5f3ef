<?php

namespace Tests\Unit\Policies;

use App\Enums\Role;
use App\Models\Ticket;
use App\Models\User;
use App\Policies\TicketPolicy;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class TicketPolicyTest extends TestCase
{
    use RefreshDatabase;

    private TicketPolicy $policy;

    protected function setUp(): void
    {
        parent::setUp();
        $this->policy = new TicketPolicy();
    }

    /** @test */
    public function any_user_can_view_any_tickets()
    {
        $user = User::factory()->create();
        $this->assertTrue($this->policy->viewAny($user));
    }

    /** @test */
    public function users_can_view_their_own_tickets()
    {
        $user = User::factory()->create();
        $ticket = Ticket::factory()->create(['user_id' => $user->id]);

        $this->assertTrue($this->policy->view($user, $ticket));
    }

    /** @test */
    public function users_cannot_view_other_users_tickets()
    {
        $user1 = User::factory()->create();
        $user2 = User::factory()->create();
        $ticket = Ticket::factory()->create(['user_id' => $user1->id]);

        $this->assertFalse($this->policy->view($user2, $ticket));
    }

    /** @test */
    public function supporters_can_view_any_ticket()
    {
        $supporter = User::factory()->create(['permissions' => Role::MINEWACHE_TEAM->value]);
        $user = User::factory()->create();
        $ticket = Ticket::factory()->create(['user_id' => $user->id]);

        $this->assertTrue($this->policy->view($supporter, $ticket));
    }

    /** @test */
    public function any_user_can_create_tickets()
    {
        $user = User::factory()->create();
        $this->assertTrue($this->policy->create($user));
    }

    /** @test */
    public function users_can_reply_to_their_own_tickets()
    {
        $user = User::factory()->create();
        $ticket = Ticket::factory()->open()->create(['user_id' => $user->id]);

        // Make sure the ticket is not closed
        $this->assertEquals('open', $ticket->status, 'Ticket should be open');

        // Debug: Check if the user ID matches the ticket user ID
        $this->assertEquals($user->id, $ticket->user_id, 'User ID should match ticket user_id');

        $this->assertTrue($this->policy->reply($user, $ticket));
    }

    /** @test */
    public function users_cannot_reply_to_other_users_tickets()
    {
        $user1 = User::factory()->create();
        $user2 = User::factory()->create();
        $ticket = Ticket::factory()->open()->create(['user_id' => $user1->id]);

        // Make sure the ticket is not closed
        $this->assertEquals('open', $ticket->status, 'Ticket should be open');

        $this->assertFalse($this->policy->reply($user2, $ticket));
    }

    /** @test */
    public function supporters_can_reply_to_any_ticket()
    {
        $supporter = User::factory()->create(['permissions' => Role::MINEWACHE_TEAM->value]);
        $user = User::factory()->create();
        $ticket = Ticket::factory()->open()->create(['user_id' => $user->id]);

        // Debug: Check if the supporter has the MINEWACHE_TEAM role
        $this->assertTrue($supporter->hasRole(Role::MINEWACHE_TEAM), 'Supporter should have MINEWACHE_TEAM role');

        // Make sure the ticket is not closed
        $this->assertEquals('open', $ticket->status, 'Ticket should be open');

        $this->assertTrue($this->policy->reply($supporter, $ticket));
    }

    /** @test */
    public function no_one_can_reply_to_closed_tickets()
    {
        $user = User::factory()->create();
        $supporter = User::factory()->create(['permissions' => Role::MINEWACHE_TEAM->value]);
        $ticket = Ticket::factory()->create([
            'user_id' => $user->id,
            'status' => 'closed',
        ]);

        $this->assertFalse($this->policy->reply($user, $ticket));
        $this->assertFalse($this->policy->reply($supporter, $ticket));
    }

    /** @test */
    public function only_supporters_can_update_tickets()
    {
        $user = User::factory()->create();
        $supporter = User::factory()->create(['permissions' => Role::MINEWACHE_TEAM->value]);
        $ticket = Ticket::factory()->create(['user_id' => $user->id]);

        $this->assertFalse($this->policy->update($user, $ticket));
        $this->assertTrue($this->policy->update($supporter, $ticket));
    }

    /** @test */
    public function only_supporters_can_delete_tickets()
    {
        $user = User::factory()->create();
        $supporter = User::factory()->create(['permissions' => Role::MINEWACHE_TEAM->value]);
        $ticket = Ticket::factory()->create(['user_id' => $user->id]);

        $this->assertFalse($this->policy->delete($user, $ticket));
        $this->assertTrue($this->policy->delete($supporter, $ticket));
    }

    /** @test */
    public function only_supporters_can_assign_tickets()
    {
        $user = User::factory()->create();
        $supporter = User::factory()->create(['permissions' => Role::MINEWACHE_TEAM->value]);
        $ticket = Ticket::factory()->create(['user_id' => $user->id]);

        $this->assertFalse($this->policy->assign($user, $ticket));
        $this->assertTrue($this->policy->assign($supporter, $ticket));
    }

    /** @test */
    public function only_supporters_can_change_ticket_status()
    {
        $user = User::factory()->create();
        $supporter = User::factory()->create(['permissions' => Role::MINEWACHE_TEAM->value]);
        $ticket = Ticket::factory()->create(['user_id' => $user->id]);

        $this->assertFalse($this->policy->changeStatus($user, $ticket));
        $this->assertTrue($this->policy->changeStatus($supporter, $ticket));
    }

    /** @test */
    public function users_can_download_attachments_from_their_tickets()
    {
        $user = User::factory()->create();
        $ticket = Ticket::factory()->create(['user_id' => $user->id]);

        $this->assertTrue($this->policy->downloadAttachment($user, $ticket));
    }

    /** @test */
    public function users_cannot_download_attachments_from_other_users_tickets()
    {
        $user1 = User::factory()->create();
        $user2 = User::factory()->create();
        $ticket = Ticket::factory()->create(['user_id' => $user1->id]);

        $this->assertFalse($this->policy->downloadAttachment($user2, $ticket));
    }

    /** @test */
    public function supporters_can_download_attachments_from_any_ticket()
    {
        $supporter = User::factory()->create(['permissions' => Role::MINEWACHE_TEAM->value]);
        $user = User::factory()->create();
        $ticket = Ticket::factory()->create(['user_id' => $user->id]);

        $this->assertTrue($this->policy->downloadAttachment($supporter, $ticket));
    }
}
