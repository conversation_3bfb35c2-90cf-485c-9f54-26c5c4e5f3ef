<?php

namespace App\Livewire;

use App\Enums\Role;
use App\Models\Ticket;
use App\Models\User;
use Livewire\Component;
use Livewire\WithPagination;

class TicketList extends Component
{
    use WithPagination;

    public $search = '';
    public $status = '';
    public $assignedTo = '';
    public $isSupporter = false;

    protected $queryString = [
        'search' => ['except' => ''],
        'status' => ['except' => ''],
        'assignedTo' => ['except' => ''],
    ];

    public function mount()
    {
        $this->isSupporter = auth()->user()->hasRole(Role::MINEWACHE_TEAM);
    }

    public function updatingSearch()
    {
        $this->resetPage();
    }

    public function updatingStatus()
    {
        $this->resetPage();
    }

    public function updatingAssignedTo()
    {
        $this->resetPage();
    }

    public function render()
    {
        $query = Ticket::query();

        // If user is not a supporter, only show their tickets
        if (!$this->isSupporter) {
            $query->where('user_id', auth()->id());
        }

        // Apply search filter
        if ($this->search) {
            $query->where(function ($q) {
                $q->where('title', 'like', '%' . $this->search . '%')
                    ->orWhere('description', 'like', '%' . $this->search . '%');
            });
        }

        // Apply status filter
        if ($this->status) {
            $query->where('status', $this->status);
        }

        // Apply assigned to filter
        if ($this->assignedTo) {
            if ($this->assignedTo === 'unassigned') {
                $query->whereNull('assigned_to');
            } else {
                $query->where('assigned_to', $this->assignedTo);
            }
        }

        $tickets = $query->with(['user', 'assignedTo', 'messages' => function($query) {
                $query->latest()->limit(1);
            }])
            ->latest()
            ->paginate(10);

        $supporters = [];
        if ($this->isSupporter) {
            $supporters = User::where('permissions', '&', Role::MINEWACHE_TEAM->value)
                ->orderBy('username')
                ->get()
                ->pluck('username', 'id')
                ->toArray();
        }

        return view('livewire.ticket-list', [
            'tickets' => $tickets,
            'supporters' => $supporters,
        ]);
    }

    public function resetFilters()
    {
        $this->search = '';
        $this->status = '';
        $this->assignedTo = '';
        $this->resetPage();
    }
}
