# CI/CD Pipeline Documentation for Minewache Website

This document provides a comprehensive guide to the CI/CD pipeline implemented for the Minewache website.

## Table of Contents

1. [Overview](#overview)
2. [Git Branch Strategy](#git-branch-strategy)
3. [Automated Testing](#automated-testing)
4. [Deployment Process](#deployment-process)
5. [Rollback Procedures](#rollback-procedures)
6. [Manual Deployment](#manual-deployment)
7. [Troubleshooting](#troubleshooting)

## Overview

The CI/CD pipeline for the Minewache website automates the testing and deployment processes to ensure code quality and streamline the release process. The pipeline consists of three main workflows:

1. **Test Workflow**: Runs automated tests on every push and pull request to the `main` and `dev` branches.
2. **Development Deployment Workflow**: Automatically deploys changes to the development server when code is pushed to the `dev` branch.
3. **Production Deployment Workflow**: Automatically deploys changes to the production server when code is pushed to the `main` branch.

## Git Branch Strategy

The project follows a simple but effective branching strategy:

### Main Branches

- **`main`**: The production branch that contains the code currently running in the production environment.
- **`dev`**: The development branch that contains code ready for testing in the development environment.

### Feature Branches

All new features, bug fixes, and other changes should be developed in feature branches that branch off from `dev`. The naming convention for feature branches is:

- `feature/feature-name` for new features
- `bugfix/bug-name` for bug fixes
- `hotfix/issue-name` for critical fixes that need to be applied to production

### Workflow

1. Create a feature branch from `dev`:
   ```bash
   git checkout dev
   git pull origin dev
   git checkout -b feature/your-feature-name
   ```

2. Make your changes, commit them, and push to the remote repository:
   ```bash
   git add .
   git commit -m "Description of your changes"
   git push origin feature/your-feature-name
   ```

3. Create a pull request to merge your feature branch into `dev`.

4. After the pull request is reviewed and approved, merge it into `dev`:
   ```bash
   git checkout dev
   git merge feature/your-feature-name
   git push origin dev
   ```

5. The CI/CD pipeline will automatically deploy the changes to the development server.

6. Once the changes have been tested and approved in the development environment, create a pull request to merge `dev` into `main`.

7. After the pull request is reviewed and approved, merge `dev` into `main`:
   ```bash
   git checkout main
   git merge dev
   git push origin main
   ```

8. The CI/CD pipeline will automatically deploy the changes to the production server.

## Automated Testing

The automated testing workflow runs on every push and pull request to the `main` and `dev` branches. It performs the following steps:

1. Sets up the testing environment with PHP 8.2, MySQL, and required extensions.
2. Installs dependencies using Composer and npm.
3. Configures the database for testing.
4. Runs the test suite using PHPUnit.
5. Uploads the test results as artifacts.

### Running Tests Locally

To run the tests locally, use the following command:

```bash
php artisan test
```

## Deployment Process

### Development Deployment

The development deployment workflow runs automatically when changes are pushed to the `dev` branch. It performs the following steps:

1. Connects to the development server using SSH.
2. Creates a backup of the current codebase.
3. Pulls the latest changes from the `dev` branch.
4. Installs dependencies using Composer and npm.
5. Builds the frontend assets.
6. Runs database migrations.
7. Clears and rebuilds caches.
8. Sets proper file permissions.
9. Restarts the required services.

### Production Deployment

The production deployment workflow runs automatically when changes are pushed to the `main` branch. It performs the following steps:

1. Connects to the production server using SSH.
2. Creates a backup of the current codebase.
3. Enables maintenance mode.
4. Pulls the latest changes from the `main` branch.
5. Installs dependencies using Composer and npm.
6. Builds the frontend assets.
7. Runs database migrations.
8. Clears and rebuilds caches.
9. Sets proper file permissions.
10. Restarts the required services.
11. Disables maintenance mode.

## Rollback Procedures

In case of a failed deployment or issues with the deployed code, you can roll back to a previous version using the following procedures:

### Automatic Rollback

The deployment workflows include automatic rollback mechanisms. If any step in the deployment process fails, the workflow will:

1. Log the error.
2. Restore the backup created at the beginning of the deployment.
3. Restart the required services.

### Manual Rollback

If you need to manually roll back to a previous version, follow these steps:

1. Connect to the server using SSH:
   ```bash
   ssh user@server-ip
   ```

2. Navigate to the website directory:
   ```bash
   cd /var/www/minewache-website
   ```

3. Check out the previous version:
   ```bash
   git log --oneline  # Find the commit hash of the previous version
   git checkout <commit-hash>
   ```

4. Reinstall dependencies and rebuild assets:
   ```bash
   composer install --no-interaction --prefer-dist --optimize-autoloader
   npm ci
   npm run build
   ```

5. Clear caches:
   ```bash
   php artisan optimize:clear
   php artisan config:cache
   php artisan route:cache
   php artisan view:cache
   ```

6. Restart services:
   ```bash
   sudo systemctl restart minewache-reverb
   sudo systemctl restart minewache-queue
   sudo systemctl restart minewache-discord
   ```

## Manual Deployment

If you need to manually deploy the application, follow these steps:

### Development Server

1. Connect to the development server using SSH:
   ```bash
   ssh user@dev-server-ip
   ```

2. Navigate to the website directory:
   ```bash
   cd /var/www/minewache-website
   ```

3. Pull the latest changes from the `dev` branch:
   ```bash
   git checkout dev
   git pull origin dev
   ```

4. Install dependencies and build assets:
   ```bash
   composer install --no-interaction --prefer-dist --optimize-autoloader
   npm ci
   npm run build
   ```

5. Run database migrations:
   ```bash
   php artisan migrate --force
   ```

6. Clear caches:
   ```bash
   php artisan optimize:clear
   php artisan config:cache
   php artisan route:cache
   php artisan view:cache
   ```

7. Restart services:
   ```bash
   sudo systemctl restart minewache-reverb
   sudo systemctl restart minewache-queue
   sudo systemctl restart minewache-discord
   ```

### Production Server

1. Connect to the production server using SSH:
   ```bash
   ssh user@prod-server-ip
   ```

2. Navigate to the website directory:
   ```bash
   cd /var/www/minewache-website
   ```

3. Enable maintenance mode:
   ```bash
   php artisan down --message="Die Website wird aktualisiert und ist in Kürze wieder verfügbar." --retry=60
   ```

4. Pull the latest changes from the `main` branch:
   ```bash
   git checkout main
   git pull origin main
   ```

5. Install dependencies and build assets:
   ```bash
   composer install --no-interaction --prefer-dist --optimize-autoloader --no-dev
   npm ci
   npm run build
   ```

6. Run database migrations:
   ```bash
   php artisan migrate --force
   ```

7. Clear caches:
   ```bash
   php artisan optimize:clear
   php artisan config:cache
   php artisan route:cache
   php artisan view:cache
   ```

8. Restart services:
   ```bash
   sudo systemctl restart minewache-reverb
   sudo systemctl restart minewache-queue
   sudo systemctl restart minewache-discord
   ```

9. Disable maintenance mode:
   ```bash
   php artisan up
   ```

## Troubleshooting

### Common Issues

1. **Deployment fails with permission errors**:
   - Ensure the SSH user has the necessary permissions to access and modify the website directory.
   - Check file ownership and permissions on the server.

2. **Tests fail in the CI environment but pass locally**:
   - Check for environment-specific configurations.
   - Ensure all required extensions are installed in the CI environment.

3. **Services fail to restart after deployment**:
   - Check the service status using `systemctl status service-name`.
   - Check the service logs for error messages.

### Getting Help

If you encounter issues with the CI/CD pipeline, contact the development team or refer to the following resources:

- GitHub Actions documentation: [https://docs.github.com/en/actions](https://docs.github.com/en/actions)
- Laravel deployment documentation: [https://laravel.com/docs/deployment](https://laravel.com/docs/deployment)
