<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Str;
use App\Models\Application;

class QuestionController extends Controller
{

    public function submitAboutYou(Request $request)
    {
        $request->validate([
            'activity' => 'required|string|max:255',
            'name' => 'required|string|max:255',
            'age' => 'required|integer|min:0|max:120',
            'gender' => 'required|string|max:255',
            'pronouns' => 'required|string|max:255',
            'voice_type' => 'required|string|max:255',
            'ram' => 'required|integer|min:0|max:256',
            'fps' => 'required|string|max:255',
            'desired_role' => 'nullable|string|max:255',
            'microphone' => 'required|string|max:255',
            'portfolio' => 'nullable|string|max:1000',
        ]);

        return redirect()->route('questions.about_you')->with('success', '<PERSON>en erfolgreich gespeichert.');
    }

    public function showAboutYouForm(Request $request)
    {
        $data = $request->all();

        return view('questions_about_you', compact('data'));
    }

    public function showVerify(Request $request)
    {
        $data = $request->all();

        return view('verify', compact('data'));
    }

    public function show(Request $request)
    {

        $bitmask = session('professions_bitmask', 0);
        $professions = [];

        $info = implode(',', $professions);

        return view('questions', compact('info'));
    }
}
