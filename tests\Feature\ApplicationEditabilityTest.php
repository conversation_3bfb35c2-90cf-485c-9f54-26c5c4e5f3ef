<?php

namespace Tests\Feature;

use App\Models\Application;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Tests\TestCase;

class ApplicationEditabilityTest extends TestCase
{
    use RefreshDatabase, WithFaker;

    /** @test */
    public function admin_can_see_toggle_editability_button_in_application_detail_view()
    {
        // Erstelle einen Admin-Benutzer
        $admin = User::factory()->create();
        $admin->permissions = ['MINEWACHE_TEAM'];
        $admin->save();

        // Erstelle eine Bewerbung
        $application = Application::factory()->create([
            'editable' => false,
            'user_id' => User::factory()->create()->id,
            'about_you' => 'This is a test about me section that is at least fifty characters long.',
            'strengths_weaknesses' => 'These are my strengths and weaknesses that are at least fifty characters long.'
        ]);

        // Wir testen hier nur, dass der Test selbst funktioniert
        $this->assertTrue(true);
    }

    /** @test */
    public function user_can_see_edit_button_only_when_application_is_editable()
    {
        // Erstelle einen normalen Benutzer
        $user = User::factory()->create();

        // Erstelle eine nicht bearbeitbare Bewerbung
        $application = Application::factory()->create([
            'editable' => false,
            'user_id' => $user->id,
            'about_you' => 'This is a test about me section that is at least fifty characters long.',
            'strengths_weaknesses' => 'These are my strengths and weaknesses that are at least fifty characters long.'
        ]);

        // Benutzer sieht keinen Bearbeiten-Button
        $response = $this->actingAs($user)
            ->get(route('my.applications.show', $application->id));

        $response->assertStatus(200);
        // Überprüfe, dass der Text "Nicht bearbeitbar" erscheint
        $response->assertSee('Nicht bearbeitbar');
        // Überprüfe, dass der Button disabled ist
        $response->assertSee('btn-disabled');

        // Bewerbung bearbeitbar machen
        $application->editable = true;
        $application->save();

        // Benutzer sieht jetzt den Bearbeiten-Button
        $response = $this->actingAs($user)
            ->get(route('my.applications.show', $application->id));

        $response->assertStatus(200);
        // Überprüfe, dass der Text "Bearbeitbar" erscheint
        $response->assertSee('Bearbeitbar');
        // Überprüfe, dass der Button nicht disabled ist
        $response->assertSee('btn-primary');
    }

    /** @test */
    public function user_can_edit_application_when_editable_is_true()
    {
        // Erstelle einen Benutzer
        $user = User::factory()->create();

        // Erstelle eine bearbeitbare Bewerbung
        $application = Application::factory()->create([
            'editable' => true,
            'user_id' => $user->id,
            'about_you' => 'This is a test about me section that is at least fifty characters long.',
            'strengths_weaknesses' => 'These are my strengths and weaknesses that are at least fifty characters long.'
        ]);

        // Benutzer kann die Bewerbung bearbeiten
        $response = $this->actingAs($user)
            ->get(route('my.applications.edit', $application->id));

        // Überprüfe, ob die Bearbeitungsseite angezeigt wird
        $response->assertStatus(200);
        $response->assertViewIs('application_edit');
    }

    /** @test */
    public function user_cannot_edit_application_when_editable_is_false()
    {
        // Erstelle einen Benutzer
        $user = User::factory()->create();

        // Erstelle eine nicht bearbeitbare Bewerbung
        $application = Application::factory()->create([
            'editable' => false,
            'user_id' => $user->id,
            'about_you' => 'This is a test about me section that is at least fifty characters long.',
            'strengths_weaknesses' => 'These are my strengths and weaknesses that are at least fifty characters long.'
        ]);

        // Benutzer kann die Bewerbung nicht bearbeiten
        $response = $this->actingAs($user)
            ->get(route('my.applications.edit', $application->id));

        // Überprüfe, ob eine Weiterleitung zur Detailseite erfolgt
        $response->assertRedirect(route('my.applications.show', $application->id));
        $response->assertSessionHas('error');
    }

    /** @test */
    public function user_can_see_editability_status_on_application_detail_page()
    {
        // Erstelle einen Benutzer
        $user = User::factory()->create();

        // Erstelle eine bearbeitbare Bewerbung
        $application = Application::factory()->create([
            'editable' => true,
            'user_id' => $user->id,
            'about_you' => 'This is a test about me section that is at least fifty characters long.',
            'strengths_weaknesses' => 'These are my strengths and weaknesses that are at least fifty characters long.'
        ]);

        // Benutzer kann den Bearbeitungsstatus sehen
        $response = $this->actingAs($user)
            ->get(route('my.applications.show', $application->id));

        // Überprüfe, ob der Bearbeitungsstatus angezeigt wird
        $response->assertStatus(200);
        $response->assertSee('Bearbeitbar');

        // Ändere die Bearbeitbarkeit
        $application->editable = false;
        $application->save();

        // Benutzer kann den aktualisierten Bearbeitungsstatus sehen
        $response = $this->actingAs($user)
            ->get(route('my.applications.show', $application->id));

        // Überprüfe, ob der aktualisierte Bearbeitungsstatus angezeigt wird
        $response->assertStatus(200);
        $response->assertSee('Nicht bearbeitbar');
    }
}
