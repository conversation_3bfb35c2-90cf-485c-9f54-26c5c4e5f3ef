<x-app-layout>
    <x-slot name="header">
        <h2 class="font-display text-xl md:text-2xl text-gray-900 dark:text-white leading-tight">
            {{ __('Logs & Monitoring') }}
        </h2>
    </x-slot>

    <div class="py-12">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <!-- Benachrichtigungen -->
            @if(session('success'))
                <div class="alert alert-success mb-6">
                    <svg xmlns="http://www.w3.org/2000/svg" class="stroke-current shrink-0 h-6 w-6" fill="none" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" /></svg>
                    <span>{{ session('success') }}</span>
                </div>
            @endif

            @if(session('error'))
                <div class="alert alert-error mb-6">
                    <svg xmlns="http://www.w3.org/2000/svg" class="stroke-current shrink-0 h-6 w-6" fill="none" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 14l2-2m0 0l2-2m-2 2l-2-2m2 2l2 2m7-2a9 9 0 11-18 0 9 9 0 0118 0z" /></svg>
                    <span>{{ session('error') }}</span>
                </div>
            @endif

            <div class="grid grid-cols-1 md:grid-cols-4 gap-6">
                <!-- Seitenleiste mit Log-Dateien -->
                <div class="md:col-span-1">
                    <div class="bg-white dark:bg-base-100 rounded-lg shadow-lg p-6">
                        <div class="flex justify-between items-center mb-4">
                            <h3 class="text-lg font-bold">Log-Dateien</h3>
                            <form action="{{ route('admin.logs.clear-all') }}" method="POST" onsubmit="return confirm('Möchtest du wirklich alle Log-Dateien löschen?');">
                                @csrf
                                @method('DELETE')
                                <button type="submit" class="btn btn-sm btn-error">Alle löschen</button>
                            </form>
                        </div>

                        <div class="divider"></div>

                        <div class="space-y-2 max-h-[500px] overflow-y-auto">
                            @forelse($logFiles as $logFile)
                                <div class="flex items-center justify-between p-2 rounded-lg {{ $selectedLog === $logFile ? 'bg-primary/10' : 'hover:bg-base-200' }}">
                                    <a href="{{ route('admin.logs.show', $logFile) }}" class="flex-1 truncate">
                                        {{ $logFile }}
                                    </a>
                                    <div class="flex gap-2">
                                        <x-modern-button variant="ghost" href="{{ route('admin.logs.show', $logFile) }}" ><svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                                            </svg></x-modern-button>
                                        <form action="{{ route('admin.logs.destroy', $logFile) }}" method="POST" onsubmit="return confirm('Möchtest du diese Log-Datei wirklich löschen?');">
                                            @csrf
                                            @method('DELETE')
                                            <button type="submit" class="btn btn-xs btn-ghost text-error">
                                                <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                                                </svg>
                                            </button>
                                        </form>
                                    </div>
                                </div>
                            @empty
                                <div class="text-center py-4 text-gray-500">
                                    Keine Log-Dateien gefunden.
                                </div>
                            @endforelse
                        </div>
                    </div>
                </div>

                <!-- Log-Inhalt -->
                <div class="md:col-span-3">
                    <div class="bg-white dark:bg-base-100 rounded-lg shadow-lg p-6">
                        <div class="flex justify-between items-center mb-4">
                            <h3 class="text-lg font-bold">
                                @if($selectedLog)
                                    Log: {{ $selectedLog }}
                                @else
                                    Log-Inhalt
                                @endif
                            </h3>

                            @if($selectedLog)
                                <div class="flex gap-2">
                                    <x-modern-button variant="primary" href="{{ route('admin.logs.show', $selectedLog) }}" ><svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                                        </svg>
                                        Aktualisieren</x-modern-button>
                                    <form action="{{ route('admin.logs.destroy', $selectedLog) }}" method="POST" onsubmit="return confirm('Möchtest du diese Log-Datei wirklich löschen?');">
                                        @csrf
                                        @method('DELETE')
                                        <button type="submit" class="btn btn-sm btn-error">
                                            <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                                            </svg>
                                            Löschen
                                        </button>
                                    </form>
                                </div>
                            @endif
                        </div>

                        @if(isset($fileInfo) && $selectedLog)
                            <div class="flex flex-wrap gap-4 mb-4 text-sm text-gray-500">
                                <div class="flex items-center gap-1">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7v8a2 2 0 002 2h6M8 7V5a2 2 0 012-2h4.586a1 1 0 01.707.293l4.414 4.414a1 1 0 01.293.707V15a2 2 0 01-2 2h-2" />
                                    </svg>
                                    <span>Größe: {{ $fileInfo['size'] }}</span>
                                </div>
                                <div class="flex items-center gap-1">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                                    </svg>
                                    <span>Letzte Änderung: {{ $fileInfo['modified'] }}</span>
                                </div>
                                <div class="flex items-center gap-1">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 10h16M4 14h16M4 18h16" />
                                    </svg>
                                    <span>Zeilen: {{ $fileInfo['lines'] }}</span>
                                </div>
                            </div>
                        @endif

                        @if($selectedLog)
                            <div class="flex flex-col md:flex-row gap-4 mb-4">
                                <div class="form-control flex-1">
                                    <div class="input-group">
                                        <x-modern-input type="text" placeholder="Suchen..." variant="outlined" id="log-search"   / />
                                        <button class="btn" id="log-search-btn">
                                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                                            </svg>
                                        </button>
                                    </div>
                                </div>

                                <div class="flex flex-wrap gap-2 items-center">
                                    <span class="text-sm font-medium">Filter:</span>
                                    <button class="btn btn-sm btn-outline" data-level="all">Alle</button>
                                    <button class="btn btn-sm btn-outline btn-error" data-level="ERROR">
                                        <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
                                        </svg>
                                        Fehler
                                    </button>
                                    <button class="btn btn-sm btn-outline btn-warning" data-level="WARNING">
                                        <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
                                        </svg>
                                        Warnungen
                                    </button>
                                    <button class="btn btn-sm btn-outline btn-info" data-level="INFO">
                                        <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                                        </svg>
                                        Info
                                    </button>
                                    <button class="btn btn-sm btn-outline btn-success" data-level="DEBUG">
                                        <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 20l4-16m4 4l4 4-4 4M6 16l-4-4 4-4" />
                                        </svg>
                                        Debug
                                    </button>
                                </div>
                            </div>
                        @endif

                        <div class="divider"></div>

                        <div class="bg-base-200 rounded-lg p-4 h-[600px] overflow-auto font-mono text-sm relative">
                            <div id="log-status" class="absolute top-2 right-2 hidden bg-base-100 text-sm py-1 px-3 rounded-full shadow-md"></div>

                            @if(isset($rawContent) && $selectedLog)
                                <div class="alert alert-info mb-4">
                                    <div>
                                        <h3 class="font-bold">Debug-Informationen</h3>
                                        <div>Datei: {{ $selectedLog }}</div>
                                        <div>Inhalt vorhanden: {{ !empty($logContent) ? 'Ja' : 'Nein' }}</div>
                                        <div>Rohinhalt Länge: {{ strlen($rawContent ?? '') }} Zeichen</div>
                                    </div>
                                </div>

                                <style>
                                    /* Log-Styling */
                                    .log-raw {
                                        white-space: pre-wrap;
                                        word-break: break-word;
                                        font-family: monospace;
                                        font-size: 0.9em;
                                        line-height: 1.5;
                                    }

                                    /* Farbige Hervorhebungen */
                                    .log-raw .error { color: #ef4444; font-weight: bold; }
                                    .log-raw .warning { color: #f59e0b; }
                                    .log-raw .info { color: #3b82f6; }
                                    .log-raw .debug { color: #10b981; }
                                    .log-raw .notice { color: #8b5cf6; }
                                    .log-raw .timestamp { color: #6b7280; }
                                    .log-raw .stack { color: #ef4444; margin-left: 20px; }
                                </style>

                                <div class="log-raw">
                                    @php
                                        // Einfache Formatierung des Rohinhalts
                                        $formattedContent = $rawContent;

                                        // Timestamps hervorheben
                                        $formattedContent = preg_replace('/\[(\d{4}-\d{2}-\d{2}[\s\d:]+)\]/', '<span class="timestamp">[$1]</span>', $formattedContent);

                                        // Log-Level hervorheben
                                        $formattedContent = preg_replace('/(\[.+?\])\s+(ERROR)/', '$1 <span class="error">$2</span>', $formattedContent);
                                        $formattedContent = preg_replace('/(\[.+?\])\s+(WARNING)/', '$1 <span class="warning">$2</span>', $formattedContent);
                                        $formattedContent = preg_replace('/(\[.+?\])\s+(INFO)/', '$1 <span class="info">$2</span>', $formattedContent);
                                        $formattedContent = preg_replace('/(\[.+?\])\s+(DEBUG)/', '$1 <span class="debug">$2</span>', $formattedContent);
                                        $formattedContent = preg_replace('/(\[.+?\])\s+(NOTICE)/', '$1 <span class="notice">$2</span>', $formattedContent);

                                        // Stack-Traces hervorheben
                                        $formattedContent = preg_replace('/(#\d+\s+.+)/', '<span class="stack">$1</span>', $formattedContent);
                                        $formattedContent = preg_replace('/(Stack trace:)/', '<span class="stack">$1</span>', $formattedContent);

                                        // Zeilenumbrüche in HTML umwandeln
                                        $formattedContent = nl2br($formattedContent);

                                        echo $formattedContent;
                                    @endphp
                                </div>
                            @elseif($logContent)
                                <style>
                                    /* Log-Level-Farben */
                                    .error-level { color: #ef4444; }
                                    .warning-level { color: #f59e0b; }
                                    .info-level { color: #3b82f6; }
                                    .debug-level { color: #10b981; }
                                    .notice-level { color: #8b5cf6; }
                                    .alert-level { color: #f43f5e; }
                                    .critical-level { color: #b91c1c; }
                                    .emergency-level { color: #7f1d1d; }

                                    /* Log-Einträge */
                                    .log-content { white-space: pre-wrap; }
                                    .log-entry {
                                        margin-bottom: 8px;
                                        padding: 8px;
                                        border: 1px solid rgba(0,0,0,0.1);
                                        border-radius: 6px;
                                        background-color: rgba(255,255,255,0.05);
                                    }
                                    .log-entry:hover { box-shadow: 0 2px 4px rgba(0,0,0,0.1); }

                                    /* Timestamp und Level */
                                    .log-timestamp {
                                        color: #6b7280;
                                        margin-right: 8px;
                                    }
                                    .log-level {
                                        padding: 2px 6px;
                                        border-radius: 4px;
                                        font-size: 0.8em;
                                        font-weight: bold;
                                        margin-right: 8px;
                                    }

                                    /* Hilfsstile */
                                    .hidden { display: none; }
                                </style>
                                <div class="log-content">{!! $logContent !!}</div>

                                <script>
                                    document.addEventListener('DOMContentLoaded', function() {
                                        // Suchfunktion
                                        const searchInput = document.getElementById('log-search');
                                        const searchButton = document.getElementById('log-search-btn');

                                        function performSearch() {
                                            const searchTerm = searchInput.value.toLowerCase();
                                            const logEntries = document.querySelectorAll('.log-entry');

                                            logEntries.forEach(entry => {
                                                if (searchTerm === '') {
                                                    entry.style.display = '';
                                                } else {
                                                    const text = entry.textContent.toLowerCase();
                                                    entry.style.display = text.includes(searchTerm) ? '' : 'none';
                                                }
                                            });

                                            // Status aktualisieren
                                            updateStatus();
                                        }

                                        if (searchInput && searchButton) {
                                            searchButton.addEventListener('click', performSearch);
                                            searchInput.addEventListener('keyup', function(e) {
                                                if (e.key === 'Enter') {
                                                    performSearch();
                                                }
                                            });
                                        }

                                        // Log-Level-Filter
                                        const levelButtons = document.querySelectorAll('button[data-level]');

                                        levelButtons.forEach(button => {
                                            button.addEventListener('click', function() {
                                                const level = this.getAttribute('data-level');
                                                const logEntries = document.querySelectorAll('.log-entry');

                                                // Aktiven Button markieren
                                                levelButtons.forEach(btn => {
                                                    btn.classList.remove('btn-active');
                                                });
                                                this.classList.add('btn-active');

                                                // Einträge filtern
                                                logEntries.forEach(entry => {
                                                    if (level === 'all') {
                                                        entry.style.display = '';
                                                    } else {
                                                        const entryLevel = entry.getAttribute('data-level');
                                                        entry.style.display = entryLevel === level ? '' : 'none';
                                                    }
                                                });

                                                // Status aktualisieren
                                                updateStatus();
                                            });
                                        });

                                        // Automatisch den "Alle"-Button aktivieren
                                        const allButton = document.querySelector('button[data-level="all"]');
                                        if (allButton) {
                                            allButton.classList.add('btn-active');
                                        }

                                        // Status-Anzeige
                                        function updateStatus() {
                                            const logStatus = document.getElementById('log-status');
                                            if (!logStatus) return;

                                            const logEntries = document.querySelectorAll('.log-entry');
                                            const visibleEntries = document.querySelectorAll('.log-entry:not([style*="display: none"])');

                                            logStatus.textContent = `${visibleEntries.length} von ${logEntries.length} Einträgen`;
                                            logStatus.classList.remove('hidden');
                                        }

                                        // Status initial anzeigen
                                        updateStatus();
                                    });
                                </script>
                            @else
                                <div class="text-center py-4 text-gray-500">
                                    Wähle eine Log-Datei aus, um den Inhalt anzuzeigen.
                                </div>
                            @endif
                        </div>
                    </div>
                </div>
            </div>

            <!-- Systemüberwachung -->
            <div class="mt-8 grid grid-cols-1 md:grid-cols-2 gap-6">
                <!-- PHP Info -->
                <div class="bg-white dark:bg-base-100 rounded-lg shadow-lg p-6">
                    <h3 class="text-lg font-bold mb-4">PHP-Informationen</h3>

                    <div class="overflow-x-auto">
                        <table class="table w-full">
                            <tbody>
                                <tr>
                                    <td class="font-medium">PHP-Version</td>
                                    <td>{{ phpversion() }}</td>
                                </tr>
                                <tr>
                                    <td class="font-medium">Laravel-Version</td>
                                    <td>{{ app()->version() }}</td>
                                </tr>
                                <tr>
                                    <td class="font-medium">Server</td>
                                    <td>{{ $_SERVER['SERVER_SOFTWARE'] ?? 'Unbekannt' }}</td>
                                </tr>
                                <tr>
                                    <td class="font-medium">Memory Limit</td>
                                    <td>{{ ini_get('memory_limit') }}</td>
                                </tr>
                                <tr>
                                    <td class="font-medium">Max Execution Time</td>
                                    <td>{{ ini_get('max_execution_time') }} Sekunden</td>
                                </tr>
                                <tr>
                                    <td class="font-medium">Upload Max Filesize</td>
                                    <td>{{ ini_get('upload_max_filesize') }}</td>
                                </tr>
                                <tr>
                                    <td class="font-medium">Post Max Size</td>
                                    <td>{{ ini_get('post_max_size') }}</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>

                <!-- Datenbank-Info -->
                <div class="bg-white dark:bg-base-100 rounded-lg shadow-lg p-6">
                    <h3 class="text-lg font-bold mb-4">Datenbank-Informationen</h3>

                    <div class="overflow-x-auto">
                        <table class="table w-full">
                            <tbody>
                                @php
                                    try {
                                        $connection = DB::connection()->getPdo();
                                        $connected = true;
                                        $driver = DB::connection()->getDriverName();
                                        $database = DB::connection()->getDatabaseName();

                                        // Datenbankversion abrufen (abhängig vom Treiber)
                                        $version = 'Unbekannt';
                                        if ($driver === 'mysql') {
                                            $version = DB::select('SELECT version() as version')[0]->version ?? 'Unbekannt';
                                        } elseif ($driver === 'sqlite') {
                                            $version = 'SQLite ' . $connection->getAttribute(\PDO::ATTR_CLIENT_VERSION);
                                        } elseif ($driver === 'pgsql') {
                                            $version = DB::select('SELECT version()')[0]->version ?? 'Unbekannt';
                                        }
                                    } catch (\Exception $e) {
                                        $connected = false;
                                        $error = $e->getMessage();
                                    }
                                @endphp

                                <tr>
                                    <td class="font-medium">Status</td>
                                    <td>
                                        @if($connected)
                                            <span class="badge badge-success">Verbunden</span>
                                        @else
                                            <span class="badge badge-error">Nicht verbunden</span>
                                        @endif
                                    </td>
                                </tr>

                                @if($connected)
                                    <tr>
                                        <td class="font-medium">Treiber</td>
                                        <td>{{ $driver }}</td>
                                    </tr>
                                    <tr>
                                        <td class="font-medium">Datenbank</td>
                                        <td>{{ $database }}</td>
                                    </tr>
                                    <tr>
                                        <td class="font-medium">Version</td>
                                        <td>{{ $version }}</td>
                                    </tr>
                                    <tr>
                                        <td class="font-medium">Tabellen</td>
                                        <td>
                                            @php
                                                $tableCount = 0;
                                                try {
                                                    if ($driver === 'mysql') {
                                                        $tableCount = count(DB::select('SHOW TABLES'));
                                                    } elseif ($driver === 'sqlite') {
                                                        $tableCount = count(DB::select("SELECT name FROM sqlite_master WHERE type='table' AND name NOT LIKE 'sqlite_%'"));
                                                    } elseif ($driver === 'pgsql') {
                                                        $tableCount = count(DB::select("SELECT tablename FROM pg_catalog.pg_tables WHERE schemaname != 'pg_catalog' AND schemaname != 'information_schema'"));
                                                    } else {
                                                        // Fallback für andere Datenbanktreiber
                                                        $tableCount = 0;
                                                    }
                                                } catch (\Exception $e) {
                                                    $tableCount = 0;
                                                }
                                            @endphp
                                            {{ $tableCount }}
                                        </td>
                                    </tr>
                                @else
                                    <tr>
                                        <td class="font-medium">Fehler</td>
                                        <td class="text-error">{{ $error }}</td>
                                    </tr>
                                @endif
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</x-app-layout>
