<x-app-layout>
    <x-slot name="header">
        <h2 class="font-display text-xl md:text-2xl text-gray-900 dark:text-white leading-tight">
            {{ __('Systemeinstellungen') }}
        </h2>
    </x-slot>

    <div class="py-12">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <!-- Benachrichtigungen -->
            @if(session('success'))
                <div class="alert alert-success mb-6">
                    <svg xmlns="http://www.w3.org/2000/svg" class="stroke-current shrink-0 h-6 w-6" fill="none" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" /></svg>
                    <span>{{ session('success') }}</span>
                </div>
            @endif

            @if(session('error'))
                <div class="alert alert-error mb-6">
                    <svg xmlns="http://www.w3.org/2000/svg" class="stroke-current shrink-0 h-6 w-6" fill="none" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 14l2-2m0 0l2-2m-2 2l-2-2m2 2l2 2m7-2a9 9 0 11-18 0 9 9 0 0118 0z" /></svg>
                    <span>{{ session('error') }}</span>
                </div>
            @endif

            <!-- Anzeige des generierten Tokens (nur einmal) -->
            @if(session('plainToken'))
                <div class="alert alert-info mb-6">
                    <svg xmlns="http://www.w3.org/2000/svg" class="stroke-current shrink-0 h-6 w-6" fill="none" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" /></svg>
                    <div>
                        <span class="font-bold">Dein neuer API-Token (nur einmal sichtbar):</span>
                        <div class="mt-2 p-2 bg-base-300 rounded-lg font-mono text-sm break-all">
                            {{ session('plainToken') }}
                        </div>
                        <span class="text-sm mt-1">Kopiere diesen Token jetzt! Er wird aus Sicherheitsgründen nicht mehr angezeigt.</span>
                    </div>
                </div>
            @endif

            <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                <!-- Systemübersicht -->
                <div class="md:col-span-2">
                    <div class="bg-white dark:bg-base-100 rounded-lg shadow-lg p-6">
                        <h3 class="text-lg font-bold mb-4">Systemübersicht</h3>

                        <div class="overflow-x-auto">
                            <table class="table w-full">
                                <tbody>
                                    @foreach($systemInfo as $key => $value)
                                        <tr>
                                            <td class="font-medium">{{ ucfirst(str_replace('_', ' ', $key)) }}</td>
                                            <td>{{ $value }}</td>
                                        </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>
                    </div>

                    <!-- Datenbank-Informationen -->
                    <div class="bg-white dark:bg-base-100 rounded-lg shadow-lg p-6 mt-6">
                        <h3 class="text-lg font-bold mb-4">Datenbank-Informationen</h3>

                        @if($databaseInfo['connected'])
                            <div class="mb-4">
                                <div class="stats shadow">
                                    <div class="stat">
                                        <div class="stat-title">Treiber</div>
                                        <div class="stat-value text-lg">{{ $databaseInfo['driver'] }}</div>
                                    </div>

                                    <div class="stat">
                                        <div class="stat-title">Datenbank</div>
                                        <div class="stat-value text-lg">{{ $databaseInfo['database'] }}</div>
                                    </div>

                                    <div class="stat">
                                        <div class="stat-title">Tabellen</div>
                                        <div class="stat-value text-lg">{{ count($databaseInfo['tables']) }}</div>
                                    </div>
                                </div>
                            </div>

                            <div class="overflow-x-auto">
                                <table class="table w-full">
                                    <thead>
                                        <tr>
                                            <th>Tabelle</th>
                                            <th>Anzahl Einträge</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        @foreach($databaseInfo['tables'] as $table => $count)
                                            <tr>
                                                <td>{{ $table }}</td>
                                                <td>{{ $count }}</td>
                                            </tr>
                                        @endforeach
                                    </tbody>
                                </table>
                            </div>
                        @else
                            <div class="alert alert-error">
                                <svg xmlns="http://www.w3.org/2000/svg" class="stroke-current shrink-0 h-6 w-6" fill="none" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 14l2-2m0 0l2-2m-2 2l-2-2m2 2l2 2m7-2a9 9 0 11-18 0 9 9 0 0118 0z" /></svg>
                                <span>Datenbankverbindung fehlgeschlagen: {{ $databaseInfo['error'] }}</span>
                            </div>
                        @endif
                    </div>
                </div>

                <!-- Aktionen -->
                <div class="md:col-span-1">
                    <!-- Cache-Verwaltung -->
                    <div class="bg-white dark:bg-base-100 rounded-lg shadow-lg p-6">
                        <h3 class="text-lg font-bold mb-4">Cache-Verwaltung</h3>

                        <div class="mb-4">
                            <p class="text-sm text-gray-600 dark:text-gray-400 mb-2">
                                Aktueller Cache-Treiber: <span class="font-medium">{{ $cacheInfo['driver'] }}</span>
                            </p>

                            <form action="{{ route('admin.system.clear-cache') }}" method="POST">
                                @csrf
                                <x-modern-button variant="primary" size="md" type="submit"><svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                                    </svg>
                                    Cache leeren</x-modern-button>
                            </form>
                        </div>
                    </div>

                    <!-- API-Token-Verwaltung -->
                    <div class="bg-white dark:bg-base-100 rounded-lg shadow-lg p-6 mt-6">
                        <div class="flex justify-between items-center mb-4">
                            <h3 class="text-lg font-bold">API-Token-Verwaltung</h3>
                            <x-modern-button variant="primary" href="{{ route('admin.api-docs') }}" ><svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                                </svg>
                                API-Dokumentation</x-modern-button>
                        </div>

                        <form action="{{ route('admin.system.generate-token') }}" method="POST" class="mb-4">
                            @csrf
                            <div class="form-control mb-2">
                                <label class="label">
                                    <span class="label-text">Token-Name</span>
                                </label>
                                <x-modern-input type="text" name="token_name" placeholder="z.B. Discord Bot" variant="outlined" required / />
                            </div>

                            <x-modern-button variant="primary" size="md" type="submit"><svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                                </svg>
                                Neuen Token generieren</x-modern-button>
                        </form>

                        <div class="divider"></div>

                        <h4 class="font-medium mb-2">Vorhandene Tokens</h4>

                        <div class="space-y-2 max-h-[300px] overflow-y-auto">
                            @forelse($apiTokens as $token)
                                <div class="flex items-center justify-between p-2 rounded-lg hover:bg-base-200">
                                    <div>
                                        <p class="font-medium">{{ $token->name }}</p>
                                        <p class="text-xs text-gray-500">Erstellt: {{ \Carbon\Carbon::parse($token->created_at)->format('d.m.Y H:i') }}</p>
                                    </div>
                                    <form action="{{ route('admin.system.delete-token', $token->id) }}" method="POST" onsubmit="return confirm('Möchtest du diesen Token wirklich löschen?');">
                                        @csrf
                                        @method('DELETE')
                                        <button type="submit" class="btn btn-xs btn-error">
                                            <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                                            </svg>
                                        </button>
                                    </form>
                                </div>
                            @empty
                                <div class="text-center py-4 text-gray-500">
                                    Keine API-Tokens vorhanden.
                                </div>
                            @endforelse
                        </div>
                    </div>
                </div>
            </div>

            <!-- Weitere Systemeinstellungen -->
            <div class="mt-8 bg-white dark:bg-base-100 rounded-lg shadow-lg p-6">
                <h3 class="text-lg font-bold mb-4">Weitere Systemeinstellungen</h3>

                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <!-- Umgebungsvariablen -->
                    <div>
                        <h4 class="font-medium mb-2">Umgebungsvariablen</h4>
                        <div class="overflow-x-auto">
                            <table class="table w-full">
                                <thead>
                                    <tr>
                                        <th>Variable</th>
                                        <th>Wert</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr>
                                        <td>APP_NAME</td>
                                        <td>{{ config('app.name') }}</td>
                                    </tr>
                                    <tr>
                                        <td>APP_ENV</td>
                                        <td>{{ config('app.env') }}</td>
                                    </tr>
                                    <tr>
                                        <td>APP_DEBUG</td>
                                        <td>{{ config('app.debug') ? 'true' : 'false' }}</td>
                                    </tr>
                                    <tr>
                                        <td>APP_URL</td>
                                        <td>{{ config('app.url') }}</td>
                                    </tr>
                                    <tr>
                                        <td>DB_CONNECTION</td>
                                        <td>{{ config('database.default') }}</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>

                    <!-- Speichernutzung -->
                    <div>
                        <h4 class="font-medium mb-2">Speichernutzung</h4>

                        @php
                            $totalSpace = disk_total_space(base_path());
                            $freeSpace = disk_free_space(base_path());
                            $usedSpace = $totalSpace - $freeSpace;
                            $usedPercentage = round(($usedSpace / $totalSpace) * 100);

                            function formatBytes($bytes, $precision = 2) {
                                $units = ['B', 'KB', 'MB', 'GB', 'TB'];
                                $bytes = max($bytes, 0);
                                $pow = floor(($bytes ? log($bytes) : 0) / log(1024));
                                $pow = min($pow, count($units) - 1);
                                $bytes /= (1 << (10 * $pow));
                                return round($bytes, $precision) . ' ' . $units[$pow];
                            }
                        @endphp

                        <div class="stats shadow w-full">
                            <div class="stat">
                                <div class="stat-title">Gesamtspeicher</div>
                                <div class="stat-value text-lg">{{ formatBytes($totalSpace) }}</div>
                            </div>

                            <div class="stat">
                                <div class="stat-title">Verwendet</div>
                                <div class="stat-value text-lg">{{ formatBytes($usedSpace) }}</div>
                                <div class="stat-desc">{{ $usedPercentage }}%</div>
                            </div>

                            <div class="stat">
                                <div class="stat-title">Frei</div>
                                <div class="stat-value text-lg">{{ formatBytes($freeSpace) }}</div>
                                <div class="stat-desc">{{ 100 - $usedPercentage }}%</div>
                            </div>
                        </div>

                        <div class="w-full bg-gray-200 rounded-full h-2.5 mt-4">
                            <div class="bg-primary h-2.5 rounded-full" style="width: {{ $usedPercentage }}%"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</x-app-layout>
