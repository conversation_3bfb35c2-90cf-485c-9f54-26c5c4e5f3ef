# Ticket Message Submission Process Documentation

This document provides a comprehensive overview of what happens when a user submits a message with attachments in the Minewache ticket system.

## Table of Contents

1. [Overview](#overview)
2. [Client-Side Process](#client-side-process)
3. [Server-Side Process](#server-side-process)
4. [Media Processing Workflow](#media-processing-workflow)
5. [Real-Time Broadcasting](#real-time-broadcasting)
6. [Discord Integration](#discord-integration)
7. [Process Flow Diagrams](#process-flow-diagrams)

## Overview

When a user submits a message with attachments in a ticket, a complex series of operations is triggered that spans multiple systems:

1. The user interacts with the web interface to compose a message and attach files
2. The Livewire component handles the form submission and file upload
3. The message is stored in the database and the file is saved to the server
4. A background job processes the media files (converting audio, optimizing images, etc.)
5. An event-driven system monitors the processing status of attachments
6. Once processing is complete, the message is sent to Discord
7. Real-time updates are broadcast to all connected clients in two stages

## Client-Side Process

### User Interface Interaction

1. **Message Composition**:
   - User navigates to a ticket view page
   - User types a message in the textarea
   - User clicks the attachment button or drags and drops files
   - The files are selected and appear in the attachment preview area

2. **Form Submission**:
   - User clicks the send button (paper airplane icon) or presses Ctrl+Enter
   - The Livewire form submission is triggered with `wire:submit="addReply"`
   - The UI shows a loading spinner on the send button
   - If file uploads are in progress, a progress bar is displayed showing the upload percentage

3. **Upload Progress Tracking**:
   - The Livewire component tracks upload progress and updates the UI
   - Progress events are handled by the `handleUploadProgress` method
   - The progress bar is updated in real-time as the file uploads

4. **Error Handling**:
   - If an error occurs during upload, an error message is displayed
   - The user can dismiss the error and try again

## Server-Side Process

### Livewire Component Handling

1. **Form Validation**:
   - The `addReply` method in the `TicketView` Livewire component is called
   - The method validates the message and attachments using the `TicketReplyForm` form object:
     ```php
     // Check if ticket is closed
     if ($this->ticket->status === 'closed') {
         $this->addError('form.message', __('tickets.ticket_closed_no_replies'));
         return;
     }

     // Check if uploads are still in progress
     if ($this->isUploading) {
         $this->addError('form.message', __('tickets.uploads_in_progress'));
         return;
     }

     // Store the message using the form
     $message = $this->form->store($this->ticket->id);
     ```
   - If validation fails, error messages are displayed to the user

2. **Message Creation**:
   - The `TicketReplyForm` object handles the message creation and validation:
     ```php
     public function store($ticketId)
     {
         $this->validate();

         // Start a database transaction to ensure all related records are created together
         return DB::transaction(function () use ($ticketId) {
             // Create the message
             $newMessage = TicketMessage::create([
                 'ticket_id' => $ticketId,
                 'user_id' => auth()->id(),
                 'message' => $this->message,
             ]);

             // Process attachments if any
             if (!empty($this->attachments)) {
                 foreach ($this->attachments as $attachment) {
                     // Store the original file
                     $originalPath = $attachment->store('ticket-attachments/original');

                     // Create attachment record
                     $newAttachment = TicketAttachment::create([
                         'ticket_message_id' => $newMessage->id,
                         'original_filename' => $attachment->getClientOriginalName(),
                         'file_path' => $originalPath,
                         'mime_type' => $attachment->getClientMimeType(),
                         'file_size' => $attachment->getSize(),
                         'processing_status' => 'pending',
                     ]);

                     // Dispatch job to process the media file
                     ProcessTicketMediaJob::dispatch($newAttachment);
                 }
             }

             // Broadcast the message creation event
             event(new TicketMessageCreated($newMessage));

             // Reset the form
             $this->reset();

             return $newMessage;
         });
     }
     ```
   - The `created` event on the `TicketMessage` model triggers the `TicketMessageCreated` event

3. **File Storage**:
   - For each attachment, the file is stored in the server's filesystem
   - A new `TicketAttachment` record is created with metadata about the file
   - The attachment is set to a `pending` status

4. **Job Dispatching**:
   - For each attachment, a `ProcessTicketMediaJob` is dispatched to process the file
   - The job is sent to the `media` queue for asynchronous processing

5. **Real-Time Broadcasting (First Stage)**:
   - The `TicketMessageCreated` event is broadcast to all connected clients
   - This event contains the message data but without final attachment URLs
   - Connected clients receive the event and update their UI to show the message with pending attachments
   - See the [Real-Time Broadcasting](#real-time-broadcasting) section for detailed information

## Media Processing Workflow

### Media File Processing

1. **Job Execution**:
   - The `ProcessTicketMediaJob` is picked up by a worker from the `media` queue
   - The job loads the attachment record and updates its status to `processing`

2. **Media Type Detection**:
   - The job determines the media type based on the MIME type
   - It identifies the file as audio, video, image, PDF, or other type

3. **Media Processing**:
   - For audio files (e.g., WAV), it converts to MP3 for better compatibility
   - For video files, it optimizes and creates a web-compatible version
   - For images, it optimizes and creates thumbnails
   - For PDFs, it creates preview images
   - For other file types, it simply marks them as completed without processing

4. **Metadata Update**:
   - After processing, the attachment record is updated with the processed file path and metadata
   - The processing status is set to `completed` or `failed` depending on the outcome

5. **Event Triggering**:
   - After processing (success or failure), the job triggers an `AttachmentProcessingFinished` event:
     ```php
     // Fire event that processing is finished (success or failure)
     event(new AttachmentProcessingFinished($this->attachment));
     ```

### Event-Driven Attachment Monitoring

1. **Event Listener Execution**:
   - The `HandleAttachmentProcessingFinished` listener receives the event
   - It checks if all attachments for the message have been processed:
     ```php
     // Check if all attachments for this message are processed
     $pendingAttachments = TicketAttachment::where('ticket_message_id', $message->id)
         ->whereIn('processing_status', ['pending', 'processing'])
         ->count();
     ```

2. **Message Completion Handling**:
   - If all attachments are processed, the message is sent to Discord
   - A second broadcast event (`TicketMessageAttachmentsReady`) is triggered with the final attachment URLs:
     ```php
     if ($pendingAttachments === 0) {
         // Send to Discord
         $this->discordService->sendTicketMessage($message);

         // Broadcast that attachments are ready
         event(new TicketMessageAttachmentsReady($message));
     }
     ```

## Real-Time Broadcasting

The ticket system uses Laravel's broadcasting system with Reverb to provide real-time updates to all connected clients. This ensures that when a message is added to a ticket, all users viewing that ticket see the new message immediately without needing to refresh the page.

### Two-Stage Broadcasting Flow

The system uses a two-stage broadcasting approach for messages with attachments:

1. **First Stage: Message Creation**
   - When a user submits a message, a new `TicketMessage` record is created in the database
   - The `TicketMessageCreated` event is immediately broadcast
   - This event contains the message text and basic attachment information, but without final processed URLs
   - Connected clients receive this event and display the message with "processing" indicators for attachments

2. **Second Stage: Attachments Ready**
   - After all attachments for a message are processed, the `TicketMessageAttachmentsReady` event is broadcast
   - This event contains the final URLs and metadata for all processed attachments
   - Connected clients receive this event and update the attachment displays with the processed media

### Event Definitions

1. **TicketMessageCreated Event**:
   - This event is triggered immediately when a new ticket message is created
   - It contains the message text and basic attachment information

2. **TicketMessageAttachmentsReady Event**:
   - This event is triggered when all attachments for a message have been processed
   - It contains the final URLs and metadata for all processed attachments:
     ```php
     public function broadcastWith()
     {
         return [
             'message_id' => $this->message->id,
             'attachments' => $this->message->attachments->map(function ($attachment) {
                 return [
                     'id' => $attachment->id,
                     'original_filename' => $attachment->original_filename,
                     'is_image' => $attachment->is_image,
                     'is_video' => $attachment->is_video,
                     'is_audio' => $attachment->is_audio,
                     'is_pdf' => $attachment->is_pdf,
                     'media_url' => $attachment->media_url,
                     'download_url' => route('tickets.attachments.download', $attachment),
                     'processing_status' => $attachment->processing_status,
                 ];
             }),
         ];
     }
     ```

### Client-Side Event Handling

1. **Livewire Component Handling**:
   - The Livewire component listens for both broadcast events using the `#[On]` attribute:
     ```php
     #[On('echo-private:tickets.{ticket.id},TicketMessageCreated')]
     public function handleNewMessage($event)
     {
         // Get the message data from the event
         $messageData = $event['message'] ?? null;

         if (!$messageData) {
             return;
         }

         // Clear typing indicator for the user who just sent a message
         if (isset($messageData['user_id'])) {
             unset($this->typingUsers[$messageData['user_id']]);
         }

         // Refresh the ticket data
         $this->ticket->refresh();
         $this->ticket->load(['messages.user', 'messages.attachments']);

         // Dispatch browser event for frontend JS
         $this->dispatch('message-added', message: $messageData);
     }

     #[On('echo-private:tickets.{ticket.id},TicketMessageAttachmentsReady')]
     public function handleAttachmentsReady($event)
     {
         // Get the message ID and attachments from the event
         $messageId = $event['message_id'] ?? null;
         $attachments = $event['attachments'] ?? [];

         if (!$messageId || empty($attachments)) {
             return;
         }

         // Find the message in the ticket's messages collection
         $message = $this->ticket->messages->firstWhere('id', $messageId);

         if (!$message) {
             // If the message isn't in the collection, refresh the ticket
             $this->ticket->refresh();
             $this->ticket->load(['messages.user', 'messages.attachments']);
             return;
         }

         // Update the attachments in the message
         foreach ($attachments as $attachmentData) {
             $attachment = $message->attachments->firstWhere('id', $attachmentData['id']);
             if ($attachment) {
                 // Update the attachment with the processed data
                 $attachment->fill([
                     'media_url' => $attachmentData['media_url'] ?? null,
                     'download_url' => $attachmentData['download_url'] ?? null,
                     'processing_status' => $attachmentData['processing_status'] ?? 'completed',
                 ]);
             }
         }

         // Dispatch an event to update the UI
         $this->dispatch('attachments-ready', messageId: $messageId, attachments: $attachments);
     }
     ```

2. **Alpine.js Integration**:
   - Alpine.js handles the UI updates when events are received:
     ```javascript
     function ticketView() {
         return {
             init() {
                 this.scrollToBottom();
                 this.setupNotifications();
             },
             handleMessageAdded(message) {
                 // Play notification sound if enabled
                 if (document.getElementById('sound-toggle').checked) {
                     this.playNotificationSound();
                 }

                 // Scroll to bottom if we're already at the bottom
                 this.scrollToBottom();

                 // Show new message indicator if we're not at the bottom
                 this.showNewMessageIndicator();
             },
             handleAttachmentsReady(messageId, attachments) {
                 console.log('Attachments ready for message:', messageId, attachments);

                 // Find all attachments for this message that are in processing state
                 const processingAttachments = document.querySelectorAll(`[data-message-id="${messageId}"] .attachment-processing`);

                 // Update each attachment with the processed data
                 attachments.forEach(attachment => {
                     // Find the attachment element by ID
                     const attachmentElement = document.querySelector(`[data-attachment-id="${attachment.id}"]`);
                     if (!attachmentElement) return;

                     // Update the attachment element based on its type
                     if (attachment.is_image) {
                         // Replace with processed image
                         // ...
                     } else if (attachment.is_audio) {
                         // Replace with processed audio player
                         // ...
                     } else if (attachment.is_video) {
                         // Replace with processed video player
                         // ...
                     } else {
                         // Replace with download link for other file types
                         // ...
                     }
                 });
             },
             // ... other methods
         };
     }
     ```

## Discord Integration

### Message Synchronization

1. **Discord Service**:
   - Once all attachments are processed, the `DiscordService::sendTicketMessage` method is called
   - This method prepares the message and attachments for sending to Discord

2. **Attachment Preparation**:
   - For each attachment, a download URL is created for the Discord bot
   - Attachment metadata is collected, including the filename, URL, size, and content type

3. **API Request**:
   - An HTTP request is sent to the Discord bot API with the message and attachment information
   - The bot receives the request and processes it

4. **Discord Bot Processing**:
   - The Discord bot formats the message with the user mention
   - It sends the message to the specified Discord channel
   - For each attachment, it downloads the file and uploads it to Discord

5. **Response Handling**:
   - If the message is sent successfully, the `TicketMessage` record is updated with the Discord message ID
   - If an error occurs, the `sync_failed` flag is set to `true` on the message

## Process Flow Diagrams

### Sequence Diagram

```mermaid
sequenceDiagram
    participant User
    participant WebUI as Web Interface
    participant Livewire as Livewire Component
    participant Form as TicketReplyForm
    participant DB as Database
    participant Storage as File Storage
    participant Queue as Job Queue
    participant MediaProcessor as Media Processor
    participant EventListener as Event Listener
    participant Discord as Discord Bot
    participant DiscordChannel as Discord Channel

    User->>WebUI: Composes message with attachments
    User->>WebUI: Clicks send button
    WebUI->>Livewire: Submit form (addReply)
    Livewire->>Form: Call store method
    Form->>DB: Create TicketMessage record
    Form->>Storage: Store original files
    Form->>DB: Create TicketAttachment records
    Form->>Queue: Dispatch ProcessTicketMediaJob for each attachment
    Form->>WebUI: Broadcast TicketMessageCreated event (Stage 1)
    WebUI->>User: Show message with processing indicators

    Queue->>MediaProcessor: Execute ProcessTicketMediaJob
    MediaProcessor->>Storage: Read original file
    MediaProcessor->>MediaProcessor: Process media (convert/optimize)
    MediaProcessor->>Storage: Store processed file
    MediaProcessor->>DB: Update attachment status to completed
    MediaProcessor->>EventListener: Trigger AttachmentProcessingFinished event

    EventListener->>DB: Check if all attachments are processed

    alt All attachments processed
        EventListener->>Discord: Send message with attachments
        Discord->>DiscordChannel: Post message with mention
        Discord->>Storage: Download processed files
        Discord->>DiscordChannel: Upload files as attachments
        Discord->>DB: Update message with Discord message ID
        EventListener->>WebUI: Broadcast TicketMessageAttachmentsReady event (Stage 2)
        WebUI->>User: Update UI with processed attachments
    end
```

### Process Activity Diagram

```mermaid
graph TD
    A[User composes message with attachments] --> B[User clicks send button]
    B --> C[Livewire validates form]

    C -->|Valid| D[TicketReplyForm creates message record]
    C -->|Invalid| C1[Show validation errors]
    C1 --> A

    D --> E[Store original files]
    E --> F[Create TicketAttachment records]
    F --> G[Dispatch ProcessTicketMediaJob for each attachment]
    G --> H[Broadcast TicketMessageCreated event]
    H --> I[Show message with processing indicators]

    J[ProcessTicketMediaJob executes] --> K[Update attachment status to processing]
    K --> L[Determine media type]
    L --> M[Process media based on type]
    M --> N[Update attachment with processed file info]
    N --> O[Trigger AttachmentProcessingFinished event]

    O --> P[Event listener checks if all attachments are processed]
    P -->|Some still processing| P
    P -->|All processed| Q[Send message to Discord]
    Q --> R[Broadcast TicketMessageAttachmentsReady event]
    R --> S[Update UI with processed attachments]
```

## Conclusion

The improved ticket message submission process with attachments involves multiple components working together in an event-driven architecture:

1. **Client-Side**: Handles user interaction, form submission, and real-time updates
2. **Server-Side**: Processes the submission, stores data, and manages file uploads
3. **Background Jobs**: Handle media processing asynchronously
4. **Event System**: Coordinates the workflow and ensures proper synchronization
5. **Two-Stage Broadcasting**: Provides immediate feedback while processing attachments
6. **Discord Integration**: Synchronizes messages between the web application and Discord

This event-driven approach offers several advantages over the previous polling-based system:

- **Efficiency**: No unnecessary database queries or job dispatches
- **Reliability**: No arbitrary timeouts for attachment processing
- **Responsiveness**: Immediate UI updates with accurate processing status
- **Scalability**: Better performance with many concurrent uploads
- **Maintainability**: Cleaner code structure with clear separation of concerns

The system is designed to be resilient, with error handling at each step and proper event triggering for both successful and failed processing.


