#!/bin/bash

# Minewache Production Environment with tmux
# This script starts all required services in a tmux session
# Use this if systemd services are not available or for temporary testing

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[0;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
NC='\033[0m' # No Color

# Function for colored output
echo_color() {
  echo -e "${!1}${2}${NC}"
}

# Check if tmux is installed
if ! command -v tmux &> /dev/null; then
    echo_color "RED" "tmux is not installed. Please install it first:"
    echo "sudo apt-get install tmux"
    exit 1
fi

# Project directory
PROJECT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")/.." && pwd)"
cd "$PROJECT_DIR"

# Session name
SESSION_NAME="minewache-prod"

# Ports used
REVERB_PORT=6001

# Log files
PHP_DISCORD_BOT_LOG="$PROJECT_DIR/storage/logs/php-discord-bot.log"

# Function to check if a port is in use
check_port() {
    local port=$1
    local process_info=$(lsof -i:$port -sTCP:LISTEN -t 2>/dev/null)
    if [ -n "$process_info" ]; then
        echo "$process_info"
        return 0
    else
        return 1
    fi
}

# Function to kill a process
kill_process() {
    local pid=$1
    local port=$2
    local force=$3

    if [ "$force" = "force" ]; then
        echo_color "YELLOW" "Killing process $pid on port $port with SIGKILL..."
        kill -9 $pid
    else
        echo_color "YELLOW" "Killing process $pid on port $port..."
        kill $pid
    fi
}

# Kill existing tmux session if it exists
if tmux has-session -t "$SESSION_NAME" 2>/dev/null; then
    echo_color "YELLOW" "Existing tmux session '$SESSION_NAME' found."
    read -p "Do you want to terminate it? (y/n): " kill_session
    if [[ "$kill_session" == "y" || "$kill_session" == "Y" ]]; then
        tmux kill-session -t "$SESSION_NAME"
        echo_color "GREEN" "Existing tmux session terminated."
    else
        echo_color "RED" "Aborted. Please terminate the existing session first."
        exit 1
    fi
fi

# Check and free required ports
echo_color "BLUE" "Checking if required ports are free..."

# Array of ports and their descriptions
declare -A ports
ports[$REVERB_PORT]="Reverb WebSocket"

# Check if ports are in use
ports_in_use=false
for port in "${!ports[@]}"; do
    if pid=$(check_port $port); then
        echo_color "YELLOW" "Port $port (${ports[$port]}) is in use by process $pid."
        ports_in_use=true
    else
        echo_color "GREEN" "Port $port (${ports[$port]}) is free."
    fi
done

# If ports are in use, ask if they should be freed
if [ "$ports_in_use" = true ]; then
    echo_color "YELLOW" "Some ports are already in use."
    read -p "Do you want to free all used ports? (y/n): " free_ports

    if [[ "$free_ports" == "y" || "$free_ports" == "Y" ]]; then
        for port in "${!ports[@]}"; do
            if pid=$(check_port $port); then
                kill_process $pid $port
                sleep 1

                # Check if the process is still running
                if check_port $port > /dev/null; then
                    echo_color "YELLOW" "Process still running. Trying with SIGKILL..."
                    kill_process $pid $port "force"
                    sleep 1
                fi

                if ! check_port $port > /dev/null; then
                    echo_color "GREEN" "Port $port has been successfully freed."
                else
                    echo_color "RED" "Port $port could not be freed. Please check manually."
                    exit 1
                fi
            fi
        done
    else
        echo_color "RED" "Aborted. Please free the required ports manually."
        exit 1
    fi
fi

# Make sure the PHP Discord Bot is stopped
echo_color "BLUE" "Stopping PHP Discord Bot if it's running..."
pkill -f "php artisan minewache:run-discord-bot"

# Ensure log directory exists
mkdir -p "$(dirname "$PHP_DISCORD_BOT_LOG")"

# Create new tmux session
echo_color "BLUE" "Creating new tmux session..."
tmux new-session -d -s "$SESSION_NAME" -n "Queue" "php artisan queue:work --queue=default,media --sleep=3 --tries=3 --timeout=3600"

# Window for Reverb
tmux new-window -t "$SESSION_NAME:1" -n "Reverb" "php artisan reverb:start --host=127.0.0.1 --port=6001"

# Window for PHP Discord Bot
tmux new-window -t "$SESSION_NAME:2" -n "Discord-Bot" "php artisan minewache:run-discord-bot 2>&1 | tee -a $PHP_DISCORD_BOT_LOG"

# Window for commands
tmux new-window -t "$SESSION_NAME:3" -n "Shell" "bash"

# Select first window
tmux select-window -t "$SESSION_NAME:0"

# Display instructions
echo_color "GREEN" "tmux session '$SESSION_NAME' has been created with the following windows:"
echo "  0: Queue Worker"
echo "  1: Reverb WebSocket Server"
echo "  2: PHP Discord Bot"
echo "  3: Shell for commands"
echo ""
echo "Connect to session: tmux attach -t $SESSION_NAME"
echo "End session: tmux kill-session -t $SESSION_NAME"
echo ""
echo_color "YELLOW" "After ending the session, please run the following commands to stop all processes:"
echo "pkill -f \"php artisan minewache:run-discord-bot\""
echo "pkill -f \"php artisan queue:work\""
echo "pkill -f \"php artisan reverb:start\""
echo ""
echo_color "BLUE" "PHP Discord bot logs are available at:"
echo "  $PHP_DISCORD_BOT_LOG"
echo ""

# Ask if the user wants to connect to the session
read -p "Do you want to connect to the tmux session now? (y/n): " answer
if [[ "$answer" == "y" || "$answer" == "Y" ]]; then
    tmux attach-session -t "$SESSION_NAME"

    # After detaching, ask if all processes should be terminated
    read -p "Do you want to terminate all processes? (y/n): " cleanup
    if [[ "$cleanup" == "y" || "$cleanup" == "Y" ]]; then
        echo_color "BLUE" "Terminating all processes..."

        # Try to stop Discord bot gracefully first
        echo_color "BLUE" "Stopping Discord bot gracefully..."
        php artisan discord:bot stop

        # Kill remaining processes
        echo_color "BLUE" "Killing remaining processes..."
        pkill -f "node.*index-unified.js"
        pkill -f "php artisan queue:work"
        pkill -f "php artisan reverb:start"

        # Kill the tmux session
        tmux kill-session -t "$SESSION_NAME" 2>/dev/null

        echo_color "GREEN" "All processes have been terminated."
    else
        echo_color "YELLOW" "Processes are still running. To terminate them later, run:"
        echo "tmux kill-session -t $SESSION_NAME"
        echo "# Or use the Laravel command:"
        echo "php artisan discord:bot stop"
        echo "# Or kill processes manually:"
        echo "pkill -f \"node.*index-unified.js\""
        echo "pkill -f \"php artisan queue:work\""
        echo "pkill -f \"php artisan reverb:start\""
    fi
fi
