@props([
    'variant' => 'primary', // primary, secondary, accent, ghost, outline, gradient
    'size' => 'md', // xs, sm, md, lg, xl
    'shape' => 'rounded', // rounded, pill, square
    'loading' => false,
    'disabled' => false,
    'icon' => null,
    'iconPosition' => 'left', // left, right
    'href' => null,
    'type' => 'button'
])

@php
$baseClasses = 'inline-flex items-center justify-center font-medium transition-all duration-300 ease-out focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed';

$variantClasses = match($variant) {
    'primary' => 'bg-primary text-primary-content hover:bg-primary-focus focus:ring-primary shadow-lg hover:shadow-xl',
    'secondary' => 'bg-secondary text-secondary-content hover:bg-secondary-focus focus:ring-secondary shadow-md hover:shadow-lg',
    'accent' => 'bg-accent text-accent-content hover:bg-accent-focus focus:ring-accent shadow-md hover:shadow-lg',
    'ghost' => 'bg-transparent text-base-content hover:bg-base-200 focus:ring-base-300',
    'outline' => 'border-2 border-primary text-primary bg-transparent hover:bg-primary hover:text-primary-content focus:ring-primary',
    'gradient' => 'bg-gradient-to-r from-primary to-secondary text-white hover:from-primary-focus hover:to-secondary-focus shadow-lg hover:shadow-xl',
    'danger' => 'bg-error text-error-content hover:bg-error-focus focus:ring-error shadow-lg hover:shadow-xl',
    'success' => 'bg-success text-success-content hover:bg-success-focus focus:ring-success shadow-lg hover:shadow-xl',
    'warning' => 'bg-warning text-warning-content hover:bg-warning-focus focus:ring-warning shadow-lg hover:shadow-xl',
    default => 'bg-primary text-primary-content hover:bg-primary-focus focus:ring-primary shadow-lg hover:shadow-xl'
};

$sizeClasses = match($size) {
    'xs' => 'px-2 py-1 text-xs gap-1',
    'sm' => 'px-3 py-1.5 text-sm gap-1.5',
    'md' => 'px-4 py-2 text-sm gap-2',
    'lg' => 'px-6 py-3 text-base gap-2.5',
    'xl' => 'px-8 py-4 text-lg gap-3',
    default => 'px-4 py-2 text-sm gap-2'
};

$shapeClasses = match($shape) {
    'pill' => 'rounded-full',
    'square' => 'rounded-none',
    'rounded' => 'rounded-xl',
    default => 'rounded-xl'
};

$classes = trim("{$baseClasses} {$variantClasses} {$sizeClasses} {$shapeClasses}");

if ($disabled || $loading) {
    $classes .= ' pointer-events-none';
}
@endphp

@if($href && !$disabled && !$loading)
    <a href="{{ $href }}" {{ $attributes->merge(['class' => $classes]) }}>
        @if($loading)
            <svg class="animate-spin h-4 w-4" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
            </svg>
            <span>Loading...</span>
        @else
            @if($icon && $iconPosition === 'left')
                {!! $icon !!}
            @endif
            {{ $slot }}
            @if($icon && $iconPosition === 'right')
                {!! $icon !!}
            @endif
        @endif
    </a>
@else
    <button 
        type="{{ $type }}" 
        {{ $attributes->merge(['class' => $classes]) }}
        @if($disabled) disabled @endif
    >
        @if($loading)
            <svg class="animate-spin h-4 w-4" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
            </svg>
            <span>Loading...</span>
        @else
            @if($icon && $iconPosition === 'left')
                {!! $icon !!}
            @endif
            {{ $slot }}
            @if($icon && $iconPosition === 'right')
                {!! $icon !!}
            @endif
        @endif
    </button>
@endif

<style>
    /* Enhanced button hover effects */
    .btn-modern-hover {
        position: relative;
        overflow: hidden;
    }
    
    .btn-modern-hover::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
        transition: left 0.5s;
    }
    
    .btn-modern-hover:hover::before {
        left: 100%;
    }
    
    /* Gradient button enhancements */
    .btn-gradient-enhanced {
        background-size: 200% 200%;
        animation: gradientShift 3s ease infinite;
    }
    
    @keyframes gradientShift {
        0% { background-position: 0% 50%; }
        50% { background-position: 100% 50%; }
        100% { background-position: 0% 50%; }
    }
    
    /* High contrast theme adjustments */
    [data-theme="minewache-high-contrast"] button {
        border: 2px solid currentColor !important;
        font-weight: bold;
    }
    
    /* Colorful theme button styles */
    [data-theme="minewache-colorful"] .bg-primary {
        background: linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%);
    }
    
    [data-theme="minewache-colorful"] .bg-secondary {
        background: linear-gradient(135deg, #06b6d4 0%, #0891b2 100%);
    }
    
    [data-theme="minewache-colorful"] .bg-accent {
        background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
    }
    
    /* Focus ring improvements */
    .focus-ring-enhanced:focus {
        outline: none;
        box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.5);
    }
    
    /* Reduced motion support */
    @media (prefers-reduced-motion: reduce) {
        .btn-gradient-enhanced {
            animation: none;
        }
        
        .btn-modern-hover::before {
            transition: none;
        }
    }
</style>
