<?php

namespace Tests\Feature\Admin;

use App\Models\User;
use App\Enums\Role;
use Tests\TestCase;
use Illuminate\Foundation\Testing\RefreshDatabase;

class AdminAccessTest extends TestCase
{
    use RefreshDatabase;

    /** @test */
    public function admin_user_has_correct_role_check()
    {
        // Create an admin user
        $adminUser = User::factory()->create([
            'permissions' => (string)Role::MINEWACHE_TEAM->value,
        ]);

        // Test the hasRole method directly
        $this->assertTrue($adminUser->hasRole(Role::MINEWACHE_TEAM));
    }

    /** @test */
    public function admin_gate_works_correctly()
    {
        // Create an admin user
        $adminUser = User::factory()->create([
            'permissions' => (string)Role::MINEWACHE_TEAM->value,
        ]);

        // Act as the admin user
        $this->actingAs($adminUser);

        // Test the gate directly
        $this->assertTrue(\Gate::allows('role', Role::MINEWACHE_TEAM));
    }

    /** @test */
    public function admin_middleware_works()
    {
        // Create an admin user
        $adminUser = User::factory()->create([
            'permissions' => (string)Role::MINEWACHE_TEAM->value,
        ]);

        // Act as the admin user
        $this->actingAs($adminUser);

        // Test the tag generator route specifically
        $response = $this->get('/admin/tag-generator');

        // Should not be forbidden (403)
        $this->assertNotEquals(403, $response->status());

        // If it's a 500 error, let's see what the error is
        if ($response->status() === 500) {
            $this->fail('500 error occurred: ' . $response->getContent());
        }

        $this->assertEquals(200, $response->status());
    }
}
