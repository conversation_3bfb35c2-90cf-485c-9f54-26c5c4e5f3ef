// tailwind.config.js
const defaultTheme = require('tailwindcss/defaultTheme');
const forms = require('@tailwindcss/forms');
const daisyui = require('daisyui');

/** @type {import('tailwindcss').Config} */
export default {
    content: [
        './vendor/laravel/framework/src/Illuminate/Pagination/resources/views/*.blade.php',
        './storage/framework/views/*.php',
        './resources/views/**/*.blade.php',
    ],

    theme: {
        extend: {
            fontFamily: {
                // Primäre Schriftart behalten aber mit moderneren Fallbacks
                sans: ['<PERSON>eist Sans', '<PERSON>eist', 'Proxima Nova', ...defaultTheme.fontFamily.sans],
                // Moderne serifenlose Schriften für 2025
                modern: ['Geist', 'SK-Modernist', ...defaultTheme.fontFamily.sans],
                // Serifenschrift für Kontrast
                serif: ['<PERSON>tera<PERSON>', 'Harmond Display', ...defaultTheme.fontFamily.serif],
                // Geometrische Option
                geometric: ['Sora', ...defaultTheme.fontFamily.sans],
                // Elegante Display-Schrift
                display: ['Anona', 'Stanley', ...defaultTheme.fontFamily.sans],
            },
            fontSize: {
                'title': '2.6rem',
                'paragraph': '1.2rem',
            },
            opacity: {
                '85': '0.85',
                '80': '0.8',
            },
            keyframes: {
                fadeIn: {
                    '0%': { opacity: '0' },
                    '100%': { opacity: '1' }
                },
                slideUp: {
                    '0%': {
                        transform: 'translateY(20px)',
                        opacity: '0'
                    },
                    '100%': {
                        transform: 'translateY(0)',
                        opacity: '1'
                    }
                },
                slideDown: {
                    '0%': {
                        transform: 'translateY(-20px)',
                        opacity: '0'
                    },
                    '100%': {
                        transform: 'translateY(0)',
                        opacity: '1'
                    }
                },
                slideLeft: {
                    '0%': {
                        transform: 'translateX(20px)',
                        opacity: '0'
                    },
                    '100%': {
                        transform: 'translateX(0)',
                        opacity: '1'
                    }
                },
                slideRight: {
                    '0%': {
                        transform: 'translateX(-20px)',
                        opacity: '0'
                    },
                    '100%': {
                        transform: 'translateX(0)',
                        opacity: '1'
                    }
                },
                pulse: {
                    '0%, 100%': { opacity: '1' },
                    '50%': { opacity: '0.7' }
                },
                bounce: {
                    '0%, 100%': { transform: 'translateY(0)' },
                    '50%': { transform: 'translateY(-5px)' }
                }
            },
            animation: {
                'fade-in': 'fadeIn 0.7s ease-in-out forwards',
                'slide-up': 'slideUp 0.7s ease-out forwards',
                'slide-down': 'slideDown 0.7s ease-out forwards',
                'slide-left': 'slideLeft 0.7s ease-out forwards',
                'slide-right': 'slideRight 0.7s ease-out forwards',
                'pulse': 'pulse 2.5s cubic-bezier(0.4, 0, 0.6, 1) infinite',
                'bounce': 'bounce 2s infinite',
                'delayed-fade': 'fadeIn 0.7s ease-in-out 0.3s forwards',
                'delayed-slide': 'slideUp 0.7s ease-out 0.3s forwards'
            }
        },
    },

    plugins: [forms, daisyui],

    daisyui: {
        themes: [
            {
                "minewache": {
                    "primary": "#3b82f6",                           // Blue-500 for primary actions
                    "primary-content": "#ffffff",                    // White text on primary
                    "primary-focus": "#2563eb",                      // Blue-600 for focus states
                    "secondary": "#475569",                          // Slate-600
                    "secondary-content": "#ffffff",                  // White text on secondary
                    "secondary-focus": "#334155",                    // Slate-700 for focus
                    "accent": "#1e293b",                             // Slate-800
                    "accent-content": "#ffffff",                     // White text on accent
                    "accent-focus": "#0f172a",                       // Slate-900 for focus
                    "neutral": "#1e293b",                            // Slate-800
                    "neutral-content": "#ffffff",                    // White text on neutral
                    "base-100": "#0f172a",                           // Slate-900 for main background
                    "base-200": "#1e293b",                           // Slate-800 for secondary background
                    "base-300": "#334155",                           // Slate-700 for tertiary background
                    "base-content": "#f8fafc",                       // Slate-50 for main text
                    "info": "#3b82f6",                               // Blue-500
                    "info-content": "#ffffff",                       // White text on info
                    "info-focus": "#2563eb",                         // Blue-600 for focus
                    "success": "#10b981",                            // Emerald-500
                    "success-content": "#ffffff",                    // White text on success
                    "success-focus": "#059669",                      // Emerald-600 for focus
                    "warning": "#f59e0b",                            // Amber-500
                    "warning-content": "#ffffff",                    // White text on warning
                    "warning-focus": "#d97706",                      // Amber-600 for focus
                    "error": "#ef4444",                              // Red-500
                    "error-content": "#ffffff",                      // White text on error
                    "error-focus": "#dc2626",                        // Red-600 for focus
                    "--rounded-box": "1.5rem",                       // Larger rounded corners for boxes
                    "--rounded-btn": "9999px",                       // Fully rounded buttons (pill shape)
                    "--rounded-badge": "9999px",                     // Fully rounded badges
                    "--animation-btn": "0.25s",
                    "--animation-input": "0.2s",
                    "--btn-focus-scale": "0.95",
                    "--border-btn": "1px",
                    "--tab-border": "1px",
                    "--tab-radius": "0.5rem",
                }
            },
            {
                "minewache-light": {
                    "primary": "#3b82f6",                           // Blue-500 for primary actions
                    "primary-content": "#ffffff",                    // White text on primary
                    "primary-focus": "#2563eb",                      // Blue-600 for focus states
                    "secondary": "#475569",                          // Slate-600
                    "secondary-content": "#ffffff",                  // White text on secondary
                    "secondary-focus": "#334155",                    // Slate-700 for focus
                    "accent": "#60a5fa",                             // Blue-400
                    "accent-content": "#ffffff",                     // White text on accent
                    "accent-focus": "#3b82f6",                       // Blue-500 for focus
                    "neutral": "#e2e8f0",                            // Slate-200
                    "neutral-content": "#1e293b",                    // Slate-800 text on neutral
                    "base-100": "#f8fafc",                           // Slate-50 for main background
                    "base-200": "#f1f5f9",                           // Slate-100 for secondary background
                    "base-300": "#e2e8f0",                           // Slate-200 for tertiary background
                    "base-content": "#0f172a",                       // Slate-900 for main text
                    "info": "#3b82f6",                               // Blue-500
                    "info-content": "#ffffff",                       // White text on info
                    "info-focus": "#2563eb",                         // Blue-600 for focus
                    "success": "#10b981",                            // Emerald-500
                    "success-content": "#ffffff",                    // White text on success
                    "success-focus": "#059669",                      // Emerald-600 for focus
                    "warning": "#f59e0b",                            // Amber-500
                    "warning-content": "#ffffff",                    // White text on warning
                    "warning-focus": "#d97706",                      // Amber-600 for focus
                    "error": "#ef4444",                              // Red-500
                    "error-content": "#ffffff",                      // White text on error
                    "error-focus": "#dc2626",                        // Red-600 for focus
                    "--rounded-box": "1.5rem",                       // Larger rounded corners for boxes
                    "--rounded-btn": "9999px",                       // Fully rounded buttons (pill shape)
                    "--rounded-badge": "9999px",                     // Fully rounded badges
                    "--animation-btn": "0.25s",
                    "--animation-input": "0.2s",
                    "--btn-focus-scale": "0.95",
                    "--border-btn": "1px",
                    "--tab-border": "1px",
                    "--tab-radius": "0.5rem",
                }
            },
            {
                "minewache-auto": {
                    "primary": "#3b82f6",
                    "primary-content": "#ffffff",
                    "primary-focus": "#2563eb",
                    "secondary": "#475569",
                    "secondary-content": "#ffffff",
                    "secondary-focus": "#334155",
                    "accent": "#1e293b",
                    "accent-content": "#ffffff",
                    "accent-focus": "#0f172a",
                    "neutral": "#1e293b",
                    "neutral-content": "#ffffff",
                    "base-100": "#0f172a",
                    "base-200": "#1e293b",
                    "base-300": "#334155",
                    "base-content": "#f8fafc",
                    "info": "#3b82f6",
                    "info-content": "#ffffff",
                    "success": "#10b981",
                    "success-content": "#ffffff",
                    "warning": "#f59e0b",
                    "warning-content": "#ffffff",
                    "error": "#ef4444",
                    "error-content": "#ffffff",
                    "--rounded-box": "1.5rem",
                    "--rounded-btn": "9999px",
                    "--rounded-badge": "9999px",
                    "--animation-btn": "0.25s",
                    "--animation-input": "0.2s",
                    "--btn-focus-scale": "0.95",
                    "--border-btn": "1px",
                    "--tab-border": "1px",
                    "--tab-radius": "0.5rem",
                }
            },
            {
                "minewache-high-contrast": {
                    "primary": "#0066ff",
                    "primary-content": "#ffffff",
                    "primary-focus": "#0052cc",
                    "secondary": "#000000",
                    "secondary-content": "#ffffff",
                    "secondary-focus": "#333333",
                    "accent": "#ff6600",
                    "accent-content": "#ffffff",
                    "accent-focus": "#cc5200",
                    "neutral": "#000000",
                    "neutral-content": "#ffffff",
                    "base-100": "#000000",
                    "base-200": "#1a1a1a",
                    "base-300": "#333333",
                    "base-content": "#ffffff",
                    "info": "#00ccff",
                    "info-content": "#000000",
                    "success": "#00ff00",
                    "success-content": "#000000",
                    "warning": "#ffff00",
                    "warning-content": "#000000",
                    "error": "#ff0000",
                    "error-content": "#ffffff",
                    "--rounded-box": "0.5rem",
                    "--rounded-btn": "0.5rem",
                    "--rounded-badge": "0.5rem",
                    "--animation-btn": "0.1s",
                    "--animation-input": "0.1s",
                    "--btn-focus-scale": "1",
                    "--border-btn": "2px",
                    "--tab-border": "2px",
                    "--tab-radius": "0.25rem",
                }
            },
            {
                "minewache-colorful": {
                    "primary": "#8b5cf6",                           // Purple-500
                    "primary-content": "#ffffff",
                    "primary-focus": "#7c3aed",                      // Purple-600
                    "secondary": "#06b6d4",                          // Cyan-500
                    "secondary-content": "#ffffff",
                    "secondary-focus": "#0891b2",                    // Cyan-600
                    "accent": "#f59e0b",                             // Amber-500
                    "accent-content": "#ffffff",
                    "accent-focus": "#d97706",                       // Amber-600
                    "neutral": "#1e293b",
                    "neutral-content": "#ffffff",
                    "base-100": "#0f172a",
                    "base-200": "#1e293b",
                    "base-300": "#334155",
                    "base-content": "#f8fafc",
                    "info": "#06b6d4",
                    "info-content": "#ffffff",
                    "success": "#10b981",
                    "success-content": "#ffffff",
                    "warning": "#f59e0b",
                    "warning-content": "#ffffff",
                    "error": "#ef4444",
                    "error-content": "#ffffff",
                    "--rounded-box": "2rem",
                    "--rounded-btn": "9999px",
                    "--rounded-badge": "9999px",
                    "--animation-btn": "0.3s",
                    "--animation-input": "0.3s",
                    "--btn-focus-scale": "0.95",
                    "--border-btn": "1px",
                    "--tab-border": "1px",
                    "--tab-radius": "1rem",
                }
            },
        ],
    },
}
