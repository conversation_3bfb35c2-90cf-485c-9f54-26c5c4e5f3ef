<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Find applications without a user_id and assign them to users based on discord_id
        $applications = DB::table('applications')->whereNull('user_id')->get();
        
        foreach ($applications as $application) {
            // Find the user with the matching discord_id
            $user = DB::table('users')->where('id', $application->discord_id)->first();
            
            if ($user) {
                // Update the application with the user_id
                DB::table('applications')
                    ->where('id', $application->id)
                    ->update(['user_id' => $user->id]);
            }
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // This migration cannot be reversed as it would be unclear which applications to unassign
    }
};
