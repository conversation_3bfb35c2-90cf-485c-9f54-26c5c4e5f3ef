<div>
    <div class="bg-gradient-modern min-h-screen py-8 px-4">
        <x-modern-card variant="elevated" size="md">
            <div class="flex items-center gap-3 mb-6">
                <x-modern-button
                    variant="ghost"
                    size="sm"
                    href="{{ route('tickets.index') }}"
                    icon='<svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path></svg>'
                >
                    {{ __('tickets.back_to_tickets') }}
                </x-modern-button>
                <div class="h-6 w-px bg-base-300"></div>
                <h1 class="text-2xl font-bold text-base-content">
                    {{ __('tickets.create_ticket') }}
                </h1>
            </div>

            <form wire:submit.prevent="submit" class="space-y-6">
                <x-modern-input
                    wire:model="title"
                    type="text"
                    id="title"
                    label="{{ __('tickets.title') }}"
                    placeholder="{{ __('tickets.title_placeholder') }}"
                    variant="glass"
                    required
                    :error="$errors->first('title')"
                />

                <div>
                    <label for="description" class="block text-sm font-medium text-base-content/70 mb-2">{{ __('tickets.description') }}</label>
                    <textarea
                        wire:model="description"
                        id="description"
                        rows="6"
                        class="w-full bg-base-100 border border-base-300 rounded-xl px-4 py-3 focus:outline-none focus:ring-2 focus:ring-primary focus:border-primary transition-all duration-300 resize-none"
                        placeholder="{{ __('tickets.description_placeholder') }}"
                        required
                    ></textarea>
                    @error('description')
                        <div class="mt-2 text-error text-sm flex items-center gap-1">
                            <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clip-rule="evenodd"></path>
                            </svg>
                            {{ $message }}
                        </div>
                    @enderror
                </div>

                <div class="flex justify-end gap-3 pt-4 border-t border-base-300/30">
                    <x-modern-button
                        variant="ghost"
                        size="md"
                        href="{{ route('tickets.index') }}"
                    >
                        {{ __('tickets.cancel') }}
                    </x-modern-button>
                    <x-modern-button
                        type="submit"
                        variant="primary"
                        size="md"
                        wire:loading.attr="disabled"
                        wire:target="submit"
                        icon='<svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8"></path></svg>'
                    >
                        <span wire:loading.remove wire:target="submit">{{ __('tickets.submit') }}</span>
                        <span wire:loading wire:target="submit" class="flex items-center gap-2">
                            <x-modern-loading type="spinner" size="xs" color="base" />
                            {{ __('tickets.submitting') }}
                        </span>
                    </x-modern-button>
                </div>
            </form>
        </x-modern-card>
    </div>
</div>
