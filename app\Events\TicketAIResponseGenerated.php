<?php

namespace App\Events;

use App\Models\TicketMessage;
use Illuminate\Broadcasting\Channel;
use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Broadcasting\PresenceChannel;
use Illuminate\Broadcasting\PrivateChannel;
use Illuminate\Contracts\Broadcasting\ShouldBroadcast;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;

class TicketAIResponseGenerated implements ShouldBroadcast
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    public $message;

    /**
     * Create a new event instance.
     */
    public function __construct(TicketMessage $message)
    {
        $this->message = $message;
        // Load relationships to include in the broadcast
        $this->message->load(['user', 'attachments']);

        // Log the event creation
        Log::info('TicketAIResponseGenerated event created', [
            'message_id' => $message->id,
            'ticket_id' => $message->ticket_id,
        ]);
    }

    /**
     * Get the channels the event should broadcast on.
     *
     * @return array<int, \Illuminate\Broadcasting\Channel>
     */
    public function broadcastOn(): array
    {
        return [
            new PrivateChannel('tickets.' . $this->message->ticket_id),
        ];
    }

    /**
     * The event's broadcast name.
     */
    public function broadcastAs(): string
    {
        return 'TicketAIResponseGenerated';
    }

    /**
     * Get the data to broadcast.
     *
     * @return array<string, mixed>
     */
    public function broadcastWith(): array
    {
        return [
            'message' => $this->message->toArray(),
            'timestamp' => now()->timestamp,
        ];
    }

    /**
     * Specify the broadcasting connection to use.
     *
     * @return string
     */
    public function broadcastVia(): string
    {
        Log::info('Broadcasting TicketAIResponseGenerated via reverb', [
            'ticket_id' => $this->message->ticket_id,
            'message_id' => $this->message->id,
        ]);
        return 'reverb';
    }
}
