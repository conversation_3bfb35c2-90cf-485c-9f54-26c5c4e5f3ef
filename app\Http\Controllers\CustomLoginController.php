<?php

namespace App\Http\Controllers;

use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Cookie;
use Illuminate\Support\Facades\Log;

class CustomLoginController extends Controller
{
    /**
     * Handle the login request.
     *
     * @param Request $request
     * @return RedirectResponse
     */
    public function login(Request $request)
    {
        // Check if this is a redirect after GDPR consent
        $fromConsent = $request->query('from_consent') === 'true';

        // Only log in debug mode
        if (config('app.debug')) {
            Log::info('Discord login request', [
                'client_id' => config('larascord.client_id'),
                'from_consent' => $fromConsent,
                'has_gdpr_cookie' => Cookie::has('gdpr_consent'),
                'session_id' => session()->getId(),
                'intended_url' => session('url.intended'),
            ]);
        }

        // Check if GDPR consent has been given
        if (!Cookie::has('gdpr_consent')) {
            if (config('app.debug')) {
                Log::info('No GDPR consent cookie found, redirecting to home with consent modal');
            }

            // Store the intended URL to redirect back after consent
            session(['url.intended' => route('auth.discord', ['from_consent' => 'true'])]);
            session()->save();

            // Redirect to home page with GDPR consent modal
            return redirect()->route('home')->with('show_gdpr_consent', true);
        }

        if (config('app.debug')) {
            Log::info('GDPR consent cookie found, proceeding to Discord OAuth');
        }

        // Proceed to the original login route using Larascord configuration
        $prefix = config('larascord.route_prefix', 'larascord');
        $callbackUrl = url('/' . $prefix . '/callback');
        $clientId = config('larascord.client_id');

        // Clear any existing login session data to prevent conflicts
        // Only do this if we're not coming from a consent redirect to avoid
        // clearing the session data that was just set
        if (!$fromConsent) {
            session()->forget(['fresh_discord_login', 'discord_login_timestamp']);

            // Store a session flag to indicate this is a fresh login attempt
            // This helps prevent the double login issue
            session(['fresh_discord_login' => true]);

            // Also store the timestamp to detect stale login attempts
            session(['discord_login_timestamp' => now()->timestamp]);

            // Ensure the session is saved immediately
            session()->save();
        }

        // Get scopes and ensure they're properly formatted for Discord OAuth
        // Discord expects scopes to be space-separated, not &-separated
        $scopes = config('larascord.scopes', 'identify');
        // Replace any & with spaces if they exist in the config
        $scopes = str_replace('&', ' ', $scopes);

        Log::info("Using Discord OAuth scopes: {$scopes}");

        if (empty($clientId)) {
            Log::error('Discord client ID not found in configuration');
            return redirect()->route('home')->with('error', 'Discord authentication is not properly configured.');
        }

        Log::info("Redirecting to Discord OAuth with callback URL: {$callbackUrl}");

        // Log the scopes for debugging
        Log::info("Using Discord OAuth scopes: {$scopes}");

        // Redirect to the standard Discord OAuth URL
        return redirect()->away(
            'https://discord.com/api/oauth2/authorize'
            . '?client_id=' . $clientId
            . '&redirect_uri=' . urlencode($callbackUrl)
            . '&response_type=code'
            . '&scope=' . urlencode($scopes) // URL encode the scopes to handle spaces correctly
            . '&prompt=' . config('larascord.prompt', 'none')
        );
    }
}
