<x-guest-layout>
    <x-slot name="heading">
        Datenschutz-Einwilligung
    </x-slot>

    <div class="min-h-screen flex flex-col sm:justify-center items-center pt-6 sm:pt-0">

    <div class="card bg-base-100 shadow-xl w-full max-w-2xl">
        <div class="card-body p-6 md:p-8">
            <div class="flex items-center gap-3 mb-6">
                <div class="bg-primary/10 p-3 rounded-full">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-primary" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" />
                    </svg>
                </div>
                <h2 class="text-2xl font-display font-semibold">Datenschutz-Einwilligung</h2>
            </div>

            @if(config('app.debug'))
            <div class="alert alert-info mb-4 text-xs">
                <div>
                    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" class="stroke-current shrink-0 w-6 h-6"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path></svg>
                    <span>Debug: GDPR Consent Cookie Status: {{ \Illuminate\Support\Facades\Cookie::has('gdpr_consent') ? 'Present' : 'Not Present' }}</span>
                </div>
            </div>
            @endif

            <div class="prose max-w-none mb-6">
                <p class="text-base-content/80 text-lg">
                    Bevor du dich mit Discord anmeldest, möchten wir dich über die Verarbeitung deiner Daten informieren und deine Einwilligung einholen.
                </p>

                <div class="bg-base-200 rounded-lg p-4 mt-6 flex gap-4">
                    <div class="shrink-0 mt-1">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-primary" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                        </svg>
                    </div>
                    <div>
                        <h3 class="text-lg font-medium">Welche Daten werden erhoben?</h3>
                        <p class="mt-1 text-sm text-base-content/80">
                            Bei der Anmeldung über Discord werden folgende Daten verarbeitet:
                        </p>
                        <ul class="mt-2 text-sm space-y-1 list-disc list-inside">
                            <li>Discord-ID</li>
                            <li>Benutzername</li>
                            <li>Globaler Name</li>
                            <li>Avatar</li>
                            <li>Weitere Profilinformationen, die in deinen Discord-Einstellungen als öffentlich markiert sind</li>
                        </ul>
                    </div>
                </div>

                <div class="bg-base-200 rounded-lg p-4 mt-4 flex gap-4">
                    <div class="shrink-0 mt-1">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-primary" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 7h6m0 10v-3m-3 3h.01M9 17h.01M9 14h.01M12 14h.01M15 11h.01M12 11h.01M9 11h.01M7 21h10a2 2 0 002-2V5a2 2 0 00-2-2H7a2 2 0 00-2 2v14a2 2 0 002 2z" />
                        </svg>
                    </div>
                    <div>
                        <h3 class="text-lg font-medium">Zweck der Datenverarbeitung</h3>
                        <p class="mt-1 text-sm text-base-content/80">
                            Diese Daten werden für folgende Zwecke verwendet:
                        </p>
                        <ul class="mt-2 text-sm space-y-1 list-disc list-inside">
                            <li>Zur Erstellung und Verwaltung deines Benutzerkontos</li>
                            <li>Zur Authentifizierung und Identifizierung</li>
                            <li>Zur Kommunikation im Rahmen der Nutzung unserer Dienste</li>
                        </ul>
                    </div>
                </div>

                <div class="bg-base-200 rounded-lg p-4 mt-4 flex gap-4">
                    <div class="shrink-0 mt-1">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-primary" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 6l3 1m0 0l-3 9a5.002 5.002 0 006.001 0M6 7l3 9M6 7l6-2m6 2l3-1m-3 1l-3 9a5.002 5.002 0 006.001 0M18 7l3 9m-3-9l-6-2m0-2v2m0 16V5m0 16H9m3 0h3" />
                        </svg>
                    </div>
                    <div>
                        <h3 class="text-lg font-medium">Rechtsgrundlage</h3>
                        <p class="mt-1 text-sm text-base-content/80">
                            Die Rechtsgrundlage für die Verarbeitung ist Art. 6 Abs. 1 lit. a DSGVO (deine Einwilligung) sowie Art. 6 Abs. 1 lit. b DSGVO (Erfüllung des Nutzungsvertrags).
                        </p>
                    </div>
                </div>

                <div class="bg-base-200 rounded-lg p-4 mt-4 flex gap-4">
                    <div class="shrink-0 mt-1">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-primary" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                        </svg>
                    </div>
                    <div>
                        <h3 class="text-lg font-medium">Speicherdauer</h3>
                        <p class="mt-1 text-sm text-base-content/80">
                            Deine Daten werden für die Dauer deiner Mitgliedschaft gespeichert. Du kannst jederzeit die Löschung deiner Daten über die Datenschutzeinstellungen beantragen.
                        </p>
                    </div>
                </div>

                <div class="bg-base-200 rounded-lg p-4 mt-4 flex gap-4">
                    <div class="shrink-0 mt-1">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-primary" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 11V7a4 4 0 118 0m-4 8v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2z" />
                        </svg>
                    </div>
                    <div>
                        <h3 class="text-lg font-medium">Deine Rechte</h3>
                        <p class="mt-1 text-sm text-base-content/80">
                            Du hast das Recht auf Auskunft, Berichtigung, Löschung und Einschränkung der Verarbeitung deiner Daten. Weitere Informationen findest du in unserer <a href="{{ route('datenschutz') }}" class="text-primary underline" target="_blank">Datenschutzerklärung</a>.
                        </p>
                    </div>
                </div>
            </div>

            <div class="divider my-6"></div>

            <form method="post" action="{{ route('auth.consent.store') }}" class="space-y-6">
                @csrf

                <div class="bg-primary/5 border border-primary/20 rounded-lg p-4">
                    <label class="flex items-start space-x-3 cursor-pointer">
                        <input type="checkbox" name="consent" value="1" class="checkbox checkbox-primary mt-1" required />
                        <span class="text-sm">
                            Ich habe die Informationen zur Datenverarbeitung gelesen und stimme der Verarbeitung meiner Daten gemäß der <a href="{{ route('datenschutz') }}" class="text-primary underline" target="_blank">Datenschutzerklärung</a> zu.
                        </span>
                    </label>
                    @error('consent')
                        <span class="text-error text-sm mt-1 block pl-7">{{ $message }}</span>
                    @enderror
                </div>

                <div class="flex justify-between items-center">
                    <a href="{{ route('home') }}" class="btn btn-ghost btn-sm">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18" />
                        </svg>
                        Zurück
                    </a>
                    <button type="submit" class="btn btn-primary">
                        Mit Discord fortfahren
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 ml-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M14 5l7 7m0 0l-7 7m7-7H3" />
                        </svg>
                    </button>
                </div>
            </form>
        </div>
    </div>
    </div>
</x-guest-layout>
