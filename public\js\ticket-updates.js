/**
 * Ticket Updates <PERSON><PERSON>
 *
 * This script handles real-time updates for ticket messages.
 * It listens for Livewire events and updates the UI accordingly.
 */
document.addEventListener('DOMContentLoaded', function() {
    console.log('Ticket updates handler initialized');

    // Function to scroll to the bottom of the messages container
    function scrollToBottom() {
        const messagesContainer = document.querySelector('.messages-container');
        if (messagesContainer) {
            messagesContainer.scrollTop = messagesContainer.scrollHeight;
        }
    }

    // Base64-encoded notification sound (short beep)
    const soundBase64 = 'data:audio/mp3;base64,SUQzBAAAAAAAI1RTU0UAAAAPAAADTGF2ZjU4Ljc2LjEwMAAAAAAAAAAAAAAA//tQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAWGluZwAAAA8AAAACAAADwAD///////////////////////////////////////////8AAAA8TEFNRTMuMTAwAc0AAAAAAAAAABSAJAJAQgAAgAAAA8DcWcGJAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA//sQZAAP8AAAaQAAAAgAAA0gAAABAAABpAAAACAAADSAAAAETEFNRTMuMTAwVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVU=';

    // Preload notification sound
    const notificationSound = new Audio(soundBase64);
    notificationSound.preload = 'auto';
    let soundLoaded = false;

    // Check if sound toggle is enabled
    function isSoundEnabled() {
        const soundToggle = document.getElementById('sound-toggle');
        return soundToggle ? soundToggle.checked : true; // Default to true if toggle not found
    }

    // Load sound when page loads
    notificationSound.addEventListener('canplaythrough', () => {
        console.log('Notification sound loaded and ready to play');
        soundLoaded = true;
    });

    // Handle loading error
    notificationSound.addEventListener('error', (e) => {
        console.error('Error loading notification sound:', e);
        soundLoaded = false;
    });

    // Function to play notification sound
    function playNotificationSound() {
        // Only play if sound is enabled
        if (!isSoundEnabled()) {
            console.log('Sound notifications are disabled');
            return;
        }

        try {
            console.log('Playing notification sound');

            // If the preloaded sound is ready, use it
            if (soundLoaded) {
                // Reset the sound to the beginning if it's already playing
                notificationSound.currentTime = 0;

                // Play the sound
                const playPromise = notificationSound.play();

                if (playPromise !== undefined) {
                    playPromise.catch(error => {
                        console.error('Error playing notification sound:', error);
                        // Try alternative approach if the main one fails
                        playFallbackSound();
                    });
                }
            } else {
                // If preloaded sound isn't ready, try a new instance
                playFallbackSound();
            }
        } catch (error) {
            console.error('Error playing notification sound:', error);
            playFallbackSound();
        }
    }

    // Fallback sound function
    function playFallbackSound() {
        try {
            // Create a new Audio instance as fallback using the base64 sound
            const fallbackAudio = new Audio(soundBase64);
            fallbackAudio.volume = 0.5;
            fallbackAudio.play().catch(error => {
                console.error('Fallback sound also failed:', error);
                // Last resort: try a simple beep sound
                try {
                    // Simple beep using AudioContext API
                    const audioContext = new (window.AudioContext || window.webkitAudioContext)();
                    const oscillator = audioContext.createOscillator();
                    oscillator.type = 'sine';
                    oscillator.frequency.setValueAtTime(800, audioContext.currentTime); // value in hertz
                    oscillator.connect(audioContext.destination);
                    oscillator.start();
                    oscillator.stop(audioContext.currentTime + 0.2); // short beep
                } catch (e) {
                    console.error('All sound playback attempts failed:', e);
                }
            });
        } catch (error) {
            console.error('Error creating fallback Audio object:', error);
        }
    }

    // Debounce function to prevent multiple calls in quick succession
    function debounce(func, wait) {
        let timeout;
        return function() {
            const context = this;
            const args = arguments;
            clearTimeout(timeout);
            timeout = setTimeout(() => func.apply(context, args), wait);
        };
    }

    // Debounced version of notification functions
    const debouncedPlaySound = debounce(() => {
        playNotificationSound();
    }, 300); // 300ms debounce

    const debouncedScrollToBottom = debounce(() => {
        scrollToBottom();
    }, 100); // 100ms debounce

    // Function to handle new message events
    function handleNewMessage(data) {
        console.log('New message event received:', data);

        // Show new message indicator if user has scrolled up
        const messagesContainer = document.querySelector('.messages-container');
        if (messagesContainer) {
            const isScrolledToBottom = messagesContainer.scrollHeight - messagesContainer.clientHeight <= messagesContainer.scrollTop + 100;

            if (!isScrolledToBottom) {
                // Show new message indicator
                const indicator = document.getElementById('new-message-indicator');
                if (indicator) {
                    indicator.classList.remove('hidden');
                    indicator.addEventListener('click', () => {
                        scrollToBottom();
                        indicator.classList.add('hidden');
                    }, { once: true });
                }
            } else {
                // User is at the bottom, scroll to show new message
                debouncedScrollToBottom();
            }
        } else {
            console.warn('Messages container not found');
        }

        // Play notification sound (debounced)
        debouncedPlaySound();
    }

    // Listen for Livewire events
    document.addEventListener('livewire:initialized', () => {
        // Listen for the messageUpdated event
        Livewire.on('messageUpdated', (data) => {
            console.log('Message updated event received:', data);
            handleNewMessage(data);
        });

        // Listen for the messageAdded event (optimized for single message)
        Livewire.on('messageAdded', (data) => {
            console.log('Message added event received:', data);
            handleNewMessage(data);
        });

        // Listen for the ticket-updated event
        Livewire.on('ticket-updated', () => {
            console.log('Ticket updated event received');
            debouncedScrollToBottom();
        });

        // Listen for the ticket-message-sent event
        Livewire.on('ticket-message-sent', (data) => {
            console.log('Ticket message sent event received:', data);
            debouncedScrollToBottom();
        });

        // Initialize WebSocket connection
        function initializeWebSocket() {
            if (window.Echo) {
                const ticketId = document.querySelector('meta[name="ticket-id"]')?.getAttribute('content');
                if (ticketId) {
                    console.log(`Initializing WebSocket connection for ticket ${ticketId}`);

                    // Subscribe to the private ticket channel
                    const channel = window.Echo.private(`tickets.${ticketId}`);

                    // Listen for new messages
                    channel.listen('.new-message', (data) => {
                        console.log('WebSocket: New message received', data);
                        handleNewMessage(data);
                    });

                    // Listen for typing events
                    channel.listen('.typing', (data) => {
                        console.log('WebSocket: Typing event received', data);
                        handleTypingEvent(data);
                    });

                    return channel;
                }
            }
            return null;
        }

        // Handle typing events
        function handleTypingEvent(data) {
            // Ignore own typing events
            const currentUserId = document.querySelector('meta[name="user-id"]')?.getAttribute('content');
            if (data.user_id === parseInt(currentUserId)) {
                return;
            }

            const typingIndicator = document.getElementById('typing-indicator');
            if (!typingIndicator) return;

            if (data.is_typing) {
                // Show typing indicator
                typingIndicator.classList.remove('hidden');

                // Store typing user info in a data attribute
                const typingUsers = JSON.parse(typingIndicator.getAttribute('data-typing-users') || '{}');
                typingUsers[data.user_id] = {
                    username: data.username,
                    timestamp: data.timestamp
                };
                typingIndicator.setAttribute('data-typing-users', JSON.stringify(typingUsers));

                // Update typing indicator text
                updateTypingIndicatorText(typingUsers);

                // Auto-hide after 5 seconds if no update
                setTimeout(() => {
                    const currentTypingUsers = JSON.parse(typingIndicator.getAttribute('data-typing-users') || '{}');
                    if (currentTypingUsers[data.user_id] &&
                        currentTypingUsers[data.user_id].timestamp === data.timestamp) {
                        // Remove this user from typing users
                        delete currentTypingUsers[data.user_id];
                        typingIndicator.setAttribute('data-typing-users', JSON.stringify(currentTypingUsers));

                        // Update or hide typing indicator
                        if (Object.keys(currentTypingUsers).length === 0) {
                            typingIndicator.classList.add('hidden');
                        } else {
                            updateTypingIndicatorText(currentTypingUsers);
                        }
                    }
                }, 5000);
            } else {
                // Remove user from typing users
                const typingUsers = JSON.parse(typingIndicator.getAttribute('data-typing-users') || '{}');
                delete typingUsers[data.user_id];
                typingIndicator.setAttribute('data-typing-users', JSON.stringify(typingUsers));

                // Update or hide typing indicator
                if (Object.keys(typingUsers).length === 0) {
                    typingIndicator.classList.add('hidden');
                } else {
                    updateTypingIndicatorText(typingUsers);
                }
            }
        }

        // Update typing indicator text based on who is typing
        function updateTypingIndicatorText(typingUsers) {
            const typingIndicator = document.getElementById('typing-indicator');
            const typingTextElement = typingIndicator.querySelector('.text-sm.font-semibold');
            if (!typingTextElement) return;

            const userCount = Object.keys(typingUsers).length;
            let text = '';

            if (userCount === 1) {
                // One user typing
                const username = Object.values(typingUsers)[0].username;
                text = `${username} schreibt...`;
            } else if (userCount === 2) {
                // Two users typing
                const users = Object.values(typingUsers);
                text = `${users[0].username} und ${users[1].username} schreiben...`;
            } else if (userCount > 2) {
                // Multiple users typing
                text = `${userCount} Personen schreiben...`;
            }

            typingTextElement.textContent = text;
        }

        // Initialize WebSocket connection
        const channel = initializeWebSocket();

        // Listen for typing events from the textarea - Livewire 3 style
        document.addEventListener('livewire:init', () => {
            Livewire.on('userTyping', (isTyping) => {
                console.log('User typing event:', isTyping);
                // This will be handled by the Livewire component
            });
        });

        // Add scroll event listener to show/hide scroll-to-bottom button
        const messagesContainer = document.querySelector('.messages-container');
        const scrollButton = document.getElementById('scroll-to-bottom');

        if (messagesContainer && scrollButton) {
            messagesContainer.addEventListener('scroll', () => {
                const isScrolledUp = messagesContainer.scrollHeight - messagesContainer.clientHeight > messagesContainer.scrollTop + 200;

                if (isScrolledUp) {
                    scrollButton.classList.remove('hidden');
                } else {
                    scrollButton.classList.add('hidden');
                }
            });

            // Add click event to scroll button
            scrollButton.addEventListener('click', () => {
                scrollToBottom();
                // Also hide the new message indicator
                const indicator = document.getElementById('new-message-indicator');
                if (indicator) indicator.classList.add('hidden');
            });
        }
    });

    // Initial scroll to bottom
    setTimeout(scrollToBottom, 500);
});
