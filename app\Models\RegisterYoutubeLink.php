<?php

namespace App\Models;

use Carbon\Carbon;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class RegisterYoutubeLink extends Model
{
    use HasFactory;

    protected $fillable = [
        'season',
        'episode',
        'link',
        'title',
        'description',
        'thumbnail_url',
        'published_at',
        'duration_seconds',
        'view_count',
        'is_featured',
        'status',
    ];

    protected $casts = [
        'published_at' => 'datetime',
        'is_featured' => 'boolean',
        'duration_seconds' => 'integer',
        'view_count' => 'integer',
    ];

    /**
     * Get the formatted duration (MM:SS or HH:MM:SS)
     */
    public function getFormattedDurationAttribute(): string
    {
        if (!$this->duration_seconds) {
            return '00:00';
        }

        $hours = floor($this->duration_seconds / 3600);
        $minutes = floor(($this->duration_seconds % 3600) / 60);
        $seconds = $this->duration_seconds % 60;

        if ($hours > 0) {
            return sprintf('%02d:%02d:%02d', $hours, $minutes, $seconds);
        }

        return sprintf('%02d:%02d', $minutes, $seconds);
    }

    /**
     * Get the formatted view count (e.g., 1.2K, 3.5M)
     */
    public function getFormattedViewCountAttribute(): string
    {
        if (!$this->view_count) {
            return '0 views';
        }

        if ($this->view_count >= 1000000) {
            return round($this->view_count / 1000000, 1) . 'M views';
        }

        if ($this->view_count >= 1000) {
            return round($this->view_count / 1000, 1) . 'K views';
        }

        return $this->view_count . ' views';
    }

    /**
     * Get the formatted published date (e.g., "2 months ago")
     */
    public function getPublishedTimeAgoAttribute(): string
    {
        if (!$this->published_at) {
            return '';
        }

        return $this->published_at->diffForHumans();
    }

    /**
     * Get the YouTube watch URL
     */
    public function getYoutubeUrlAttribute(): string
    {
        return 'https://www.youtube.com/watch?v=' . $this->link;
    }

    /**
     * Get the YouTube embed URL
     */
    public function getEmbedUrlAttribute(): string
    {
        return 'https://www.youtube.com/embed/' . $this->link;
    }

    /**
     * Scope a query to only include featured videos
     */
    public function scopeFeatured($query)
    {
        return $query->where('is_featured', true);
    }

    /**
     * Scope a query to only include active videos
     */
    public function scopeActive($query)
    {
        return $query->where('status', 'active');
    }

    /**
     * Scope a query to order by season and episode
     */
    public function scopeOrdered($query)
    {
        return $query->orderBy('season', 'desc')
                     ->orderBy('episode', 'asc');
    }

    /**
     * Scope a query to get the latest video
     */
    public function scopeLatest($query)
    {
        return $query->orderBy('published_at', 'desc');
    }
}
