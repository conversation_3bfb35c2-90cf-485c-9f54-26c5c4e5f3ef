
import React, { useState, useEffect, useCallback } from 'react';
import { FileUpload } from './components/FileUpload';
import { ProgressBarDisplay } from './components/ProgressBarDisplay';
import { ResultsDisplay } from './components/ResultsDisplay';
import { ApiKeyInput } from './components/ApiKeyInput';
import { generateTagsForVideo } from './services/geminiService';
import type { AnalysisResult, LoadingPhase, HashtagPreference } from './types';
import { MAX_FILE_SIZE_MB, SUPPORTED_VIDEO_TYPES, API_KEY_STORAGE_KEY, API_KEY_REGEX } from './constants';
import { AcademicCapIcon, ExclamationTriangleIcon, KeyIcon, InformationCircleIcon, ChevronDownIcon } from './components/common/Icons'; // Updated header icon
import { GlassCard } from './components/common/GlassCard';

const readFileAsBase64 = (file: File): Promise<{ base64Data: string; mimeType: string }> => {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();
    reader.onload = () => {
      const result = reader.result as string;
      const mimeType = result.substring(result.indexOf(':') + 1, result.indexOf(';'));
      const base64Data = result.split(',')[1];
      resolve({ base64Data, mimeType });
    };
    reader.onerror = (error) => reject(error);
    reader.readAsDataURL(file);
  });
};

interface CachedAnalysis {
  fileIdentifier: string;
  result: AnalysisResult;
}

const App: React.FC = () => {
  const [apiKey, setApiKey] = useState<string | null>(() => {
    try {
      return localStorage.getItem(API_KEY_STORAGE_KEY);
    } catch (e) {
      console.error("Failed to read API key from localStorage on init:", e);
      return null;
    }
  });
  const [videoFile, setVideoFile] = useState<File | null>(null);
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [loadingPhase, setLoadingPhase] = useState<LoadingPhase>('idle');
  const [progress, setProgress] = useState<number>(0);
  const [analysisResult, setAnalysisResult] = useState<AnalysisResult | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [showResults, setShowResults] = useState<boolean>(false);
  const [cachedAnalysis, setCachedAnalysis] = useState<CachedAnalysis | null>(null);
  const [apiKeyError, setApiKeyError] = useState<string | null>(null);
  const [hashtagPreference, setHashtagPreference] = useState<HashtagPreference>('both');
  const [regenerationTrigger, setRegenerationTrigger] = useState<number>(0);


  useEffect(() => {
    try {
      localStorage.getItem(API_KEY_STORAGE_KEY); 
    } catch (e) {
      console.error("Initial localStorage access check failed:", e);
      setError("Could not access browser storage. API key persistence may be affected.");
    }
  }, []);

  const handleApiKeyChange = useCallback((newKey: string) => {
    const trimmedKey = newKey.trim();
    if (!API_KEY_REGEX.test(trimmedKey)) {
        setApiKeyError("Invalid API key format. Please check your key.");
        return; 
    }
    setApiKeyError(null); 

    if (trimmedKey) {
      try {
        localStorage.setItem(API_KEY_STORAGE_KEY, trimmedKey);
        setApiKey(trimmedKey);
        setError(null); 
      } catch (e) {
        console.error("Failed to save API key to localStorage:", e);
        setError("Could not save API key: Browser storage might be disabled or full. Key is set for current session only.");
        setApiKey(trimmedKey); 
      }
    } else { 
      try {
        localStorage.removeItem(API_KEY_STORAGE_KEY);
      } catch (e) {
        console.error("Failed to remove API key from localStorage (empty key case):", e);
      }
      setApiKey(null);
    }
  }, []);
  
  const clearApiKey = useCallback(() => {
    try {
      localStorage.removeItem(API_KEY_STORAGE_KEY);
    } catch (e) {
      console.error("Failed to remove API key from localStorage:", e);
      setError("Could not clear API key from browser storage. Please clear manually if issues persist.");
    }
    setApiKey(null);
    setApiKeyError(null); 
    resetState();
  }, []);

  const resetState = useCallback((keepError: boolean = false, keepApiKeyError: boolean = false) => {
    setVideoFile(null);
    setIsLoading(false);
    setLoadingPhase('idle');
    setProgress(0);
    setAnalysisResult(null);
    if (!keepError) { 
        setError(null);
    }
    if (!keepApiKeyError) {
        setApiKeyError(null);
    }
    setShowResults(false);
  }, []);

  const handleFileSelect = useCallback((file: File) => {
    resetState(false, !!apiKeyError); // Clear general errors, keep API key error if it exists

    if (!apiKey) {
      setError("Please ensure your Gemini API Key is configured for the application to work.");
      return;
    }
    if (!SUPPORTED_VIDEO_TYPES.includes(file.type)) {
      setError(`Unsupported file type: ${file.type}. Please upload MP4, MOV, WEBM, MKV or AVI.`);
      return;
    }
    const fileSizeMB = file.size / (1024 * 1024);
    if (fileSizeMB > MAX_FILE_SIZE_MB) {
      setError(`File is too large (max ${MAX_FILE_SIZE_MB}MB). This may cause browser issues or API errors. Use smaller clips for reliable results.`);
    }
    
    setVideoFile(file);
    setError(null); // Clear any previous file-related errors
  }, [resetState, apiKey, apiKeyError]);


  const processFile = useCallback(async (currentVideoFile: File, currentApiKey: string, isRegenerating: boolean = false) => {
    setIsLoading(true);
    setAnalysisResult(null); 
    setShowResults(false);
    setError(null); 
    setApiKeyError(null);

    const currentFileIdentifier = `${currentVideoFile.name}_${currentVideoFile.size}_${currentVideoFile.lastModified}`;
    if (!isRegenerating && cachedAnalysis && cachedAnalysis.fileIdentifier === currentFileIdentifier) {
      setAnalysisResult(cachedAnalysis.result);
      setShowResults(true);
      setIsLoading(false);
      setLoadingPhase('idle');
      setError("Displayed cached analysis for this video."); 
      setTimeout(() => setError(null), 3000);
      return;
    }

    try {
      setLoadingPhase('reading');
      setProgress(0);
      for (let i = 0; i <= 20; i += Math.floor(Math.random() * 3 + 2)) {
          await new Promise(resolve => setTimeout(resolve, 50));
          setProgress(i);
      }
      
      const { base64Data, mimeType } = await readFileAsBase64(currentVideoFile);
      setProgress(20); 

      setLoadingPhase('analyzing');
      const resultPromise = generateTagsForVideo(
        currentApiKey, 
        base64Data,
        mimeType
      );
      
      let analysisProg = 20;
      const totalAnalysisDurationEstimate = 7000; 
      const analysisIntervalTime = 150;
      const increments = (95 - 20) / (totalAnalysisDurationEstimate / analysisIntervalTime);

      const interval = setInterval(() => {
        analysisProg += increments;
        if (analysisProg <= 95) {
          setProgress(Math.round(analysisProg));
        } else {
          setProgress(95);
          clearInterval(interval); 
        }
      }, analysisIntervalTime);

      const result = await resultPromise;
      clearInterval(interval);
      setProgress(100);
      setAnalysisResult(result);
      setCachedAnalysis({ fileIdentifier: currentFileIdentifier, result }); 
      setShowResults(true);

    } catch (err) {
      console.error("Error during video processing or analysis:", err);
      const errorMessage = err instanceof Error ? err.message : "An unknown error occurred.";
      if (errorMessage.toLowerCase().includes("api key not valid") || errorMessage.toLowerCase().includes("invalid api key") || errorMessage.toLowerCase().includes("api_key_invalid") || errorMessage.toLowerCase().includes("invalid gemini api key") || errorMessage.includes("API key is not valid")) {
           setError(`Gemini API Key is invalid or has insufficient permissions. (${errorMessage})`);
           setApiKeyError(`Invalid API Key. Please verify and save again.`); 
      } else if (errorMessage.toLowerCase().includes("quota")) {
           setError(`API quota exceeded. Please try again later. (${errorMessage})`);
      } else if (errorMessage.includes("API key is not configured")) { 
           setError(`Gemini API Key is missing or not passed correctly to the service. ${errorMessage}`);
           setApiKeyError(`API Key configuration issue.`);
      }
      else {
          setError(`Analysis failed: ${errorMessage}`);
      }
      setProgress(0); 
      setShowResults(false); 
      setCachedAnalysis(null);
    } finally {
      setIsLoading(false);
      setLoadingPhase('idle');
    }
  }, [cachedAnalysis]); 

  useEffect(() => {
    if (videoFile && apiKey && !isLoading) { 
      processFile(videoFile, apiKey, regenerationTrigger > 0 && isLoading); 
    }
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [videoFile, apiKey, regenerationTrigger]); 

  const handleRegenerate = () => {
    if (videoFile && apiKey) {
        setCachedAnalysis(null); 
        setRegenerationTrigger(prev => prev + 1); 
    }
  };
  
  return (
    <div className="min-h-screen bg-slate-900 flex flex-col items-center p-4 sm:p-6 lg:p-8 transition-all duration-500 ease-in-out overflow-y-auto">
      <header className="mb-8 text-center w-full max-w-2xl">
        <h1 className="text-4xl sm:text-5xl lg:text-6xl font-bold bg-clip-text text-transparent bg-gradient-to-r from-amber-400 via-orange-500 to-red-500 flex items-center justify-center space-x-3">
          <AcademicCapIcon className="w-10 h-10 sm:w-12 sm:h-12 lg:w-14 lg:h-14 text-orange-500" />
          <span>Saros-Tag-Generator</span>
        </h1>
        <p className="mt-3 text-lg sm:text-xl text-slate-400">
          Lade dein Video hoch. Entfessle sein virales Potenzial mit KI. (Max {MAX_FILE_SIZE_MB}MB)
        </p>
      </header>

      <main className="w-full max-w-2xl flex-grow">
        {!apiKey || apiKeyError ? (
          <ApiKeyInput 
            currentKey={apiKey || ''} 
            onSaveKey={handleApiKeyChange} 
            error={apiKeyError}
            clearApiError={() => setApiKeyError(null)}
          />
        ) : (
          <GlassCard className="p-4 mb-6 text-center">
            <div className="flex items-center justify-center">
                <KeyIcon className="w-5 h-5 mr-2 text-green-400"/>
                <p className="text-green-400">Gemini API Key ist aktiv.</p>
                <button 
                    onClick={clearApiKey} 
                    className="ml-4 text-xs text-orange-400 hover:text-orange-300 underline"
                    aria-label="Change API Key"
                >
                    Schlüssel ändern
                </button>
            </div>
          </GlassCard>
        )}

        {error && (
          <GlassCard 
            className={`my-6 p-4 border flex items-start space-x-3 transition-opacity duration-300 ${error.startsWith("Displayed cached") ? 'bg-blue-800/60 border-blue-700 text-blue-200' : 'bg-red-800/60 border-red-700 text-red-200'}`}
            role="alert"
          >
            {error.startsWith("Displayed cached") ? <InformationCircleIcon className="h-6 w-6 text-blue-300 flex-shrink-0 mt-0.5"/> : <ExclamationTriangleIcon className="h-6 w-6 text-red-300 flex-shrink-0 mt-0.5" /> }
            <span className="flex-grow">{error}</span>
          </GlassCard>
        )}

        {apiKey && !apiKeyError && !showResults && !isLoading && (
          <FileUpload onFileSelect={handleFileSelect} disabled={isLoading || !apiKey || !!apiKeyError} />
        )}
        
        {isLoading && videoFile && (
          <ProgressBarDisplay
            fileName={videoFile.name}
            progress={progress}
            phase={loadingPhase}
          />
        )}
        
        {showResults && analysisResult && !isLoading && (
          <>
            <div className="my-4 flex justify-center">
              <GlassCard className="p-3">
                <label htmlFor="hashtagPreference" className="block text-sm font-medium text-slate-300 mr-3">Hashtags anzeigen für:</label>
                <div className="relative inline-block">
                  <select
                    id="hashtagPreference"
                    value={hashtagPreference}
                    onChange={(e) => setHashtagPreference(e.target.value as HashtagPreference)}
                    className="appearance-none bg-slate-700 border border-slate-600 text-slate-100 text-sm rounded-lg focus:ring-orange-500 focus:border-orange-500 block w-full p-2.5 pr-8"
                  >
                    <option value="both">Beide (TikTok & Instagram)</option>
                    <option value="tiktok">Nur TikTok</option>
                    <option value="instagram">Nur Instagram</option>
                  </select>
                  <ChevronDownIcon className="w-5 h-5 text-slate-400 absolute top-1/2 right-2.5 transform -translate-y-1/2 pointer-events-none" />
                </div>
              </GlassCard>
            </div>
            <ResultsDisplay 
              result={analysisResult} 
              onAnalyzeAnother={() => { resetState(false, !!apiKeyError); }} 
              videoFileName={videoFile?.name || "dein Video"}
              hashtagPreference={hashtagPreference}
              onRegenerate={handleRegenerate}
            />
          </>
        )}
      </main>

      <footer className="mt-12 text-center text-sm text-slate-500 w-full max-w-2xl">
        <p>&copy; {new Date().getFullYear()} Saros-Tag-Generator. Created by Sarocesch.</p>
        <p>Für optimale Ergebnisse verwende Videoclips deutlich unter {MAX_FILE_SIZE_MB}MB.</p>
      </footer>
    </div>
  );
};

export default App;