import React, { useState, useCallback } from 'react';
import { UploadCloudIcon, KeyIcon } from './common/Icons'; 
import { SUPPORTED_VIDEO_TYPES, MAX_FILE_SIZE_MB } from '../constants';

interface FileUploadProps {
  onFileSelect: (file: File) => void;
  disabled: boolean;
}

export const FileUpload: React.FC<FileUploadProps> = ({ onFileSelect, disabled }) => {
  const [isDragging, setIsDragging] = useState(false);

  const handleFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    if (event.target.files && event.target.files[0]) {
      onFileSelect(event.target.files[0]);
    }
  };

  const handleDrop = useCallback((event: React.DragEvent<HTMLDivElement>) => {
    event.preventDefault();
    event.stopPropagation();
    setIsDragging(false);
    if (disabled) return;

    if (event.dataTransfer.files && event.dataTransfer.files[0]) {
      onFileSelect(event.dataTransfer.files[0]);
    }
  }, [onFileSelect, disabled]);

  const handleDragOver = useCallback((event: React.DragEvent<HTMLDivElement>) => {
    event.preventDefault();
    event.stopPropagation();
    if (!disabled) setIsDragging(true);
  }, [disabled]);

  const handleDragLeave = useCallback((event: React.DragEvent<HTMLDivElement>) => {
    event.preventDefault();
    event.stopPropagation();
    setIsDragging(false);
  }, []);

  const acceptedFileTypes = SUPPORTED_VIDEO_TYPES.join(',');

  return (
    <div 
      className={`
        w-full p-8 sm:p-12 border-4 rounded-xl transition-all duration-300 ease-in-out
        flex flex-col items-center justify-center text-center 
        bg-slate-800/50 backdrop-blur-md
        ${isDragging && !disabled ? 'border-orange-500 shadow-[0_0_15px_theme(colors.orange.500)]' : 'border-dashed border-slate-600'}
        ${!disabled ? 'hover:border-orange-400 cursor-pointer' : ''}
        ${disabled ? 'opacity-60 cursor-not-allowed border-slate-700' : ''}
      `}
      onDrop={handleDrop}
      onDragOver={handleDragOver}
      onDragLeave={handleDragLeave}
      onClick={() => !disabled && document.getElementById('video-upload-input')?.click()}
      aria-disabled={disabled}
    >
      <input
        type="file"
        id="video-upload-input"
        className="hidden"
        accept={acceptedFileTypes}
        onChange={handleFileChange}
        disabled={disabled}
      />
      <UploadCloudIcon className={`w-16 h-16 sm:w-20 sm:h-20 mb-6 transition-colors duration-300 ${isDragging && !disabled ? 'text-orange-400' : (disabled ? 'text-slate-600' : 'text-slate-500')}`} />
      <p className={`text-lg sm:text-xl font-semibold transition-colors duration-300 ${isDragging && !disabled ? 'text-orange-300' : (disabled ? 'text-slate-500' : 'text-slate-300')}`}>
        {disabled ? "API Key setzen zum Hochladen" : "Videodatei hierhin ziehen"}
      </p>
      {!disabled && <p className="text-sm text-slate-400 mt-1">oder klicken zum Auswählen</p>}
      {disabled && 
        <p className="text-sm text-yellow-400 mt-2 flex items-center">
            <KeyIcon className="w-4 h-4 mr-2"/> Ein Gemini API Key ist erforderlich.
        </p>
      }
      <p className="text-xs text-slate-500 mt-4">
        Unterstützt: MP4, MOV, WEBM, MKV, AVI (Max {MAX_FILE_SIZE_MB}MB).
      </p>
      <p className="text-xs text-yellow-500 mt-1">
        Warnung: Große Dateien (nahe {MAX_FILE_SIZE_MB}MB) können Browser/API-Probleme verursachen.
      </p>
    </div>
  );
};