@props([
    'ticket',
    'isSupporter' => false,
    'compact' => false
])

@php
$actions = [];

// View action - always available
$actions[] = [
    'label' => __('tickets.view'),
    'href' => route('tickets.show', $ticket),
    'icon' => '<svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path></svg>',
    'variant' => 'ghost',
    'primary' => true
];

// Supporter actions
if ($isSupporter) {
    // Edit/Assign action
    $actions[] = [
        'label' => __('tickets.manage'),
        'href' => route('tickets.show', $ticket) . '#manage',
        'icon' => '<svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path></svg>',
        'variant' => 'outline'
    ];

    // Status actions based on current status
    if ($ticket->status === 'open') {
        $actions[] = [
            'label' => __('tickets.mark_in_progress'),
            'action' => 'setStatus',
            'params' => ['in_progress'],
            'icon' => '<svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path></svg>',
            'variant' => 'secondary'
        ];
    } elseif ($ticket->status === 'in_progress') {
        $actions[] = [
            'label' => __('tickets.mark_closed'),
            'action' => 'setStatus',
            'params' => ['closed'],
            'icon' => '<svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path></svg>',
            'variant' => 'success'
        ];
    } elseif ($ticket->status === 'closed') {
        $actions[] = [
            'label' => __('tickets.reopen'),
            'action' => 'setStatus',
            'params' => ['open'],
            'icon' => '<svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 11V7a4 4 0 118 0m-4 8v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2z"></path></svg>',
            'variant' => 'primary'
        ];
    }
}

$containerClasses = $compact ? 'flex items-center gap-2' : 'flex flex-col sm:flex-row gap-2';
@endphp

<div class="{{ $containerClasses }}">
    @foreach($actions as $action)
        @if(isset($action['href']))
            {{-- Link action --}}
            <x-modern-button
                variant="{{ $action['variant'] }}"
                size="{{ $compact ? 'sm' : 'md' }}"
                href="{{ $action['href'] }}"
                icon="{!! $action['icon'] !!}"
                class="{{ isset($action['primary']) ? 'order-first' : '' }}"
            >
                @if(!$compact)
                    {{ $action['label'] }}
                @endif
            </x-modern-button>
        @elseif(isset($action['action']))
            {{-- Wire action --}}
            @php
                $wireParams = isset($action['params']) ? implode(',', array_map(fn($p) => "'{$p}'", $action['params'])) : '';
                $wireClick = $action['action'] . '(' . $wireParams . ')';
            @endphp
            <x-modern-button
                variant="{{ $action['variant'] }}"
                size="{{ $compact ? 'sm' : 'md' }}"
                wire:click="{{ $wireClick }}"
                icon="{!! $action['icon'] !!}"
            >
                @if(!$compact)
                    {{ $action['label'] }}
                @endif
            </x-modern-button>
        @endif
    @endforeach

    {{-- Message count badge --}}
    @if($ticket->messages->count() > 0)
        <x-modern-status-badge
            status="default"
            size="{{ $compact ? 'xs' : 'sm' }}"
            variant="filled"
            class="ml-auto"
        >
            {{ $ticket->messages->count() }}
        </x-modern-status-badge>
    @endif
</div>

<style>
    /* Responsive adjustments */
    @media (max-width: 640px) {
        .modern-ticket-actions {
            flex-direction: column;
            align-items: stretch;
        }

        .modern-ticket-actions .modern-button {
            justify-content: center;
        }
    }

    /* Theme-specific styling */
    [data-theme="minewache-high-contrast"] .modern-ticket-actions .modern-button {
        border-width: 2px;
        font-weight: bold;
    }
</style>
