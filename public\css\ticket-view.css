/**
 * Ticket View Styles
 * Custom styles for the ticket view component
 */

/* Typing indicator */
.typing-dot {
    display: inline-block;
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background-color: #6b7280;
    margin: 0 2px;
    animation: typing-dot 1.4s infinite ease-in-out both;
}

.typing-dot:nth-child(1) {
    animation-delay: -0.32s;
}

.typing-dot:nth-child(2) {
    animation-delay: -0.16s;
}

@keyframes typing-dot {
    0%, 80%, 100% { transform: scale(0); }
    40% { transform: scale(1); }
}

/* Hide scrollbar but keep functionality */
.hide-scrollbar {
    -ms-overflow-style: none;  /* IE and Edge */
    scrollbar-width: none;  /* Firefox */
}
.hide-scrollbar::-webkit-scrollbar {
    display: none; /* Chrome, Safari, Opera */
}

/* Fade-in animation for new messages */
@keyframes fadeIn {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
}

.animate-fadeIn {
    animation: fadeIn 0.3s ease-out forwards;
}

/* New message indicator */
.new-message-indicator {
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% { box-shadow: 0 0 0 0 rgba(59, 130, 246, 0.7); }
    70% { box-shadow: 0 0 0 10px rgba(59, 130, 246, 0); }
    100% { box-shadow: 0 0 0 0 rgba(59, 130, 246, 0); }
}

/* Upload progress animations */
.fill-animation {
    animation: fillProgress1 2s ease-in-out infinite;
}

.fill-animation2-3 {
    animation: fillProgress2 2s ease-in-out infinite;
}

.fill-animation3-3 {
    animation: fillProgress3 2s ease-in-out infinite;
}

@keyframes fillProgress1 {
    0% { width: 0%; }
    100% { width: 33%; }
}

@keyframes fillProgress2 {
    0% { width: 33%; }
    100% { width: 66%; }
}

@keyframes fillProgress3 {
    0% { width: 66%; }
    100% { width: 100%; }
}

/* Message hover effects */
.message-bubble {
    transition: transform 0.2s ease-in-out;
}

.message-bubble:hover {
    transform: translateY(-2px);
}

/* Improved focus styles for accessibility */
button:focus-visible,
a:focus-visible,
input:focus-visible,
textarea:focus-visible,
select:focus-visible {
    outline: 2px solid #3b82f6;
    outline-offset: 2px;
}

/* Tooltip improvements */
.tooltip {
    transition: opacity 0.2s ease-in-out, transform 0.2s ease-in-out;
    transform-origin: center bottom;
}

.tooltip.tooltip-shown {
    transform: translateY(-2px);
}

/* Status badge styles */
.badge {
    transition: all 0.2s ease-in-out;
}

.badge:hover {
    filter: brightness(1.1);
}

/* Attachment preview hover effects */
.attachment-preview {
    transition: transform 0.2s ease-in-out, box-shadow 0.2s ease-in-out;
}

.attachment-preview:hover {
    transform: scale(1.02);
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

/* Improved scrollbar styling for browsers that support it */
.styled-scrollbar::-webkit-scrollbar {
    width: 8px;
}

.styled-scrollbar::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 4px;
}

.styled-scrollbar::-webkit-scrollbar-thumb {
    background: #888;
    border-radius: 4px;
}

.styled-scrollbar::-webkit-scrollbar-thumb:hover {
    background: #555;
}

/* Dark mode scrollbar */
.dark .styled-scrollbar::-webkit-scrollbar-track {
    background: #374151;
}

.dark .styled-scrollbar::-webkit-scrollbar-thumb {
    background: #4B5563;
}

.dark .styled-scrollbar::-webkit-scrollbar-thumb:hover {
    background: #6B7280;
}
