/**
 * Application Manager JavaScript
 * Contains all the JavaScript functionality for the application manager
 */

// Initialize tabs functionality
function initTabs() {
    const tabs = document.querySelectorAll('[data-tab]');
    const tabContents = document.querySelectorAll('[data-tab-content]');
    
    if (tabs.length === 0) return; // No tabs found
    
    tabs.forEach(tab => {
        tab.addEventListener('click', () => {
            const target = tab.dataset.tab;
            
            // Mark active tab
            tabs.forEach(t => t.classList.remove('tab-active'));
            tab.classList.add('tab-active');
            
            // Show/hide tab content
            tabContents.forEach(content => {
                if (content.dataset.tabContent === target) {
                    content.classList.remove('hidden');
                } else {
                    content.classList.add('hidden');
                }
            });
        });
    });
    
    // Activate first tab by default
    if (tabs.length > 0 && !tabs[0].classList.contains('tab-active')) {
        tabs[0].click();
    }
}

// Initialize clipboard functionality
function initClipboard() {
    // Listen for clipboard events from Livewire
    window.addEventListener('copyToClipboard', event => {
        const text = event.detail.text;
        
        if (navigator.clipboard) {
            navigator.clipboard.writeText(text)
                .then(() => {
                    console.log('Text copied to clipboard');
                })
                .catch(err => {
                    console.error('Failed to copy text: ', err);
                    fallbackCopyToClipboard(text);
                });
        } else {
            fallbackCopyToClipboard(text);
        }
    });
}

// Fallback clipboard method for browsers that don't support the Clipboard API
function fallbackCopyToClipboard(text) {
    const textArea = document.createElement('textarea');
    textArea.value = text;
    
    // Make the textarea out of viewport
    textArea.style.position = 'fixed';
    textArea.style.left = '-999999px';
    textArea.style.top = '-999999px';
    document.body.appendChild(textArea);
    
    textArea.focus();
    textArea.select();
    
    try {
        const successful = document.execCommand('copy');
        const msg = successful ? 'successful' : 'unsuccessful';
        console.log('Fallback: Copying text was ' + msg);
    } catch (err) {
        console.error('Fallback: Unable to copy', err);
    }
    
    document.body.removeChild(textArea);
}

// Initialize modals
function initModals() {
    // Listen for modal events from Livewire
    window.addEventListener('openEditabilityModal', () => {
        const modal = document.getElementById('editability_modal');
        if (modal) {
            modal.showModal();
        }
    });
    
    window.addEventListener('closeEditabilityModal', () => {
        const modal = document.getElementById('editability_modal');
        if (modal) {
            modal.close();
        }
    });
}

// Initialize all functionality when the DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    initTabs();
    initClipboard();
    initModals();
});

// Re-initialize when Livewire updates the DOM
document.addEventListener('livewire:navigated', function() {
    initTabs();
    initClipboard();
    initModals();
});

// Re-initialize when Livewire updates a component
document.addEventListener('livewire:update', function() {
    initTabs();
    initClipboard();
    initModals();
});
