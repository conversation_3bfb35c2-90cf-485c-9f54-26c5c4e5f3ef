<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('ticket_attachments', function (Blueprint $table) {
            $table->id();
            $table->foreignId('ticket_message_id')->constrained('ticket_messages')->onDelete('cascade');
            $table->string('discord_attachment_id')->unique(); // Discord's own ID for the attachment
            $table->string('filename');
            $table->text('url'); // Original Discord CDN URL
            $table->string('local_path')->nullable(); // Path in local storage after download
            $table->string('mime_type')->nullable();
            $table->unsignedInteger('size'); // In bytes
            // --- Merge from add_media_fields migration ---
            $table->string('processed_file_path')->nullable();
            $table->string('thumbnail_path')->nullable();
            $table->string('media_type')->nullable();
            $table->json('media_metadata')->nullable();
            $table->enum('processing_status', ['pending', 'processing', 'completed', 'failed'])->default('pending');
            $table->text('processing_error')->nullable();
            // --- End merge ---
            $table->timestamps();
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('ticket_attachments');
    }
};
