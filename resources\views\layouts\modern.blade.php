<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}" data-theme="minewache">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="csrf-token" content="{{ csrf_token() }}">

    <title>{{ config('app.name', 'Minewache') }}</title>

    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Literata:ital,wght@0,400;0,700;1,400&family=Sora:wght@400;500;600;700&display=swap" rel="stylesheet">

    <!-- Inline theme styles to prevent FOUC -->
    <style>
        /* Dark theme (minewache) */
        [data-theme="minewache"] {
            --color-base-100: #0f172a; /* slate-900 */
            --color-base-200: #1e293b; /* slate-800 */
            --color-base-300: #334155; /* slate-700 */
            --color-base-content: #f8fafc; /* slate-50 */
            --color-primary: #3b82f6; /* blue-500 */
            --color-primary-content: #ffffff; /* white */
        }
        
        /* Glassmorphism effect */
        .glass {
            background: rgba(30, 41, 59, 0.6); /* slate-800 with transparency */
            backdrop-filter: blur(12px);
            border: 1px solid rgba(255, 255, 255, 0.1);
            box-shadow: 0 8px 32px 0 rgba(0, 0, 0, 0.37);
        }
    </style>

    <!-- Theme initialization script (before any other scripts) -->
    <script src="{{ asset('js/theme-init.js') }}"></script>

    <!-- Scripts -->
    @vite(['resources/css/app.css', 'resources/css/modern-ui.css', 'resources/js/app.js'])

    <!-- jQuery for AJAX handling -->
    <script src="https://code.jquery.com/jquery-3.7.1.min.js" integrity="sha256-/JqT3SQfawRcv/BIHPThkBvs0OEvtFFmqPF/lYI/Cxo=" crossorigin="anonymous"></script>

    <!-- Additional scripts -->
    @stack('scripts')
</head>
<body class="font-sans antialiased bg-gradient-to-br from-slate-900 to-slate-800 min-h-screen">
    <div class="min-h-screen">
        @include('layouts.navigation-modern')

        <!-- Page Heading -->
        @if (isset($header))
            <header class="max-w-7xl mx-auto py-6 px-4 sm:px-6 lg:px-8">
                <x-modern-card variant="elevated" size="md">
                    {{ $header }}
                </x-modern-card>
            </header>
        @endif

        <!-- Page Content -->
        <main>
            {{ $slot }}
        </main>
    </div>
    
    <!-- Notification container for toast messages -->
    <div id="toast-container" class="fixed bottom-4 right-4 z-50"></div>
</body>
</html>
