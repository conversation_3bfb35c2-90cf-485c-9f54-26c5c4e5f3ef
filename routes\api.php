<?php

use App\Http\Controllers\PermissionController;
use App\Enums\Role;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider and all of them will
| be assigned to the "api" middleware group. Make something great!
|
*/

// Public health check endpoints
Route::get('/ping', function () {
    return response()->json([
        'status' => 'ok',
        'timestamp' => now()->toIso8601String()
    ]);
});

// Health check endpoints - both with and without /api prefix for compatibility
Route::get('/health', [\App\Http\Controllers\HealthController::class, 'check']);
Route::get('health', [\App\Http\Controllers\HealthController::class, 'check']);

// Discord bot management endpoints (protected by API token)
Route::middleware('api.token')->group(function () {
    Route::post('/discord-bot/restart', [\App\Http\Controllers\HealthController::class, 'restartDiscordBot']);
});

// Discord bot webhook endpoints
Route::post('/discord/role-update', [PermissionController::class, 'updateRolesFromBot'])
    ->middleware('api.token');

// Alternative endpoint for compatibility
Route::post('/role-update', [PermissionController::class, 'updateRolesFromBot'])
    ->middleware('api.token');

// Local Discord bot API endpoints
Route::prefix('discord-bot')->middleware('api.token')->group(function () {
    // Get user roles from local bot
    Route::get('/users/{userId}/roles', [PermissionController::class, 'getUserRolesFromLocalBot']);

    // Update user permissions based on roles from local bot
    Route::post('/users/{userId}/sync', [PermissionController::class, 'syncUserRolesFromLocalBot']);

    // Bot health check endpoints
    Route::get('/health/check', [\App\Http\Controllers\BotHealthController::class, 'checkBotStatus']);
    Route::get('/health/status', [\App\Http\Controllers\BotHealthController::class, 'getBotStatus']);
});

// Enhanced auth test endpoint for checking tokens
Route::get('/auth-test', function (Request $request) {
    return response()->json([
        'success' => true,
        'status' => 'success',
        'message' => 'API token is valid',
        'data' => [
            'token_type' => 'Bearer',
            'client_ip' => $request->ip(),
            'server_time' => now()->toIso8601String()
        ],
        'timestamp' => now()->toIso8601String()
    ]);
})->middleware('api.token');

// Debug endpoint for checking role mappings (protected by API token)
Route::get('/debug/role-mapping', function () {
    // Get the role mappings directly from the PermissionController
    $controller = app(PermissionController::class);
    $roleMapping = $controller->getRoleMapping();

    // Get role names with their values for better debugging
    $roles = collect(Role::cases())->map(function ($role) {
        return [
            'name' => $role->name,
            'label' => $role->label(),
            'value' => $role->value,
            'binary' => decbin($role->value)
        ];
    })->keyBy('name')->all();

    return response()->json([
        'success' => true,
        'status' => 'success',
        'message' => 'Role mapping information',
        'data' => [
            'role_mappings' => $roleMapping,
            'guild_id' => config('services.discord.guild_id'),
            'roles' => $roles
        ],
        'timestamp' => now()->toIso8601String()
    ]);
})->middleware('api.token');

// Legacy route for backward compatibility
Route::middleware('auth:sanctum')->get('/user', function (Request $request) {
    return $request->user();
});

// Ticket system API routes
Route::prefix('tickets')->middleware('api.token')->group(function () {
    // Create a ticket from Discord
    Route::post('/create', [\App\Http\Controllers\TicketApiController::class, 'create']);

    // Reconstruct a ticket from Discord channel
    Route::post('/reconstruct', [\App\Http\Controllers\TicketApiController::class, 'reconstruct']);

    // Add a message to a ticket from Discord
    Route::post('/{ticket}/messages', [\App\Http\Controllers\TicketApiController::class, 'addMessage']);

    // Update ticket status from Discord
    Route::post('/{ticket}/status', [\App\Http\Controllers\TicketApiController::class, 'updateStatus']);

    // Get ticket information
    Route::get('/{ticket}', [\App\Http\Controllers\TicketApiController::class, 'show']);

    // Get ticket by Discord channel ID
    Route::get('/channel/{channelId}', [\App\Http\Controllers\TicketAiController::class, 'getTicketByChannel']);

    // Update ticket Gemini consent
    Route::post('/{ticket}/consent', [\App\Http\Controllers\TicketAiController::class, 'updateTicketConsent']);

    // Generate AI response for a ticket
    Route::post('/{ticket}/ai-response', [\App\Http\Controllers\TicketAiController::class, 'generateAiResponseApi']);

    // Get all tickets for a user
    Route::get('/user/{userId}', [\App\Http\Controllers\TicketApiController::class, 'userTickets']);

    // File attachment endpoints
    Route::get('/attachments/{attachment}/download', [\App\Http\Controllers\TicketApiController::class, 'downloadAttachment'])
        ->name('api.tickets.attachments.download')
        ->middleware('signed');  // Ensure the URL is signed

    // Direct download for Discord bot (authenticated with API token instead of signature)
    Route::get('/attachments/{attachment}/bot-download', [\App\Http\Controllers\TicketApiController::class, 'botDownloadAttachment'])
        ->name('api.tickets.attachments.bot-download')
        ->middleware('api.token');

    // Upload attachment from Discord
    Route::post('/attachments/upload', [\App\Http\Controllers\TicketApiController::class, 'uploadAttachment']);

    // Discord bot integration endpoints
    Route::prefix('discord')->group(function () {
        // Create a ticket channel on Discord
        Route::post('/create-channel', [\App\Http\Controllers\Api\DiscordTicketController::class, 'createChannel']);

        // Send a message to Discord
        Route::post('/send-message', [\App\Http\Controllers\Api\DiscordTicketController::class, 'sendMessage']);

        // Update ticket status on Discord
        Route::post('/update-status', [\App\Http\Controllers\Api\DiscordTicketController::class, 'updateStatus']);

        // Update ticket assignment on Discord
        Route::post('/update-assignment', [\App\Http\Controllers\Api\DiscordTicketController::class, 'updateAssignment']);

        // Receive a message from Discord
        Route::post('/receive-message', [\App\Http\Controllers\Api\DiscordTicketController::class, 'receiveMessage']);
    });
});

// Discord Language File Endpoint
Route::get('/v1/discord/lang/{lang?}', [\App\Http\Controllers\Api\DiscordLangController::class, 'getLang']);
