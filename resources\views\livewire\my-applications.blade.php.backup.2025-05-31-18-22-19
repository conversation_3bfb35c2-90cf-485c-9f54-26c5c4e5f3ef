@php
    use App\Enums\Role;
@endphp

<div class="container px-4 py-8 mx-auto animate-fade-in">
    <!-- Header mit Animation -->
    <div class="flex flex-col mb-8 md:flex-row md:items-center md:justify-between">
        <h1 class="mb-4 text-2xl font-bold animate-slide-up md:mb-0 md:text-3xl text-base-content font-sora">
            Meine Bewerbungen
        </h1>

        <!-- Statistiken für den Benutzer -->
        <div class="flex flex-wrap gap-2 text-sm">
            <div class="stats bg-base-200 shadow-md">
                <div class="stat p-2">
                    <div class="stat-title text-xs">Gesamt</div>
                    <div class="stat-value text-lg">{{ $totalApplications }}</div>
                </div>
                <div class="stat p-2">
                    <div class="stat-title text-xs">Ausstehend</div>
                    <div class="stat-value text-lg text-warning">{{ $pendingApplications }}</div>
                </div>
                <div class="stat p-2">
                    <div class="stat-title text-xs">Angenommen</div>
                    <div class="stat-value text-lg text-success">{{ $approvedApplications }}</div>
                </div>
                <div class="stat p-2">
                    <div class="stat-title text-xs">Abgelehnt</div>
                    <div class="stat-value text-lg text-error">{{ $rejectedApplications }}</div>
                </div>
            </div>
        </div>
    </div>

    @if (session()->has('message'))
        <div class="alert shadow-lg mb-6 {{ session('animation') ? 'animate-' . session('animation') : 'animate-slide-up' }}">
            <x-heroicon-o-information-circle class="w-6 h-6 text-info" />
            <div>{{ session('message') }}</div>
        </div>
    @endif

    @if (session()->has('error'))
        <div class="alert shadow-lg mb-6 alert-error animate-slide-up">
            <x-heroicon-o-x-circle class="w-6 h-6" />
            <div>{{ session('error') }}</div>
        </div>
    @endif

    <!-- Hauptinhalt -->
    <div>
        <!-- Listenansicht -->
        @if ($viewMode === 'list')
            <!-- Filter -->
            <div class="p-4 mb-6 rounded-lg shadow-md animate-fade-in bg-base-200">
                <div class="grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-4">
                    <!-- Suchfeld -->
                    <div>
                        <label class="label">
                            <span class="label-text">Suche</span>
                        </label>
                        <input type="text" wire:model.live.debounce.300ms="searchTerm" placeholder="Suche..." class="w-full input input-bordered" />
                    </div>

                    <!-- Status-Filter -->
                    <div>
                        <label class="label">
                            <span class="label-text">Status</span>
                        </label>
                        <select wire:model.live="status" class="w-full select select-bordered">
                            <option value="all">Alle</option>
                            <option value="pending">Ausstehend</option>
                            <option value="approved">Angenommen</option>
                            <option value="rejected">Abgelehnt</option>
                        </select>
                    </div>

                    <!-- Berufs-Filter -->
                    <div>
                        <label class="label">
                            <span class="label-text">Beruf</span>
                        </label>
                        <select wire:model.live="profession" class="w-full select select-bordered">
                            <option value="all">Alle</option>
                            @foreach ($availableProfessions as $key => $value)
                                <option value="{{ $key }}">{{ $value }}</option>
                            @endforeach
                        </select>
                    </div>

                    <!-- Filter zurücksetzen -->
                    <div class="flex items-end">
                        <button wire:click="resetFilters" class="w-full btn btn-outline">
                            <x-heroicon-o-x class="w-5 h-5 mr-2" />
                            Filter zurücksetzen
                        </button>
                    </div>
                </div>
            </div>

            <!-- Bewerbungsliste -->
            <div class="overflow-x-auto animate-fade-in rounded-box">
                @if ($applications->count() > 0)
                    <table class="table table-zebra">
                        <thead class="text-sm bg-base-200">
                            <tr>
                                <th class="hidden md:table-cell">ID</th>
                                <th>
                                    <button wire:click="sortBy('created_at')" class="flex items-center hover:text-primary">
                                        Datum
                                        @if ($sortField === 'created_at')
                                            <x-heroicon-o-chevron-{{ $sortDirection === 'asc' ? 'up' : 'down' }} class="w-4 h-4 ml-1" />
                                        @endif
                                    </button>
                                </th>
                                <th>Berufe</th>
                                <th>
                                    <button wire:click="sortBy('status')" class="flex items-center hover:text-primary">
                                        Status
                                        @if ($sortField === 'status')
                                            <x-heroicon-o-chevron-{{ $sortDirection === 'asc' ? 'up' : 'down' }} class="w-4 h-4 ml-1" />
                                        @endif
                                    </button>
                                </th>
                                <th>
                                    <button wire:click="sortBy('updated_at')" class="flex items-center hover:text-primary">
                                        Zuletzt bearbeitet
                                        @if ($sortField === 'updated_at')
                                            <x-heroicon-o-chevron-{{ $sortDirection === 'asc' ? 'up' : 'down' }} class="w-4 h-4 ml-1" />
                                        @endif
                                    </button>
                                </th>
                                <th>Aktionen</th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach ($applications as $application)
                                <tr class="hover:bg-base-200/50 hover:cursor-pointer" wire:key="application-{{ $application->id }}">
                                    <td class="hidden md:table-cell" wire:click="viewApplication({{ $application->id }})">
                                        #{{ $application->id }}
                                    </td>
                                    <td wire:click="viewApplication({{ $application->id }})">
                                        <div class="font-medium">
                                            {{ $application->created_at->format('d.m.Y') }}
                                        </div>
                                        <div class="text-xs opacity-70">
                                            {{ $application->created_at->format('H:i') }} Uhr
                                        </div>
                                    </td>
                                    <td wire:click="viewApplication({{ $application->id }})">
                                        <div class="flex flex-wrap gap-1">
                                            @foreach ($application->professions as $profession)
                                                <div class="badge badge-sm badge-outline">
                                                    {{ $this->getReadableProfession($profession) }}
                                                </div>
                                            @endforeach
                                        </div>
                                    </td>
                                    <td wire:click="viewApplication({{ $application->id }})">
                                        @php
                                            $statusClass = $statusClasses[$application->status] ?? $statusClasses['pending'];
                                        @endphp
                                        <div class="badge {{ $statusClass['badge'] }}">
                                            {{ $this->getReadableStatus($application->status) }}
                                        </div>
                                    </td>
                                    <td wire:click="viewApplication({{ $application->id }})">
                                        {{ $application->updated_at->diffForHumans() }}
                                    </td>
                                    <td class="space-x-1">
                                        <button wire:click="viewApplication({{ $application->id }})" class="btn btn-sm btn-primary">
                                            <x-heroicon-o-eye class="w-4 h-4" />
                                            <span class="hidden sm:inline">Details</span>
                                        </button>

                                        @if ($application->editable)
                                            <button wire:click="editApplication({{ $application->id }})" class="btn btn-sm btn-outline btn-secondary">
                                                <x-heroicon-o-pencil class="w-4 h-4" />
                                                <span class="hidden sm:inline">Bearbeiten</span>
                                            </button>
                                        @endif
                                    </td>
                                </tr>
                            @endforeach
                        </tbody>
                    </table>

                    <!-- Pagination -->
                    <div class="mt-4">
                        {{ $applications->links() }}
                    </div>
                @else
                    <!-- Leerer Zustand -->
                    <div class="flex flex-col items-center justify-center p-8 text-center bg-base-200 rounded-box">
                        <x-heroicon-o-document-text class="w-16 h-16 mb-4 text-base-content/50" />
                        <h3 class="mb-2 text-lg font-medium">Keine Bewerbungen gefunden</h3>
                        <p class="mb-4 text-base-content/70">
                            Du hast noch keine Bewerbungen eingereicht oder deine Filter ergeben keine Treffer.
                        </p>
                        <a href="{{ route('applications.wizard') }}" class="btn btn-primary">
                            <x-heroicon-o-plus class="w-5 h-5 mr-2" />
                            Neue Bewerbung erstellen
                        </a>
                    </div>
                @endif
            </div>

        <!-- Detailansicht -->
        @elseif ($viewMode === 'detail' && $selectedApplication)
            <div class="p-6 rounded-lg shadow-md bg-base-200 animate-slide-up">
                <div class="flex justify-between mb-6">
                    <button wire:click="backToList" class="btn btn-ghost">
                        <x-heroicon-o-arrow-left class="w-5 h-5 mr-2" />
                        Zurück zur Übersicht
                    </button>

                    <div class="flex">
                        @if ($selectedApplication->editable)
                            <button wire:click="editApplication({{ $selectedApplication->id }})" class="btn btn-outline btn-secondary">
                                <x-heroicon-o-pencil class="w-5 h-5 mr-2" />
                                Bearbeiten
                            </button>
                        @endif
                    </div>
                </div>

                <!-- Status-Banner -->
                @php
                    $statusClass = $statusClasses[$selectedApplication->status] ?? $statusClasses['pending'];
                @endphp
                <div class="mb-6 rounded-lg p-4 {{ $statusClass['bg'] }} {{ $statusClass['border'] }} border">
                    <div class="flex items-center">
                        <x-dynamic-component :component="'heroicon-o-' . $statusClass['icon']" class="w-6 h-6 mr-2 {{ $statusClass['text'] }}" />
                        <span class="text-lg font-medium {{ $statusClass['text'] }}">
                            Status: {{ $this->getReadableStatus($selectedApplication->status) }}
                        </span>
                    </div>

                    @if ($selectedApplication->status === 'rejected' && $selectedApplication->team_comment)
                        <div class="mt-4">
                            <h4 class="font-medium mb-2">Feedback vom Team:</h4>
                            <div class="p-3 bg-base-100 rounded">
                                {!! nl2br(e($selectedApplication->team_comment)) !!}
                            </div>
                        </div>
                    @elseif ($selectedApplication->status === 'approved' && $selectedApplication->team_comment)
                        <div class="mt-4">
                            <h4 class="font-medium mb-2">Feedback vom Team:</h4>
                            <div class="p-3 bg-base-100 rounded">
                                {!! nl2br(e($selectedApplication->team_comment)) !!}
                            </div>
                        </div>
                    @endif
                </div>

                <!-- Bewerbungsdetails -->
                <div class="grid gap-6 mb-6 md:grid-cols-2">
                    <!-- Linke Spalte - Persönliche Informationen -->
                    <div class="card bg-base-100 shadow-sm">
                        <div class="card-body">
                            <h2 class="card-title mb-4 pb-2 border-b border-base-300">
                                <x-heroicon-o-user class="w-5 h-5 mr-2" />
                                Persönliche Informationen
                            </h2>

                            <div class="space-y-4">
                                <div>
                                    <span class="block text-sm font-medium text-base-content/70">Name</span>
                                    <span>{{ $selectedApplication->name }}</span>
                                </div>

                                <div>
                                    <span class="block text-sm font-medium text-base-content/70">Alter</span>
                                    <span>{{ $selectedApplication->age }} Jahre</span>
                                </div>

                                <div>
                                    <span class="block text-sm font-medium text-base-content/70">Geschlecht</span>
                                    <span>{{ $selectedApplication->gender }}</span>
                                </div>

                                @if ($selectedApplication->pronouns)
                                    <div>
                                        <span class="block text-sm font-medium text-base-content/70">Pronomen</span>
                                        <span>{{ $selectedApplication->pronouns }}</span>
                                    </div>
                                @endif

                                <div>
                                    <span class="block text-sm font-medium text-base-content/70">Berufe</span>
                                    <div class="flex flex-wrap gap-1 mt-1">
                                        @foreach ($selectedApplication->professions as $profession)
                                            <div class="badge badge-outline">
                                                {{ $this->getReadableProfession($profession) }}
                                            </div>
                                        @endforeach
                                    </div>
                                </div>

                                <div>
                                    <span class="block text-sm font-medium text-base-content/70">Eingereicht am</span>
                                    <span>{{ $selectedApplication->created_at->format('d.m.Y H:i') }} Uhr</span>
                                </div>

                                @if ($selectedApplication->reviewer)
                                    <div>
                                        <span class="block text-sm font-medium text-base-content/70">Überprüft von</span>
                                        <span>{{ $selectedApplication->reviewer->name }}</span>
                                    </div>

                                    <div>
                                        <span class="block text-sm font-medium text-base-content/70">Überprüft am</span>
                                        <span>{{ $selectedApplication->reviewed_at->format('d.m.Y H:i') }} Uhr</span>
                                    </div>
                                @endif
                            </div>
                        </div>
                    </div>

                    <!-- Rechte Spalte - Berufsspezifische Informationen -->
                    <div class="card bg-base-100 shadow-sm">
                        <div class="card-body">
                            <h2 class="card-title mb-4 pb-2 border-b border-base-300">
                                <x-heroicon-o-briefcase class="w-5 h-5 mr-2" />
                                Berufsspezifische Informationen
                            </h2>

                            <div class="space-y-4">
                                <!-- Schauspieler-spezifische Informationen -->
                                @if (in_array('actor', $selectedApplication->professions) || in_array('voice_actor', $selectedApplication->professions))
                                    @if ($selectedApplication->voice_type)
                                        <div>
                                            <span class="block text-sm font-medium text-base-content/70">Stimmtyp</span>
                                            <span>{{ $selectedApplication->voice_type }}</span>
                                        </div>
                                    @endif

                                    @if ($selectedApplication->microphone)
                                        <div>
                                            <span class="block text-sm font-medium text-base-content/70">Mikrofon</span>
                                            <span>{{ $selectedApplication->microphone }}</span>
                                        </div>
                                    @endif
                                @endif

                                <!-- Builder/Cameraman-spezifische Informationen -->
                                @if (in_array('builder', $selectedApplication->professions) || in_array('cameraman', $selectedApplication->professions))
                                    @if ($selectedApplication->ram)
                                        <div>
                                            <span class="block text-sm font-medium text-base-content/70">RAM</span>
                                            <span>{{ $selectedApplication->ram }}</span>
                                        </div>
                                    @endif

                                    @if ($selectedApplication->fps)
                                        <div>
                                            <span class="block text-sm font-medium text-base-content/70">FPS</span>
                                            <span>{{ $selectedApplication->fps }}</span>
                                        </div>
                                    @endif

                                    @if ($selectedApplication->gpu)
                                        <div>
                                            <span class="block text-sm font-medium text-base-content/70">GPU</span>
                                            <span>{{ $selectedApplication->gpu }}</span>
                                        </div>
                                    @endif
                                @endif

                                <!-- Designer-spezifische Informationen -->
                                @if (in_array('designer', $selectedApplication->professions))
                                    @if ($selectedApplication->program)
                                        <div>
                                            <span class="block text-sm font-medium text-base-content/70">Programm</span>
                                            <span>{{ $selectedApplication->program }}</span>
                                        </div>
                                    @endif

                                    @if ($selectedApplication->design_style)
                                        <div>
                                            <span class="block text-sm font-medium text-base-content/70">Design-Stil</span>
                                            <span>{{ $selectedApplication->design_style }}</span>
                                        </div>
                                    @endif

                                    @if ($selectedApplication->favorite_design)
                                        <div>
                                            <span class="block text-sm font-medium text-base-content/70">Lieblings-Design</span>
                                            <span>{{ $selectedApplication->favorite_design }}</span>
                                        </div>
                                    @endif

                                    @if ($selectedApplication->portfolio)
                                        <div>
                                            <span class="block text-sm font-medium text-base-content/70">Portfolio</span>
                                            <a href="{{ $selectedApplication->portfolio }}" target="_blank" class="link link-primary">
                                                {{ $selectedApplication->portfolio }}
                                                <x-heroicon-o-external-link class="inline-block w-4 h-4 ml-1" />
                                            </a>
                                        </div>
                                    @endif
                                @endif

                                <!-- Developer-spezifische Informationen -->
                                @if (in_array('developer', $selectedApplication->professions))
                                    @if ($selectedApplication->languages)
                                        <div>
                                            <span class="block text-sm font-medium text-base-content/70">Programmiersprachen</span>
                                            <span>{{ $selectedApplication->languages }}</span>
                                        </div>
                                    @endif

                                    @if ($selectedApplication->ide)
                                        <div>
                                            <span class="block text-sm font-medium text-base-content/70">IDE</span>
                                            <span>{{ $selectedApplication->ide }}</span>
                                        </div>
                                    @endif
                                @endif

                                <!-- Musikproduzent-spezifische Informationen -->
                                @if (in_array('music_producer', $selectedApplication->professions))
                                    @if ($selectedApplication->daw)
                                        <div>
                                            <span class="block text-sm font-medium text-base-content/70">DAW</span>
                                            <span>{{ $selectedApplication->daw }}</span>
                                        </div>
                                    @endif
                                @endif
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Über dich Abschnitt -->
                <div class="mb-6">
                    <div class="card bg-base-100 shadow-sm">
                        <div class="card-body">
                            <h2 class="card-title mb-4 pb-2 border-b border-base-300">
                                <x-heroicon-o-identification class="w-5 h-5 mr-2" />
                                Über dich
                            </h2>

                            <div class="space-y-6">
                                <div>
                                    <h3 class="mb-2 text-base-content/70 font-medium">Erzähle uns etwas über dich:</h3>
                                    <div class="p-3 bg-base-200 rounded whitespace-pre-wrap">
                                        {!! nl2br(e($selectedApplication->about_you)) !!}
                                    </div>
                                </div>

                                <div>
                                    <h3 class="mb-2 text-base-content/70 font-medium">Was sind deine Stärken und Schwächen?</h3>
                                    <div class="p-3 bg-base-200 rounded whitespace-pre-wrap">
                                        {!! nl2br(e($selectedApplication->strengths_weaknesses)) !!}
                                    </div>
                                </div>

                                @if ($selectedApplication->final_words)
                                    <div>
                                        <h3 class="mb-2 text-base-content/70 font-medium">Abschließende Worte:</h3>
                                        <div class="p-3 bg-base-200 rounded whitespace-pre-wrap">
                                            {!! nl2br(e($selectedApplication->final_words)) !!}
                                        </div>
                                    </div>
                                @endif
                            </div>
                        </div>
                    </div>
                </div>
            </div>

        <!-- Bearbeitungsmodus -->
        @elseif ($viewMode === 'edit' && $selectedApplication)
            <div class="p-6 rounded-lg shadow-md bg-base-200 animate-slide-up">
                <div class="mb-6">
                    <button wire:click="backToDetail" class="btn btn-ghost">
                        <x-heroicon-o-arrow-left class="w-5 h-5 mr-2" />
                        Zurück zur Detailansicht
                    </button>
                    <h2 class="mt-4 text-2xl font-bold">Bewerbung bearbeiten</h2>
                </div>

                <!-- Formular für die Bearbeitung -->
                <form wire:submit.prevent="updateApplication">
                    <!-- Über dich Abschnitt -->
                    <div class="mb-6 card bg-base-100 shadow-sm">
                        <div class="card-body">
                            <h2 class="card-title mb-4 pb-2 border-b border-base-300">
                                <x-heroicon-o-identification class="w-5 h-5 mr-2" />
                                Über dich
                            </h2>

                            <div class="space-y-6">
                                <!-- Über dich -->
                                <div>
                                    <label class="label">
                                        <span class="label-text">Erzähle uns etwas über dich:</span>
                                    </label>
                                    <textarea
                                        wire:model="editableFields.about_you"
                                        class="textarea textarea-bordered w-full h-40"
                                        placeholder="Erzähle uns etwas über dich..."
                                    ></textarea>
                                    @error('editableFields.about_you')
                                        <label class="label">
                                            <span class="label-text-alt text-error">{{ $message }}</span>
                                        </label>
                                    @enderror
                                </div>

                                <!-- Stärken und Schwächen -->
                                <div>
                                    <label class="label">
                                        <span class="label-text">Was sind deine Stärken und Schwächen?</span>
                                    </label>
                                    <textarea
                                        wire:model="editableFields.strengths_weaknesses"
                                        class="textarea textarea-bordered w-full h-40"
                                        placeholder="Was sind deine Stärken und Schwächen?"
                                    ></textarea>
                                    @error('editableFields.strengths_weaknesses')
                                        <label class="label">
                                            <span class="label-text-alt text-error">{{ $message }}</span>
                                        </label>
                                    @enderror
                                </div>

                                <!-- Abschließende Worte -->
                                <div>
                                    <label class="label">
                                        <span class="label-text">Abschließende Worte (optional):</span>
                                    </label>
                                    <textarea
                                        wire:model="editableFields.final_words"
                                        class="textarea textarea-bordered w-full h-24"
                                        placeholder="Hast du noch abschließende Worte?"
                                    ></textarea>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Berufsspezifische Informationen -->
                    <div class="mb-6 card bg-base-100 shadow-sm">
                        <div class="card-body">
                            <h2 class="card-title mb-4 pb-2 border-b border-base-300">
                                <x-heroicon-o-briefcase class="w-5 h-5 mr-2" />
                                Berufsspezifische Informationen
                            </h2>

                            <!-- Schauspieler/Synchronsprecher-spezifische Felder -->
                            @if (in_array('actor', $selectedApplication->professions) ||
                                in_array('voice_actor', $selectedApplication->professions))
                                <div class="grid gap-4 md:grid-cols-2">
                                    <!-- Stimmtyp -->
                                    <div>
                                        <label class="label">
                                            <span class="label-text">Stimmtyp</span>
                                        </label>
                                        <input
                                            type="text"
                                            wire:model="editableFields.voice_type"
                                            class="input input-bordered w-full"
                                            placeholder="Beschreibe deinen Stimmtyp"
                                        >
                                        @error('editableFields.voice_type')
                                            <label class="label">
                                                <span class="label-text-alt text-error">{{ $message }}</span>
                                            </label>
                                        @enderror
                                    </div>

                                    <!-- Mikrofon -->
                                    <div>
                                        <label class="label">
                                            <span class="label-text">Mikrofon</span>
                                        </label>
                                        <input
                                            type="text"
                                            wire:model="editableFields.microphone"
                                            class="input input-bordered w-full"
                                            placeholder="Welches Mikrofon nutzt du?"
                                        >
                                        @error('editableFields.microphone')
                                            <label class="label">
                                                <span class="label-text-alt text-error">{{ $message }}</span>
                                            </label>
                                        @enderror
                                    </div>
                                </div>
                            @endif

                            <!-- No-Voice Schauspieler-spezifische Felder -->
                            @if (in_array('actor_no_voice', $selectedApplication->professions))
                                <div class="grid gap-4 md:grid-cols-2">
                                    <!-- Mikrofon -->
                                    <div>
                                        <label class="label">
                                            <span class="label-text">Mikrofon</span>
                                        </label>
                                        <input
                                            type="text"
                                            wire:model="editableFields.microphone"
                                            class="input input-bordered w-full"
                                            placeholder="Welches Mikrofon nutzt du?"
                                        >
                                        @error('editableFields.microphone')
                                            <label class="label">
                                                <span class="label-text-alt text-error">{{ $message }}</span>
                                            </label>
                                        @enderror
                                    </div>
                                </div>
                            @endif

                            <!-- Builder/Kameramann-spezifische Felder -->
                            @if (in_array('builder', $selectedApplication->professions) ||
                                in_array('cameraman', $selectedApplication->professions))
                                <div class="grid gap-4 md:grid-cols-3">
                                    <!-- RAM -->
                                    <div>
                                        <label class="label">
                                            <span class="label-text">RAM</span>
                                        </label>
                                        <input
                                            type="text"
                                            wire:model="editableFields.ram"
                                            class="input input-bordered w-full"
                                            placeholder="Wie viel RAM hast du?"
                                        >
                                        @error('editableFields.ram')
                                            <label class="label">
                                                <span class="label-text-alt text-error">{{ $message }}</span>
                                            </label>
                                        @enderror
                                    </div>

                                    <!-- FPS -->
                                    <div>
                                        <label class="label">
                                            <span class="label-text">FPS</span>
                                        </label>
                                        <input
                                            type="text"
                                            wire:model="editableFields.fps"
                                            class="input input-bordered w-full"
                                            placeholder="Wie viele FPS erreichst du in Minecraft?"
                                        >
                                        @error('editableFields.fps')
                                            <label class="label">
                                                <span class="label-text-alt text-error">{{ $message }}</span>
                                            </label>
                                        @enderror
                                    </div>

                                    <!-- GPU -->
                                    <div>
                                        <label class="label">
                                            <span class="label-text">GPU</span>
                                        </label>
                                        <input
                                            type="text"
                                            wire:model="editableFields.gpu"
                                            class="input input-bordered w-full"
                                            placeholder="Welche Grafikkarte nutzt du?"
                                        >
                                        @error('editableFields.gpu')
                                            <label class="label">
                                                <span class="label-text-alt text-error">{{ $message }}</span>
                                            </label>
                                        @enderror
                                    </div>
                                </div>
                            @endif

                            <!-- Designer-spezifische Felder -->
                            @if (in_array('designer', $selectedApplication->professions))
                                <div class="grid gap-4 md:grid-cols-2">
                                    <!-- Programm -->
                                    <div>
                                        <label class="label">
                                            <span class="label-text">Programm</span>
                                        </label>
                                        <input
                                            type="text"
                                            wire:model="editableFields.program"
                                            class="input input-bordered w-full"
                                            placeholder="Welche Design-Programme nutzt du?"
                                        >
                                        @error('editableFields.program')
                                            <label class="label">
                                                <span class="label-text-alt text-error">{{ $message }}</span>
                                            </label>
                                        @enderror
                                    </div>

                                    <!-- Design-Stil -->
                                    <div>
                                        <label class="label">
                                            <span class="label-text">Design-Stil</span>
                                        </label>
                                        <input
                                            type="text"
                                            wire:model="editableFields.design_style"
                                            class="input input-bordered w-full"
                                            placeholder="Beschreibe deinen Design-Stil"
                                        >
                                        @error('editableFields.design_style')
                                            <label class="label">
                                                <span class="label-text-alt text-error">{{ $message }}</span>
                                            </label>
                                        @enderror
                                    </div>

                                    <!-- Lieblings-Design -->
                                    <div>
                                        <label class="label">
                                            <span class="label-text">Lieblings-Design</span>
                                        </label>
                                        <input
                                            type="text"
                                            wire:model="editableFields.favorite_design"
                                            class="input input-bordered w-full"
                                            placeholder="Was ist dein Lieblings-Design?"
                                        >
                                        @error('editableFields.favorite_design')
                                            <label class="label">
                                                <span class="label-text-alt text-error">{{ $message }}</span>
                                            </label>
                                        @enderror
                                    </div>

                                    <!-- Portfolio -->
                                    <div>
                                        <label class="label">
                                            <span class="label-text">Portfolio (optional)</span>
                                        </label>
                                        <input
                                            type="url"
                                            wire:model="editableFields.portfolio"
                                            class="input input-bordered w-full"
                                            placeholder="Link zu deinem Portfolio"
                                        >
                                    </div>
                                </div>
                            @endif

                            <!-- Developer-spezifische Felder -->
                            @if (in_array('developer', $selectedApplication->professions))
                                <div class="grid gap-4 md:grid-cols-2">
                                    <!-- Programmiersprachen -->
                                    <div>
                                        <label class="label">
                                            <span class="label-text">Programmiersprachen</span>
                                        </label>
                                        <input
                                            type="text"
                                            wire:model="editableFields.languages"
                                            class="input input-bordered w-full"
                                            placeholder="Welche Programmiersprachen beherrschst du?"
                                        >
                                        @error('editableFields.languages')
                                            <label class="label">
                                                <span class="label-text-alt text-error">{{ $message }}</span>
                                            </label>
                                        @enderror
                                    </div>

                                    <!-- IDE -->
                                    <div>
                                        <label class="label">
                                            <span class="label-text">IDE</span>
                                        </label>
                                        <input
                                            type="text"
                                            wire:model="editableFields.ide"
                                            class="input input-bordered w-full"
                                            placeholder="Welche IDE nutzt du?"
                                        >
                                        @error('editableFields.ide')
                                            <label class="label">
                                                <span class="label-text-alt text-error">{{ $message }}</span>
                                            </label>
                                        @enderror
                                    </div>
                                </div>
                            @endif

                            <!-- Musikproduzent-spezifische Felder -->
                            @if (in_array('music_producer', $selectedApplication->professions))
                                <div class="grid gap-4 md:grid-cols-2">
                                    <!-- DAW -->
                                    <div>
                                        <label class="label">
                                            <span class="label-text">DAW</span>
                                        </label>
                                        <input
                                            type="text"
                                            wire:model="editableFields.daw"
                                            class="input input-bordered w-full"
                                            placeholder="Welche DAW nutzt du?"
                                        >
                                        @error('editableFields.daw')
                                            <label class="label">
                                                <span class="label-text-alt text-error">{{ $message }}</span>
                                            </label>
                                        @enderror
                                    </div>
                                </div>
                            @endif
                        </div>
                    </div>

                    <!-- Wichtiger Hinweis -->
                    <div class="mb-6 alert alert-warning">
                        <x-heroicon-o-exclamation-triangle class="w-6 h-6" />
                        <div>
                            <div class="font-bold">Wichtiger Hinweis</div>
                            <div class="text-sm">Durch das Bearbeiten deiner Bewerbung wird diese wieder auf "Ausstehend" gesetzt und muss erneut vom Team überprüft werden.</div>
                        </div>
                    </div>

                    <!-- Button-Gruppe -->
                    <div class="flex justify-between">
                        <button type="button" wire:click="backToDetail" class="btn btn-ghost">
                            <x-heroicon-o-x-mark class="w-5 h-5 mr-2" />
                            Abbrechen
                        </button>

                        <button type="submit" class="btn btn-primary">
                            <x-heroicon-o-check class="w-5 h-5 mr-2" />
                            Bewerbung aktualisieren
                        </button>
                    </div>
                </form>
            </div>
        @endif
    </div>
</div>
