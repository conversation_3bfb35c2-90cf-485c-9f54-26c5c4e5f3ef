<?php

return [

    /*
    |--------------------------------------------------------------------------
    | Debugging Tools Configuration
    |--------------------------------------------------------------------------
    |
    | This file contains configuration for various debugging tools used in the
    | application. These settings control whether debugging tools are enabled
    | and how they behave in different environments.
    |
    */

    /*
    |--------------------------------------------------------------------------
    | Master Switch
    |--------------------------------------------------------------------------
    |
    | This option may be used to disable all debugging tools regardless
    | of their individual configuration, which simply provides a single
    | and convenient way to enable or disable debugging features.
    |
    */

    'enabled' => env('DEBUGGING_ENABLED', env('APP_DEBUG', false)),

    /*
    |--------------------------------------------------------------------------
    | Telescope Configuration
    |--------------------------------------------------------------------------
    |
    | These options control the behavior of Laravel Telescope in the application.
    |
    */

    'telescope' => [
        'enabled' => env('TELESCOPE_ENABLED', env('APP_DEBUG', false)),
        'allow_production' => env('TELESCOPE_ALLOW_PRODUCTION', false),
    ],

    /*
    |--------------------------------------------------------------------------
    | Pulse Configuration
    |--------------------------------------------------------------------------
    |
    | These options control the behavior of Laravel Pulse in the application.
    |
    */

    'pulse' => [
        'enabled' => env('PULSE_ENABLED', env('APP_DEBUG', false)),
        'allow_production' => env('PULSE_ALLOW_PRODUCTION', false),
    ],

    /*
    |--------------------------------------------------------------------------
    | Frontend Debugging
    |--------------------------------------------------------------------------
    |
    | These options control frontend debugging features like console logs,
    | debug panels, and WebSocket debugging tools.
    |
    */

    'frontend' => [
        'console_logs' => env('DEBUG_CONSOLE_LOGS', env('APP_DEBUG', false)),
        'debug_panels' => env('DEBUG_PANELS', env('APP_DEBUG', false)),
        'websocket_debug' => env('DEBUG_WEBSOCKETS', env('APP_DEBUG', false)),
    ],

];
