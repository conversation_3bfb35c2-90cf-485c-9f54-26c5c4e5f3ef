# Deployment Guide für MineWache Website

Dieser Guide erklärt, wie die MineWache Website auf einem Ubuntu 24.04 Server mit Nginx als Webserver bereitgestellt und optimiert wird.

## Inhaltsverzeichnis

1. [Servervoraussetzungen](#1-servervoraussetzungen)
2. [Systemaktualisierung](#2-systemaktualisierung)
3. [Benötigte Software installieren](#3-benötigte-software-installieren)
4. [Datenbank einrichten](#4-datenbank-einrichten)
5. [Anwendung bereitstellen](#5-anwendung-bereitstellen)
6. [Nginx konfigurieren](#6-nginx-konfigurieren)
7. [SSL-Zertifikat einrichten](#7-ssl-zertifikat-einrichten)
8. [Laravel für Produktion optimieren](#8-laravel-für-produktion-optimieren)
9. [Caching und Queues einrichten](#9-caching-und-queues-einrichten)
10. [Monitoring und Logging](#10-monitoring-und-logging)
11. [Automatische Backups](#11-automatische-backups)
12. [Sicherheitsmaßnahmen](#12-sicherheitsmaßnahmen)
13. [Wartung und Updates](#13-wartung-und-updates)
14. [Production WebSocket Proxy Configuration](#14-production-websocket-proxy-configuration)

## 1. Servervoraussetzungen

Minimale Serveranforderungen:
- Ubuntu 24.04 LTS
- 2 GB RAM (4 GB empfohlen)
- 20 GB SSD-Speicher
- Nicht-Root-Benutzer mit sudo-Rechten

## 2. Systemaktualisierung

Aktualisiere das System vor der Installation:

```bash
sudo apt update
sudo apt upgrade -y
```

## 3. Benötigte Software installieren

### PHP und Erweiterungen

```bash
sudo apt install -y php8.2-fpm php8.2-cli php8.2-common php8.2-mysql php8.2-zip php8.2-gd php8.2-mbstring php8.2-curl php8.2-xml php8.2-bcmath php8.2-intl
```

### FFmpeg

FFmpeg wird für die Medienverarbeitung (Videos, Audio, Bilder) im Ticket-System benötigt:

```bash
sudo apt install -y ffmpeg
```

Überprüfe die Installation:

```bash
ffmpeg -version
```

### Nginx

```bash
sudo apt install -y nginx
```

### Composer

```bash
curl -sS https://getcomposer.org/installer | sudo php -- --install-dir=/usr/local/bin --filename=composer
```

### Node.js und NPM (für Frontend-Assets)

```bash
curl -fsSL https://deb.nodesource.com/setup_20.x | sudo -E bash -
sudo apt install -y nodejs
```

### Git

```bash
sudo apt install -y git
```

## 4. Datenbank einrichten

### MySQL installieren

```bash
sudo apt install -y mysql-server
```

### MySQL absichern

```bash
sudo mysql_secure_installation
```

### Datenbank und Benutzer erstellen

```bash
sudo mysql -e "CREATE DATABASE minewache_db CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;"
sudo mysql -e "CREATE USER 'minewache_user'@'localhost' IDENTIFIED BY 'SICHERES_PASSWORT';"
sudo mysql -e "GRANT ALL PRIVILEGES ON minewache_db.* TO 'minewache_user'@'localhost';"
sudo mysql -e "FLUSH PRIVILEGES;"
```

Ersetze `SICHERES_PASSWORT` mit einem starken Passwort.

## 5. Anwendung bereitstellen

### Webserver-Benutzer einrichten

```bash
sudo mkdir -p /var/www/minewache
sudo chown -R www-data:www-data /var/www/minewache
sudo chmod -R 755 /var/www/minewache
```

### Anwendung klonen

```bash
cd /var/www
sudo git clone https://github.com/dein-username/minewache-website.git minewache
cd minewache
```

### Abhängigkeiten installieren

```bash
sudo -u www-data composer install --no-dev --optimize-autoloader
sudo -u www-data npm install
sudo -u www-data npm run build
```

### Umgebungsvariablen konfigurieren

```bash
sudo -u www-data cp .env.example .env
sudo -u www-data php artisan key:generate
```

Bearbeite die `.env`-Datei mit den richtigen Einstellungen:

```bash
sudo nano .env
```

Wichtige Einstellungen:

```
APP_ENV=production
APP_DEBUG=false
APP_URL=https://deine-domain.de

DB_CONNECTION=mysql
DB_HOST=127.0.0.1
DB_PORT=3306
DB_DATABASE=minewache_db
DB_USERNAME=minewache_user
DB_PASSWORD=SICHERES_PASSWORT

DISCORD_API_KEY=dein_discord_api_key
LARASCORD_CLIENT_ID=dein_client_id
LARASCORD_CLIENT_SECRET=dein_client_secret
LARASCORD_GUILD_ID=dein_guild_id
LARASCORD_REDIRECT_URI=https://deine-domain.de/larascord/callback

MAIL_MAILER=smtp
MAIL_HOST=dein_mail_host
MAIL_PORT=587
MAIL_USERNAME=dein_mail_username
MAIL_PASSWORD=dein_mail_passwort
MAIL_ENCRYPTION=tls
MAIL_FROM_ADDRESS=<EMAIL>
MAIL_FROM_NAME="${APP_NAME}"

# FFmpeg Konfiguration
FFMPEG_BINARIES=/usr/bin/ffmpeg
FFPROBE_BINARIES=/usr/bin/ffprobe
FFMPEG_TEMPORARY_FILES_ROOT=/tmp
```

### Berechtigungen setzen

```bash
sudo chown -R www-data:www-data /var/www/minewache
sudo find /var/www/minewache -type f -exec chmod 644 {} \;
sudo find /var/www/minewache -type d -exec chmod 755 {} \;
sudo chmod -R ug+rwx /var/www/minewache/storage /var/www/minewache/bootstrap/cache
```

### Datenbank migrieren

```bash
cd /var/www/minewache
sudo -u www-data php artisan migrate --force
```

## 6. Nginx konfigurieren

Erstelle eine Nginx-Konfigurationsdatei:

```bash
sudo nano /etc/nginx/sites-available/minewache
```

Füge folgenden Inhalt ein:

```nginx
server {
    listen 80;
    server_name deine-domain.de www.deine-domain.de;
    root /var/www/minewache/public;

    add_header X-Frame-Options "SAMEORIGIN";
    add_header X-Content-Type-Options "nosniff";

    index index.php;

    charset utf-8;

    location / {
        try_files $uri $uri/ /index.php?$query_string;
    }

    location = /favicon.ico { access_log off; log_not_found off; }
    location = /robots.txt  { access_log off; log_not_found off; }

    error_page 404 /index.php;

    location ~ \.php$ {
        fastcgi_pass unix:/var/run/php/php8.2-fpm.sock;
        fastcgi_param SCRIPT_FILENAME $realpath_root$fastcgi_script_name;
        include fastcgi_params;
    }

    location ~ /\.(?!well-known).* {
        deny all;
    }
}
```

Aktiviere die Konfiguration:

```bash
sudo ln -s /etc/nginx/sites-available/minewache /etc/nginx/sites-enabled/
sudo nginx -t
sudo systemctl restart nginx
```

## 7. SSL-Zertifikat einrichten

### Certbot installieren

```bash
sudo apt install -y certbot python3-certbot-nginx
```

### SSL-Zertifikat beantragen

```bash
sudo certbot --nginx -d deine-domain.de -d www.deine-domain.de
```

Folge den Anweisungen und wähle die Option, um HTTP auf HTTPS umzuleiten.

## 8. Laravel für Produktion optimieren

### Konfiguration cachen

```bash
cd /var/www/minewache
sudo -u www-data php artisan config:cache
sudo -u www-data php artisan route:cache
sudo -u www-data php artisan view:cache
```

### Optimierung der Autoloader-Klassen

Dies wurde bereits beim Composer-Install mit dem `--optimize-autoloader` Flag durchgeführt.

### OPcache aktivieren

Bearbeite die PHP-FPM-Konfiguration:

```bash
sudo nano /etc/php/8.2/fpm/php.ini
```

Füge folgende Einstellungen hinzu oder ändere sie:

```ini
opcache.enable=1
opcache.memory_consumption=128
opcache.interned_strings_buffer=8
opcache.max_accelerated_files=4000
opcache.revalidate_freq=60
opcache.fast_shutdown=1
opcache.enable_cli=1
```

Starte PHP-FPM neu:

```bash
sudo systemctl restart php8.2-fpm
```

## 9. Caching und Queues einrichten

### Redis installieren (für Caching und Queues)

```bash
sudo apt install -y redis-server
```

Konfiguriere Redis in der `.env`-Datei:

```
CACHE_DRIVER=redis
QUEUE_CONNECTION=redis
SESSION_DRIVER=redis
REDIS_HOST=127.0.0.1
REDIS_PASSWORD=null
REDIS_PORT=6379
```

### Supervisor für Queue Worker einrichten

```bash
sudo apt install -y supervisor
```

Erstelle eine Supervisor-Konfiguration:

```bash
sudo nano /etc/supervisor/conf.d/minewache-worker.conf
```

Füge folgenden Inhalt ein:

```ini
[program:minewache-worker]
process_name=%(program_name)s_%(process_num)02d
command=php /var/www/minewache/artisan queue:work redis --sleep=3 --tries=3 --max-time=3600
autostart=true
autorestart=true
stopasgroup=true
killasgroup=true
user=www-data
numprocs=2
redirect_stderr=true
stdout_logfile=/var/www/minewache/storage/logs/worker.log
stopwaitsecs=3600
```

Für die Medienverarbeitung mit FFmpeg empfehlen wir eine separate Queue-Konfiguration mit höheren Ressourcenlimits:

```ini
[program:minewache-media-worker]
process_name=%(program_name)s_%(process_num)02d
command=php /var/www/minewache/artisan queue:work redis --queue=media --sleep=3 --tries=3 --max-time=7200 --memory=512
autostart=true
autorestart=true
stopasgroup=true
killasgroup=true
user=www-data
numprocs=1
redirect_stderr=true
stdout_logfile=/var/www/minewache/storage/logs/media-worker.log
stopwaitsecs=7200
```

Aktiviere und starte den Supervisor:

```bash
sudo supervisorctl reread
sudo supervisorctl update
sudo supervisorctl start all
```

## 10. Monitoring und Logging

### Logrotate konfigurieren

```bash
sudo nano /etc/logrotate.d/minewache
```

Füge folgenden Inhalt ein:

```
/var/www/minewache/storage/logs/*.log {
    daily
    missingok
    rotate 14
    compress
    delaycompress
    notifempty
    create 0640 www-data www-data
    sharedscripts
    postrotate
        /usr/bin/supervisorctl restart minewache-worker:* > /dev/null 2>/dev/null || true
    endscript
}
```

### Monitoring mit Prometheus und Grafana (optional)

Für fortgeschrittenes Monitoring kannst du Prometheus und Grafana einrichten. Dies würde den Rahmen dieses Guides sprengen, aber es gibt viele gute Tutorials dazu online.

## 11. Automatische Backups

### Backup-Skript erstellen

```bash
sudo nano /usr/local/bin/backup-minewache.sh
```

Füge folgenden Inhalt ein:

```bash
#!/bin/bash

# Variablen
TIMESTAMP=$(date +"%Y%m%d_%H%M%S")
BACKUP_DIR="/var/backups/minewache"
DB_USER="minewache_user"
DB_PASS="SICHERES_PASSWORT"
DB_NAME="minewache_db"
APP_DIR="/var/www/minewache"

# Backup-Verzeichnis erstellen
mkdir -p $BACKUP_DIR

# Datenbank-Backup
mysqldump -u $DB_USER -p$DB_PASS $DB_NAME | gzip > "$BACKUP_DIR/db_backup_$TIMESTAMP.sql.gz"

# Anwendungs-Backup (ohne Vendor-Verzeichnis und Node-Module)
cd $APP_DIR && tar --exclude="./vendor" --exclude="./node_modules" --exclude="./storage/logs" -czf "$BACKUP_DIR/app_backup_$TIMESTAMP.tar.gz" .

# Alte Backups löschen (älter als 30 Tage)
find $BACKUP_DIR -name "*.gz" -type f -mtime +30 -delete
```

Mache das Skript ausführbar:

```bash
sudo chmod +x /usr/local/bin/backup-minewache.sh
```

### Cron-Job für automatische Backups einrichten

```bash
sudo crontab -e
```

Füge folgende Zeile hinzu:

```
0 2 * * * /usr/local/bin/backup-minewache.sh > /var/log/minewache-backup.log 2>&1
```

Dies führt das Backup täglich um 2 Uhr morgens aus.

## 12. Sicherheitsmaßnahmen

### Firewall einrichten

```bash
sudo apt install -y ufw
sudo ufw allow ssh
sudo ufw allow 'Nginx Full'
sudo ufw enable
```

### Fail2Ban installieren

```bash
sudo apt install -y fail2ban
```

Konfiguriere Fail2Ban für Nginx:

```bash
sudo nano /etc/fail2ban/jail.local
```

Füge folgenden Inhalt ein:

```ini
[nginx-http-auth]
enabled = true
filter = nginx-http-auth
port = http,https
logpath = /var/log/nginx/error.log

[nginx-botsearch]
enabled = true
filter = nginx-botsearch
port = http,https
logpath = /var/log/nginx/access.log
```

Starte Fail2Ban neu:

```bash
sudo systemctl restart fail2ban
```

### Regelmäßige Sicherheitsupdates

Aktiviere automatische Sicherheitsupdates:

```bash
sudo apt install -y unattended-upgrades
sudo dpkg-reconfigure -plow unattended-upgrades
```

## 13. Wartung und Updates

### Anwendung aktualisieren

```bash
cd /var/www/minewache
sudo -u www-data git pull
sudo -u www-data composer install --no-dev --optimize-autoloader
sudo -u www-data npm install
sudo -u www-data npm run build
sudo -u www-data php artisan migrate --force
sudo -u www-data php artisan config:cache
sudo -u www-data php artisan route:cache
sudo -u www-data php artisan view:cache
sudo supervisorctl restart minewache-worker:*
```

### Wartungsmodus aktivieren/deaktivieren

Aktivieren:

```bash
cd /var/www/minewache
sudo -u www-data php artisan down --message="Die Website wird aktualisiert und ist in Kürze wieder verfügbar." --retry=60
```

Deaktivieren:

```bash
cd /var/www/minewache
sudo -u www-data php artisan up
```

## Fazit

Dieser Guide bietet eine umfassende Anleitung zur Bereitstellung und Optimierung der MineWache Website auf einem Ubuntu 24.04 Server mit Nginx. Durch Befolgen dieser Schritte erhältst du eine sichere, schnelle und zuverlässige Produktionsumgebung.

Für weitere Optimierungen könntest du folgende Themen erkunden:
- Content Delivery Network (CDN) für statische Assets
- Horizontale Skalierung mit mehreren Servern
- Container-basierte Bereitstellung mit Docker
- CI/CD-Pipelines für automatisierte Deployments

Bei Fragen oder Problemen kannst du dich an das Entwicklungsteam wenden oder einen Issue auf GitHub erstellen.

## 14. Production WebSocket Proxy Configuration

When deploying Laravel Reverb with WebSockets in a production environment, using a reverse proxy like Nginx is essential. The proxy handles incoming client connections (often over HTTPS on standard ports 443/80) and forwards them to the Reverb server, which typically runs on an internal, non-public port. This setup simplifies SSL/TLS termination, allows custom WebSocket paths, and enhances security.

### Client-Side Configuration (`resources/js/bootstrap.js`)

Your client-side JavaScript, typically in `resources/js/bootstrap.js` (or a similar file imported by your main application JavaScript), should be configured to connect to the WebSocket server via the reverse proxy. Key parameters for Laravel Echo include:

-   `wsHost: window.location.hostname`: Tells Echo to connect to the same hostname as the main website.
-   `wsPort` / `wssPort`: In production, these are usually standard ports `80` (for `ws://`) and `443` (for `wss://`). Echo will typically choose the correct port based on the protocol.
-   `forceTLS: (document.location.protocol === "https:")`: Ensures that `wss://` is used if the main site is loaded over HTTPS.
-   `path: '/app'`: This is a custom path that Nginx will listen on for WebSocket connections. It must match the `location` block in your Nginx configuration.
-   `namespace: ''`: This is usually left blank for Reverb, as the app key is handled by the `broadcaster` configuration.

Example snippet from `bootstrap.js`:

```javascript
// In resources/js/bootstrap.js or similar
import Echo from 'laravel-echo';
import Pusher from 'pusher-js';

window.Pusher = Pusher;

window.Echo = new Echo({
    broadcaster: 'reverb',
    key: import.meta.env.VITE_REVERB_APP_KEY,
    wsHost: import.meta.env.VITE_REVERB_HOST, // Should resolve to window.location.hostname in production .env
    wsPort: import.meta.env.VITE_REVERB_PORT, // Should be 80 or 443 (proxied) in production .env
    wssPort: import.meta.env.VITE_REVERB_PORT, // Should be 443 (proxied) in production .env
    forceTLS: (import.meta.env.VITE_REVERB_SCHEME === 'https'), // Should be true if site is HTTPS
    enabledTransports: ['ws', 'wss'],
    path: '/app', // Custom path for Nginx proxy
    namespace: '', // Reverb typically doesn't use a Pusher app cluster / namespace
});
```

Ensure your production `.env` file sets `VITE_REVERB_HOST` to your application's domain and `VITE_REVERB_PORT` to `443` (if using HTTPS) or `80` (if using HTTP), and `VITE_REVERB_SCHEME` to `https` or `http` accordingly. The `path` option is crucial for Nginx to correctly route WebSocket traffic.

### Reverb Server

Your Laravel Reverb server must be running and listening on an internal host and port. This is configured in your `.env` file:

-   `REVERB_SERVER_HOST="127.0.0.1"` (or another internal IP)
-   `REVERB_SERVER_PORT="6001"` (or any other available internal port)

You would typically start the Reverb server using:
```bash
php artisan reverb:start --host="${REVERB_SERVER_HOST}" --port="${REVERB_SERVER_PORT}"
```
It's recommended to run this process using a process manager like Supervisor in production (see section 9).

### Nginx Configuration Example

Add the following `location` block to your Nginx server configuration (usually within your main `server` block, e.g., in `/etc/nginx/sites-available/deine-domain.de`). This block should come *before* your general PHP processing `location ~ \.php$` block.

```nginx
# WebSocket proxy for Laravel Reverb
# Ensure this location block is placed *before* your PHP processing block.
location /app { # This path must match 'path' in your client-side Echo config
    # Proxy to the Reverb server
    # Ensure REVERB_SERVER_HOST and REVERB_SERVER_PORT match your .env settings
    proxy_pass http://127.0.0.1:6001; # Example: http://REVERB_SERVER_HOST:REVERB_SERVER_PORT

    # Required for WebSocket upgrades
    proxy_http_version 1.1;
    proxy_set_header Upgrade $http_upgrade;
    proxy_set_header Connection "upgrade";

    # Pass client information
    proxy_set_header Host $host;
    proxy_set_header X-Real-IP $remote_addr;
    proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    proxy_set_header X-Forwarded-Proto $scheme;

    # Increase timeouts for long-lived connections
    proxy_read_timeout 86400s; # 24 hours
    proxy_send_timeout 86400s; # 24 hours

    # Optional: Disable buffering for more real-time communication
    # proxy_buffering off;

    # Optional: Access logging for WebSocket connections
    # access_log /var/log/nginx/websocket_access.log;
    # error_log /var/log/nginx/websocket_error.log;
}
```

**Important Considerations:**

*   **`proxy_pass http://127.0.0.1:6001;`**: Replace `127.0.0.1:6001` with the actual host and port your Reverb server is listening on, as defined by `REVERB_SERVER_HOST` and `REVERB_SERVER_PORT` in your `.env` file.
*   **`location /app`**: The path `/app` must exactly match the `path` option configured in your client-side Laravel Echo setup. If you change it in one place, you must change it in the other.
*   **Order of Nginx `location` blocks**: This WebSocket proxy `location` block should generally be placed *before* any broader `location` blocks that might also match the path (e.g., `location /`) or your main PHP processing block (`location ~ \.php$`). This ensures WebSocket requests are specifically handled by this proxy configuration.

### Reload Nginx Configuration

After adding or modifying the Nginx configuration, always test and reload Nginx for the changes to take effect:

```bash
sudo nginx -t
sudo systemctl reload nginx
```

### Troubleshooting Tips

*   **Verify Reverb Server**:
    *   Ensure your Reverb server is running: `ps aux | grep reverb:start` or check Supervisor status.
    *   Check it's listening on the configured host/port: `ss -tulnp | grep 6001` (replace 6001 if different).
    *   Test internal connectivity to Reverb from the server itself if possible (e.g., using `curl` or `telnet` to the Reverb host/port, though WebSocket handshakes are more complex).
*   **Check Nginx Error Logs**:
    *   Examine `/var/log/nginx/error.log` (or your site-specific error log) for any messages related to the `/app` path or proxying.
*   **Browser Developer Tools**:
    *   Open your browser's developer console (usually F12).
    *   Go to the "Network" tab and filter by "WS" (WebSockets).
    *   Observe the WebSocket connection attempt. Check the request headers, response headers, and any error messages.
    *   Common errors include 404 (Nginx not finding the `/app` location or Reverb server down), 502 Bad Gateway (Reverb server issue or Nginx cannot connect), or SSL handshake errors.
*   **Client-Side Configuration**: Double-check that `wsHost`, `wsPort`/`wssPort`, `forceTLS`, and `path` in your `bootstrap.js` correctly reflect your production Nginx setup. For example, in production, `VITE_REVERB_PORT` (used for `wsPort` and `wssPort` in Echo) should effectively be `443` (if using HTTPS) or `80` (if using HTTP) because Nginx is proxying these standard ports.

By correctly configuring Nginx as a reverse proxy, you can securely and efficiently manage WebSocket connections for your Laravel Reverb application in production.
