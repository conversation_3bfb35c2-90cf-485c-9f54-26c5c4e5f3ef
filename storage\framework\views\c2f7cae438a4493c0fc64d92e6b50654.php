<?php $attributes ??= new \Illuminate\View\ComponentAttributeBag;

$__newAttributes = [];
$__propNames = \Illuminate\View\ComponentAttributeBag::extractPropNames(([
    'position' => 'bottom-right', // bottom-right, bottom-left, top-right, top-left
    'size' => 'md', // sm, md, lg
    'variant' => 'primary',
    'icon' => null,
    'label' => null,
    'href' => null,
    'extended' => false // Show label alongside icon
]));

foreach ($attributes->all() as $__key => $__value) {
    if (in_array($__key, $__propNames)) {
        $$__key = $$__key ?? $__value;
    } else {
        $__newAttributes[$__key] = $__value;
    }
}

$attributes = new \Illuminate\View\ComponentAttributeBag($__newAttributes);

unset($__propNames);
unset($__newAttributes);

foreach (array_filter(([
    'position' => 'bottom-right', // bottom-right, bottom-left, top-right, top-left
    'size' => 'md', // sm, md, lg
    'variant' => 'primary',
    'icon' => null,
    'label' => null,
    'href' => null,
    'extended' => false // Show label alongside icon
]), 'is_string', ARRAY_FILTER_USE_KEY) as $__key => $__value) {
    $$__key = $$__key ?? $__value;
}

$__defined_vars = get_defined_vars();

foreach ($attributes->all() as $__key => $__value) {
    if (array_key_exists($__key, $__defined_vars)) unset($$__key);
}

unset($__defined_vars); ?>

<?php
$positionClasses = match($position) {
    'bottom-right' => 'fixed bottom-6 right-6',
    'bottom-left' => 'fixed bottom-6 left-6',
    'top-right' => 'fixed top-6 right-6',
    'top-left' => 'fixed top-6 left-6',
    default => 'fixed bottom-6 right-6'
};

$sizeClasses = match($size) {
    'sm' => 'w-12 h-12',
    'md' => 'w-14 h-14',
    'lg' => 'w-16 h-16',
    default => 'w-14 h-14'
};

$iconSizeClasses = match($size) {
    'sm' => 'w-5 h-5',
    'md' => 'w-6 h-6',
    'lg' => 'w-7 h-7',
    default => 'w-6 h-6'
};

$variantClasses = match($variant) {
    'primary' => 'bg-primary hover:bg-primary-focus text-primary-content shadow-primary/25',
    'secondary' => 'bg-secondary hover:bg-secondary-focus text-secondary-content shadow-secondary/25',
    'accent' => 'bg-accent hover:bg-accent-focus text-accent-content shadow-accent/25',
    'success' => 'bg-success hover:bg-success/90 text-success-content shadow-success/25',
    'warning' => 'bg-warning hover:bg-warning/90 text-warning-content shadow-warning/25',
    'error' => 'bg-error hover:bg-error/90 text-error-content shadow-error/25',
    default => 'bg-primary hover:bg-primary-focus text-primary-content shadow-primary/25'
};

$baseClasses = 'inline-flex items-center justify-center rounded-full shadow-lg transition-all duration-300 ease-out hover:scale-110 hover:shadow-xl focus:outline-none focus:ring-4 focus:ring-offset-2 focus:ring-primary/50 z-50';

$extendedClasses = $extended && $label ? 'px-6 gap-3' : '';

$classes = implode(' ', array_filter([
    $baseClasses,
    $positionClasses,
    $extended && $label ? 'rounded-full' : $sizeClasses,
    $variantClasses,
    $extendedClasses
]));
?>

<?php if($href): ?>
    <a href="<?php echo e($href); ?>" <?php echo e($attributes->merge(['class' => $classes])); ?>>
        <?php if($icon): ?>
            <div class="<?php echo e($iconSizeClasses); ?>">
                <?php echo $icon; ?>

            </div>
        <?php endif; ?>
        <?php if($extended && $label): ?>
            <span class="font-medium text-sm"><?php echo e($label); ?></span>
        <?php endif; ?>
        <?php echo e($slot); ?>

    </a>
<?php else: ?>
    <button <?php echo e($attributes->merge(['class' => $classes])); ?>>
        <?php if($icon): ?>
            <div class="<?php echo e($iconSizeClasses); ?>">
                <?php echo $icon; ?>

            </div>
        <?php endif; ?>
        <?php if($extended && $label): ?>
            <span class="font-medium text-sm"><?php echo e($label); ?></span>
        <?php endif; ?>
        <?php echo e($slot); ?>

    </button>
<?php endif; ?>

<style>
    /* Enhanced shadow and animation effects */
    .modern-fab {
        backdrop-filter: blur(8px);
        box-shadow: 
            0 4px 6px -1px rgba(0, 0, 0, 0.1),
            0 2px 4px -1px rgba(0, 0, 0, 0.06),
            0 0 0 1px rgba(255, 255, 255, 0.1);
    }
    
    .modern-fab:hover {
        box-shadow: 
            0 20px 25px -5px rgba(0, 0, 0, 0.1),
            0 10px 10px -5px rgba(0, 0, 0, 0.04),
            0 0 0 1px rgba(255, 255, 255, 0.15);
    }
    
    /* Pulse animation for attention */
    .modern-fab.pulse {
        animation: fab-pulse 2s infinite;
    }
    
    @keyframes fab-pulse {
        0%, 100% {
            transform: scale(1);
        }
        50% {
            transform: scale(1.05);
        }
    }
    
    /* Theme-specific adjustments */
    [data-theme="minewache-light"] .modern-fab {
        box-shadow: 
            0 4px 6px -1px rgba(0, 0, 0, 0.15),
            0 2px 4px -1px rgba(0, 0, 0, 0.1);
    }
    
    [data-theme="minewache-high-contrast"] .modern-fab {
        border: 3px solid currentColor;
        font-weight: bold;
    }
    
    [data-theme="minewache-colorful"] .modern-fab {
        background: linear-gradient(135deg, var(--color-primary) 0%, var(--color-secondary) 100%);
    }
    
    /* Responsive adjustments */
    @media (max-width: 640px) {
        .modern-fab {
            bottom: 1rem;
            right: 1rem;
        }
        
        .modern-fab.extended {
            padding-left: 1rem;
            padding-right: 1rem;
        }
    }
    
    /* Accessibility improvements */
    .modern-fab:focus-visible {
        outline: 2px solid var(--color-primary);
        outline-offset: 2px;
    }
    
    /* Reduced motion support */
    @media (prefers-reduced-motion: reduce) {
        .modern-fab {
            transition: none;
        }
        
        .modern-fab:hover {
            transform: none;
        }
        
        .modern-fab.pulse {
            animation: none;
        }
    }
</style>
<?php /**PATH C:\Users\<USER>\Documents\GitHub\minewache-website\resources\views/components/modern-fab.blade.php ENDPATH**/ ?>