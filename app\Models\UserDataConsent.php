<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class UserDataConsent extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'user_id',
        'share_username',
        'share_applications',
        'share_tickets',
        'share_discord_info',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'share_username' => 'boolean',
        'share_applications' => 'boolean',
        'share_tickets' => 'boolean',
        'share_discord_info' => 'boolean',
    ];

    /**
     * Get the user that owns the consent.
     */
    public function user()
    {
        return $this->belongsTo(User::class);
    }
}
