# Recommended .env Settings for GDPR Compliance

To ensure your application is fully GDPR compliant, please update the following settings in your `.env` file:

## Session Cookie Settings

```
SESSION_DRIVER=database
SESSION_LIFETIME=120
SESSION_SECURE_COOKIE=true
SESSION_COOKIE=minewache_session
SESSION_DOMAIN=null
SESSION_SAME_SITE=lax
SESSION_HTTP_ONLY=true
```

## Security Settings

```
SANCTUM_STATEFUL_DOMAINS=yourdomain.com
SESSION_DOMAIN=.yourdomain.com
```

## Important Notes

1. `SESSION_SECURE_COOKIE=true` ensures cookies are only sent over HTTPS connections
2. `SESSION_HTTP_ONLY=true` prevents JavaScript from accessing cookies
3. `SESSION_SAME_SITE=lax` prevents CSRF attacks while allowing normal navigation
4. `SESSION_LIFETIME=120` sets session timeout to 120 minutes (adjust as needed)

## Additional Recommendations

1. Make sure your website is served over HTTPS
2. Update your domain names in the settings above
3. Consider implementing a shorter session lifetime for enhanced security
4. Review and update your privacy policy regularly

These settings help ensure your application complies with GDPR requirements for secure data handling and user privacy.
