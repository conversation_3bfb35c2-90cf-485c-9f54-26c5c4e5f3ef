<x-app-layout>
    <x-slot name="heading">
        UI/UX Component Demo
    </x-slot>

    <div class="space-y-12 py-8">
        <!-- Theme Showcase -->
        <section>
            <h2 class="text-3xl font-bold mb-6 text-center">Enhanced Theme System</h2>
            <div class="text-center mb-8">
                <p class="text-lg text-base-content/80">
                    Experience our new multi-theme system with smooth transitions and accessibility features.
                    Use the theme switcher in the top-right corner to explore all available themes.
                </p>
            </div>
        </section>

        <!-- Modern Cards Showcase -->
        <section>
            <h2 class="text-3xl font-bold mb-6">Modern Card Components</h2>
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">

                <x-modern-card variant="default" size="md">
                    <h3 class="text-xl font-semibold mb-3">Default Card</h3>
                    <p class="text-base-content/80">Standard glass effect with subtle shadows and smooth transitions.</p>
                </x-modern-card>

                <x-modern-card variant="elevated" size="md" interactive="true">
                    <h3 class="text-xl font-semibold mb-3">Elevated Card</h3>
                    <p class="text-base-content/80">Enhanced shadows with hover effects. Try hovering over this card!</p>
                </x-modern-card>

                <x-modern-card variant="outlined" size="md">
                    <h3 class="text-xl font-semibold mb-3">Outlined Card</h3>
                    <p class="text-base-content/80">Clean border-based design with transparent background.</p>
                </x-modern-card>

                <x-modern-card variant="filled" size="md">
                    <h3 class="text-xl font-semibold mb-3">Filled Card</h3>
                    <p class="text-base-content/80">Solid background for better content separation.</p>
                </x-modern-card>

                <x-modern-card variant="gradient" size="md" interactive="true">
                    <h3 class="text-xl font-semibold mb-3">Gradient Card</h3>
                    <p class="text-base-content/80">Beautiful gradient overlay with interactive effects.</p>
                </x-modern-card>

                <x-modern-card variant="default" size="lg" blur="true">
                    <h3 class="text-xl font-semibold mb-3">Large Blur Card</h3>
                    <p class="text-base-content/80">Enhanced blur effect with larger padding for important content.</p>
                </x-modern-card>

            </div>
        </section>

        <!-- Modern Buttons Showcase -->
        <section>
            <h2 class="text-3xl font-bold mb-6">Modern Button Components</h2>

            <div class="space-y-8">
                <!-- Button Variants -->
                <div>
                    <h3 class="text-xl font-semibold mb-4">Button Variants</h3>
                    <div class="flex flex-wrap gap-4">
                        <x-modern-button variant="primary">Primary</x-modern-button>
                        <x-modern-button variant="secondary">Secondary</x-modern-button>
                        <x-modern-button variant="accent">Accent</x-modern-button>
                        <x-modern-button variant="ghost">Ghost</x-modern-button>
                        <x-modern-button variant="outline">Outline</x-modern-button>
                        <x-modern-button variant="gradient">Gradient</x-modern-button>
                    </div>
                </div>

                <!-- Button Sizes -->
                <div>
                    <h3 class="text-xl font-semibold mb-4">Button Sizes</h3>
                    <div class="flex flex-wrap items-center gap-4">
                        <x-modern-button variant="primary" size="xs">Extra Small</x-modern-button>
                        <x-modern-button variant="primary" size="sm">Small</x-modern-button>
                        <x-modern-button variant="primary" size="md">Medium</x-modern-button>
                        <x-modern-button variant="primary" size="lg">Large</x-modern-button>
                        <x-modern-button variant="primary" size="xl">Extra Large</x-modern-button>
                    </div>
                </div>

                <!-- Button Shapes -->
                <div>
                    <h3 class="text-xl font-semibold mb-4">Button Shapes</h3>
                    <div class="flex flex-wrap gap-4">
                        <x-modern-button variant="primary" shape="square">Square</x-modern-button>
                        <x-modern-button variant="primary" shape="rounded">Rounded</x-modern-button>
                        <x-modern-button variant="primary" shape="pill">Pill</x-modern-button>
                    </div>
                </div>

                <!-- Buttons with Icons -->
                <div>
                    <h3 class="text-xl font-semibold mb-4">Buttons with Icons</h3>
                    <div class="flex flex-wrap gap-4">
                        <x-modern-button
                            variant="primary"
                            icon='<svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4" /></svg>'
                            iconPosition="left"
                        >
                            Add Item
                        </x-modern-button>

                        <x-modern-button
                            variant="secondary"
                            icon='<svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" /></svg>'
                            iconPosition="right"
                        >
                            Continue
                        </x-modern-button>

                        <x-modern-button variant="accent" loading="true">
                            Loading...
                        </x-modern-button>
                    </div>
                </div>

                <!-- Semantic Buttons -->
                <div>
                    <h3 class="text-xl font-semibold mb-4">Semantic Buttons</h3>
                    <div class="flex flex-wrap gap-4">
                        <x-modern-button variant="success">Success</x-modern-button>
                        <x-modern-button variant="warning">Warning</x-modern-button>
                        <x-modern-button variant="danger">Danger</x-modern-button>
                    </div>
                </div>
            </div>
        </section>

        <!-- Modern Inputs Showcase -->
        <section>
            <h2 class="text-3xl font-bold mb-6">Modern Input Components</h2>

            <div class="grid grid-cols-1 md:grid-cols-2 gap-8">
                <div class="space-y-6">
                    <x-modern-input
                        label="Default Input"
                        placeholder="Enter your text..."
                        helper="This is a helper text"
                    />

                    <x-modern-input
                        label="Filled Input"
                        placeholder="Filled variant..."
                        variant="filled"
                    />

                    <x-modern-input
                        label="Outlined Input"
                        placeholder="Outlined variant..."
                        variant="outlined"
                    />
                </div>

                <div class="space-y-6">
                    <x-modern-input
                        label="Glass Input"
                        placeholder="Glass effect..."
                        variant="glass"
                    />

                    <x-modern-input
                        label="Input with Icon"
                        placeholder="Search..."
                        icon='<svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" /></svg>'
                    />

                    <x-modern-input
                        label="Error State"
                        placeholder="This has an error..."
                        error="This field is required"
                        required="true"
                    />
                </div>
            </div>
        </section>

        <!-- Theme-Specific Features -->
        <section>
            <h2 class="text-3xl font-bold mb-6">Theme-Specific Features</h2>
            <x-modern-card variant="gradient" size="lg">
                <div class="text-center">
                    <h3 class="text-2xl font-semibold mb-4">Adaptive Design</h3>
                    <p class="text-lg text-base-content/80 mb-6">
                        All components automatically adapt to the selected theme, providing consistent
                        visual hierarchy and optimal contrast ratios across all theme variants.
                    </p>
                    <div class="grid grid-cols-2 md:grid-cols-5 gap-4 text-sm">
                        <div class="p-3 rounded-lg bg-primary/20">
                            <div class="font-semibold">Dark Theme</div>
                            <div class="text-xs opacity-75">Classic & Modern</div>
                        </div>
                        <div class="p-3 rounded-lg bg-primary/20">
                            <div class="font-semibold">Light Theme</div>
                            <div class="text-xs opacity-75">Clean & Bright</div>
                        </div>
                        <div class="p-3 rounded-lg bg-primary/20">
                            <div class="font-semibold">Auto Theme</div>
                            <div class="text-xs opacity-75">System Sync</div>
                        </div>
                        <div class="p-3 rounded-lg bg-primary/20">
                            <div class="font-semibold">High Contrast</div>
                            <div class="text-xs opacity-75">Accessibility</div>
                        </div>
                        <div class="p-3 rounded-lg bg-primary/20">
                            <div class="font-semibold">Colorful</div>
                            <div class="text-xs opacity-75">Vibrant & Fun</div>
                        </div>
                    </div>
                </div>
            </x-modern-card>
        </section>

        <!-- Call to Action -->
        <section class="text-center">
            <x-modern-card variant="elevated" size="xl" class="max-w-4xl mx-auto">
                <h2 class="text-4xl font-bold mb-4">Ready to Experience the New Design?</h2>
                <p class="text-xl text-base-content/80 mb-8">
                    Explore the enhanced UI/UX throughout the entire website with improved accessibility,
                    smooth animations, and beautiful theme transitions.
                </p>
                <div class="flex flex-wrap justify-center gap-4">
                    <x-modern-button
                        variant="gradient"
                        size="lg"
                        href="{{ route('home') }}"
                        icon='<svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6" /></svg>'
                    >
                        Back to Home
                    </x-modern-button>

                    <x-modern-button
                        variant="outline"
                        size="lg"
                        href="{{ route('bewerben') }}"
                    >
                        Apply Now
                    </x-modern-button>
                </div>
            </x-modern-card>
        </section>
    </div>
</x-app-layout>
