<?php

return [
    // Application Form Progress
    'step_of' => 'Step :current of :total',
    'completed' => ':percent% Completed',
    'personal_data' => 'Personal Data',
    'role_specific' => 'Role-Specific',
    'about_you' => 'About You',
    'review' => 'Review', // Used in progress indicator
    'review_submit' => 'Review & Submit', // Header for step 4

    // General Headers/Titles
    'application_form' => 'Application Form',
    'application_description' => 'Join our creative team and help bring amazing Minecraft content to life.',
    'personal_data_description' => 'Tell us a bit about yourself to get started.',

    // Form Fields - General
    'name' => 'Name',
    'name_placeholder' => 'Your preferred name',
    'name_help' => 'What should we call you?', // Not used in Blade, but good for reference
    'name_description' => 'This can be your Discord name, nickname, or real name - your choice.', // Not used in Blade

    'age' => 'Age',
    'age_placeholder' => 'Your age',
    'age_help' => 'We need to know your age for role assignment purposes.', // Not used in Blade

    'gender' => 'Gender',
    'gender_select' => 'Select an option',
    'gender_male' => 'Male',
    'gender_female' => 'Female',
    'gender_diverse' => 'Diverse',
    'gender_no_info' => 'Prefer not to say',
    // 'gender_other' => 'Other', // Not present in current select options

    'pronouns' => 'Pronouns',
    'pronouns_placeholder' => 'e.g., he/him, she/her, they/them',
    'optional' => '(optional)',

    // Professions
    'professions' => 'Professions', // Used in Review section
    'professions_question' => 'What role(s) interest you?',
    'professions_select_description' => 'Select all positions you\'d like to apply for.',
    'professions_help' => 'Choose at least one', // Not used in Blade
    'professions_select_at_least_one' => 'Select at least one', // Not used in Blade

    'profession_description' => [ // This is the main list for profession labels
        'actor' => 'Actor',
        'actor_no_voice' => 'Actor (No voice)',
        'voice_actor' => 'Voice Actor',
        'builder' => 'Builder',
        'designer' => 'Designer',
        'cutter' => 'Cutter',
        'cameraman' => 'Cameraman',
        'developer' => 'Developer',
        'modeler' => '3D Modeler',
        'music_producer' => 'Music Producer',
        'other' => 'Other'
    ],
    'profession_subtitle' => [
        'actor' => 'Voice + Performance',
        'actor_no_voice' => 'Performance Only',
        'voice_actor' => 'Voice Only',
        'builder' => 'Architecture & Design',
        'designer' => 'Graphics & Visual',
        'cutter' => 'Video Editing',
        'cameraman' => 'Recording & Filming',
        'developer' => 'Mods & Programming',
        'modeler' => '3D Models & Assets',
        'music_producer' => 'Audio & Music',
        'other' => 'Custom Role'
    ],
    'profession_description_long' => [
        'actor' => 'Bring characters to life with voice acting and in-game performance.',
        'actor_no_voice' => 'Focus on in-game acting and character performance without voice work.',
        'voice_actor' => 'Provide voice work for characters without in-game performance.',
        'builder' => 'Create stunning builds and architectural designs for our world.',
        'designer' => 'Design graphics, thumbnails, and visual content for our brand.',
        'cutter' => 'Edit and produce high-quality video content for our channels.',
        'cameraman' => 'Capture amazing footage and manage recording sessions.',
        'developer' => 'Develop mods and technical solutions for our Minecraft server.',
        'modeler' => 'Create 3D models, textures, and assets for our projects.',
        'music_producer' => 'Compose and produce music and sound effects for our content.',
        'other' => 'Bring your unique skills and talents to our creative team.'
    ],
    'other_profession' => 'Other Profession',
    'other_profession_placeholder' => 'Please specify your other profession',
    'other_profession_help' => 'Please describe in detail', // Not used in Blade

    // Step 2: Role-Specific Information
    'please_select_at_least_one_profession' => 'Please go back and select at least one profession.',
    'acting_voice_acting' => 'Acting & Voice Acting', // Section title
    'voice_type' => 'Voice Type',
    'voice_type_deep' => 'Deep',
    'voice_type_medium' => 'Medium',
    'voice_type_high' => 'High',
    'microphone' => 'Microphone',
    'microphone_placeholder' => 'e.g., Rode NT-USB, Blue Yeti',

    'builder_cameraman' => 'PC & Technical Information', // Section title
    'has_pc_question' => 'Do you have a PC?',
    'has_modrinth_question' => 'Do you use Modrinth?', // Changed from "Hast du Modrinth?"
    'has_minecraft_java_question' => 'Do you have Minecraft Java Edition?',

    'ram' => 'RAM (PC)',
    'ram_placeholder' => 'e.g., 16GB',
    'fps' => 'Average FPS in Minecraft',
    // FPS Options - ensuring valid key names
    'fps_less_than_10' => '<10 FPS',
    'fps_10' => '10-19 FPS', // Assuming "10-" meant 10 to something
    'fps_20' => '20-29 FPS',
    'fps_30' => '30-39 FPS',
    'fps_40' => '40-49 FPS',
    'fps_50' => '50-59 FPS',
    'fps_greater_than_60' => '60+ FPS',

    'gpu' => 'Graphics Card (GPU)',
    'gpu_placeholder' => 'e.g., NVIDIA GeForce RTX 3060, AMD Radeon RX 6700 XT',

    'designer' => 'Designer', // Section title for designer-specific questions
    'design_programs' => 'Design Programs',
    'design_programs_placeholder' => 'e.g., Photoshop, Illustrator, Figma',
    'design_style' => 'Preferred Design Style',
    'design_style_placeholder' => 'e.g., Minimalist, Cartoonish, Realistic',
    'favorite_design' => 'Favorite Design Example',
    'favorite_design_placeholder' => 'Link to a design you admire',

    'developer' => 'Developer', // Section title
    'programming_languages' => 'Programming Languages',
    'programming_languages_placeholder' => 'e.g., Java, JavaScript, Python',
    'preferred_ide' => 'Preferred IDE',
    'preferred_ide_placeholder' => 'e.g., VS Code, IntelliJ IDEA',

    'modeler' => '3D Modeler', // Section title
    'modeler_software_label' => '3D Modeling Software', // Label for the input field
    // 'daw_placeholder' is used for modeler software placeholder in blade, which is confusing.
    // Let's provide a specific one for modeler software if possible, or make daw_placeholder more generic.
    // For now, `daw_placeholder` will be used as per blade.

    'music_producer' => 'Music Producer', // Section title
    'daw' => 'DAW (Digital Audio Workstation)', // Label for the input field
    'daw_placeholder' => 'e.g., FL Studio, Ableton Live, Logic Pro', // Used for modeler and music producer

    'portfolio' => 'Portfolio / References',
    'portfolio_placeholder' => 'Link(s) to your work (e.g., GitHub, ArtStation, YouTube)',
    'desired_role' => 'Desired Role Description',
    'desired_role_placeholder' => 'Briefly describe your ideal role or contributions',

    // Step 3: About You
    'experience' => 'Experience', // Not directly in blade, but related to 'about_you' section
    'experience_placeholder' => 'Your experience in the selected roles', // Not directly in blade
    'motivation' => 'Motivation', // Not directly in blade
    'motivation_placeholder' => 'Why are you interested in joining our team?', // Not directly in blade
    'about_you_text' => 'About You', // General term, matches progress bar
    'about_you_placeholder' => 'Tell us more about yourself, your skills, and your interests (min. 50 characters).',
    'strengths_weaknesses' => 'Strengths & Weaknesses',
    'strengths_weaknesses_placeholder' => 'What are you good at? What areas are you working to improve? (min. 50 characters).',
    'final_words' => 'Final Words',
    'final_words_placeholder' => 'Anything else you\'d like to share?',
    'characters' => 'characters', // For character count

    // Step 4: Review & Submit
    'confirm_correct_information' => 'I confirm that all information provided is correct and I have read the GDPR notice.',
    'not_specified' => 'Not specified',
    'checkbox_questions' => 'Checkbox Questions', // For the review section if such a dynamic part exists
    'yes' => 'Yes',
    'no' => 'No',

    // Form Controls & Actions
    'next_step' => 'Next Step',
    'previous_step' => 'Previous Step',
    'submit_application' => 'Submit Application',
    'edit_application' => 'Edit Application',

    // Processing & Submission Status
    'processing' => 'Processing...',
    'application_submitted' => 'Application Submitted!',
    'application_updated' => 'Application Updated!',
    'thank_you' => 'Thank you for your application!',
    'we_will_contact' => 'We will review your application and contact you via Discord if you are shortlisted.',

    // Errors & Notices
    'required_fields_notice' => 'Fields marked with * are required.', // Not currently in blade, but good to have
    'application_not_editable' => 'This application cannot be edited at this time.',
    'application_not_found_or_no_access' => 'Application not found or you do not have access to it.',
    'filtering_error' => 'Error filtering: :error', // For admin panel, not wizard

    // General Table/Admin terms (might not be used in wizard)
    'created_at' => 'Created At',
    'updated_at' => 'Updated At',
    'status' => 'Status',
    'confirmation' => 'Confirmation', // General term
];
