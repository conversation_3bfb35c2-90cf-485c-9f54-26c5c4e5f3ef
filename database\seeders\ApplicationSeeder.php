<?php

namespace Database\Seeders;

use App\Models\Application;
use App\Models\User;
use Illuminate\Database\Seeder;

class ApplicationSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create an admin user for reviewing applications
        $admin = User::factory()->admin()->create([
            'username' => 'ReviewAdmin',
            'global_name' => 'Review Administrator',
        ]);

        // Create pending applications for each profession
        Application::factory()->actor()->count(5)->create();
        Application::factory()->builder()->count(5)->create();
        Application::factory()->designer()->count(5)->create();
        Application::factory()->developer()->count(5)->create();

        // Create approved applications with the admin as reviewer
        foreach (['actor', 'builder', 'designer', 'developer'] as $profession) {
            Application::factory()->$profession()->create([
                'status' => 'approved',
                'reviewer_id' => $admin->id,
                'reviewed_at' => now()->subDays(rand(1, 30)),
                'team_comment' => 'Approved application for ' . $profession,
            ]);
        }

        // Create rejected applications with the admin as reviewer
        foreach (['actor', 'builder', 'designer', 'developer'] as $profession) {
            Application::factory()->$profession()->create([
                'status' => 'rejected',
                'reviewer_id' => $admin->id,
                'reviewed_at' => now()->subDays(rand(1, 30)),
                'team_comment' => 'Rejected application for ' . $profession,
            ]);
        }

        // Create applications with specific attributes for testing
        
        // Young applicant (age < 16)
        Application::factory()->actor()->create([
            'age' => 11,
            'status' => 'pending',
        ]);

        // Applicant with low RAM
        Application::factory()->actor()->create([
            'ram' => 4,
            'status' => 'pending',
        ]);

        // Applicant with low FPS
        Application::factory()->actor()->create([
            'fps' => '20',
            'status' => 'pending',
        ]);

        // Applicant with short about_you
        Application::factory()->actor()->create([
            'about_you' => 'Short description',
            'status' => 'pending',
        ]);

        // Applicant with short strengths_weaknesses
        Application::factory()->actor()->create([
            'strengths_weaknesses' => 'Short strengths',
            'status' => 'pending',
        ]);

        // Excellent applicant
        Application::factory()->actor()->create([
            'age' => 25,
            'about_you' => str_repeat('Detailed about me. ', 30),
            'strengths_weaknesses' => str_repeat('My strengths and weaknesses. ', 20),
            'ram' => 32,
            'fps' => '144',
            'status' => 'pending',
        ]);
    }
}
