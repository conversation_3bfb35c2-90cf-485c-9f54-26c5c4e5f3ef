<div class="mt-4 mb-6 border border-base-300 rounded-lg p-4 bg-base-200" id="ai-response-generator" data-ticket-id="{{ $ticket->id }}">
    <div class="flex items-center justify-between mb-4">
        <div class="flex items-center gap-2">
            <x-heroicon-o-chip class="w-6 h-6 text-primary" />
            <h3 class="text-lg font-semibold">{{ __('tickets.ai_assistant') }}</h3>
        </div>
        <span class="text-xs text-base-content/70">{{ __('tickets.powered_by') }} {{ $languageModelName ?? 'AI Model' }}</span>
    </div>

    <div id="ai-response-container" class="hidden">
        <div class="mb-4">
            <textarea
                id="ai-generated-response"
                class="textarea textarea-bordered w-full h-32"
                readonly
            ></textarea>
        </div>

        <div class="flex justify-between items-center">
            <div class="text-sm text-base-content/70">
                {{ __('tickets.ai_response_disclaimer') }}
            </div>

            <div class="flex gap-2">
                <button id="discard-ai-response-btn" class="btn btn-ghost btn-sm">
                    {{ __('tickets.discard') }}
                </button>

                <form action="{{ route('tickets.ai.reply', $ticket) }}" method="POST" id="ai-response-form">
                    @csrf
                    <input type="hidden" name="message" id="ai-response-input">
                    <button type="submit" class="btn btn-success btn-sm">
                        {{ __('tickets.use_response') }}
                    </button>
                </form>
            </div>
        </div>
    </div>

    <div id="ai-response-loading" class="hidden py-8 flex justify-center">
        <div class="loading loading-spinner loading-lg"></div>
    </div>

    <div id="ai-response-error" class="hidden">
        <div class="alert alert-error">
            <svg xmlns="http://www.w3.org/2000/svg" class="stroke-current shrink-0 h-6 w-6" fill="none" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 14l2-2m0 0l2-2m-2 2l-2-2m2 2l2 2m7-2a9 9 0 11-18 0 9 9 0 0118 0z" /></svg>
            <span id="ai-error-message">{{ __('tickets.ai_error') }}</span>
        </div>
    </div>
</div>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        const generateBtn = document.getElementById('generate-ai-response-btn');
        const responseContainer = document.getElementById('ai-response-container');
        const loadingContainer = document.getElementById('ai-response-loading');
        const errorContainer = document.getElementById('ai-response-error');
        const errorMessage = document.getElementById('ai-error-message');
        const generatedResponse = document.getElementById('ai-generated-response');
        const discardBtn = document.getElementById('discard-ai-response-btn');
        const responseForm = document.getElementById('ai-response-form');
        const responseInput = document.getElementById('ai-response-input');
        const generator = document.getElementById('ai-response-generator');

        // Function to reset the UI
        function resetUI() {
            responseContainer.classList.add('hidden');
            loadingContainer.classList.add('hidden');
            errorContainer.classList.add('hidden');
            generatedResponse.value = '';
        }

        // Function to generate AI response
        function generateResponse() {
            // Check if user has consented to AI
            const hasConsented = {{ auth()->user()->hasConsentedToAi() ? 'true' : 'false' }};

            if (!hasConsented) {
                // Show consent modal
                window.showAiConsentModal(function() {
                    // This will be called after consent is given
                    generateResponse();
                });
                return;
            }

            // Reset UI
            resetUI();

            // Show loading
            loadingContainer.classList.remove('hidden');
            generateBtn.disabled = true;

            // Make API request
            fetch(generateBtn.dataset.generateUrl, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                }
            })
            .then(response => response.json())
            .then(data => {
                loadingContainer.classList.add('hidden');

                if (data.success) {
                    responseContainer.classList.remove('hidden');
                    generatedResponse.value = data.response;
                    responseInput.value = data.response;
                } else {
                    errorContainer.classList.remove('hidden');

                    if (data.requiresConsent) {
                        // Show consent modal
                        window.showAiConsentModal(function() {
                            // This will be called after consent is given
                            generateResponse();
                        });
                    } else {
                        errorMessage.textContent = data.message || '{{ __('tickets.ai_error') }}';
                    }
                }
            })
            .catch(error => {
                loadingContainer.classList.add('hidden');
                errorContainer.classList.remove('hidden');
                console.error('Error generating AI response:', error);
            })
            .finally(() => {
                generateBtn.disabled = false;
            });
        }

        // Generate button click
        generateBtn.addEventListener('click', generateResponse);

        // Discard button click
        discardBtn.addEventListener('click', resetUI);
    });
</script>
