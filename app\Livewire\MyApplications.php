<?php

namespace App\Livewire;

use Livewire\Component;
use App\Models\Application;
use Illuminate\Support\Facades\Auth;
use Livewire\WithPagination;
use Livewire\Attributes\Url;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Pagination\LengthAwarePaginator;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Session;

class MyApplications extends Component
{
    use WithPagination;

    // Filter und Sortieroptionen mit URL-Synchronisierung für Bookmarking
    #[Url]
    public $search = '';

    #[Url]
    public $status = 'all';

    #[Url]
    public $profession = 'all';

    #[Url]
    public $sortField = 'created_at';

    #[Url]
    public $sortDirection = 'desc';

    // Variable für verzögerte Suche
    public $searchTerm = '';

    // Für Detailansicht und Bearbeitung
    public $selectedApplication = null;
    public $viewMode = 'list'; // 'list', 'detail', 'edit'
    public $editableFields = []; // Speichert bearbeitete Felder

    // Für Statistiken
    public $totalApplications = 0;
    public $pendingApplications = 0;
    public $approvedApplications = 0;
    public $rejectedApplications = 0;

    // Livewire lifecycle hook - wird bei Initialisierung ausgeführt
    public function mount()
    {
        $this->searchTerm = $this->search;
        $this->loadStatistics();
    }

    /**
     * Updatet den Suchfilter mit Debounce für bessere Performance
     */
    public function updatedSearchTerm()
    {
        $this->search = $this->searchTerm;
        $this->resetPage();
    }

    /**
     * Wird ausgelöst, wenn sich die Filter ändern
     */
    public function updatedStatus()
    {
        $this->resetPage();
    }

    /**
     * Wird ausgelöst, wenn sich die Berufsfilter ändern
     */
    public function updatedProfession()
    {
        $this->resetPage();
    }

    /**
     * Lade Statistiken für den Benutzer
     */
    public function loadStatistics()
    {
        $userId = Auth::id();

        $this->totalApplications = Application::where('discord_id', $userId)->count();
        $this->pendingApplications = Application::where('discord_id', $userId)->where('status', 'pending')->count();
        $this->approvedApplications = Application::where('discord_id', $userId)->where('status', 'approved')->count();
        $this->rejectedApplications = Application::where('discord_id', $userId)->where('status', 'rejected')->count();
    }

    /**
     * Setze Sortierfeld
     */
    public function sortBy($field)
    {
        $sortableFields = [
            'created_at' => __('application.created_at'),
            'status' => __('application.status'),
            'updated_at' => __('application.updated_at')
        ];

        // Überprüfen ob das Feld sortierbar ist
        if (!array_key_exists($field, $sortableFields)) {
            return;
        }

        if ($this->sortField === $field) {
            $this->sortDirection = $this->sortDirection === 'asc' ? 'desc' : 'asc';
        } else {
            $this->sortField = $field;
            $this->sortDirection = 'asc';
        }

        // Seite zurücksetzen
        $this->resetPage();

        // Benachrichtigung
        session()->flash('message', "Liste sortiert nach {$sortableFields[$field]} ({$this->sortDirection})");
    }

    /**
     * Wähle eine Bewerbung für Detailansicht aus
     */
    public function viewApplication($id)
    {
        $this->selectedApplication = Application::where('id', $id)
            ->where('discord_id', Auth::id())
            ->first();

        if (!$this->selectedApplication) {
            session()->flash('error', __('application.application_not_found_or_no_access'));
            return;
        }

        $this->viewMode = 'detail';
    }

    /**
     * Zurück zur Listenansicht
     */
    public function backToList()
    {
        $this->viewMode = 'list';
        $this->selectedApplication = null;
        $this->resetPage();
    }

    /**
     * Bearbeitungsmodus für Bewerbung öffnen
     */
    public function editApplication($id)
    {
        $application = Application::where('id', $id)
            ->where('discord_id', Auth::id())
            ->where('editable', true)
            ->first();

        if (!$application) {
            session()->flash('error', __('application.application_not_editable'));
            return;
        }

        // Weiterleitung zur Bearbeitungsseite mit dem ApplicationWizard
        return redirect()->route('my.applications.edit', $application->id);
    }

    /**
     * Validiere bearbeitete Bewerbung
     *
     * @deprecated Wird nicht mehr verwendet, da die Bearbeitung jetzt über den ApplicationWizard erfolgt
     */
    protected function validateEditFields()
    {
        // Diese Methode wird nicht mehr verwendet, da die Bearbeitung jetzt über den ApplicationWizard erfolgt
        return;

        // Validiere berufs-spezifische Felder
        if (in_array('actor', $this->selectedApplication->professions) ||
            in_array('voice_actor', $this->selectedApplication->professions)) {
            $this->validate([
                'editableFields.voice_type' => 'required|string',
                'editableFields.microphone' => 'required|string',
            ], [
                'editableFields.voice_type.required' => 'Bitte fülle das Feld "Stimmtyp" aus.',
                'editableFields.microphone.required' => 'Bitte fülle das Feld "Mikrofon" aus.',
            ]);
        }

        if (in_array('actor_no_voice', $this->selectedApplication->professions)) {
            $this->validate([
                'editableFields.microphone' => 'required|string',
            ], [
                'editableFields.microphone.required' => 'Bitte fülle das Feld "Mikrofon" aus.',
            ]);
        }

        if (in_array('builder', $this->selectedApplication->professions) ||
            in_array('cameraman', $this->selectedApplication->professions)) {
            $this->validate([
                'editableFields.ram' => 'required|string',
                'editableFields.fps' => 'required|string',
                'editableFields.gpu' => 'required|string',
            ], [
                'editableFields.ram.required' => 'Bitte fülle das Feld "RAM" aus.',
                'editableFields.fps.required' => 'Bitte fülle das Feld "FPS" aus.',
                'editableFields.gpu.required' => 'Bitte fülle das Feld "GPU" aus.',
            ]);
        }

        if (in_array('designer', $this->selectedApplication->professions)) {
            $this->validate([
                'editableFields.program' => 'required|string',
                'editableFields.design_style' => 'required|string',
                'editableFields.favorite_design' => 'required|string',
            ], [
                'editableFields.program.required' => 'Bitte fülle das Feld "Programm" aus.',
                'editableFields.design_style.required' => 'Bitte fülle das Feld "Design-Stil" aus.',
                'editableFields.favorite_design.required' => 'Bitte fülle das Feld "Lieblings-Design" aus.',
            ]);
        }

        if (in_array('developer', $this->selectedApplication->professions)) {
            $this->validate([
                'editableFields.languages' => 'required|string',
                'editableFields.ide' => 'required|string',
            ], [
                'editableFields.languages.required' => 'Bitte fülle das Feld "Programmiersprachen" aus.',
                'editableFields.ide.required' => 'Bitte fülle das Feld "IDE" aus.',
            ]);
        }

        if (in_array('music_producer', $this->selectedApplication->professions)) {
            $this->validate([
                'editableFields.daw' => 'required|string',
            ], [
                'editableFields.daw.required' => 'Bitte fülle das Feld "DAW" aus.',
            ]);
        }
    }

    /**
     * Bewerbung aktualisieren
     *
     * @deprecated Wird nicht mehr verwendet, da die Bearbeitung jetzt über den ApplicationWizard erfolgt
     */
    public function updateApplication()
    {
        // Diese Methode wird nicht mehr verwendet, da die Bearbeitung jetzt über den ApplicationWizard erfolgt
        // Weiterleitung zur Bearbeitungsseite
        if ($this->selectedApplication) {
            return redirect()->route('my.applications.edit', $this->selectedApplication->id);
        }

        return redirect()->route('my.applications');
    }

    /**
     * Filter zurücksetzen
     */
    public function resetFilters()
    {
        $this->search = '';
        $this->searchTerm = '';
        $this->status = 'all';
        $this->profession = 'all';
        $this->resetPage();

        session()->flash('message', 'Filter wurden zurückgesetzt.');
        session()->flash('animation', 'fade-in');
    }

    /**
     * Liefert CSS-Klassen für visuelle Darstellung des Status
     *
     * @param string $status Status der Bewerbung
     * @return array Array mit CSS-Klassen für verschiedene Elemente
     */
    public function getStatusVisualClasses($status)
    {
        switch ($status) {
            case 'approved':
                return [
                    'badge' => 'badge-success',
                    'icon' => 'check-circle',
                    'bg' => 'bg-success/10',
                    'border' => 'border-success/30',
                    'text' => 'text-success'
                ];
            case 'rejected':
                return [
                    'badge' => 'badge-error',
                    'icon' => 'x-circle',
                    'bg' => 'bg-error/10',
                    'border' => 'border-error/30',
                    'text' => 'text-error'
                ];
            default: // pending
                return [
                    'badge' => 'badge-warning',
                    'icon' => 'clock',
                    'bg' => 'bg-warning/10',
                    'border' => 'border-warning/30',
                    'text' => 'text-warning'
                ];
        }
    }

    /**
     * Übersetzt den Status in eine lesbare Form
     */
    public function getReadableStatus($status)
    {
        $statusLabels = [
            'pending' => 'Ausstehend',
            'approved' => 'Angenommen',
            'rejected' => 'Abgelehnt'
        ];

        return $statusLabels[$status] ?? 'Unbekannt';
    }

    /**
     * Übersetzt Berufsbezeichnungen ins Deutsche
     */
    public function getReadableProfession($profession)
    {
        $professionOptions = [
            'actor' => 'Schauspieler',
            'actor_no_voice' => 'Schauspieler (No voice)',
            'voice_actor' => 'Synchronsprecher',
            'builder' => 'Builder',
            'designer' => 'Designer',
            'cutter' => 'Cutter',
            'cameraman' => 'Kameramann',
            'developer' => 'Developer',
            'modeler' => '3D-Modellierer',
            'music_producer' => 'Musikproduzent',
            'other' => 'Andere'
        ];

        return $professionOptions[$profession] ?? $profession;
    }

    /**
     * Erstellt ein leeres Pagination-Objekt
     *
     * @return LengthAwarePaginator
     */
    protected function createEmptyPaginator(): LengthAwarePaginator
    {
        return new LengthAwarePaginator(
            [], // leere Ergebnisse
            0,  // Gesamtzahl 0
            10, // Items pro Seite
            1,  // aktuelle Seite
            ['path' => request()->url()] // URL für Pagination-Links
        );
    }

    /**
     * Ermittelt die verfügbaren Berufe für den Filter
     */
    protected function getAvailableProfessions()
    {
        try {
            // Bekannte Berufstypen
            $professionOptions = [
                'actor' => 'Schauspieler',
                'actor_no_voice' => 'Schauspieler (No voice)',
                'voice_actor' => 'Synchronsprecher',
                'builder' => 'Builder',
                'designer' => 'Designer',
                'cutter' => 'Cutter',
                'cameraman' => 'Kameramann',
                'developer' => 'Developer',
                'modeler' => '3D-Modellierer',
                'music_producer' => 'Musikproduzent',
                'other' => 'Andere'
            ];

            return $professionOptions;
        } catch (\Exception $e) {
            return [];
        }
    }

    /**
     * Hauptrender-Methode
     */
    public function render()
    {
        try {
            $userId = Auth::id();
            $query = Application::where('discord_id', $userId);

            // Suche - universell kompatibel mit allen Datenbanken
            if (!empty($this->search)) {
                $searchTerm = '%' . $this->search . '%';
                $query->where(function (Builder $q) use ($searchTerm) {
                    // Standardfelder durchsuchen - funktioniert mit allen DB-Typen
                    $q->where('name', 'like', $searchTerm)
                      ->orWhere('about_you', 'like', $searchTerm)
                      ->orWhere('strengths_weaknesses', 'like', $searchTerm)
                      ->orWhere('final_words', 'like', $searchTerm);

                    // JSON-Suche durch cast zu String - universell kompatibel
                    $search = $this->search;
                    // Verwende eine Closure um den Scope für die Variable zu behalten
                    $q->orWhere(function($query) use ($search) {
                        $query->where(DB::raw("CAST(professions AS TEXT)"), 'like', '%' . $search . '%');
                    });
                });
            }

            // Status-Filter
            if ($this->status !== 'all') {
                $query->where('status', $this->status);
            }

            // Berufs-Filter - universeller Ansatz für JSON in allen DBs
            if ($this->profession !== 'all') {
                $profession = $this->profession;
                $query->where(function($q) use ($profession) {
                    // Cast zu Text ist der universellste Weg
                    $q->where(DB::raw("CAST(professions AS TEXT)"), 'like', '%' . $profession . '%');
                });
            }

            // Sortierung
            if (in_array($this->sortField, ['created_at', 'updated_at', 'status'])) {
                // Für Standard-Felder kann orderBy verwendet werden
                $query->orderBy($this->sortField, $this->sortDirection);
            } else {
                // Fallback für unbekannte Felder
                $query->orderBy('created_at', $this->sortDirection);
            }

            // Pagination mit Exception-Handling
            try {
                $applications = $query->paginate(10);
            } catch (\Exception $e) {
                // Bei Fehlern leeres Pagination-Objekt zurückgeben
                $applications = $this->createEmptyPaginator();
                session()->flash('error', __('application.filtering_error', ['error' => $e->getMessage()]));
            }

            // Verfügbare Berufe für den Filter
            $availableProfessions = $this->getAvailableProfessions();

            // Ansicht rendern
            return view('livewire.my-applications', [
                'applications' => $applications,
                'availableProfessions' => $availableProfessions,
                'statusClasses' => [
                    'approved' => $this->getStatusVisualClasses('approved'),
                    'rejected' => $this->getStatusVisualClasses('rejected'),
                    'pending' => $this->getStatusVisualClasses('pending'),
                ]
            ]);

        } catch (\Exception $e) {
            // Master-Exception-Handler
            session()->flash('error', 'Fehler beim Laden der Bewerbungen: ' . $e->getMessage());

            // Leeres Pagination-Objekt erstellen
            $applications = $this->createEmptyPaginator();

            return view('livewire.my-applications', [
                'applications' => $applications,
                'availableProfessions' => $this->getAvailableProfessions(),
                'statusClasses' => [
                    'approved' => $this->getStatusVisualClasses('approved'),
                    'rejected' => $this->getStatusVisualClasses('rejected'),
                    'pending' => $this->getStatusVisualClasses('pending'),
                ]
            ]);
        }
    }
}
