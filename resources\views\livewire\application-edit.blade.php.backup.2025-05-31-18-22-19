<div>
    <!-- Bearbeitungsansicht einer Bewerbung -->
    <div class="bg-white dark:bg-base-100 rounded-xl shadow-lg p-6">
        <div class="flex justify-between items-center mb-6">
            <button wire:click="backToList" class="btn btn-ghost gap-2">
                <x-heroicon-o-arrow-left class="w-5 h-5"/>
                {{ __('admin.cancel') }}
            </button>

            <h2 class="text-xl font-bold">{{ __('admin.edit_application') }}</h2>
        </div>

        <div class="grid grid-cols-1 gap-6">
            <!-- Bewerberstatus ändern -->
            <div class="card bg-base-200 shadow-md">
                <div class="card-body">
                    <h3 class="card-title flex items-center gap-2">
                        <x-heroicon-o-flag class="w-5 h-5 text-primary"/>
                        {{ __('admin.update_status') }}
                    </h3>

                    <div class="mt-4">
                        <div class="flex flex-wrap gap-4 justify-center">
                            <label class="label cursor-pointer flex flex-col items-center bg-base-100 p-4 rounded-lg border-2 border-base-300 hover:bg-base-300/30 transition-all duration-200
                                @if($applicationStatus === 'pending') border-warning bg-warning/10 @endif">
                                <input type="radio" name="status" value="pending" class="radio radio-warning"
                                       wire:model="applicationStatus"/>
                                <x-heroicon-o-clock class="w-8 h-8 mt-2 mb-2 text-warning"/>
                                <span class="label-text font-medium">{{ __('admin.statistics.pending') }}</span>
                            </label>

                            <label class="label cursor-pointer flex flex-col items-center bg-base-100 p-4 rounded-lg border-2 border-base-300 hover:bg-base-300/30 transition-all duration-200
                                @if($applicationStatus === 'approved') border-success bg-success/10 @endif">
                                <input type="radio" name="status" value="approved" class="radio radio-success"
                                       wire:model="applicationStatus"/>
                                <x-heroicon-o-check-circle class="w-8 h-8 mt-2 mb-2 text-success"/>
                                <span
                                    class="label-text font-medium">{{ __('admin.statistics.approved') }}</span>
                            </label>

                            <label class="label cursor-pointer flex flex-col items-center bg-base-100 p-4 rounded-lg border-2 border-base-300 hover:bg-base-300/30 transition-all duration-200
                                @if($applicationStatus === 'rejected') border-error bg-error/10 @endif">
                                <input type="radio" name="status" value="rejected" class="radio radio-error"
                                       wire:model="applicationStatus"/>
                                <x-heroicon-o-x-circle class="w-8 h-8 mt-2 mb-2 text-error"/>
                                <span
                                    class="label-text font-medium">{{ __('admin.statistics.rejected') }}</span>
                            </label>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Kommentar zum Bewerber -->
            <div class="card bg-base-200 shadow-md">
                <div class="card-body">
                    <h3 class="card-title flex items-center gap-2">
                        <x-heroicon-o-chat-bubble-left class="w-5 h-5 text-primary"/>
                        {{ __('admin.notes_about_applicant') }}
                    </h3>

                    <div class="mt-4">
                        <p class="text-sm text-base-content/70 mb-2">
                            {{ __('admin.notes_description') }}
                        </p>

                        <textarea wire:model="comment" rows="5" class="textarea textarea-bordered w-full"
                                  placeholder="{{ __('admin.notes_placeholder') }}"></textarea>
                    </div>
                </div>
            </div>

            <!-- Speicher-Button -->
            <div class="flex justify-end mt-4">
                <button wire:click="updateApplication" class="btn btn-primary gap-2">
                    <x-heroicon-o-check class="w-5 h-5"/>
                    {{ __('admin.save_changes') }}
                </button>
            </div>
        </div>
    </div>
</div>
