<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateApplicationsTable extends Migration
{
    public function up()
    {
        Schema::create('applications', function (Blueprint $table) {
            $table->id();
            $table->integer('discord_id');
            $table->string('name')->nullable();
            $table->integer('age')->nullable();
            $table->string('gender')->nullable();
            $table->string('pronouns')->nullable();
            $table->json('professions')->nullable();
            $table->json('checkboxQuestions')->nullable();
            $table->text('about_you')->nullable();
            $table->text('strengths_weaknesses')->nullable();
            $table->text('final_words')->nullable();
            $table->string('voice_type')->nullable();
            $table->integer('ram')->nullable();
            $table->string('fps')->nullable();
            $table->string('desired_role')->nullable();
            $table->text('portfolio')->nullable();
            $table->string('microphone')->nullable();
            $table->string('daw')->nullable();
            $table->string('program')->nullable();
            $table->string('design_style')->nullable();
            $table->string('favorite_design')->nullable();
            $table->string('gpu')->nullable();
            $table->string('languages')->nullable();
            $table->string('ide')->nullable();
            $table->integer('staus')->default(0);
            $table->timestamps();
        });
    }

    public function down()
    {
        Schema::dropIfExists('applications');
    }
}
