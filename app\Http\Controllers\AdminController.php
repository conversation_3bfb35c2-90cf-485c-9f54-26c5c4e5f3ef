<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Gate;
use Illuminate\Support\Facades\Route;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Artisan;
use App\Models\Application;
use App\Models\User;
use App\Enums\Role;

class AdminController extends Controller
{
    /**
     * Zeigt die Bewerbungsverwaltung für Teammitglieder
     */
    public function applications()
    {
        // Hier könnte man weitere Berechtigungsprüfungen einbauen
        // z.B. ob der angemeldete Benutzer Teammitglied ist

        return view('admin.applications');
    }

    /**
     * Dashboard für Administratoren
     */
    public function dashboard()
    {
        return view('admin.dashboard');
    }

    /**
     * Zeigt die Benutzerverwaltung für Teammitglieder
     */
    public function users()
    {
        // Prü<PERSON>, ob der Benutzer die Berechtigung hat
        Gate::authorize('MINEWACHE_TEAM');

        return view('admin.users');
    }

    /**
     * Synchronisiert die Rollen eines Benutzers mit Discord (Admin UI Version)
     *
     * @param User $user Der zu synchronisierende Benutzer
     * @return \Illuminate\Http\RedirectResponse
     */
    public function syncRoles(User $user)
    {
        // Prüfen, ob der Benutzer die Berechtigung hat
        Gate::authorize('MINEWACHE_TEAM');

        // Cache-Key für den Synchronisierungsstatus
        $cacheKey = "discord_role_sync_{$user->id}";

        // Prüfe, ob bereits eine kürzliche Synchronisierung stattgefunden hat (Rate-Limiting)
        if (Cache::has($cacheKey)) {
            return redirect()->route('admin.users')
                ->with('warning', "Rollen für {$user->username} wurden kürzlich synchronisiert. Bitte warten Sie einen Moment, bevor Sie es erneut versuchen.");
        }

        try {
            // Führe die eigentliche Synchronisierung durch
            $result = $this->syncUserRoles($user);

            if ($result['success']) {
                // Erfolgreiche Synchronisierung
                return redirect()->route('admin.users')
                    ->with('success', "Berechtigungen für {$user->username} erfolgreich synchronisiert");
            } else {
                // Fehler bei der Synchronisierung
                return redirect()->route('admin.users')
                    ->with('error', "Fehler bei der Synchronisierung der Berechtigungen für {$user->username}: {$result['error']}");
            }
        } catch (\Exception $e) {
            // Detaillierte Fehlerprotokollierung
            Log::error("Discord Rollen-Synchronisierung fehlgeschlagen", [
                'user_id' => $user->id,
                'username' => $user->username,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            // Fehler bei der Synchronisierung
            return redirect()->route('admin.users')
                ->with('error', "Fehler bei der Synchronisierung der Berechtigungen für {$user->username}: {$e->getMessage()}");
        }
    }

    /**
     * Synchronisiert die Rollen eines Benutzers mit Discord (wiederverwendbare Version)
     * Diese Methode kann von anderen Teilen der Anwendung aufgerufen werden, ohne eine Umleitung zu verursachen
     *
     * @param User $user Der zu synchronisierende Benutzer
     * @param bool $bypassRateLimiting Optional: Rate-Limiting umgehen (Standard: false)
     * @param bool $bypassPermissionCheck Optional: Berechtigungsprüfung umgehen (Standard: false)
     * @return array Ergebnis der Synchronisierung ['success' => bool, 'message' => string, 'error' => string|null]
     */
    public function syncUserRoles(User $user, bool $bypassRateLimiting = false, bool $bypassPermissionCheck = false)
    {
        // Prüfen, ob der Benutzer die Berechtigung hat, es sei denn, die Prüfung wird umgangen
        if (!$bypassPermissionCheck) {
            try {
                Gate::authorize('MINEWACHE_TEAM');
            } catch (\Exception $e) {
                Log::warning("Unauthorized role synchronization attempt", [
                    'user_id' => auth()->id(),
                    'target_user_id' => $user->id
                ]);
                return [
                    'success' => false,
                    'message' => 'Keine Berechtigung zur Rollensynchronisierung',
                    'error' => 'Unauthorized'
                ];
            }
        }
        try {
            // Validierung der Benutzer-ID
            if (empty($user->id)) {
                Log::error("Ungültige Benutzer-ID bei Rollen-Synchronisierung");
                return [
                    'success' => false,
                    'message' => 'Ungültige Discord-Benutzer-ID',
                    'error' => 'Invalid user ID'
                ];
            }

            // Cache-Key für den Synchronisierungsstatus
            $cacheKey = "discord_role_sync_{$user->id}";

            // Prüfe, ob bereits eine kürzliche Synchronisierung stattgefunden hat (Rate-Limiting)
            if (!$bypassRateLimiting && Cache::has($cacheKey)) {
                return [
                    'success' => false,
                    'message' => "Rollen für {$user->username} wurden kürzlich synchronisiert",
                    'error' => 'Rate limited'
                ];
            }

            // Rolle vor der Synchronisierung für Vergleich speichern
            $previousRoles = $user->permissions;

            // Versuche zuerst, den lokalen Discord-Bot zu verwenden
            $syncedWithBot = $this->syncRolesWithLocalBot($user);

            // Wenn die Synchronisierung mit dem Bot fehlgeschlagen ist, Fallback zur alten Methode
            if (!$syncedWithBot) {
                Log::notice("Fallback zur klassischen Discord API-Methode für Benutzer {$user->username}", [
                    'user_id' => $user->id,
                ]);

                // Verzögerung einbauen, um Rate-Limiting zu vermeiden
                sleep(1);

                // Fallback zur alten Methode
                $permissionController = app(\App\Http\Controllers\PermissionController::class);
                $result = $permissionController->syncRoles($user);

                // Prüfen, ob die Synchronisierung mit der alten Methode erfolgreich war
                if (!$result) {
                    throw new \Exception("Die Synchronisierung mit der Discord API ist fehlgeschlagen");
                }
            }

            // Benutzer aus der Datenbank neu laden, um aktuelle Daten zu haben
            $user->refresh();

            // Rollen-Änderungen protokollieren
            $this->logRoleChanges($user, $previousRoles);

            // Cache setzen, um zu häufige Anfragen zu verhindern (30 Sekunden)
            Cache::put($cacheKey, true, now()->addSeconds(30));

            return [
                'success' => true,
                'message' => "Berechtigungen für {$user->username} erfolgreich synchronisiert",
                'error' => null
            ];
        } catch (\Exception $e) {
            // Detaillierte Fehlerprotokollierung
            Log::error("Discord Rollen-Synchronisierung fehlgeschlagen", [
                'user_id' => $user->id,
                'username' => $user->username,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return [
                'success' => false,
                'message' => "Fehler bei der Synchronisierung der Berechtigungen",
                'error' => $e->getMessage()
            ];
        }
    }

    /**
     * Zeigt die API-Dokumentation an
     */
    public function apiDocs()
    {
        // Prüfen, ob der Benutzer die Berechtigung hat
        Gate::authorize('MINEWACHE_TEAM');

        return view('admin.api-docs');
    }

    /**
     * Versucht, Rollen mit dem lokalen Discord-Bot zu synchronisieren
     *
     * @param User $user Der zu synchronisierende Benutzer
     * @return bool True bei erfolgreicher Synchronisierung, andernfalls False
     */
    private function syncRolesWithLocalBot(User $user): bool
    {
        try {
            // Bot-Konfiguration aus der Umgebung laden
            $botApiUrl = config('services.discord.bot_url', env('DISCORD_BOT_API_URL', 'http://127.0.0.1:3001'));
            // Verwende den korrekten Endpunkt für die Benutzersynchronisierung
            $endpoint = "{$botApiUrl}/api/users/{$user->id}/sync";
            $apiKey = config('services.discord.api_key');

            if (empty($apiKey)) {
                Log::warning("Discord Bot API-Key nicht konfiguriert");
                return false;
            }

            Log::debug("Versuche Rollen mit lokalem Discord-Bot zu synchronisieren", [
                'user_id' => $user->id,
                'endpoint' => $endpoint
            ]);

            // Anfrage an den lokalen Discord-Bot senden mit Retry-Logik
            $maxRetries = 2;
            $attempt = 0;

            do {
                $attempt++;

                if ($attempt > 1) {
                    // Progressiv längere Wartezeit zwischen Versuchen
                    $waitTime = pow(2, $attempt - 1);
                    sleep($waitTime);
                }

                try {
                    $response = Http::withHeaders([
                        'Authorization' => "Bearer {$apiKey}",
                        'Accept' => 'application/json',
                        'X-Request-Source' => 'AdminController'
                    ])
                    ->timeout(10)  // Längeres Timeout für zuverlässigere Antwort
                    ->post($endpoint, [
                        'force_sync' => true,
                        'permissions' => $user->permissions
                    ]);

                    // Erfolgreiche Antwort
                    if ($response->successful()) {
                        $data = $response->json();

                        // Prüfen, ob die Antwort die erwarteten Felder enthält
                        if (isset($data['success']) && $data['success']) {
                            Log::info("Discord-Rollen erfolgreich synchronisiert mit lokalem Bot", [
                                'user_id' => $user->id,
                                'username' => $user->username,
                                'roles_added' => $data['roles_added'] ?? [],
                                'roles_removed' => $data['roles_removed'] ?? []
                            ]);

                            // Aktualisiere die Berechtigungen des Benutzers, falls der Bot neue Werte zurückgibt
                            if (isset($data['new_permissions']) && is_numeric($data['new_permissions'])) {
                                $user->permissions = (int) $data['new_permissions'];
                                $user->save();
                            }

                            return true;
                        }
                    }

                    // Bei Fehler Details protokollieren und weiteren Versuch starten
                    Log::warning("Fehler bei der Bot-Antwort, Versuch {$attempt} von {$maxRetries}", [
                        'status' => $response->status(),
                        'body' => $response->body(),
                        'user_id' => $user->id
                    ]);

                } catch (\Exception $botError) {
                    Log::warning("Ausnahme bei Bot-Kommunikation, Versuch {$attempt} von {$maxRetries}", [
                        'error' => $botError->getMessage(),
                        'user_id' => $user->id
                    ]);
                }
            } while ($attempt < $maxRetries);

            // Nach allen Versuchen immer noch kein Erfolg
            return false;

        } catch (\Exception $e) {
            Log::error("Kritischer Fehler bei der Discord-Bot-Kommunikation", [
                'user_id' => $user->id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return false;
        }
    }

    /**
     * Protokolliert Änderungen an den Benutzerrollen
     *
     * @param User $user Der Benutzer, dessen Rollen geändert wurden
     * @param int $previousRoles Die vorherigen Berechtigungen des Benutzers
     * @return void
     */
    private function logRoleChanges(User $user, int $previousRoles): void
    {
        $currentRoles = $user->permissions;

        if ($previousRoles === $currentRoles) {
            Log::info("Keine Rollenänderungen für Benutzer {$user->username}", [
                'user_id' => $user->id
            ]);
            return;
        }

        // Bestimme hinzugefügte und entfernte Rollen
        $addedRoles = [];
        $removedRoles = [];

        // Überprüfe jede mögliche Rolle
        foreach (Role::cases() as $role) {
            $hadRole = ($previousRoles & $role->value) !== 0;
            $hasRole = ($currentRoles & $role->value) !== 0;

            if (!$hadRole && $hasRole) {
                $addedRoles[] = $role->name;
            } elseif ($hadRole && !$hasRole) {
                $removedRoles[] = $role->name;
            }
        }

        Log::info("Rollenänderungen für Benutzer {$user->username}", [
            'user_id' => $user->id,
            'added_roles' => $addedRoles,
            'removed_roles' => $removedRoles,
            'previous_permissions' => $previousRoles,
            'current_permissions' => $currentRoles
        ]);
    }

    /**
     * Verwaltet und überwacht Discord Bot-Status
     */
    public function discordBotStatus()
    {
        // Prüfen, ob der Benutzer die Berechtigung hat
        Gate::authorize('MINEWACHE_TEAM');

        try {
            // Check if the PHP Discord bot process is running
            $isRunning = false;

            if (PHP_OS_FAMILY === 'Windows') {
                // Windows command to check for the process
                $command = 'tasklist /FI "WINDOWTITLE eq php artisan minewache:run-discord-bot*" /NH';
                exec($command, $output);
                $isRunning = count($output) > 0 && !str_contains(implode(' ', $output), 'No tasks');
            } else {
                // Linux/Mac command to check for the process
                $command = "ps aux | grep '[p]hp artisan minewache:run-discord-bot'";
                exec($command, $output);
                $isRunning = count($output) > 0;
            }

            $status = [
                'online' => $isRunning,
                'last_check' => now(),
                'error' => null,
                'uptime' => null,
                'version' => 'PHP Discord Bot'
            ];

            // Log the status check
            Log::info('Discord bot status checked from admin panel', [
                'online' => $status['online']
            ]);

            return view('admin.discord-bot-status', ['status' => $status]);
        } catch (\Exception $e) {
            Log::error("Fehler beim Abrufen des Discord Bot-Status", [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return view('admin.discord-bot-status', [
                'status' => [
                    'online' => false,
                    'error' => "Fehler beim Abrufen des Bot-Status: {$e->getMessage()}",
                    'last_check' => now()
                ]
            ]);
        }
    }

    /**
     * Start the Discord bot
     *
     * @param Request $request
     * @return \Illuminate\Http\RedirectResponse
     */
    public function startDiscordBot(Request $request)
    {
        // Prüfen, ob der Benutzer die Berechtigung hat
        Gate::authorize('MINEWACHE_TEAM');

        try {
            // Log the start request
            Log::warning('Discord bot start requested by admin', [
                'user_id' => Auth::id(),
                'username' => Auth::user()->username,
                'ip' => $request->ip()
            ]);

            // Check if the bot is already running
            if (PHP_OS_FAMILY === 'Windows') {
                $checkCommand = 'tasklist /FI "WINDOWTITLE eq php artisan minewache:run-discord-bot*" /NH';
                exec($checkCommand, $checkOutput);
                $isRunning = count($checkOutput) > 0 && !str_contains(implode(' ', $checkOutput), 'No tasks');
            } else {
                $checkCommand = "ps aux | grep '[p]hp artisan minewache:run-discord-bot'";
                exec($checkCommand, $checkOutput);
                $isRunning = count($checkOutput) > 0;
            }

            if ($isRunning) {
                Log::info('Discord bot is already running');
                return redirect()->route('admin.discord-bot-status')
                    ->with('info', 'Discord-Bot läuft bereits.');
            }

            // Start the bot using the appropriate method for the OS
            if (PHP_OS_FAMILY === 'Windows') {
                // On Windows, use start-discord.bat
                $command = 'start-discord.bat';
                exec($command, $output, $returnCode);
            } else {
                // On Linux/Mac, use Artisan command directly
                $command = 'nohup php artisan minewache:run-discord-bot > /dev/null 2>&1 &';
                exec($command, $output, $returnCode);
            }

            $success = $returnCode === 0;

            // Record the start attempt
            Cache::put('discord_bot_last_start_attempt', now(), now()->addHours(1));

            // Wait a moment and check if the bot is now running
            sleep(3);

            if (PHP_OS_FAMILY === 'Windows') {
                $checkCommand = 'tasklist /FI "WINDOWTITLE eq php artisan minewache:run-discord-bot*" /NH';
                exec($checkCommand, $checkOutput);
                $isRunning = count($checkOutput) > 0 && !str_contains(implode(' ', $checkOutput), 'No tasks');
            } else {
                $checkCommand = "ps aux | grep '[p]hp artisan minewache:run-discord-bot'";
                exec($checkCommand, $checkOutput);
                $isRunning = count($checkOutput) > 0;
            }

            if ($isRunning) {
                return redirect()->route('admin.discord-bot-status')
                    ->with('success', 'Discord-Bot wurde erfolgreich gestartet.');
            } else {
                return redirect()->route('admin.discord-bot-status')
                    ->with('error', 'Discord-Bot konnte nicht gestartet werden. Bitte überprüfen Sie die Logs.');
            }
        } catch (\Exception $e) {
            Log::error('Error starting Discord bot', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return redirect()->route('admin.discord-bot-status')
                ->with('error', 'Fehler beim Starten des Discord-Bots: ' . $e->getMessage());
        }
    }

    /**
     * Stop the Discord bot
     *
     * @param Request $request
     * @return \Illuminate\Http\RedirectResponse
     */
    public function stopDiscordBot(Request $request)
    {
        // Prüfen, ob der Benutzer die Berechtigung hat
        Gate::authorize('MINEWACHE_TEAM');

        try {
            // Log the stop request
            Log::warning('Discord bot stop requested by admin', [
                'user_id' => Auth::id(),
                'username' => Auth::user()->username,
                'ip' => $request->ip()
            ]);

            // Check if the bot is running
            if (PHP_OS_FAMILY === 'Windows') {
                $checkCommand = 'tasklist /FI "WINDOWTITLE eq php artisan minewache:run-discord-bot*" /NH';
                exec($checkCommand, $checkOutput);
                $isRunning = count($checkOutput) > 0 && !str_contains(implode(' ', $checkOutput), 'No tasks');
            } else {
                $checkCommand = "ps aux | grep '[p]hp artisan minewache:run-discord-bot'";
                exec($checkCommand, $checkOutput);
                $isRunning = count($checkOutput) > 0;
            }

            if (!$isRunning) {
                Log::info('Discord bot is already stopped');
                return redirect()->route('admin.discord-bot-status')
                    ->with('info', 'Discord-Bot ist bereits gestoppt.');
            }

            // Stop the bot using the appropriate method for the OS
            if (PHP_OS_FAMILY === 'Windows') {
                // On Windows, use stop-discord.bat
                $command = 'stop-discord.bat';
                exec($command, $output, $returnCode);
            } else {
                // On Linux/Mac, kill the process
                $command = "pkill -f 'php artisan minewache:run-discord-bot'";
                exec($command, $output, $returnCode);
            }

            $success = $returnCode === 0;

            // Record the stop attempt
            Cache::put('discord_bot_last_stop_attempt', now(), now()->addHours(1));

            // Wait a moment and check if the bot is now stopped
            sleep(2);

            if (PHP_OS_FAMILY === 'Windows') {
                $checkCommand = 'tasklist /FI "WINDOWTITLE eq php artisan minewache:run-discord-bot*" /NH';
                exec($checkCommand, $checkOutput);
                $isRunning = count($checkOutput) > 0 && !str_contains(implode(' ', $checkOutput), 'No tasks');
            } else {
                $checkCommand = "ps aux | grep '[p]hp artisan minewache:run-discord-bot'";
                exec($checkCommand, $checkOutput);
                $isRunning = count($checkOutput) > 0;
            }

            if (!$isRunning) {
                return redirect()->route('admin.discord-bot-status')
                    ->with('success', 'Discord-Bot wurde erfolgreich gestoppt.');
            } else {
                return redirect()->route('admin.discord-bot-status')
                    ->with('error', 'Discord-Bot konnte nicht gestoppt werden. Bitte überprüfen Sie die Logs.');
            }
        } catch (\Exception $e) {
            Log::error('Error stopping Discord bot', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return redirect()->route('admin.discord-bot-status')
                ->with('error', 'Fehler beim Stoppen des Discord-Bots: ' . $e->getMessage());
        }
    }

    /**
     * Restart the Discord bot
     *
     * @param Request $request
     * @return \Illuminate\Http\RedirectResponse
     */
    public function restartDiscordBot(Request $request)
    {
        // Prüfen, ob der Benutzer die Berechtigung hat
        Gate::authorize('MINEWACHE_TEAM');

        try {
            // Log the restart request
            Log::warning('Discord bot restart requested by admin', [
                'user_id' => Auth::id(),
                'username' => Auth::user()->username,
                'ip' => $request->ip()
            ]);

            // First stop the bot
            if (PHP_OS_FAMILY === 'Windows') {
                // On Windows, use stop-discord.bat
                $stopCommand = 'stop-discord.bat';
                exec($stopCommand, $stopOutput, $stopReturnCode);
            } else {
                // On Linux/Mac, kill the process
                $stopCommand = "pkill -f 'php artisan minewache:run-discord-bot'";
                exec($stopCommand, $stopOutput, $stopReturnCode);
            }

            // Wait a moment for the bot to stop
            sleep(2);

            // Then start the bot
            if (PHP_OS_FAMILY === 'Windows') {
                // On Windows, use start-discord.bat
                $startCommand = 'start-discord.bat';
                exec($startCommand, $startOutput, $startReturnCode);
            } else {
                // On Linux/Mac, use Artisan command directly
                $startCommand = 'nohup php artisan minewache:run-discord-bot > /dev/null 2>&1 &';
                exec($startCommand, $startOutput, $startReturnCode);
            }

            $success = $startReturnCode === 0;

            // Record the restart attempt
            Cache::put('discord_bot_last_restart_attempt', now(), now()->addHours(1));

            // Wait a moment and check if the bot is now running
            sleep(3);

            if (PHP_OS_FAMILY === 'Windows') {
                $checkCommand = 'tasklist /FI "WINDOWTITLE eq php artisan minewache:run-discord-bot*" /NH';
                exec($checkCommand, $checkOutput);
                $isRunning = count($checkOutput) > 0 && !str_contains(implode(' ', $checkOutput), 'No tasks');
            } else {
                $checkCommand = "ps aux | grep '[p]hp artisan minewache:run-discord-bot'";
                exec($checkCommand, $checkOutput);
                $isRunning = count($checkOutput) > 0;
            }

            if ($isRunning) {
                return redirect()->route('admin.discord-bot-status')
                    ->with('success', 'Discord-Bot wurde erfolgreich neu gestartet.');
            } else {
                return redirect()->route('admin.discord-bot-status')
                    ->with('error', 'Discord-Bot konnte nicht neu gestartet werden. Bitte überprüfen Sie die Logs.');
            }
        } catch (\Exception $e) {
            Log::error('Failed to restart Discord bot from admin panel', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return redirect()->route('admin.discord-bot-status')
                ->with('error', 'Fehler beim Neustart des Discord-Bots: ' . $e->getMessage());
        }
    }
}
