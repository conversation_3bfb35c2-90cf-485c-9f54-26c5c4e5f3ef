/**
 * <PERSON><PERSON>kript wird ausgeführt, wenn die Entwicklungsumgebung beendet wird.
 * <PERSON><PERSON> stellt sicher, dass der Discord-Bot und der Reverb-Server ordnungsgemäß beendet werden.
 */
import { execSync } from 'child_process';
import path from 'path';
import { fileURLToPath } from 'url';
import { exec } from 'child_process';

console.log('🧹 Führe Aufräumarbeiten durch...');

// PHP Discord-Bot stoppen
try {
  console.log('🤖 Stoppe PHP Discord-Bot...');
  const isWindows = process.platform === 'win32';
  if (isWindows) {
    // Auf Windows, verwende stop-discord.bat
    execSync('stop-discord.bat', { stdio: 'inherit' });
  } else {
    // Auf Linux/Mac, verwende pkill
    execSync("pkill -f 'php artisan minewache:run-discord-bot'", { stdio: 'inherit' });
  }
  console.log('✅ PHP Discord-Bot erfolgreich gestoppt.');
} catch (error) {
  console.error('❌ Fehler beim Stoppen des PHP Discord-Bots:', error.message);
}

// Reverb-Server stoppen
try {
  console.log('🔌 Stoppe Reverb-Server...');

  // Prüfen, ob der Reverb-Server läuft
  const isWindows = process.platform === 'win32';

  if (isWindows) {
    // Auf Windows: Finde den Prozess, der auf Port 6001 hört, und beende ihn
    try {
      // Finde die PID des Prozesses, der auf Port 6001 hört
      const findPidCommand = 'netstat -ano | findstr :6001';
      const netstatOutput = execSync(findPidCommand, { encoding: 'utf8' });

      // Extrahiere die PIDs aus der Ausgabe
      const pidRegex = /\s+(\d+)$/gm;
      const matches = [...netstatOutput.matchAll(pidRegex)];
      const pids = [...new Set(matches.map(match => match[1]))];

      if (pids.length > 0) {
        // Beende nur PHP-Prozesse, die auf Port 6001 hören
        for (const pid of pids) {
          if (pid !== '0') {
            let processName = '';
            try {
              const tasklistOutput = execSync(`tasklist /FI "PID eq ${pid}" /FO CSV /NH`, { encoding: 'utf8' });
              const nameMatch = tasklistOutput.match(/^"([^"]+)"/);
              if (nameMatch) {
                processName = nameMatch[1].toLowerCase();
              }
            } catch (tasklistError) {
              // Fehler beim Abrufen des Prozessnamens ignorieren
            }
            if (processName === 'php.exe') {
              console.log(`🔍 Gefundener Reverb-Prozess mit PID ${pid} (${processName}). Beende...`);
              execSync(`taskkill /F /PID ${pid}`, { stdio: 'inherit' });
            } else {
              console.log(`⚠️  Prozess mit PID ${pid} (${processName}) wird übersprungen (nicht php.exe).`);
            }
          }
        }
        console.log('✅ Reverb-Server erfolgreich gestoppt.');
      } else {
        console.log('ℹ️ Kein laufender Reverb-Server gefunden.');
      }
    } catch (netstatError) {
      console.error('❌ Fehler beim Finden des Reverb-Prozesses:', netstatError.message);
    }
  } else {
    // Auf Unix-Systemen: Versuche, den Reverb-Server mit einem Artisan-Befehl zu stoppen
    try {
      execSync('php artisan reverb:stop', { stdio: 'inherit' });
      console.log('✅ Reverb-Server erfolgreich gestoppt.');
    } catch (artisanError) {
      console.error('❌ Fehler beim Stoppen des Reverb-Servers mit Artisan:', artisanError.message);

      // Fallback: Finde den Prozess, der auf Port 6001 hört, und beende ihn
      try {
        execSync('pkill -f "php artisan reverb:start"', { stdio: 'inherit' });
        console.log('✅ Reverb-Server erfolgreich gestoppt (mit pkill).');
      } catch (pkillError) {
        console.error('❌ Fehler beim Stoppen des Reverb-Servers mit pkill:', pkillError.message);
      }
    }
  }
} catch (error) {
  console.error('❌ Fehler beim Stoppen des Reverb-Servers:', error.message);
}

console.log('👋 Aufräumarbeiten abgeschlossen. Auf Wiedersehen!');
