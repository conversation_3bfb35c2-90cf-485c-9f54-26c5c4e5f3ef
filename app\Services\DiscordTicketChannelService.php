<?php

namespace App\Services;

use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Config;
use App\Models\Ticket; // For status constants if needed
use Exception;

class DiscordTicketChannelService
{
    protected DiscordMessagingService $messagingService;
    protected string $botToken;
    protected const DISCORD_API_BASE_URL = 'https://discord.com/api/v10';

    // Permission bitwise values
    protected const PERM_SEND_MESSAGES = 1 << 11; // 2048
    protected const PERM_ATTACH_FILES = 1 << 15; // 32768
    protected const PERM_VIEW_CHANNEL = 1 << 10; // 1024


    public function __construct(DiscordMessagingService $messagingService)
    {
        $this->messagingService = $messagingService;
        $this->botToken = Config::get('minewache_discord_bot.token');
        if (empty($this->botToken)) {
            Log::error('DiscordTicketChannelService: Bot token is not configured.');
            // Consider throwing an exception here or handling this state appropriately
        }
    }

    /**
     * Processes the closure of a Discord ticket channel using REST API.
     * Sends a message, renames channel, updates permissions.
     *
     * @param string $channelId
     * @param Ticket $ticketModel The Laravel Ticket model.
     * @param string|null $reason
     * @param string|null $closedByTag User tag of who closed it.
     * @return bool
     */
    public function closeChannel(string $channelId, Ticket $ticketModel, ?string $reason, ?string $closedByTag = 'System'): bool
    {
        if (empty($this->botToken)) {
            Log::error("DiscordTicketChannelService: Bot token not configured, cannot close channel {$channelId}.");
            return false;
        }

        // 1. Send message (remains the same, uses DiscordMessagingService)
        $closureMessage = "Ticket #{$ticketModel->id} has been closed by {$closedByTag}.";
        if ($reason) $closureMessage .= "\nReason: " . htmlspecialchars($reason); // htmlspecialchars for safety if reason is user-provided
        $embedData = ['title' => 'Ticket Closed', 'description' => $closureMessage, 'color' => 0x28a745]; // Green color
        $embed = $this->messagingService->createEmbed($embedData);
        if ($embed) {
            $this->messagingService->sendMessageToChannel($channelId, '', [$embed]);
        } else {
            $this->messagingService->sendMessageToChannel($channelId, $closureMessage);
        }

        // 2. Rename channel via REST API
        $baseName = $ticketModel->title ? substr(preg_replace('/[^a-z0-9_]/i', '', strtolower($ticketModel->title)), 0, 50) : $ticketModel->id;
        $newChannelName = "closed-{$ticketModel->id}-{$baseName}";
        $newChannelName = substr($newChannelName, 0, 100); // Ensure name is within Discord's limits

        $renameResponse = Http::withHeaders([
            'Authorization' => 'Bot ' . $this->botToken,
            'Content-Type' => 'application/json',
        ])->patch(self::DISCORD_API_BASE_URL . "/channels/{$channelId}", [
            'name' => $newChannelName,
        ]);

        if ($renameResponse->successful()) {
            Log::info("DiscordTicketChannelService: Renamed channel {$channelId} to {$newChannelName} for ticket #{$ticketModel->id} via REST.");
        } else {
            Log::error("DiscordTicketChannelService: Error renaming channel {$channelId} for ticket #{$ticketModel->id} via REST.", [
                'status' => $renameResponse->status(),
                'response' => $renameResponse->body(),
            ]);
            // Optionally, decide if failure here should prevent further actions or return false
        }

        // 3. Update permissions for the ticket creator via REST API
        $ticketCreatorDiscordId = $ticketModel->discord_user_id;
        if ($ticketCreatorDiscordId) {
            // Fetch current permissions for the user to determine their existing 'allow' bitmask.
            // This is a simplification; real-world scenarios might need to fetch all overwrites
            // or have a known 'base' permission set. For this refactor, we assume we need to modify
            // existing permissions based on a typical ticket setup.
            // A GET request to /channels/{channel.id} would give all overwrites.
            // For simplicity, we'll construct a new permission set that removes write access.
            // We assume the user initially had VIEW_CHANNEL, SEND_MESSAGES, ATTACH_FILES.
            // New permissions: VIEW_CHANNEL (allow), SEND_MESSAGES (deny), ATTACH_FILES (deny).

            // Let's assume the user initially had: VIEW_CHANNEL | SEND_MESSAGES | ATTACH_FILES
            // We want to keep VIEW_CHANNEL, but remove SEND_MESSAGES and ATTACH_FILES.
            // So, the new 'allow' will be just VIEW_CHANNEL.
            // The new 'deny' will explicitly include SEND_MESSAGES and ATTACH_FILES.

            // It's often better to fetch the current permissions to correctly calculate the new bitmask.
            // However, the original code modified existing permissions. Let's try to replicate that behavior.
            // The original code did:
            // $newAllowPermissions->remove(ChannelPermission::SEND_MESSAGES());
            // $newAllowPermissions->remove(ChannelPermission::ATTACH_FILES());
            // This implies we need to know the original 'allow' value.
            // Without fetching the channel details, we can't know the original 'allow' value.
            // For this refactor, let's set a defined state: allow VIEW_CHANNEL, deny SEND_MESSAGES & ATTACH_FILES.
            // This is a common state for a closed ticket channel for the original user.

            $newAllowBitwise = self::PERM_VIEW_CHANNEL;
            $newDenyBitwise = self::PERM_SEND_MESSAGES | self::PERM_ATTACH_FILES;

            $permissionsResponse = Http::withHeaders([
                'Authorization' => 'Bot ' . $this->botToken,
                'Content-Type' => 'application/json',
            ])->put(self::DISCORD_API_BASE_URL . "/channels/{$channelId}/permissions/{$ticketCreatorDiscordId}", [
                'id' => $ticketCreatorDiscordId,
                'type' => 1, // 1 for a member overwrite
                'allow' => (string) $newAllowBitwise,
                'deny' => (string) $newDenyBitwise,
            ]);

            if ($permissionsResponse->successful()) {
                Log::info("DiscordTicketChannelService: Updated permissions for creator {$ticketCreatorDiscordId} in channel {$channelId} via REST.");
            } else {
                Log::error("DiscordTicketChannelService: Error updating permissions for creator {$ticketCreatorDiscordId} in {$channelId} via REST.", [
                    'status' => $permissionsResponse->status(),
                    'response' => $permissionsResponse->body(),
                ]);
            }
        } else {
            Log::warning("DiscordTicketChannelService: No ticket creator Discord ID found for ticket #{$ticketModel->id}, skipping permission update.");
        }

        return true; // Assuming success unless a critical error causes an early return
    }
}
