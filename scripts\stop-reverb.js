/**
 * <PERSON><PERSON> Skript stoppt den Reverb-Server.
 */
import { execSync } from 'child_process';
import path from 'path';
import { fileURLToPath } from 'url';

console.log('🔌 Stoppe Reverb-Server...');

// Prüfen, ob der Reverb-Server läuft
const isWindows = process.platform === 'win32';

try {
  if (isWindows) {
    // Auf Windows: Finde den Prozess, der auf Port 6001 hört, und beende ihn
    try {
      // Finde die PID des Prozesses, der auf Port 6001 hört
      const findPidCommand = 'netstat -ano | findstr :6001';
      const netstatOutput = execSync(findPidCommand, { encoding: 'utf8' });

      // Extrahiere die PIDs aus der Ausgabe
      const pidRegex = /\s+(\d+)$/gm;
      const matches = [...netstatOutput.matchAll(pidRegex)];
      const pids = [...new Set(matches.map(match => match[1]))];

      if (pids.length > 0) {
        // Beende jeden <PERSON>, der auf Port 8080 hört
        for (const pid of pids) {
          // Überspringe PID 0, da dies ein Systemprozess ist
          if (pid !== '0') {
            console.log(`🔍 Gefundener Reverb-Prozess mit PID ${pid}. Beende...`);
            execSync(`taskkill /F /PID ${pid}`, { stdio: 'inherit' });
          }
        }
        console.log('✅ Reverb-Server erfolgreich gestoppt.');
      } else {
        console.log('ℹ️ Kein laufender Reverb-Server gefunden.');
      }
    } catch (netstatError) {
      console.error('❌ Fehler beim Finden des Reverb-Prozesses:', netstatError.message);
    }
  } else {
    // Auf Unix-Systemen: Versuche, den Reverb-Server mit einem Artisan-Befehl zu stoppen
    try {
      execSync('php artisan reverb:stop', { stdio: 'inherit' });
      console.log('✅ Reverb-Server erfolgreich gestoppt.');
    } catch (artisanError) {
      console.error('❌ Fehler beim Stoppen des Reverb-Servers mit Artisan:', artisanError.message);

      // Fallback: Finde den Prozess, der auf Port 8080 hört, und beende ihn
      try {
        execSync('pkill -f "php artisan reverb:start"', { stdio: 'inherit' });
        console.log('✅ Reverb-Server erfolgreich gestoppt (mit pkill).');
      } catch (pkillError) {
        console.error('❌ Fehler beim Stoppen des Reverb-Servers mit pkill:', pkillError.message);
      }
    }
  }
} catch (error) {
  console.error('❌ Fehler beim Stoppen des Reverb-Servers:', error.message);
}
