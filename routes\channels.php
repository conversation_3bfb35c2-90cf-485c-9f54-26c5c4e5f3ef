<?php

use App\Models\Ticket;
use App\Models\User;
use Illuminate\Support\Facades\Broadcast;

/*
|--------------------------------------------------------------------------
| Broadcast Channels
|--------------------------------------------------------------------------
|
| Here you may register all of the event broadcasting channels that your
| application supports. The given channel authorization callbacks are
| used to check if an authenticated user can listen to the channel.
|
*/

// Autorisierung für private Ticket-Kanäle
Broadcast::channel('tickets.{ticketId}', function (User $user, int $ticketId) {
    $ticket = Ticket::find($ticketId);
    
    // Benutzer kann den Kanal abonnieren, wenn er der Ersteller des Tickets ist
    // oder wenn er ein Supporter ist
    return $ticket && ($user->id === $ticket->user_id || $user->isSupporter());
});

// Autorisierung für private Benutzer-Kanäle
Broadcast::channel('App.Models.User.{id}', function (User $user, int $id) {
    return (int) $user->id === (int) $id;
});
