<nav x-data="{ open: false, mobileSubmenu: null }" class="glass sticky top-0 sm:top-4 z-40 max-w-7xl mx-auto rounded-none sm:rounded-full shadow-2xl transition-all duration-300 mb-4 sm:mb-8" :class="{'shadow-lg': window.scrollY > 20}" @scroll.window="$el.classList.toggle('shadow-lg', window.scrollY > 20)">
    <!-- Mobile Navigation Backdrop (for better touch area) -->
    <div x-show="open" @click="open = false; mobileSubmenu = null" class="fixed inset-0 bg-black/20 backdrop-blur-sm z-30 sm:hidden" x-transition:enter="transition-opacity ease-linear duration-300" x-transition:enter-start="opacity-0" x-transition:enter-end="opacity-100" x-transition:leave="transition-opacity ease-linear duration-300" x-transition:leave-start="opacity-100" x-transition:leave-end="opacity-0"></div>
    <!-- Primary Navigation Menu -->
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="flex justify-between h-16 sm:h-16 items-center">
            <div class="flex items-center">
                <!-- Logo with improved visibility -->
                <div class="shrink-0 flex items-center opacity-0" x-data x-init="setTimeout(() => $el.classList.add('opacity-100', 'transition-opacity', 'duration-700'), 100)">
                    <a @auth
                           @cannot('ANGENOMMEN')
                               href="{{ route('bewerben') }}"
                           @else
                               href="/"
                           @endcannot
                       @else
                           href="/"
                       @endauth
                       aria-label="Die Minewache - Zur Startseite"
                       class="flex items-center"
                    >
                        <x-application-logo class="block h-8 sm:h-10 w-auto fill-current text-primary transition-all duration-300 hover:scale-110" />
                        <span class="ml-2 font-bold text-lg hidden sm:block">Die Minewache</span>
                    </a>
                </div>

                <!-- Simplified Navigation Links with Apply button highlighted -->
                <div class="hidden md:flex items-center">
                    <nav class="flex items-center space-x-6 md:space-x-8 lg:space-x-10 sm:ml-8 md:ml-12" aria-label="Main Navigation">
                        @php $navItems = [
                            ['name' => 'navigation.home', 'route' => 'home', 'delay' => 150],
                            ['name' => 'navigation.youtube', 'route' => 'youtube', 'delay' => 200],
                            ['name' => 'navigation.mods', 'route' => 'mods', 'delay' => 250],
                            ['name' => 'navigation.more', 'route' => '', 'delay' => 300, 'dropdown' => true, 'items' => [
                                ['name' => 'navigation.partners', 'route' => 'partner'],
                                ['name' => 'navigation.links', 'route' => 'links'],
                                ['name' => 'navigation.branding', 'route' => 'branding'],
                            ]],
                        ]; @endphp

                        @foreach($navItems as $navItem)
                            @if(isset($navItem['dropdown']) && $navItem['dropdown'])
                                <div class="opacity-0 relative" x-data="{ open: false }" x-init="setTimeout(() => $el.classList.add('opacity-100', 'transition-opacity', 'duration-500'), {{ $navItem['delay'] }})">
                                    <button @click="open = !open" @click.away="open = false" class="relative inline-flex items-center px-3 py-4 text-sm font-medium text-slate-300 hover:text-white transition-all duration-300 ease-in-out gap-1.5" aria-haspopup="true" :aria-expanded="open">
                                        <span>{{ __($navItem['name']) }}</span>
                                        <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 transition-transform duration-300" :class="{'rotate-180': open}" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
                                        </svg>
                                    </button>

                                    <!-- Dropdown menu -->
                                    <div x-show="open"
                                         x-transition:enter="transition ease-out duration-200"
                                         x-transition:enter-start="opacity-0 scale-95"
                                         x-transition:enter-end="opacity-100 scale-100"
                                         x-transition:leave="transition ease-in duration-150"
                                         x-transition:leave-start="opacity-100 scale-100"
                                         x-transition:leave-end="opacity-0 scale-95"
                                         class="absolute left-0 mt-1 w-48 glass rounded-xl shadow-lg z-50"
                                         style="display: none;">
                                        <div class="py-1">
                                            @foreach($navItem['items'] as $item)
                                                <a href="{{ route($item['route']) }}" class="block px-4 py-2.5 hover:bg-slate-700/50 rounded-md mx-1 my-0.5 transition-colors duration-300 {{ request()->routeIs($item['route']) ? 'text-blue-300 font-medium' : 'text-base-content/80' }}">
                                                    {{ __($item['name']) }}
                                                </a>
                                            @endforeach
                                        </div>
                                    </div>
                                </div>
                            @else
                                <div class="opacity-0" x-data x-init="setTimeout(() => $el.classList.add('opacity-100', 'transition-opacity', 'duration-500'), {{ $navItem['delay'] }})">
                                    <x-nav-link
                                        :href="route($navItem['route'])"
                                        :active="request()->routeIs($navItem['route'])"
                                    >
                                        <span>{{ __($navItem['name']) }}</span>
                                    </x-nav-link>
                                </div>
                            @endif
                        @endforeach
                    </nav>
                </div>
            </div>

            <!-- Right side navigation elements -->
            <div class="hidden sm:flex sm:items-center sm:ml-6 opacity-0 gap-4" x-data x-init="setTimeout(() => $el.classList.add('opacity-100', 'transition-opacity', 'duration-700'), 250)">
                <!-- Apply Button (Highlighted) -->
                <div class="opacity-0" x-data x-init="setTimeout(() => $el.classList.add('opacity-100', 'transition-opacity', 'duration-500'), 150)">
                    {{-- Use btn btn-primary for consistency and highlighting --}}
                    <x-modern-button variant="primary" href="{{ route('bewerben') }}" ><svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                        </svg>
                        {{ __('navigation.apply') }}</x-modern-button>
                </div>

                <!-- Language Switcher with improved styling -->
                <div class="opacity-0" x-data x-init="setTimeout(() => $el.classList.add('opacity-100', 'transition-opacity', 'duration-500'), 200)">
                    <x-language-switcher />
                </div>

                @auth
                    <x-dropdown align="right" width="48">
                        <x-slot name="trigger">
                            <button class="flex items-center hover:bg-slate-700/50 px-2 py-1.5 rounded-full transition-all duration-300">
                                <img class="h-8 w-8 rounded-full object-cover ring-2 ring-blue-500/30 hover:ring-blue-500 transition-all duration-300"
                                     src="{{ Auth::user()->getAvatar(['extension' => 'webp', 'size' => 32]) }}"
                                     alt="{{ Auth::user()->getTagAttribute() }}" />
                                <svg class="fill-current h-4 w-4 ml-1 text-base-content/70" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 111.414 1.414l-4 4a1 1 01-1.414 0l-4-4a1 1 010-1.414z" clip-rule="evenodd" />
                                </svg>
                            </button>
                        </x-slot>

                        <x-slot name="content">
                            <div class="py-2 px-4 border-b border-slate-700">
                                <p class="text-sm font-medium text-base-content">{{ Auth::user()->global_name }}</p>
                                @can('MINEWACHE_TEAM')
                                    <p class="text-xs text-warning font-medium">Minewache Team</p>
                                @endif
                            </div>

                            <x-dropdown-link :href="route('profile.edit')" class="hover:bg-slate-700/50 px-4 py-2.5 rounded-md mx-1 my-0.5 transition-colors duration-300">
                                {{ __('navigation.profile') }}
                            </x-dropdown-link>

                            <x-dropdown-link :href="route('my.applications')" class="hover:bg-slate-700/50 px-4 py-2.5 rounded-md mx-1 my-0.5 transition-colors duration-300">
                                {{ __('navigation.my_applications') }}
                            </x-dropdown-link>

                            <x-dropdown-link :href="route('tickets.index')" class="hover:bg-slate-700/50 px-4 py-2.5 rounded-md mx-1 my-0.5 transition-colors duration-300">
                                {{ __('navigation.my_tickets') }}
                            </x-dropdown-link>

                            @can('MINEWACHE_TEAM')
                                <x-dropdown-link :href="route('admin.dashboard')" class="hover:bg-slate-700/50 text-amber-300 px-4 py-2.5 rounded-md mx-1 my-0.5 transition-colors duration-300">
                                    {{ __('navigation.admin_panel') }}
                                </x-dropdown-link>
                            @endcan

                            <div class="border-t border-slate-700 mt-1 pt-1">
                                <!-- Authentication -->
                                <form method="POST" action="{{ route('logout') }}">
                                    @csrf

                                    <x-dropdown-link :href="route('logout')"
                                                    onclick="event.preventDefault();
                                                        this.closest('form').submit();"
                                                    class="hover:bg-slate-700/50 text-red-400 px-4 py-2.5 rounded-md mx-1 my-0.5 transition-colors duration-300">
                                        {{ __('auth.logout') }}
                                    </x-dropdown-link>
                                </form>
                            </div>
                        </x-slot>
                    </x-dropdown>
                @else
                    <div class="flex">
                        {{-- Use btn btn-primary for consistency --}}
                        <x-modern-button variant="primary" href="{{ route('auth.discord') }}" >{{ __('auth.login_with_discord') }}</x-modern-button>
                    </div>
                @endauth
            </div>

            <!-- Hamburger with Animation (Improved) -->
            <div class="flex items-center sm:hidden opacity-0 ml-2" x-data x-init="setTimeout(() => $el.classList.add('opacity-100', 'transition-opacity', 'duration-700'), 300)">
                <button @click="open = ! open; mobileSubmenu = null" class="bg-slate-700/50 hover:bg-slate-600/50 text-white rounded-full p-3 shadow-lg transition-all duration-300 hover:scale-110" aria-label="Toggle menu">
                    <svg class="h-6 w-6" stroke="currentColor" fill="none" viewBox="0 0 24 24">
                        <path :class="{'hidden': open, 'inline-flex': ! open }" class="inline-flex transition-transform duration-300" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16" />
                        <path :class="{'hidden': ! open, 'inline-flex': open }" class="hidden transition-transform duration-300" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                    </svg>
                </button>
            </div>
        </div>
    </div>

    <!-- Responsive Navigation Menu (Simplified) -->
    <div :class="{'block': open, 'hidden': ! open}" class="hidden sm:hidden glass rounded-b-3xl shadow-2xl mt-0 sm:mt-2 fixed top-16 left-0 right-0 z-40 mobile-menu-container border-t border-slate-600/30"
         x-transition:enter="transition ease-out duration-200"
         x-transition:enter-start="opacity-0 -translate-y-2"
         x-transition:enter-end="opacity-100 translate-y-0"
         x-transition:leave="transition ease-in duration-150"
         x-transition:leave-start="opacity-100 translate-y-0"
         x-transition:leave-end="opacity-0 -translate-y-2">
        <div class="pt-3 pb-3 space-y-2 px-4">
            <!-- Main Navigation Links with Submenus -->
            <div class="space-y-1">
                <!-- Home -->
                <x-responsive-nav-link :href="route('home')" :active="request()->routeIs('home')" class="text-base-content hover:bg-slate-700/50 rounded-xl py-3 px-4 transition-colors duration-300 flex items-center font-medium">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6" />
                    </svg>
                    {{ __('navigation.home') }}
                </x-responsive-nav-link>

                <!-- YouTube -->
                <x-responsive-nav-link :href="route('youtube')" :active="request()->routeIs('youtube')" class="text-base-content hover:bg-slate-700/50 rounded-xl py-3 px-4 transition-colors duration-300 flex items-center font-medium">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 4v16M17 4v16M3 8h4m10 0h4M3 12h18M3 16h4m10 0h4M4 20h16a1 1 0 001-1V5a1 1 0 00-1-1H4a1 1 0 00-1 1v14a1 1 0 001 1z" />
                    </svg>
                    {{ __('navigation.youtube') }}
                </x-responsive-nav-link>

                <!-- Mods -->
                <x-responsive-nav-link :href="route('mods')" :active="request()->routeIs('mods')" class="text-base-content hover:bg-slate-700/50 rounded-xl py-3 px-4 transition-colors duration-300 flex items-center font-medium">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 20l4-16m4 4l4 4-4 4M6 16l-4-4 4-4" />
                    </svg>
                    {{ __('navigation.mods') }}
                </x-responsive-nav-link>

                <!-- More - With Submenu -->
                <div class="relative" x-data="{ open: false }">
                    <button @click="mobileSubmenu = (mobileSubmenu === 'more') ? null : 'more'"
                            class="w-full text-left text-base-content hover:bg-slate-700/50 rounded-xl py-3 px-4 transition-colors duration-300 flex items-center justify-between font-medium">
                        <div class="flex items-center">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 12h.01M12 12h.01M19 12h.01M6 12a1 1 0 11-2 0 1 1 0 012 0zm7 0a1 1 0 11-2 0 1 1 0 012 0zm7 0a1 1 0 11-2 0 1 1 0 012 0z" />
                            </svg>
                            {{ __('navigation.more') }}
                        </div>
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 transition-transform duration-300" :class="{'rotate-180': mobileSubmenu === 'more'}" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
                        </svg>
                    </button>
                    <div x-show="mobileSubmenu === 'more'"
                         x-transition:enter="transition ease-out duration-200"
                         x-transition:enter-start="opacity-0 -translate-y-2"
                         x-transition:enter-end="opacity-100 translate-y-0"
                         x-transition:leave="transition ease-in duration-150"
                         x-transition:leave-start="opacity-100 translate-y-0"
                         x-transition:leave-end="opacity-0 -translate-y-2"
                         class="pl-4 pr-2 py-2 space-y-1 mobile-submenu mt-1">
                        <x-responsive-nav-link :href="route('partner')" :active="request()->routeIs('partner')" class="text-base-content/80 hover:text-base-content hover:bg-slate-700/50 rounded-lg py-2 px-3 transition-colors duration-300 flex items-center">
                            <span class="w-5 mr-2 text-center">•</span>
                            {{ __('navigation.partners') }}
                        </x-responsive-nav-link>
                        <x-responsive-nav-link :href="route('links')" :active="request()->routeIs('links')" class="text-base-content/80 hover:text-base-content hover:bg-slate-700/50 rounded-lg py-2 px-3 transition-colors duration-300 flex items-center">
                            <span class="w-5 mr-2 text-center">•</span>
                            {{ __('navigation.links') }}
                        </x-responsive-nav-link>
                        <x-responsive-nav-link :href="route('branding')" :active="request()->routeIs('branding')" class="text-base-content/80 hover:text-base-content hover:bg-slate-700/50 rounded-lg py-2 px-3 transition-colors duration-300 flex items-center">
                            <span class="w-5 mr-2 text-center">•</span>
                            {{ __('navigation.branding') }}
                        </x-responsive-nav-link>
                    </div>
                </div>
            </div>

            <!-- Divider -->
            <div class="border-t border-slate-700 my-3"></div>

            <!-- Apply Button (Highlighted) -->
            <div class="px-2 py-2">
                <x-modern-button variant="primary" href="{{ route('bewerben') }}" ><svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                    </svg>
                    {{ __('navigation.apply') }}</x-modern-button>
            </div>

            <!-- Language Switcher -->
            <div class="border-t border-slate-700 my-3 pt-3 pb-2 px-2">
                <div class="flex justify-between items-center">
                    <span class="text-base text-base-content/70">{{ __('navigation.language') }}</span>
                    <x-language-switcher />
                </div>
            </div>
        </div>

        <!-- User Account Section -->
        @auth
            <div class="border-t border-slate-700 py-3">
                <div class="px-4 py-2">
                    <div class="flex items-center gap-3">
                        <img class="h-10 w-10 rounded-full object-cover ring-2 ring-blue-500/30"
                             src="{{ Auth::user()->getAvatar(['extension' => 'webp', 'size' => 40]) }}"
                             alt="{{ Auth::user()->getTagAttribute() }}" />
                        <div>
                            <div class="font-medium text-base-content text-lg">{{ Auth::user()->global_name }}</div>
                            @can('MINEWACHE_TEAM')
                                <div class="text-sm text-warning font-medium">Minewache Team</div>
                            @endcan
                        </div>
                    </div>
                </div>

                <!-- User Menu - With Submenu -->
                <div class="relative" x-data="{ open: false }">
                    <button @click="mobileSubmenu = (mobileSubmenu === 'account') ? null : 'account'"
                            class="w-full text-left text-base-content hover:bg-slate-700/50 rounded-xl py-3 px-4 transition-colors duration-300 flex items-center justify-between font-medium">
                        <div class="flex items-center">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                            </svg>
                            {{ __('navigation.my_account') }}
                        </div>
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 transition-transform duration-300" :class="{'rotate-180': mobileSubmenu === 'account'}" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
                        </svg>
                    </button>
                    <div x-show="mobileSubmenu === 'account'"
                         x-transition:enter="transition ease-out duration-200"
                         x-transition:enter-start="opacity-0 -translate-y-2"
                         x-transition:enter-end="opacity-100 translate-y-0"
                         x-transition:leave="transition ease-in duration-150"
                         x-transition:leave-start="opacity-100 translate-y-0"
                         x-transition:leave-end="opacity-0 -translate-y-2"
                         class="pl-4 pr-2 py-2 space-y-1 mobile-submenu mt-1 mx-2">
                        <x-responsive-nav-link :href="route('profile.edit')" class="text-base-content/80 hover:text-base-content hover:bg-slate-700/50 rounded-lg py-2 px-3 transition-colors duration-300 flex items-center">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                            </svg>
                            {{ __('navigation.profile') }}
                        </x-responsive-nav-link>

                        <x-responsive-nav-link :href="route('my.applications')" class="text-base-content/80 hover:text-base-content hover:bg-slate-700/50 rounded-lg py-2 px-3 transition-colors duration-300 flex items-center">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                            </svg>
                            {{ __('navigation.my_applications') }}
                        </x-responsive-nav-link>

                        <x-responsive-nav-link :href="route('tickets.index')" class="text-base-content/80 hover:text-base-content hover:bg-slate-700/50 rounded-lg py-2 px-3 transition-colors duration-300 flex items-center">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 8h10M7 12h4m1 8l-4-4H5a2 2 0 01-2-2V6a2 2 0 012-2h14a2 2 0 012 2v8a2 2 0 01-2 2h-3l-4 4z" />
                            </svg>
                            {{ __('navigation.my_tickets') }}
                        </x-responsive-nav-link>

                        @can('MINEWACHE_TEAM')
                            <x-responsive-nav-link :href="route('admin.dashboard')" class="text-amber-300 hover:text-amber-200 hover:bg-slate-700/50 rounded-lg py-2 px-3 transition-colors duration-300 flex items-center">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" />
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                                </svg>
                                {{ __('navigation.admin_panel') }}
                            </x-responsive-nav-link>
                        @endcan
                    </div>
                </div>

                <!-- Logout Button -->
                <div class="px-2 py-2 mt-2">
                    <form method="POST" action="{{ route('logout') }}">
                        @csrf
                        <button type="submit" onclick="event.preventDefault(); this.closest('form').submit();"
                                class="w-full flex items-center justify-center gap-2 text-red-400 hover:text-red-300 hover:bg-red-900/20 rounded-xl py-3 px-4 transition-colors duration-300 font-medium">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1" />
                            </svg>
                            {{ __('auth.logout') }}
                        </button>
                    </form>
                </div>
            </div>
        @else
            <div class="border-t border-slate-700 py-3">
                <div class="px-4">
                    <x-modern-button variant="primary" href="{{ route('auth.discord') }}" ><svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 24 24" fill="currentColor">
                            <path d="M20.317 4.3698a19.7913 19.7913 0 00-4.8851-1.5152.0741.0741 0 00-.0785.0371c-.211.3753-.4447.8648-.6083 1.2495-1.8447-.2762-3.68-.2762-5.4868 0-.1636-.3847-.4058-.8742-.6177-1.2495a.077.077 0 00-.0785-.037 19.7363 19.7363 0 00-4.8852 1.515.0699.0699 0 00-.0321.0277C.5334 9.0458-.319 13.5799.0992 18.0578a.0824.0824 0 00.0312.0561c2.0528 1.5076 4.0413 2.4228 5.9929 3.0294a.0777.0777 0 00.0842-.0276c.4616-.6304.8731-1.2952 1.226-1.9942a.076.076 0 00-.0416-.1057c-.6528-.2476-1.2743-.5495-1.8722-.8923a.077.077 0 01-.0076-.1277c.1258-.0943.2517-.1923.3718-.2914a.0743.0743 0 01.0776-.0105c3.9278 1.7933 8.18 1.7933 12.0614 0a.0739.0739 0 01.0785.0095c.1202.099.246.1981.3728.2924a.077.077 0 01-.0066.1276 12.2986 12.2986 0 01-1.873.8914.0766.0766 0 00-.0407.1067c.3604.698.7719 1.3628 1.225 1.9932a.076.076 0 00.0842.0286c1.961-.6067 3.9495-1.5219 6.0023-3.0294a.077.077 0 00.0313-.0552c.5004-5.177-.8382-9.6739-3.5485-13.6604a.061.061 0 00-.0312-.0286zM8.02 15.3312c-1.1825 0-2.1569-1.0857-2.1569-2.419 0-1.3332.9555-2.4189 2.157-2.4189 1.2108 0 2.1757 1.0952 2.1568 2.419 0 1.3332-.9555 2.4189-2.1569 2.4189zm7.9748 0c-1.1825 0-2.1569-1.0857-2.1569-2.419 0-1.3332.9554-2.4189 2.1569-2.4189 1.2108 0 2.1757 1.0952 2.1568 2.419 0 1.3332-.946 2.4189-2.1568 2.4189Z"/>
                        </svg>
                        {{ __('auth.login_with_discord') }}</x-modern-button>
                </div>
            </div>
        @endauth
    </div>
</nav>
