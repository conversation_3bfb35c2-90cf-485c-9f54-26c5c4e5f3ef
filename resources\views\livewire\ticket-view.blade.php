@push('styles')
    @vite('resources/css/ticket-view-custom.css')
@endpush

<div
    x-data="ticketView()"
    x-init="init()"
    @message-added.window="handleMessageAdded($event.detail.message)"
    @play-notification.window="playNotificationSound()"
    @attachments-ready.window="handleAttachmentsReady($event.detail.messageId, $event.detail.attachments)"
    @typing-update.window="handleTypingUpdate($event.detail.users)"
    id="ticket-view"
    class="bg-gradient-modern py-6 px-4"
>
    <!-- Notification sound -->
    <audio id="notification-sound" preload="auto" class="hidden">
        <source src="{{ asset('audio/notification.mp3') }}" type="audio/mpeg">
        <source src="{{ asset('sounds/notification.opus') }}" type="audio/opus">
        {{ __('tickets.audio_not_supported') }}
    </audio>

    <div class="container mx-auto">
        <div class="grid grid-cols-1 lg:grid-cols-12 gap-4">
            <!-- First Column: Breadcrumbs, Header, Info -->
            <div class="lg:col-span-4 space-y-4">
            {{-- Breadcrumbs --}}
            @include('livewire.ticket.partials.breadcrumbs', ['ticket' => $ticket])

            {{-- Header --}}
            @include('livewire.ticket.partials.header', [
                'ticket' => $ticket,
                'isSupporter' => $isSupporter,
            ])

            {{-- Ticket Info --}}
            @include('livewire.ticket.partials.info', ['ticket' => $ticket])
        </div>

            <!-- Second Column: AI, Conversation, Modals -->
            <div class="lg:col-span-8 space-y-4">
            {{-- AI Consent Prompt --}}
            @include('livewire.ticket.partials.ai-consent', [
                'showAiConsentPrompt' => $showAiConsentPrompt,
            ])

            {{-- AI Response Pending --}}
            @include('livewire.ticket.partials.ai-pending', [
                'aiResponsePending' => $aiResponsePending,
            ])

            {{-- Conversation & Reply --}}
            @include('livewire.ticket.partials.conversation', [
                'ticket' => $ticket,
                'showAiConsentPrompt' => $showAiConsentPrompt,
                'aiResponsePending' => $aiResponsePending,
                'quotedMessage' => $quotedMessage,
                'form' => $form,
                'uploadError' => $uploadError,
                'isSupporter' => $isSupporter,
            ])

                {{-- Modals --}}
                @include('livewire.ticket.partials.modals')
            </div>
        </div>
    </div>
</div>

@push('scripts')
    @vite('resources/js/ticket-view-custom.js')
@endpush
