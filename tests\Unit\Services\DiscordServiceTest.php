<?php

namespace Tests\Unit\Services;

use Tests\TestCase;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Collection;
use Mockery;
use App\Services\DiscordService;
use App\Models\Ticket;
use App\Models\TicketMessage;

class DiscordServiceTest extends TestCase
{
    protected function tearDown(): void
    {
        Mockery::close();
        parent::tearDown();
    }

    public function test_send_ticket_message_successful()
    {
        // Fake Discord API response for send-message
        Http::fake([
            '*api/tickets/send-message' => Http::response(['message_id' => 'discord-msg-123'], 200),
        ]);

        // Mock Ticket with a valid discord_channel_id
        $ticket = Mockery::mock(Ticket::class);
        $ticket->discord_channel_id = 'channel-123';
        $ticket->id = 1;

        // Mock TicketMessage with no attachments
        $message = Mockery::mock(TicketMessage::class);
        $message->ticket = $ticket;
        $message->id = 99;
        $message->user_id = 42;
        $message->message = 'Test message';
        $message->attachments = new Collection();
        // Expect save() to be called once to persist discord_message_id
        $message->shouldReceive('save')->once();

        $service = new DiscordService();
        $result = $service->sendTicketMessage($message);

        $this->assertTrue($result, 'Expected sendTicketMessage to return true on successful response');
    }

    public function test_send_ticket_message_failure_missing_channel_id()
    {
        // No HTTP fake needed because it should bail out before calling Http

        // Mock Ticket without a discord_channel_id
        $ticket = Mockery::mock(Ticket::class);
        $ticket->discord_channel_id = null;
        $ticket->id = 2;

        // Mock TicketMessage
        $message = Mockery::mock(TicketMessage::class);
        $message->ticket = $ticket;
        $message->id = 100;
        $message->user_id = 43;
        $message->message = 'Test';
        $message->attachments = new Collection();

        $service = new DiscordService();
        $result = $service->sendTicketMessage($message);

        $this->assertFalse($result, 'Expected sendTicketMessage to return false when channel ID is missing');
    }

    public function test_update_ticket_status_successful()
    {
        // Fake Discord API response for update-status
        Http::fake([
            '*api/tickets/update-status' => Http::response([], 200),
        ]);

        // Mock Ticket with a valid discord_channel_id
        $ticket = Mockery::mock(Ticket::class);
        $ticket->discord_channel_id = 'channel-456';
        $ticket->status = 'closed';
        $ticket->id = 3;

        $service = new DiscordService();
        $result = $service->updateTicketStatus($ticket);

        $this->assertTrue($result, 'Expected updateTicketStatus to return true on 200 response');
    }

    public function test_update_ticket_status_failure_on_api_error()
    {
        // Fake Discord API response with server error
        Http::fake([
            '*api/tickets/update-status' => Http::response([], 500),
        ]);

        // Mock Ticket with a valid discord_channel_id
        $ticket = Mockery::mock(Ticket::class);
        $ticket->discord_channel_id = 'channel-789';
        $ticket->status = 'open';
        $ticket->id = 4;

        $service = new DiscordService();
        $result = $service->updateTicketStatus($ticket);

        $this->assertFalse($result, 'Expected updateTicketStatus to return false on non-200 response');
    }

    public function test_update_ticket_status_failure_missing_channel_id()
    {
        // Mock Ticket without a discord_channel_id
        $ticket = Mockery::mock(Ticket::class);
        $ticket->discord_channel_id = null;
        $ticket->status = 'pending';
        $ticket->id = 5;

        $service = new DiscordService();
        $result = $service->updateTicketStatus($ticket);

        $this->assertFalse($result, 'Expected updateTicketStatus to return false when channel ID is missing');
    }

    public function test_update_ticket_assignment_successful()
    {
        // Fake Discord API response for update-assignment
        Http::fake([
            '*api/tickets/update-assignment' => Http::response([], 200),
        ]);

        // Mock Ticket with a valid discord_channel_id
        $ticket = Mockery::mock(Ticket::class);
        $ticket->discord_channel_id = 'channel-321';
        $ticket->assigned_to = 7;
        $ticket->id = 6;

        $service = new DiscordService();
        $result = $service->updateTicketAssignment($ticket);

        $this->assertTrue($result, 'Expected updateTicketAssignment to return true on 200 response');
    }

    public function test_update_ticket_assignment_failure_on_api_error()
    {
        // Fake Discord API response with client error
        Http::fake([
            '*api/tickets/update-assignment' => Http::response([], 404),
        ]);

        // Mock Ticket with a valid discord_channel_id
        $ticket = Mockery::mock(Ticket::class);
        $ticket->discord_channel_id = 'channel-654';
        $ticket->assigned_to = 9;
        $ticket->id = 7;

        $service = new DiscordService();
        $result = $service->updateTicketAssignment($ticket);

        $this->assertFalse($result, 'Expected updateTicketAssignment to return false on non-200 response');
    }

    public function test_update_ticket_assignment_failure_missing_channel_id()
    {
        // Mock Ticket without a discord_channel_id
        $ticket = Mockery::mock(Ticket::class);
        $ticket->discord_channel_id = null;
        $ticket->assigned_to = 11;
        $ticket->id = 8;

        $service = new DiscordService();
        $result = $service->updateTicketAssignment($ticket);

        $this->assertFalse($result, 'Expected updateTicketAssignment to return false when channel ID is missing');
    }
}