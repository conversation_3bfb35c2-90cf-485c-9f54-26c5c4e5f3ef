<?php

namespace App\Providers;

use Illuminate\Support\ServiceProvider;
use Illuminate\Support\Facades\App;
use Illuminate\Support\Facades\Config;
use Illuminate\Support\Facades\View;

class DebuggingServiceProvider extends ServiceProvider
{
    /**
     * Register services.
     */
    public function register(): void
    {
        // Register any debugging services
    }

    /**
     * Bootstrap services.
     */
    public function boot(): void
    {
        // Disable debugging tools in production or when explicitly disabled
        if ((App::environment('production') && !config('debugging.telescope.allow_production')) ||
            !config('debugging.enabled')) {
            $this->disableDebuggingTools();
        }

        // Share debugging state with all views
        View::share('debuggingEnabled', $this->isDebuggingEnabled());
        View::share('debugConsole', config('debugging.frontend.console_logs', false));
        View::share('debugPanels', config('debugging.frontend.debug_panels', false));
        View::share('debugWebsockets', config('debugging.frontend.websocket_debug', false));
    }

    /**
     * Disable all debugging tools in production
     */
    private function disableDebuggingTools(): void
    {
        // Disable Telescope
        Config::set('telescope.enabled', config('debugging.telescope.enabled', false));

        // Disable Pulse
        Config::set('pulse.enabled', config('debugging.pulse.enabled', false));

        // Log that debugging tools have been configured
        if (App::environment('production')) {
            \Illuminate\Support\Facades\Log::info('Debugging tools configured for production', [
                'telescope' => config('telescope.enabled'),
                'pulse' => config('pulse.enabled'),
                'frontend_debug' => config('debugging.frontend.debug_panels')
            ]);
        }
    }

    /**
     * Check if debugging is enabled
     */
    private function isDebuggingEnabled(): bool
    {
        // Check the master debugging switch first
        if (!config('debugging.enabled', false)) {
            return false;
        }

        // In production, only enable if explicitly allowed
        if (App::environment('production')) {
            return config('app.debug', false) &&
                   (config('debugging.telescope.allow_production', false) ||
                    config('debugging.pulse.allow_production', false));
        }

        // In non-production environments, enable if app.debug is true
        return config('app.debug', true);
    }
}
