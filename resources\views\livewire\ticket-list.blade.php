<div>
    <div class="bg-gradient-modern min-h-screen py-8 px-4">
        <x-modern-card variant="elevated" size="md">
            <div class="flex flex-col md:flex-row justify-between items-start md:items-center mb-6">
                <h1 class="text-2xl font-bold text-base-content mb-4 md:mb-0">
                    {{ $isSupporter ? __('tickets.all_tickets') : __('tickets.my_tickets') }}
                </h1>
                <div class="flex flex-col sm:flex-row gap-3">
                    <x-modern-button
                        variant="primary"
                        size="md"
                        href="{{ route('tickets.create') }}"
                        icon='<svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path></svg>'
                        iconPosition="left"
                    >
                        {{ __('tickets.new_ticket') }}
                    </x-modern-button>
                </div>
            </div>

            <!-- Filters -->
            <x-modern-card variant="outlined" size="sm" class="mb-6">
                <div class="flex flex-col md:flex-row gap-4">
                    <div class="flex-1">
                        <x-modern-input
                            wire:model.live.debounce.300ms="search"
                            type="text"
                            id="search"
                            label="{{ __('admin.search') }}"
                            placeholder="{{ __('admin.search') }}..."
                            variant="glass"
                            icon='<svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path></svg>'
                        />
                    </div>

                    <div class="w-full md:w-48">
                        <label for="status" class="block text-sm font-medium text-base-content/70 mb-1">{{ __('tickets.status') }}</label>
                        <select wire:model.live="status" id="status" class="w-full bg-base-100 border border-base-300 rounded-xl px-4 py-2 focus:outline-none focus:ring-2 focus:ring-primary focus:border-primary transition-all duration-300">
                            <option value="">{{ __('admin.all_statuses') }}</option>
                            <option value="open">{{ __('tickets.status_open') }}</option>
                            <option value="in_progress">{{ __('tickets.status_in_progress') }}</option>
                            <option value="closed">{{ __('tickets.status_closed') }}</option>
                        </select>
                    </div>

                    @if($isSupporter)
                        <div class="w-full md:w-48">
                            <label for="assignedTo" class="block text-sm font-medium text-base-content/70 mb-1">{{ __('tickets.assigned_to') }}</label>
                            <select wire:model.live="assignedTo" id="assignedTo" class="w-full bg-base-100 border border-base-300 rounded-xl px-4 py-2 focus:outline-none focus:ring-2 focus:ring-primary focus:border-primary transition-all duration-300">
                                <option value="">{{ __('admin.all') }}</option>
                                <option value="unassigned">{{ __('tickets.unassigned') }}</option>
                                @foreach($supporters as $id => $name)
                                    <option value="{{ $id }}">{{ $name }}</option>
                                @endforeach
                            </select>
                        </div>
                    @endif

                    <div class="flex items-end">
                        <x-modern-button
                            wire:click="resetFilters"
                            variant="ghost"
                            size="md"
                            icon='<svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path></svg>'
                        >
                            {{ __('admin.reset_filters') }}
                        </x-modern-button>
                    </div>
                </div>
            </x-modern-card>

            <!-- Tickets List -->
            @if($tickets->isEmpty())
                <x-modern-card variant="outlined" size="lg" class="text-center">
                    <svg class="mx-auto h-12 w-12 text-base-content/40 mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                    </svg>
                    <h3 class="text-lg font-semibold text-base-content mb-2">{{ __('tickets.no_tickets') }}</h3>
                    <p class="text-base-content/70 mb-6">{{ __('tickets.no_tickets_description') }}</p>
                    <x-modern-button
                        variant="primary"
                        size="md"
                        href="{{ route('tickets.create') }}"
                        icon='<svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path></svg>'
                    >
                        {{ __('tickets.create_first_ticket') }}
                    </x-modern-button>
                </x-modern-card>
            @else
                <x-modern-card variant="elevated" size="sm" class="overflow-hidden">
                    <div class="overflow-x-auto">
                        <table class="w-full text-left modern-table">
                            <thead>
                                <tr class="bg-base-200/50 text-base-content/70 text-xs uppercase font-semibold">
                                    <th class="px-4 py-3">ID</th>
                                    <th class="px-4 py-3">{{ __('tickets.title') }}</th>
                                    @if($isSupporter)
                                        <th class="px-4 py-3">{{ __('tickets.created_by') }}</th>
                                    @endif
                                    <th class="px-4 py-3">{{ __('tickets.status') }}</th>
                                    <th class="px-4 py-3">{{ __('tickets.created_at') }}</th>
                                    <th class="px-4 py-3">{{ __('tickets.assigned_to') }}</th>
                                    <th class="px-4 py-3">{{ __('tickets.actions') }}</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach($tickets as $ticket)
                                    <tr class="border-b border-base-300/20 hover:bg-base-200/30 cursor-pointer transition-colors duration-200" onclick="window.location='{{ route('tickets.show', $ticket) }}'">
                                        <td class="px-4 py-3 font-mono text-sm text-base-content/60">#{{ $ticket->id }}</td>
                                        <td class="px-4 py-3">
                                            <div class="flex flex-col">
                                                <a href="{{ route('tickets.show', $ticket) }}" class="font-medium text-primary hover:text-primary-focus transition-colors">
                                                    {{ $ticket->title }}
                                                </a>
                                                @if($ticket->messages->isNotEmpty())
                                                    <span class="text-xs text-base-content/50 mt-1 truncate max-w-xs">
                                                        {{ Str::limit($ticket->messages->last()->message, 50) }}
                                                    </span>
                                                @endif
                                            </div>
                                        </td>
                                        @if($isSupporter)
                                            <td class="px-4 py-3">
                                                <div class="flex items-center gap-2">
                                                    <img class="w-6 h-6 rounded-full modern-avatar" src="{{ $ticket->user->getAvatar(['extension' => 'webp', 'size' => 24]) }}" alt="{{ $ticket->user->username }}">
                                                    <span class="text-sm">{{ $ticket->user->username }}</span>
                                                </div>
                                            </td>
                                        @endif
                                        <td class="px-4 py-3">
                                            <x-modern-status-badge :status="$ticket->status" size="sm">
                                                {{ $ticket->statusLabel }}
                                            </x-modern-status-badge>
                                        </td>
                                        <td class="px-4 py-3">
                                            <div class="flex flex-col">
                                                <span class="text-sm">{{ $ticket->created_at->format('d.m.Y') }}</span>
                                                <span class="text-xs text-base-content/50">{{ $ticket->created_at->format('H:i') }}</span>
                                            </div>
                                        </td>
                                        <td class="px-4 py-3">
                                            @if($ticket->assignedTo)
                                                <div class="flex items-center gap-2">
                                                    <img class="w-6 h-6 rounded-full modern-avatar" src="{{ $ticket->assignedTo->getAvatar(['extension' => 'webp', 'size' => 24]) }}" alt="{{ $ticket->assignedTo->username }}">
                                                    <span class="text-sm">{{ $ticket->assignedTo->username }}</span>
                                                </div>
                                            @else
                                                <span class="text-base-content/50 italic text-sm">{{ __('tickets.unassigned') }}</span>
                                            @endif
                                        </td>
                                        <td class="px-4 py-3">
                                            <x-modern-ticket-actions
                                                :ticket="$ticket"
                                                :isSupporter="$isSupporter"
                                                compact="true"
                                            />
                                        </td>
                                    </tr>
                                @endforeach
                            </tbody>
                        </table>
                    </div>

                </x-modern-card>

                <div class="mt-6">
                    {{ $tickets->links() }}
                </div>
            @endif
        </x-modern-card>
    </div>

    <style>
        /* Modern table styling */
        .modern-table {
            border-collapse: separate;
            border-spacing: 0;
        }

        .modern-table thead th {
            position: sticky;
            top: 0;
            z-index: 10;
            backdrop-filter: blur(8px);
        }

        .modern-table tbody tr:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
        }

        .modern-avatar {
            border: 2px solid rgba(255, 255, 255, 0.2);
            transition: all 0.2s ease;
        }

        .modern-avatar:hover {
            transform: scale(1.1);
            border-color: var(--color-primary);
        }

        /* Theme-specific table adjustments */
        [data-theme="minewache-light"] .modern-table tbody tr:hover {
            background-color: rgba(0, 0, 0, 0.02);
        }

        [data-theme="minewache-dark"] .modern-table tbody tr:hover {
            background-color: rgba(255, 255, 255, 0.05);
        }

        [data-theme="minewache-high-contrast"] .modern-table {
            border: 2px solid currentColor;
        }

        [data-theme="minewache-high-contrast"] .modern-table th,
        [data-theme="minewache-high-contrast"] .modern-table td {
            border: 1px solid currentColor;
        }

        /* Responsive table improvements */
        @media (max-width: 768px) {
            .modern-table {
                font-size: 0.875rem;
            }

            .modern-table th,
            .modern-table td {
                padding: 0.5rem;
            }
        }
    </style>
</div>
