/**
 * Dieses Skript startet die Entwicklungsumgebung manuell
 */
import { spawn } from 'child_process';
import { fileURLToPath } from 'url';
import path from 'path';
import fs from 'fs';

// Pfad zum aktuellen Verzeichnis
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Farben für die Konsolenausgabe
const colors = {
  reset: '\x1b[0m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m',
};

// Funktion zum Starten eines Prozesses
function startProcess(command, args, name, color) {
  console.log(`${color}[${name}] Starte...${colors.reset}`);

  const proc = spawn(command, args, {
    stdio: 'pipe',
    shell: true
  });

  proc.stdout.on('data', (data) => {
    console.log(`${color}[${name}] ${data.toString().trim()}${colors.reset}`);
  });

  proc.stderr.on('data', (data) => {
    console.error(`${color}[${name}] FEHLER: ${data.toString().trim()}${colors.reset}`);
  });

  proc.on('close', (code) => {
    if (code !== 0) {
      console.error(`${colors.red}[${name}] Prozess beendet mit Code ${code}${colors.reset}`);
    } else {
      console.log(`${color}[${name}] Prozess beendet${colors.reset}`);
    }
  });

  return proc;
}

// Vorbereitung: PHP Discord-Bot stoppen
console.log(`${colors.cyan}Bereite Entwicklungsumgebung vor...${colors.reset}`);
try {
  // Verwende stop-discord.bat auf Windows, pkill auf Linux/Mac
  const isWindows = process.platform === 'win32';
  const stopCommand = isWindows ? 'stop-discord.bat' : "pkill -f 'php artisan minewache:run-discord-bot'";
  const stopBot = spawn(isWindows ? stopCommand : 'sh', isWindows ? [] : ['-c', stopCommand], { stdio: 'inherit', shell: true });
  await new Promise((resolve) => {
    stopBot.on('close', resolve);
  });
} catch (error) {
  console.error(`${colors.red}Fehler beim Stoppen des PHP Discord-Bots: ${error.message}${colors.reset}`);
}

// Starte alle Prozesse
const processes = [
  startProcess('php', ['artisan', 'serve'], 'Laravel', colors.green),
  startProcess('php', ['artisan', 'queue:work-reverb', '--queue=default,media', '--timeout=0'], 'Queue', colors.yellow),
  startProcess(process.platform === 'win32' ? 'start-discord.bat' : 'php', process.platform === 'win32' ? [] : ['artisan', 'minewache:run-discord-bot'], 'Discord', colors.magenta),
  startProcess('npx', ['vite'], 'Vite', colors.blue),
];

// Behandle Beenden des Skripts
process.on('SIGINT', async () => {
  console.log(`\n${colors.cyan}Beende alle Prozesse...${colors.reset}`);

  // Beende alle Prozesse
  for (const proc of processes) {
    if (!proc.killed) {
      proc.kill();
    }
  }

  // Stoppe den PHP Discord-Bot explizit
  try {
    // Verwende stop-discord.bat auf Windows, pkill auf Linux/Mac
    const isWindows = process.platform === 'win32';
    const stopCommand = isWindows ? 'stop-discord.bat' : "pkill -f 'php artisan minewache:run-discord-bot'";
    const stopBot = spawn(isWindows ? stopCommand : 'sh', isWindows ? [] : ['-c', stopCommand], { stdio: 'inherit', shell: true });
    await new Promise((resolve) => {
      stopBot.on('close', resolve);
    });
  } catch (error) {
    console.error(`${colors.red}Fehler beim Stoppen des PHP Discord-Bots: ${error.message}${colors.reset}`);
  }

  console.log(`${colors.cyan}Alle Prozesse beendet. Auf Wiedersehen!${colors.reset}`);
  process.exit(0);
});

console.log(`${colors.cyan}Entwicklungsumgebung gestartet. Drücke Ctrl+C zum Beenden.${colors.reset}`);
