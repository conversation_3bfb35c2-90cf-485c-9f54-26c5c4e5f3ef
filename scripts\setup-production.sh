#!/bin/bash

# Production setup script for Minewache website
# This script sets up systemd services for all components

# Exit on error
set -e

# Check if running as root
if [ "$EUID" -ne 0 ]; then
  echo "Please run this script as root or with sudo"
  exit 1
fi

# Project directory - adjust this to your production path
PROJECT_DIR="/var/www/minewache-website"
LOG_DIR="/var/log"

# Web server user/group
WEB_USER="www-data"
WEB_GROUP="www-data"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[0;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to display colored messages
function echo_color() {
  local color=$1
  local message=$2
  echo -e "${color}${message}${NC}"
}

# Function to setup a systemd service
function setup_service() {
  local name=$1
  local description=$2
  local command=$3
  local working_dir=$4  # Optional working directory parameter
  local service_file="/etc/systemd/system/minewache-${name}.service"

  # If working_dir is not provided, use PROJECT_DIR
  if [ -z "$working_dir" ]; then
    working_dir="${PROJECT_DIR}"
  fi

  echo_color $BLUE "Setting up ${name} service..."

  # Create service file
  cat > "$service_file" << EOF
[Unit]
Description=${description}
After=network.target
Wants=network-online.target

[Service]
Type=simple
User=${WEB_USER}
Group=${WEB_GROUP}
WorkingDirectory=${working_dir}
ExecStart=${command}
Restart=always
RestartSec=10
StandardOutput=append:${LOG_DIR}/minewache-${name}.log
StandardError=append:${LOG_DIR}/minewache-${name}-error.log
Environment=APP_ENV=production

[Install]
WantedBy=multi-user.target
EOF

  # Create log files if they don't exist
  touch "${LOG_DIR}/minewache-${name}.log"
  touch "${LOG_DIR}/minewache-${name}-error.log"

  # Set proper permissions
  chown ${WEB_USER}:${WEB_GROUP} "${LOG_DIR}/minewache-${name}.log"
  chown ${WEB_USER}:${WEB_GROUP} "${LOG_DIR}/minewache-${name}-error.log"
  chmod 644 "${LOG_DIR}/minewache-${name}.log"
  chmod 644 "${LOG_DIR}/minewache-${name}-error.log"

  # Enable and start the service
  systemctl daemon-reload
  systemctl enable "minewache-${name}.service"
  systemctl restart "minewache-${name}.service"

  echo_color $GREEN "${name} service setup complete"
}

# Main script execution
echo_color $BLUE "Setting up Minewache production services..."

# Check if project directory exists
if [ ! -d "$PROJECT_DIR" ]; then
  echo_color $RED "Project directory $PROJECT_DIR does not exist!"
  exit 1
fi

# Setup Queue Worker service
setup_service "queue" "Minewache Laravel Queue Worker" "/usr/bin/php artisan queue:work --queue=default,media --sleep=3 --tries=3 --timeout=3600"

# Setup Reverb WebSocket service
setup_service "reverb" "Minewache Reverb WebSocket Server" "/usr/bin/php artisan reverb:start --host=127.0.0.1 --port=6001"

# Setup PHP Discord Bot service
setup_service "discord" "Minewache PHP Discord Bot" "/usr/bin/php artisan minewache:run-discord-bot"

echo_color $GREEN "All services have been set up successfully!"
echo_color $YELLOW "You can check their status with:"
echo "  systemctl status minewache-queue.service"
echo "  systemctl status minewache-reverb.service"
echo "  systemctl status minewache-discord.service"

echo_color $YELLOW "View logs with:"
echo "  tail -f /var/log/minewache-queue.log"
echo "  tail -f /var/log/minewache-reverb.log"
echo "  tail -f /var/log/minewache-discord.log"

echo_color $GREEN "Setup complete!"
