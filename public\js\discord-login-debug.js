/**
 * Discord Login Debug Script
 * 
 * This script helps debug Discord login issues by monitoring session state
 * and providing visual feedback about the login process.
 */

document.addEventListener('DOMContentLoaded', function() {
    // Create a debug panel
    createDebugPanel();
    
    // Check if we're on the login page or callback page
    if (window.location.pathname === '/auth/discord' || 
        window.location.pathname === '/larascord/callback') {
        
        // Add a message to the debug panel
        addDebugMessage('Detected Discord login page or callback page');
        
        // Check session state
        checkSessionState();
    }
    
    // Add event listener to the Discord login button
    const discordLoginButtons = document.querySelectorAll('a[href="/auth/discord"]');
    discordLoginButtons.forEach(button => {
        button.addEventListener('click', function(e) {
            // Add a message to the debug panel
            addDebugMessage('Discord login button clicked');
            
            // Store a timestamp in localStorage
            localStorage.setItem('discord_login_timestamp', Date.now());
            
            // Continue with the normal click behavior
        });
    });
});

/**
 * Creates a debug panel on the page
 */
function createDebugPanel() {
    // Create the debug panel
    const debugPanel = document.createElement('div');
    debugPanel.id = 'discord-login-debug-panel';
    debugPanel.style.position = 'fixed';
    debugPanel.style.bottom = '10px';
    debugPanel.style.right = '10px';
    debugPanel.style.width = '300px';
    debugPanel.style.maxHeight = '200px';
    debugPanel.style.overflowY = 'auto';
    debugPanel.style.backgroundColor = 'rgba(0, 0, 0, 0.8)';
    debugPanel.style.color = '#fff';
    debugPanel.style.padding = '10px';
    debugPanel.style.borderRadius = '5px';
    debugPanel.style.fontFamily = 'monospace';
    debugPanel.style.fontSize = '12px';
    debugPanel.style.zIndex = '9999';
    
    // Add a title
    const title = document.createElement('h3');
    title.textContent = 'Discord Login Debug';
    title.style.margin = '0 0 10px 0';
    title.style.padding = '0';
    title.style.fontSize = '14px';
    title.style.fontWeight = 'bold';
    debugPanel.appendChild(title);
    
    // Add a messages container
    const messagesContainer = document.createElement('div');
    messagesContainer.id = 'discord-login-debug-messages';
    debugPanel.appendChild(messagesContainer);
    
    // Add a close button
    const closeButton = document.createElement('button');
    closeButton.textContent = 'Close';
    closeButton.style.position = 'absolute';
    closeButton.style.top = '10px';
    closeButton.style.right = '10px';
    closeButton.style.padding = '2px 5px';
    closeButton.style.fontSize = '10px';
    closeButton.style.backgroundColor = '#333';
    closeButton.style.color = '#fff';
    closeButton.style.border = 'none';
    closeButton.style.borderRadius = '3px';
    closeButton.style.cursor = 'pointer';
    closeButton.addEventListener('click', function() {
        debugPanel.style.display = 'none';
    });
    debugPanel.appendChild(closeButton);
    
    // Add the debug panel to the page
    document.body.appendChild(debugPanel);
    
    // Add initial message
    addDebugMessage('Debug panel initialized');
}

/**
 * Adds a message to the debug panel
 * 
 * @param {string} message The message to add
 */
function addDebugMessage(message) {
    const messagesContainer = document.getElementById('discord-login-debug-messages');
    if (!messagesContainer) return;
    
    const messageElement = document.createElement('div');
    messageElement.style.marginBottom = '5px';
    messageElement.style.borderBottom = '1px solid #333';
    messageElement.style.paddingBottom = '5px';
    
    const timestamp = new Date().toLocaleTimeString();
    messageElement.innerHTML = `<span style="color: #999;">[${timestamp}]</span> ${message}`;
    
    messagesContainer.appendChild(messageElement);
    messagesContainer.scrollTop = messagesContainer.scrollHeight;
}

/**
 * Checks the session state by making an AJAX request to the debug endpoint
 */
function checkSessionState() {
    // Add a message to the debug panel
    addDebugMessage('Checking session state...');
    
    // Make an AJAX request to the debug endpoint
    fetch('/debug/session-state')
        .then(response => response.json())
        .then(data => {
            // Add the session state to the debug panel
            addDebugMessage(`Session ID: ${data.session_id}`);
            addDebugMessage(`Fresh login: ${data.fresh_discord_login ? 'Yes' : 'No'}`);
            addDebugMessage(`Login timestamp: ${data.discord_login_timestamp || 'Not set'}`);
            addDebugMessage(`Time since login: ${data.time_since_login || 'N/A'}`);
            addDebugMessage(`GDPR consent: ${data.gdpr_consent ? 'Yes' : 'No'}`);
            addDebugMessage(`Intended URL: ${data.intended_url || 'Not set'}`);
            addDebugMessage(`Authenticated: ${data.authenticated ? 'Yes' : 'No'}`);
            
            // Check localStorage timestamp
            const localTimestamp = localStorage.getItem('discord_login_timestamp');
            if (localTimestamp) {
                const timeSinceLocalLogin = Math.floor((Date.now() - localTimestamp) / 1000);
                addDebugMessage(`Local timestamp: ${localTimestamp}`);
                addDebugMessage(`Time since local login: ${timeSinceLocalLogin} seconds`);
            } else {
                addDebugMessage('No local timestamp found');
            }
            
            // If we're on the callback page, check for code parameter
            if (window.location.pathname === '/larascord/callback') {
                const urlParams = new URLSearchParams(window.location.search);
                const code = urlParams.get('code');
                if (code) {
                    addDebugMessage(`Code parameter: ${code.substring(0, 10)}...`);
                } else {
                    addDebugMessage('No code parameter found');
                }
            }
        })
        .catch(error => {
            addDebugMessage(`Error checking session state: ${error.message}`);
        });
}
