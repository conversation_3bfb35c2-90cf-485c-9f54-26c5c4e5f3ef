<?php

namespace App\Models;

use App\Events\TicketMessageCreated;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class TicketMessage extends Model
{
    use HasFactory;

    protected $fillable = [
        'ticket_id',
        'user_id',
        'message',
        'is_from_discord',
        'discord_message_id',
        'sync_failed',
        'is_system_message',
        'message_source',
    ];

    protected $casts = [
        'is_from_discord' => 'boolean',
        'sync_failed' => 'boolean',
        'is_system_message' => 'boolean',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
        'message_source' => 'string',
    ];

    /**
     * The event map for the model.
     *
     * @var array
     */
    protected $dispatchesEvents = [
        'created' => TicketMessageCreated::class,
    ];

    /**
     * Relationship to the ticket
     */
    public function ticket()
    {
        return $this->belongsTo(Ticket::class);
    }

    /**
     * Relationship to the user who sent the message
     */
    public function user()
    {
        return $this->belongsTo(User::class, 'user_id');
    }

    /**
     * Relationship to attachments
     */
    public function attachments()
    {
        return $this->hasMany(TicketAttachment::class);
    }
}
