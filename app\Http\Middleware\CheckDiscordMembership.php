<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Session;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Carbon;

class CheckDiscordMembership
{
    /**
     * Löscht den Discord-Mitgliedschafts-Cache für einen Benutzer
     *
     * @param int $userId Die Benutzer-ID
     * @return void
     */
    public static function clearMembershipCache($userId)
    {
        $cacheKey = 'discord_member_' . $userId;
        Cache::forget($cacheKey);
        Log::debug('Cleared Discord membership cache', [
            'user_id' => $userId,
            'cache_key' => $cacheKey
        ]);
    }

    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure  $next
     * @return mixed
     */
    public function handle(Request $request, Closure $next)
    {
        // Simulation nur in Nicht-Produktionsumgebungen oder wenn Debug-Modus aktiviert ist
        if ((config('app.debug') || !app()->environment('production')) &&
            Session::has('simulate_discord_membership')) {

            $simulatedStatus = Session::get('simulate_discord_membership');

            if (config('app.debug')) {
                Log::debug('Using simulated Discord membership status', [
                    'simulated_status' => $simulatedStatus ? 'member' : 'not member',
                    'environment' => app()->environment()
                ]);
            }

            if ($simulatedStatus === false) {
                return redirect()->route('discord.membership.required');
            }

            return $next($request);
        }

        $user = Auth::user();

        if (!$user) {
            return redirect()->route('login');
        }

        // Prüfen, ob der Benutzer bereits als Discord-Mitglied verifiziert wurde
        // Cache für 24 Stunden, um wiederholte API-Aufrufe zu vermeiden
        $cacheKey = 'discord_member_' . $user->id;
        if (Cache::has($cacheKey)) {
            Log::debug('Using cached Discord membership status', [
                'user_id' => $user->id,
                'is_member' => 'yes (cached)'
            ]);
            return $next($request);
        }

        try {
            // Get the guild ID from the configuration
            $guildId = config('services.discord.guild_id');

            Log::debug('Checking Discord membership', [
                'user_id' => $user->id,
                'guild_id' => $guildId
            ]);

            if (empty($guildId)) {
                Log::error('Discord guild ID not found in configuration');
                // If guild ID is not configured, allow access to avoid blocking all users
                return $next($request);
            }

            try {
                // Versuche zuerst, den lokalen Discord-Bot zu verwenden
                $botApiUrl = env('DISCORD_BOT_API_URL', 'http://localhost:3001');
                $endpoint = "{$botApiUrl}/api/users/{$user->id}/roles";
                $apiKey = config('services.discord.api_key');
                $useLocalBot = true;

                try {
                    // First, check with Discord API directly to verify membership
                    // This is more reliable than the local bot check
                    $guildMember = $user->getGuildMember($guildId);

                    if ($guildMember) {
                        Log::debug('User verified as Discord member via Discord API first', [
                            'user_id' => $user->id,
                            'guild_id' => $guildId,
                            'roles' => $guildMember->roles ?? []
                        ]);

                        // User is a member according to Discord API, set cache and proceed
                        Cache::put($cacheKey, true, now()->addHours(24));

                        // Still try the local bot check for role synchronization, but don't block on it
                        $useLocalBot = true;
                    }

                    // Prüfen, ob der Bot online ist
                    $botStatus = Cache::get('discord_bot_status');
                    $botIsOnline = $botStatus && $botStatus['status'] === 'online';

                    // Wenn der Bot offline ist und der letzte Check weniger als 1 Minute her ist,
                    // direkt auf die Discord-API zurückfallen
                    if (!$botIsOnline && $botStatus && now()->diffInSeconds($botStatus['last_checked']) < 60) {
                        Log::warning('Skipping local bot check because bot is known to be offline', [
                            'user_id' => $user->id,
                            'bot_status' => $botStatus
                        ]);

                        // If we already verified with Discord API, proceed
                        if ($guildMember) {
                            return $next($request);
                        }

                        $useLocalBot = false;
                        throw new \Exception('Bot is known to be offline');
                    }

                    // Anfrage an den lokalen Discord-Bot senden mit Cache-Refresh-Parameter
                    $response = Http::withHeaders([
                        'Authorization' => "Bearer {$apiKey}",
                        'Accept' => 'application/json',
                        'X-Request-Source' => 'CheckDiscordMembership'
                    ])->timeout(5)->get("{$endpoint}?refresh=true"); // Längeres Timeout für bessere Zuverlässigkeit

                    if ($response->successful()) {
                        Log::debug('Successfully checked membership using local Discord bot', [
                            'user_id' => $user->id,
                            'status' => $response->status()
                        ]);

                        // Wenn wir hier sind, ist der Benutzer ein Mitglied des Servers
                        // (sonst hätte der Bot einen 404-Fehler zurückgegeben)

                        // Cache für 24 Stunden setzen
                        Cache::put($cacheKey, true, now()->addHours(24));
                        Log::debug('User verified as Discord member (local bot), setting cache', [
                            'user_id' => $user->id,
                            'cache_key' => $cacheKey
                        ]);

                        return $next($request);
                    } else {
                        // Wenn der Bot einen 404-Fehler zurückgibt, ist der Benutzer kein Mitglied
                        if ($response->status() === 404) {
                            Log::info('User is not a member of the Discord server (local bot check)', [
                                'user_id' => $user->id,
                                'guild_id' => $guildId
                            ]);

                            // If we already verified with Discord API, trust that over the local bot
                            if (isset($guildMember) && $guildMember) {
                                Log::warning('Discord API and local bot disagree about membership status. Trusting Discord API.', [
                                    'user_id' => $user->id,
                                    'discord_api' => 'member',
                                    'local_bot' => 'not member'
                                ]);

                                // Still proceed since Discord API verified membership
                                return $next($request);
                            }

                            // Check if this is an AJAX request
                            if ($request->ajax() || $request->wantsJson()) {
                                return response()->json([
                                    'success' => false,
                                    'status' => 'error',
                                    'error_code' => 'not_guild_member',
                                    'message' => 'Du bist kein Mitglied des Discord-Servers',
                                    'redirect_url' => route('discord.membership.required'),
                                    'timestamp' => now()->toIso8601String()
                                ], 403);
                            }

                            // Redirect to a page explaining they need to join the Discord server
                            return redirect()->route('discord.membership.required');
                        }

                        // Bei anderen Fehlern auf die Discord-API zurückfallen
                        $useLocalBot = false;
                        Log::warning('Failed to check membership using local Discord bot, falling back to Discord API', [
                            'user_id' => $user->id,
                            'status' => $response->status(),
                            'response' => $response->json()
                        ]);
                    }
                } catch (\Exception $botError) {
                    // Bei Fehlern mit dem lokalen Bot auf die Discord-API zurückfallen
                    $useLocalBot = false;
                    Log::warning('Error using local Discord bot, falling back to Discord API', [
                        'user_id' => $user->id,
                        'error' => $botError->getMessage()
                    ]);
                }

                // Fallback zur Discord-API, wenn der lokale Bot nicht verfügbar ist
                if (!$useLocalBot) {
                    // Check if the user is a member of the guild using Larascord's InteractsWithDiscord trait
                    $guildMember = $user->getGuildMember($guildId);

                    Log::debug('Guild member check result (Discord API)', [
                        'user_id' => $user->id,
                        'is_member' => $guildMember ? 'yes' : 'no'
                    ]);

                    if (!$guildMember) {
                        Log::info('User is not a member of the Discord server (Discord API check)', [
                            'user_id' => $user->id,
                            'guild_id' => $guildId
                        ]);

                        // Check if this is an AJAX request
                        if ($request->ajax() || $request->wantsJson()) {
                            return response()->json([
                                'success' => false,
                                'status' => 'error',
                                'error_code' => 'not_guild_member',
                                'message' => 'Du bist kein Mitglied des Discord-Servers',
                                'redirect_url' => route('discord.membership.required'),
                                'timestamp' => now()->toIso8601String()
                            ], 403);
                        }

                        // Redirect to a page explaining they need to join the Discord server
                        return redirect()->route('discord.membership.required');
                    } else {
                        // Benutzer ist Mitglied, Cache für 24 Stunden setzen
                        Cache::put($cacheKey, true, now()->addHours(24));
                        Log::debug('User verified as Discord member (Discord API), setting cache', [
                            'user_id' => $user->id,
                            'cache_key' => $cacheKey
                        ]);
                    }
                }
            } catch (\Exception $membershipError) {
                Log::error('Error checking Discord membership in middleware', [
                    'user_id' => $user->id,
                    'error' => $membershipError->getMessage(),
                    'trace' => $membershipError->getTraceAsString()
                ]);

                // Im Entwicklungsmodus erlauben wir den Zugriff trotz Fehler
                if (app()->environment('local')) {
                    Log::warning('Allowing access despite Discord membership check error (development mode)');
                    return $next($request);
                }

                // Check if this is an AJAX request
                if ($request->ajax() || $request->wantsJson()) {
                    return response()->json([
                        'success' => false,
                        'status' => 'error',
                        'error_code' => 'discord_api_error',
                        'message' => 'Bei der Überprüfung deiner Discord-Mitgliedschaft ist ein Fehler aufgetreten. Bitte versuche es später erneut.',
                        'redirect_url' => route('discord.membership.required'),
                        'timestamp' => now()->toIso8601String()
                    ], 500);
                }

                // Im Produktionsmodus zur Fehlerseite umleiten
                return redirect()->route('discord.membership.required')
                    ->with('error', 'Bei der Überprüfung deiner Discord-Mitgliedschaft ist ein Fehler aufgetreten. Bitte versuche es später erneut.');
            }

            return $next($request);

        } catch (\Exception $e) {
            Log::error('Error checking Discord membership', [
                'user_id' => $user->id,
                'error' => $e->getMessage()
            ]);

            // In case of an error, we'll allow access to avoid blocking users due to API issues
            return $next($request);
        }
    }
}
