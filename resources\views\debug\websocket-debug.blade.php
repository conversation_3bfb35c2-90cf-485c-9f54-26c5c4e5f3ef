<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="csrf-token" content="{{ csrf_token() }}">

    <title>WebSocket Diagnostics - {{ config('app.name', 'Laravel') }}</title>

    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.bunny.net">
    <link href="https://fonts.bunny.net/css?family=figtree:400,500,600&display=swap" rel="stylesheet" />

    <!-- Scripts -->
    @vite(['resources/css/app.css', 'resources/js/app.js'])

    <!-- Livewire -->
    @livewireStyles
</head>
<body class="font-sans antialiased bg-gray-900 text-white">
    <div class="min-h-screen flex flex-col">
        <header class="bg-gray-800 shadow">
            <div class="max-w-7xl mx-auto py-6 px-4 sm:px-6 lg:px-8">
                <h1 class="text-2xl font-bold">WebSocket Diagnostics</h1>
            </div>
        </header>

        <main class="flex-1 p-6">
            <div class="max-w-7xl mx-auto">
                <div class="bg-gray-800 overflow-hidden shadow-sm sm:rounded-lg p-6">
                    <h2 class="text-xl font-semibold mb-4">Connection Status</h2>

                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
                        <div class="bg-gray-700 p-4 rounded-lg">
                            <h3 class="text-lg font-medium mb-2">WebSocket Status</h3>
                            <div class="flex items-center mb-2">
                                <span class="mr-2">Status:</span>
                                <span id="connection-status" class="px-2 py-1 rounded text-sm font-medium">Checking...</span>
                            </div>
                            <div class="flex items-center mb-2">
                                <span class="mr-2">Connection ID:</span>
                                <span id="connection-id" class="font-mono text-sm">-</span>
                            </div>
                            <div class="flex items-center mb-2">
                                <span class="mr-2">Socket ID:</span>
                                <span id="socket-id" class="font-mono text-sm">-</span>
                            </div>
                        </div>

                        <div class="bg-gray-700 p-4 rounded-lg">
                            <h3 class="text-lg font-medium mb-2">Configuration</h3>
                            <div class="grid grid-cols-2 gap-2 text-sm">
                                <div>WebSocket Host:</div>
                                <div id="ws-host" class="font-mono">-</div>

                                <div>WebSocket Port:</div>
                                <div id="ws-port" class="font-mono">-</div>

                                <div>WebSocket Path:</div>
                                <div id="ws-path" class="font-mono">-</div>

                                <div>Secure Connection:</div>
                                <div id="ws-secure" class="font-mono">-</div>

                                <div>Browser URL:</div>
                                <div id="browser-url" class="font-mono">-</div>
                            </div>
                        </div>
                    </div>

                    <div class="mb-6">
                        <h2 class="text-xl font-semibold mb-2">Environment Variables</h2>
                        <div class="bg-gray-700 p-4 rounded-lg overflow-x-auto">
                            <table class="min-w-full text-sm">
                                <thead>
                                    <tr class="border-b border-gray-600">
                                        <th class="text-left py-2 px-4">Variable</th>
                                        <th class="text-left py-2 px-4">Value</th>
                                    </tr>
                                </thead>
                                <tbody id="env-variables">
                                    <tr><td colspan="2" class="py-2 px-4">Loading...</td></tr>
                                </tbody>
                            </table>
                        </div>
                    </div>

                    <div class="mb-6">
                        <h2 class="text-xl font-semibold mb-2">Actions</h2>
                        <div class="flex flex-wrap gap-2">
                            <button id="btn-reconnect" class="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600">
                                Reconnect
                            </button>
                            <button id="btn-test-event" class="px-4 py-2 bg-green-500 text-white rounded hover:bg-green-600">
                                Test Event
                            </button>
                            <button id="btn-refresh" class="px-4 py-2 bg-gray-500 text-white rounded hover:bg-gray-600">
                                Refresh Page
                            </button>
                        </div>
                    </div>

                    <div>
                        <h2 class="text-xl font-semibold mb-2">Event Log</h2>
                        <div id="event-log" class="h-64 overflow-y-auto p-4 bg-gray-900 rounded font-mono text-sm"></div>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const connectionStatus = document.getElementById('connection-status');
            const connectionId = document.getElementById('connection-id');
            const socketId = document.getElementById('socket-id');
            const wsHost = document.getElementById('ws-host');
            const wsPort = document.getElementById('ws-port');
            const wsPath = document.getElementById('ws-path');
            const wsSecure = document.getElementById('ws-secure');
            const browserUrl = document.getElementById('browser-url');
            const eventLog = document.getElementById('event-log');
            const btnReconnect = document.getElementById('btn-reconnect');
            const btnTestEvent = document.getElementById('btn-test-event');
            const btnRefresh = document.getElementById('btn-refresh');
            const envVariables = document.getElementById('env-variables');

            // Set browser URL
            browserUrl.textContent = window.location.href;

            // Function to update connection status
            function updateConnectionStatus(state) {
                connectionStatus.textContent = state;

                // Set appropriate status color
                connectionStatus.className = 'px-2 py-1 rounded text-sm font-medium';

                switch(state) {
                    case 'connected':
                        connectionStatus.classList.add('bg-green-500', 'text-white');
                        break;
                    case 'connecting':
                        connectionStatus.classList.add('bg-yellow-500', 'text-black');
                        break;
                    case 'disconnected':
                        connectionStatus.classList.add('bg-gray-500', 'text-white');
                        break;
                    case 'unavailable':
                    case 'failed':
                        connectionStatus.classList.add('bg-red-500', 'text-white');
                        break;
                    default:
                        connectionStatus.classList.add('bg-blue-500', 'text-white');
                }
            }

            // Function to log messages
            function log(message, level = 'info') {
                const timestamp = new Date().toISOString().split('T')[1].slice(0, -1);
                const entry = document.createElement('div');
                entry.className = 'mb-1';

                switch(level) {
                    case 'error':
                        entry.classList.add('text-red-400');
                        break;
                    case 'warning':
                        entry.classList.add('text-yellow-400');
                        break;
                    case 'success':
                        entry.classList.add('text-green-400');
                        break;
                    default:
                        entry.classList.add('text-gray-300');
                }

                entry.textContent = `[${timestamp}] ${message}`;
                eventLog.appendChild(entry);
                eventLog.scrollTop = eventLog.scrollHeight;
            }

            // Refresh button
            btnRefresh.addEventListener('click', () => {
                window.location.reload();
            });

            // Check if Echo is available
            if (window.Echo) {
                log('Echo is available');

                // Get and display WebSocket configuration
                if (window.Echo.connector && window.Echo.connector.options) {
                    const options = window.Echo.connector.options;
                    wsHost.textContent = options.wsHost || 'unknown';
                    wsPort.textContent = options.wsPort || 'unknown';
                    wsPath.textContent = options.wsPath || '/';
                    wsSecure.textContent = options.forceTLS ? 'Yes (WSS)' : 'No (WS)';

                    log(`WebSocket configuration: ${options.wsHost}:${options.wsPort}${options.wsPath || '/'} (${options.forceTLS ? 'WSS' : 'WS'})`);

                    // Display environment variables
                    envVariables.innerHTML = '';

                    // Add row for each important config value
                    const envVars = [
                        { name: 'wsHost', value: options.wsHost },
                        { name: 'wsPort', value: options.wsPort },
                        { name: 'wssPort', value: options.wssPort },
                        { name: 'wsPath', value: options.wsPath || '/' },
                        { name: 'forceTLS', value: options.forceTLS ? 'true' : 'false' },
                        { name: 'broadcaster', value: options.broadcaster },
                        { name: 'key', value: options.key },
                        { name: 'enabledTransports', value: Array.isArray(options.enabledTransports) ? options.enabledTransports.join(', ') : options.enabledTransports },
                        { name: 'window.location.hostname', value: window.location.hostname },
                        { name: 'window.location.protocol', value: window.location.protocol },
                    ];

                    envVars.forEach(variable => {
                        const row = document.createElement('tr');
                        row.className = 'border-b border-gray-600';
                        row.innerHTML = `
                            <td class="py-2 px-4 font-medium">${variable.name}</td>
                            <td class="py-2 px-4 font-mono">${variable.value}</td>
                        `;
                        envVariables.appendChild(row);
                    });
                } else {
                    log('Echo connector options not available', 'error');
                }

                // Check connection status
                if (window.Echo.connector && window.Echo.connector.pusher) {
                    const pusher = window.Echo.connector.pusher;

                    // Update initial connection status
                    updateConnectionStatus(pusher.connection.state);
                    log(`Initial connection state: ${pusher.connection.state}`);

                    // Listen for connection state changes
                    pusher.connection.bind('state_change', (states) => {
                        log(`Connection state changed: ${states.previous} -> ${states.current}`);
                        updateConnectionStatus(states.current);
                    });

                    // Listen for connection established
                    pusher.connection.bind('connected', () => {
                        log('Connection established', 'success');
                        connectionId.textContent = pusher.connection.connectionId || 'Unknown';
                        socketId.textContent = pusher.connection.socket_id || 'Unknown';
                    });

                    // Listen for connection errors
                    pusher.connection.bind('error', (error) => {
                        log(`Connection error: ${JSON.stringify(error)}`, 'error');
                    });

                    // Reconnect button
                    btnReconnect.addEventListener('click', () => {
                        log('Manual reconnection requested');
                        pusher.disconnect();
                        setTimeout(() => {
                            pusher.connect();
                        }, 1000);
                    });

                    // Test event button
                    btnTestEvent.addEventListener('click', () => {
                        log('Sending test event');
                        // Create a simple event using Livewire
                        if (window.Livewire) {
                            window.Livewire.dispatch('echo-test');
                            log('Test event dispatched via Livewire');

                            // Listen for the response
                            window.Livewire.on('echo-test-response', (data) => {
                                log(`Test response received: ${data.message}`, 'success');
                                log(`Response timestamp: ${data.timestamp}`);
                            });
                        } else {
                            log('Livewire not available, cannot send test event', 'error');
                        }
                    });
                } else {
                    log('Echo connector or pusher not available', 'error');
                    updateConnectionStatus('Not Available');
                }
            } else {
                log('Echo is not available', 'error');
                updateConnectionStatus('Not Available');
            }
        });
    </script>
    <!-- Include the WebSocketTest component to handle test events -->
    <div style="display: none;">
        @livewire('web-socket-test')
    </div>

    @livewireScripts
</body>
</html>
