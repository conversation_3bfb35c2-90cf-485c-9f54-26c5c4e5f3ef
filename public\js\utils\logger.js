/**
 * Environment-aware logging utility
 *
 * This utility provides logging functions that only output in development environments
 * and are silenced in production, improving performance and security.
 */

(function(window) {
    // Determine if we're in a development environment
    const isDevelopment =
        window.location.hostname === 'localhost' ||
        window.location.hostname === '127.0.0.1' ||
        window.location.hostname.includes('.local') ||
        (window.import && window.import.meta && window.import.meta.env &&
         (window.import.meta.env.VITE_APP_ENV === 'local' || window.import.meta.env.MODE === 'development')) ||
        (typeof process !== 'undefined' && process.env && process.env.NODE_ENV === 'development');

    // Create a logger object with methods that mirror console
    const logger = {
        // For general information
        log: function(...args) {
            if (isDevelopment) {
                console.log(...args);
            }
        },

        // For warnings
        warn: function(...args) {
            if (isDevelopment) {
                console.warn(...args);
            }
        },

        // For errors - we keep these even in production for critical issues
        error: function(...args) {
            console.error(...args);
        },

        // For debugging information (most verbose)
        debug: function(...args) {
            if (isDevelopment) {
                console.debug(...args);
            }
        },

        // For performance measurements
        time: function(label) {
            if (isDevelopment) {
                console.time(label);
            }
        },

        timeEnd: function(label) {
            if (isDevelopment) {
                console.timeEnd(label);
            }
        },

        // For grouping related logs
        group: function(label) {
            if (isDevelopment) {
                console.group(label);
            }
        },

        groupEnd: function() {
            if (isDevelopment) {
                console.groupEnd();
            }
        },

        // Force logging even in production (use sparingly)
        forceLog: function(...args) {
            console.log('[FORCED]', ...args);
        }
    };

    // Add to window object
    window.Logger = logger;
})(window);
