<x-app-layout>
    <x-slot name="header">
        <h2 class="font-semibold text-xl text-base-content leading-tight">
            {{ __('Bewerbung eingereicht') }}
        </h2>
    </x-slot>

    <div class="py-12">
        <div class="max-w-3xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="bg-base-200 shadow-lg rounded-lg overflow-hidden">
                <div class="p-6">
                    <div class="flex flex-col items-center text-center">
                        <div class="rounded-full bg-success/20 p-3 mb-4 animate-pulse">
                            <x-heroicon-o-check-circle class="h-12 w-12 text-success" />
                        </div>
                        
                        <h2 class="text-2xl font-bold mb-2 animate-fade-in">Bewerbung erfolgreich eingereicht!</h2>
                        <p class="text-lg mb-6">Vielen Dank für dein Interesse an Minewache.</p>
                        
                        <div class="prose prose-sm mb-6">
                            <p>Deine Bewerbung wurde gespeichert und wird von unserem Team geprüft. Wir werden uns so schnell wie möglich bei dir melden.</p>
                            <p>Du kannst den Status deiner Bewerbung jederzeit unter "Meine Bewerbungen" einsehen.</p>
                        </div>
                        
                        <div class="alert alert-info mb-6 text-left">
                            <x-heroicon-o-information-circle class="h-6 w-6" />
                            <div>
                                <div class="font-medium">Nächste Schritte</div>
                                <div class="text-sm mt-1">
                                    <ol class="space-y-1 list-decimal list-inside">
                                        <li>Halte dein Discord offen, um über Nachrichten informiert zu werden</li>
                                        <li>Bereite dich auf ein mögliches Gespräch mit unserem Team vor</li>
                                        <li>Du kannst währenddessen zusätzliche Beispiele deiner Arbeit vorbereiten</li>
                                    </ol>
                                </div>
                            </div>
                        </div>
                        
                        <div class="flex flex-col space-y-3 sm:flex-row sm:space-y-0 sm:space-x-3 mt-4">
                            <a href="{{ route('my.applications') }}" class="btn btn-primary">
                                <x-heroicon-o-document-text class="h-5 w-5 mr-2" />
                                Meine Bewerbungen
                            </a>
                            <a href="{{ route('home') }}" class="btn btn-outline">
                                <x-heroicon-o-home class="h-5 w-5 mr-2" />
                                Zurück zur Startseite
                            </a>
                        </div>
                    </div>
                </div>
            </div>

            @if(session('application_id'))
            <div class="mt-8 opacity-0 animate-slide-up" 
                 x-data
                 x-init="setTimeout(() => $el.classList.add('opacity-100'), 500)">
                <div class="bg-base-100 shadow-md rounded-lg p-6">
                    <h3 class="text-lg font-medium mb-4 flex items-center">
                        <x-heroicon-o-calendar class="h-5 w-5 mr-2 text-primary" />
                        Bewerbungs-ID: #{{ session('application_id') }}
                    </h3>
                    <p class="text-sm">
                        Bitte bewahre diese ID auf. Sie kann hilfreich sein, wenn du mit unserem Team über deine Bewerbung kommunizierst.
                    </p>
                </div>
            </div>
            @endif
        </div>
    </div>
</x-app-layout>