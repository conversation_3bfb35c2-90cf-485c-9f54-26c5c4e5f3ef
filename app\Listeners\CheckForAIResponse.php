<?php

namespace App\Listeners;

use App\Events\TicketMessageCreated;
use App\Services\AITicketService;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Support\Facades\Log;

class CheckForAIResponse implements ShouldQueue
{
    use InteractsWithQueue;

    /**
     * The AI ticket service.
     *
     * @var \App\Services\AITicketService
     */
    protected $aiTicketService;

    /**
     * Create the event listener.
     */
    public function __construct(AITicketService $aiTicketService)
    {
        $this->aiTicketService = $aiTicketService;
    }

    /**
     * Handle the event.
     */
    public function handle(TicketMessageCreated $event): void
    {
        $message = $event->message;
        $ticket = $message->ticket;

        Log::debug('CheckForAIResponse: Processing new message', [
            'ticket_id' => $ticket->id,
            'message_id' => $message->id,
            'message_source' => $message->message_source,
            'is_system_message' => $message->is_system_message,
            'user_id' => $message->user_id
        ]);

        // Skip if the message is from AI or is a system message
        if ($message->message_source === 'ai' || $message->is_system_message) {
            Log::debug('CheckForAIResponse: Skipping AI or system message', [
                'ticket_id' => $ticket->id,
                'message_id' => $message->id,
                'message_source' => $message->message_source,
                'is_system_message' => $message->is_system_message
            ]);
            return;
        }

        // Skip if the message is from a supporter
        if ($message->user && $message->user->isSupporter()) {
            Log::debug('CheckForAIResponse: Skipping message from supporter', [
                'ticket_id' => $ticket->id,
                'message_id' => $message->id,
                'user_id' => $message->user_id,
                'is_supporter' => true
            ]);
            return;
        }

        // Wir prüfen nicht mehr, ob die letzte Nachricht von der KI kam,
        // da dies verhindert, dass Benutzer Rückfragen stellen können.
        // Stattdessen prüfen wir nur, ob es in den letzten Sekunden eine KI-Nachricht gab,
        // um Endlosschleifen zu vermeiden.

        // Prüfe nur, ob es eine sehr kürzlich gesendete AI-Nachricht gab (innerhalb der letzten 2 Sekunden)
        // Dies verhindert nur Endlosschleifen, erlaubt aber Folgefragen nach einer kurzen Pause
        $veryRecentAiMessage = $ticket->messages()
            ->where('message_source', 'ai')
            ->where('created_at', '>=', now()->subSeconds(2))
            ->exists();

        if ($veryRecentAiMessage) {
            Log::debug('CheckForAIResponse: Skipping because there was a very recent AI message (within 2 seconds)', [
                'ticket_id' => $ticket->id,
                'message_id' => $message->id
            ]);
            return;
        }

        // Prüfe, ob die letzte Nachricht vom Benutzer ist, der das Ticket erstellt hat
        // Dies stellt sicher, dass die KI nur auf Nachrichten des Ticket-Erstellers antwortet
        if ($message->user_id !== $ticket->user_id) {
            Log::debug('CheckForAIResponse: Skipping because message is not from ticket creator', [
                'ticket_id' => $ticket->id,
                'message_id' => $message->id,
                'message_user_id' => $message->user_id,
                'ticket_user_id' => $ticket->user_id
            ]);
            return;
        }

        Log::debug('CheckForAIResponse: Message is from a regular user, checking AI availability', [
            'ticket_id' => $ticket->id,
            'message_id' => $message->id,
            'user_id' => $message->user_id,
            'gemini_consent' => $ticket->gemini_consent
        ]);

        // Check if AI assistance is available for this ticket
        if (!$this->aiTicketService->isAIAssistanceAvailable($ticket)) {
            Log::info('CheckForAIResponse: AI assistance not available for ticket', [
                'ticket_id' => $ticket->id,
                'message_id' => $message->id,
                'gemini_consent' => $ticket->gemini_consent,
                'assigned_to' => $ticket->assigned_to,
                'status' => $ticket->status
            ]);
            return;
        }

        // Generate an AI response
        try {
            Log::info('CheckForAIResponse: Generating AI response for ticket', [
                'ticket_id' => $ticket->id,
                'message_id' => $message->id,
            ]);

            $aiMessage = $this->aiTicketService->generateResponse($ticket);

            if ($aiMessage) {
                Log::info('CheckForAIResponse: AI response generated successfully', [
                    'ticket_id' => $ticket->id,
                    'original_message_id' => $message->id,
                    'ai_message_id' => $aiMessage->id
                ]);
            } else {
                Log::warning('CheckForAIResponse: AI response generation returned null', [
                    'ticket_id' => $ticket->id,
                    'message_id' => $message->id
                ]);
            }
        } catch (\Exception $e) {
            Log::error('CheckForAIResponse: Error generating AI response', [
                'ticket_id' => $ticket->id,
                'message_id' => $message->id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
        }
    }
}
