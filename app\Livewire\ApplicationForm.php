<?php

namespace App\Livewire;

use Livewire\Component;
use Illuminate\Validation\ValidationException;
use App\Http\Controllers\QuestionController;

class ApplicationForm extends Component
{
    public $name;
    public $professions = [];
    public $gender;
    public $pronouns;
    public $age;
    public $confirmation;
    public $otherProfession;
    public $tempProfessions = [];

    /**
     * Definiert die verfügbaren Berufsoptionen mit korrekter Übersetzung
     */
    protected function getProfessionOptions() {
        return [
            'actor' => __('application.profession_description.actor'),
            'actor_no_voice' => __('application.profession_description.actor_no_voice'),
            'voice_actor' => __('application.profession_description.voice_actor'),
            'builder' => __('application.profession_description.builder'),
            'designer' => __('application.profession_description.designer'),
            'cutter' => __('application.profession_description.cutter'),
            'cameraman' => __('application.profession_description.cameraman'),
            'developer' => __('application.profession_description.developer'),
            'modeler' => __('application.profession_description.modeler'),
            'music_producer' => __('application.profession_description.music_producer'),
            'other' => __('application.profession_description.other')
        ];
    }

    protected $professionOptions = [];

    /**
     * Initialisierung der Komponente
     */
    public function mount()
    {
        $this->tempProfessions = $this->professions;
        $this->professionOptions = $this->getProfessionOptions();
    }

    /**
     * Render-Methode für die View
     */
    public function render()
    {
        // Refresh profession options to ensure translations are up-to-date
        $this->professionOptions = $this->getProfessionOptions();

        return view('livewire.application-form', [
            'professionOptions' => $this->professionOptions
        ]);
    }

    /**
     * Umschaltet die Auswahl eines Berufs (an/aus)
     */
    public function toggleProfession($profession)
    {
        try {
            if (in_array($profession, $this->professions)) {
                $this->professions = array_values(array_diff($this->professions, [$profession]));
            } else {
                $this->professions[] = $profession;
            }

            $this->tempProfessions = $this->professions;

        } catch (ValidationException $e) {
            $this->tempProfessions = $this->professions;
            $this->addError('professions', $e->getMessage());
        }
    }

    /**
     * Validierungsregeln für das Formular
     */
    public function rules()
    {
        return [
            'name' => 'required|string|max:255',
            'professions' => 'required|array|min:1',
            'gender' => 'required|string',
            'pronouns' => 'nullable|string|max:50',
            'age' => 'required|integer|min:12|max:99',
            'confirmation' => 'required|accepted',
            'otherProfession' => 'nullable|required_if:professions,other|string|max:255',
        ];
    }

    /**
     * Validierungsnachrichten mit Übersetzungen und benutzerdefinierten Fehlermeldungen
     */
    public function messages()
    {
        return [
            // Custom German error messages
            'professions.required' => 'Bitte wähle mindestens eine Tätigkeit aus.',
            'professions.min' => 'Bitte wähle mindestens eine Tätigkeit aus.',
            'name.required' => 'Bitte gib deinen Namen an.',
            'age.required' => 'Bitte gib dein Alter an.',
            'age.min' => 'Du musst mindestens 12 Jahre alt sein.',
            'age.max' => 'Das angegebene Alter ist zu hoch.',
            'confirmation.required' => 'Bitte bestätige deine Verfügbarkeit.',
            'confirmation.accepted' => 'Du musst die Verfügbarkeit bestätigen.',
            'otherProfession.required_if' => 'Bitte gib deine andere Tätigkeit an.',
            'gender.required' => 'Bitte wähle ein Geschlecht aus.',

            // Translated messages (fallback)
            'name.string' => __('validation.string', ['attribute' => __('application.name')]),
            'name.max' => __('validation.max.string', ['attribute' => __('application.name'), 'max' => 255]),
            'professions.array' => __('validation.array', ['attribute' => __('application.professions')]),
            'gender.string' => __('validation.string', ['attribute' => __('application.gender')]),
            'pronouns.string' => __('validation.string', ['attribute' => __('application.pronouns')]),
            'pronouns.max' => __('validation.max.string', ['attribute' => __('application.pronouns'), 'max' => 50]),
            'age.integer' => __('validation.integer', ['attribute' => __('application.age')]),
            'otherProfession.string' => __('validation.string', ['attribute' => __('application.other_profession')]),
            'otherProfession.max' => __('validation.max.string', ['attribute' => __('application.other_profession'), 'max' => 255]),
        ];
    }

    /**
     * Weiterleitung zur nächsten Seite nach Absenden des Formulars
     */
    public function submit()
    {
        // Validiere die Eingabe nach den definierten Regeln
        $this->validate();

        // Weiterleitung zur Fragenseite
        return redirect()->route('questions');
    }
}
