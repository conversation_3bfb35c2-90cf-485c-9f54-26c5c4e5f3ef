#!/bin/bash

# Takedown script for Minewache website
# This script removes systemd services created by setup-production.sh

# Exit on error
set -e

# Check if running as root
if [ "$EUID" -ne 0 ]; then
  echo "Please run this script as root or with sudo"
  exit 1
fi

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[0;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to display colored messages
function echo_color() {
  local color=$1
  local message=$2
  echo -e "${color}${message}${NC}"
}

# Function to remove a systemd service
function remove_service() {
  local name=$1
  local service_file="/etc/systemd/system/minewache-${name}.service"
  
  echo_color $BLUE "Removing ${name} service..."
  
  # Check if service exists
  if [ ! -f "$service_file" ]; then
    echo_color $YELLOW "Service file $service_file does not exist. Skipping."
    return
  fi
  
  # Stop and disable the service
  echo_color $BLUE "Stopping and disabling minewache-${name}.service..."
  systemctl stop "minewache-${name}.service" || true
  systemctl disable "minewache-${name}.service" || true
  
  # Remove the service file
  echo_color $BLUE "Removing service file $service_file..."
  rm -f "$service_file"
  
  echo_color $GREEN "${name} service removed successfully"
}

# Main script execution
echo_color $BLUE "Starting Minewache production services takedown..."

# Ask for confirmation
echo_color $YELLOW "This script will remove all Minewache systemd services."
read -p "Are you sure you want to continue? (y/n): " confirm
if [[ "$confirm" != "y" && "$confirm" != "Y" ]]; then
  echo_color $RED "Takedown aborted."
  exit 1
fi

# Remove services
remove_service "queue"
remove_service "reverb"
remove_service "discord"

# Also check for and remove legacy services
remove_service "discord-role"
remove_service "discord-ticket"

# Reload systemd
echo_color $BLUE "Reloading systemd daemon..."
systemctl daemon-reload

# Ask if log files should be removed
echo_color $YELLOW "Do you want to remove the log files as well?"
read -p "Remove log files? (y/n): " remove_logs
if [[ "$remove_logs" == "y" || "$remove_logs" == "Y" ]]; then
  echo_color $BLUE "Removing log files..."
  rm -f /var/log/minewache-queue.log
  rm -f /var/log/minewache-queue-error.log
  rm -f /var/log/minewache-reverb.log
  rm -f /var/log/minewache-reverb-error.log
  rm -f /var/log/minewache-discord.log
  rm -f /var/log/minewache-discord-error.log
  # Legacy log files
  rm -f /var/log/minewache-discord-role.log
  rm -f /var/log/minewache-discord-role-error.log
  rm -f /var/log/minewache-discord-ticket.log
  rm -f /var/log/minewache-discord-ticket-error.log
  echo_color $GREEN "Log files removed successfully"
fi

echo_color $GREEN "Takedown complete! All Minewache systemd services have been removed."
echo_color $YELLOW "If you want to reinstall the services, run setup-production.sh"
