<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateRegisterYoutubeLinksTable extends Migration
{
    public function up()
    {
        Schema::create('register_youtube_links', function (Blueprint $table) {
            $table->id();
            $table->integer('season');
            $table->integer('episode');
            $table->string('link');
            $table->timestamps();
        });
    }

    public function down()
    {
        Schema::dropIfExists('register_youtube_links');
    }
}
