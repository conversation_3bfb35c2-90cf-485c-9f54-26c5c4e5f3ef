<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Cookie;

class GdprConsentMiddleware
{
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure  $next
     * @return mixed
     */
    public function handle(Request $request, Closure $next)
    {
        // Skip consent check for the consent routes themselves to avoid loops
        if ($request->is('auth/consent') || $request->is('auth/consent/*')) {
            return $next($request);
        }

        // Skip GDPR check for the login page if it already has the show_gdpr_consent flag
        if ($request->is('login') && session()->has('show_gdpr_consent')) {
            return $next($request);
        }

        // Only log in local environment to avoid unnecessary logging of personal data
        if (app()->environment('local')) {
            \Illuminate\Support\Facades\Log::debug("GDPR Consent Cookie: " . (<PERSON><PERSON>::has('gdpr_consent') ? 'Present' : 'Not Present'), [
                'session_id' => session()->getId(),
            ]);
        }

        // Check if this is a login-related route
        if (($request->is('login') || $request->is('auth/discord')) && !Cookie::has('gdpr_consent')) {
            // If this is the Discord auth route, store it with a special parameter
            // to indicate it's coming from consent
            if ($request->is('auth/discord')) {
                session(['url.intended' => route('auth.discord', ['from_consent' => 'true'])]);
            } else {
                // Store the intended URL to redirect back after consent
                session(['url.intended' => $request->fullUrl()]);
            }

            // Ensure the session is saved immediately
            session()->save();

            // Log for debugging
            if (config('app.debug')) {
                \Illuminate\Support\Facades\Log::debug("Storing intended URL for consent redirect", [
                    'url' => session('url.intended'),
                    'session_id' => session()->getId(),
                    'route' => $request->route() ? $request->route()->getName() : 'unknown'
                ]);
            }

            // Redirect to home page with GDPR consent modal
            return redirect()->route('home')->with('show_gdpr_consent', true);
        }

        // Special handling for the Discord callback
        if ($request->is('larascord/callback') && !Cookie::has('gdpr_consent')) {
            // For the callback, we'll set the GDPR consent cookie automatically
            // This prevents the redirect loop when returning from Discord
            $minutes = 60 * 24 * 30; // 30 days

            // Create a secure cookie
            $cookie = Cookie::make('gdpr_consent', 'true', $minutes);
            $cookie->withSecure(config('app.env') !== 'local'); // Secure in production
            $cookie->withHttpOnly(); // Not accessible via JavaScript
            $cookie->withSameSite('lax'); // Restrict cross-site requests
            Cookie::queue($cookie);

            if (config('app.debug')) {
                \Illuminate\Support\Facades\Log::debug('Auto-setting GDPR consent cookie for Discord callback', [
                    'session_id' => session()->getId(),
                    'fresh_login' => session('fresh_discord_login', false),
                    'login_timestamp' => session('discord_login_timestamp'),
                    'has_code' => $request->has('code')
                ]);
            }
        }

        return $next($request);
    }
}
