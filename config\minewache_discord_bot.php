<?php

use Discord\WebSockets\Intents;

return [
    'token' => env('DISCORD_BOT_TOKEN'),
    'guild_id' => env('LARASCORD_GUILD_ID'), // Consider renaming LARASCORD_GUILD_ID to DISCORD_GUILD_ID in .env later

    // Define the Gateway Intents your bot needs.
    // Refer to DiscordPHP documentation and Discord Developer Portal for available intents.
    // Common intents used by the previous Node.js bot:
    // Guilds, GuildMembers, GuildMessages, MessageContent, DirectMessages.
    // NFR-SEC-003: Request only necessary Discord Intents.
    'intents' => [
        Intents::GUILDS,
        Intents::GUILD_MEMBERS,
        Intents::GUILD_MESSAGES,
        Intents::MESSAGE_CONTENT, // Requires Privileged Intent toggle in Discord Dev Portal
        Intents::DIRECT_MESSAGES,
        // Add other intents like GUILD_PRESENCES if needed for specific features
    ],

    // Default activity for the bot
    'activity' => [
        'name' => env('DISCORD_BOT_ACTIVITY_NAME', 'Minewache'),
        'type' => env('DISCORD_BOT_ACTIVITY_TYPE', 'PLAYING'), // PLAYING, STREAMING, LISTENING, WATCHING, COMPETING
        // 'url' => env('DISCORD_BOT_STREAMING_URL', null), // Only for STREAMING type
    ],

    // Specific channel for bot-related logging within Laravel, if desired
    'log_channel' => env('DISCORD_BOT_LOG_CHANNEL', null),

    // Ticket system specific configurations
    'tickets' => [
        'category_id' => env('DISCORD_TICKET_CATEGORY_ID'),
        'support_role_ids' => explode(',', env('DISCORD_SUPPORT_ROLE_IDS', '')), // Comma-separated list of role IDs
        // Add other ticket related configs here if needed
    ],

    // Add other bot-specific configurations as they become necessary
];
