# Discord Bot API für Rollenzuweisung

## Erweiterung des bestehenden Nachrichtenendpunkts

Um die automatische Zuweisung von Rollen an Benutzer über den Discord-Bo<PERSON> zu ermöglichen, muss der bestehende API-Endpunkt für Direktnachrichten im Discord-Bot erweitert werden.

### Endpunkt: `/api/messages/send`

**Methode:** POST

**Authentifizierung:** <PERSON><PERSON> (API-Key)

**Request-Body:**
```json
{
  "user_id": "123456789012345678",
  "content": "_Rollenzuweisung_",
  "assign_roles": ["1183491860304494642", "1033491844715257866"]
}
```

**Parameter:**
- `user_id`: Die Discord-ID des Benutzers, dem die Rollen zugewiesen werden sollen
- `content`: Der Inhalt der Nachricht (kann eine Dummy-Nachricht sein)
- `assign_roles`: <PERSON> A<PERSON><PERSON> von <PERSON>-<PERSON>-IDs, die dem Benutzer zugewiesen werden sollen

**Erfolgreiche Antwort (200 OK):**
```json
{
  "success": true,
  "message": "Rollen erfolgreich zugewiesen",
  "user": {
    "id": "123456789012345678",
    "username": "BenutzerName",
    "roles_added": ["1183491860304494642", "1033491844715257866"]
  }
}
```

**Fehlerantwort (400 Bad Request):**
```json
{
  "success": false,
  "message": "Ungültige Discord-ID oder Rollen",
  "error": "Detaillierte Fehlermeldung"
}
```

**Fehlerantwort (404 Not Found):**
```json
{
  "success": false,
  "message": "Benutzer nicht gefunden",
  "error": "Der Benutzer mit der angegebenen Discord-ID wurde nicht gefunden"
}
```

**Fehlerantwort (500 Internal Server Error):**
```json
{
  "success": false,
  "message": "Interner Serverfehler",
  "error": "Detaillierte Fehlermeldung"
}
```

## Implementierung im Discord-Bot

Hier ist ein Beispiel für die Erweiterung des bestehenden Nachrichtenendpunkts in einem Node.js-basierten Discord-Bot:

```javascript
// Bestehender Endpunkt zum Senden von Direktnachrichten
router.post('/send', authenticateApiKey, async (req, res) => {
  try {
    const { user_id, content, assign_roles } = req.body;

    if (!user_id || !content) {
      return res.status(400).json({
        success: false,
        message: 'Ungültige Anfrage',
        error: 'user_id und content sind erforderlich'
      });
    }

    // Discord-Client (sollte bereits initialisiert sein)
    const client = req.app.get('discordClient');

    // Guild abrufen (Server-ID sollte konfiguriert sein)
    const guild = client.guilds.cache.get(process.env.GUILD_ID);

    if (!guild) {
      return res.status(500).json({
        success: false,
        message: 'Server nicht gefunden',
        error: 'Der Discord-Server wurde nicht gefunden'
      });
    }

    // Benutzer abrufen
    let member;
    try {
      member = await guild.members.fetch(user_id);

      if (!member) {
        return res.status(404).json({
          success: false,
          message: 'Benutzer nicht gefunden',
          error: 'Der Benutzer mit der angegebenen Discord-ID wurde nicht gefunden'
        });
      }
    } catch (memberError) {
      return res.status(404).json({
        success: false,
        message: 'Benutzer nicht gefunden',
        error: memberError.message
      });
    }

    // Nachricht senden
    try {
      await member.send(content);
      console.log(`Nachricht an ${member.user.username} (${user_id}) gesendet: ${content}`);
    } catch (dmError) {
      console.error(`Fehler beim Senden der Nachricht an ${user_id}:`, dmError);
      return res.status(500).json({
        success: false,
        message: 'Fehler beim Senden der Nachricht',
        error: dmError.message
      });
    }

    // Wenn Rollen zugewiesen werden sollen
    let roleResult = { roles_added: [], roles_failed: [] };

    if (assign_roles && Array.isArray(assign_roles) && assign_roles.length > 0) {
      console.log(`Versuche, Rollen für ${member.user.username} (${user_id}) zuzuweisen:`, assign_roles);

      // Rollen hinzufügen
      for (const roleId of assign_roles) {
        try {
          const role = await guild.roles.fetch(roleId);

          if (role) {
            await member.roles.add(role);
            roleResult.roles_added.push(roleId);
            console.log(`Rolle ${role.name} (${roleId}) für ${member.user.username} hinzugefügt`);
          } else {
            roleResult.roles_failed.push({ id: roleId, reason: 'Rolle nicht gefunden' });
            console.warn(`Rolle ${roleId} nicht gefunden`);
          }
        } catch (roleError) {
          roleResult.roles_failed.push({ id: roleId, reason: roleError.message });
          console.error(`Fehler beim Hinzufügen der Rolle ${roleId}:`, roleError);
        }
      }
    }

    return res.status(200).json({
      success: true,
      message: 'Nachricht erfolgreich gesendet' + (roleResult.roles_added.length > 0 ? ' und Rollen zugewiesen' : ''),
      user: {
        id: user_id,
        username: member.user.username,
        roles_added: roleResult.roles_added,
        roles_failed: roleResult.roles_failed
      }
    });

  } catch (error) {
    console.error('Fehler beim Senden der Nachricht:', error);

    return res.status(500).json({
      success: false,
      message: 'Interner Serverfehler',
      error: error.message
    });
  }
});
```

## Wichtige Hinweise zur Integration

1. Stellen Sie sicher, dass der Discord-Client mit den richtigen Intents initialisiert wird:

```javascript
const client = new Client({
  intents: [
    GatewayIntentBits.Guilds,
    GatewayIntentBits.GuildMembers,
    GatewayIntentBits.GuildMessages,
    GatewayIntentBits.DirectMessages,
    // Weitere benötigte Intents
  ]
});
```

2. Aktivieren Sie die Privileged Gateway Intents im Discord Developer Portal:
   - SERVER MEMBERS INTENT
   - MESSAGE CONTENT INTENT

3. Stellen Sie sicher, dass der Bot die nötigen Berechtigungen auf dem Server hat:
   - MANAGE_ROLES (Rollen verwalten)
   - SEND_MESSAGES (Nachrichten senden)
   - Wichtig: Die Rolle des Bots muss in der Rollenhierarchie über den Rollen stehen, die er zuweisen soll

## Testen des Endpunkts

Sie können den Endpunkt mit einem Tool wie Postman oder curl testen:

```bash
curl -X POST \
  http://localhost:3000/api/messages/send \
  -H 'Authorization: Bearer IhrGeheimesAPIKey' \
  -H 'Content-Type: application/json' \
  -d '{
    "user_id": "123456789012345678",
    "content": "_Rollenzuweisung_",
    "assign_roles": ["1183491860304494642", "1033491844715257866"]
  }'
```

## Debugging-Tipps

1. Überprüfen Sie die Logs des Discord-Bots, um zu sehen, ob die Anfrage ankommt und wie sie verarbeitet wird.

2. Stellen Sie sicher, dass die Discord-ID korrekt ist. Sie können die ID eines Benutzers in Discord überprüfen, indem Sie den Entwicklermodus aktivieren und dann mit der rechten Maustaste auf den Benutzer klicken.

3. Überprüfen Sie, ob die Rollen-IDs korrekt sind und ob der Bot die Berechtigung hat, diese Rollen zuzuweisen.

4. Testen Sie zunächst mit einer einfachen Nachricht ohne Rollenzuweisung, um zu prüfen, ob der Bot überhaupt erreichbar ist:

```bash
curl -X POST \
  http://localhost:3000/api/messages/send \
  -H 'Authorization: Bearer IhrGeheimesAPIKey' \
  -H 'Content-Type: application/json' \
  -d '{
    "user_id": "123456789012345678",
    "content": "Test-Nachricht"
  }'
```

## Sicherheitshinweise

- Verwenden Sie einen sicheren API-Key und speichern Sie ihn in Umgebungsvariablen
- Implementieren Sie Rate-Limiting, um Missbrauch zu verhindern
- Protokollieren Sie alle API-Aufrufe für Audit-Zwecke
- Beschränken Sie den Zugriff auf den Endpunkt auf vertrauenswürdige IP-Adressen
