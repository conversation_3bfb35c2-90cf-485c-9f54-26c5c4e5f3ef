<?php

namespace App\Http\Controllers;

use App\Enums\Role;
use App\Models\User;
use App\Services\PermissionSyncService; // Add this
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Gate;
use Illuminate\Support\Facades\Http; // Keep if still used for direct bot calls (legacy)
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Response;
use Illuminate\Support\Facades\Session;

class PermissionController extends Controller
{
    protected PermissionSyncService $permissionSyncService;

    public function __construct(PermissionSyncService $permissionSyncService)
    {
        $this->permissionSyncService = $permissionSyncService;
    }

    /**
     * Initialisiert die Berechtigungen für einen Benutzer nach dem Discord-Login.
     * Prüft auch, ob der Benutzer Mitglied des Discord-Servers ist.
     *
     * @param Request $request
     * @return \Illuminate\Http\RedirectResponse
     */
    public function initialize(Request $request)
    {
        $user = $request->user();

        try {
            Log::info("PermissionController: Initializing permissions for user {$user->id} ({$user->name}) via web flow.");

            $guildId = config('services.discord.guild_id');
            $discordRoleIds = []; // Initialize as empty array

            if (app()->environment('local') && Session::has('simulate_discord_membership')) {
                // ... (simulation logic remains largely the same) ...
                if (Session::get('simulate_discord_membership') === false) {
                    return redirect()->route('discord.membership.required');
                }
                // Simulated roles (ensure these are IDs)
                $discordRoleIds = ['1071024925437067294', '1335685797080465434']; // Example MINEWACHE_TEAM Role IDs
                Log::debug("PermissionController: Using simulated Discord roles for user {$user->id}: " . implode(', ', $discordRoleIds));
            } else {
                try {
                    $guildMember = $user->getGuildMember($guildId);
                    if (!$guildMember) {
                        Log::warning("PermissionController: User {$user->id} is not a member of the Discord server {$guildId}.");
                        return redirect()->route('discord.membership.required');
                    }
                    $discordRoleIds = $guildMember->roles ?? []; // These are role IDs from Larascord
                    Log::debug("PermissionController: Fetched Discord roles for user {$user->id} from guild {$guildId}: " . implode(', ', $discordRoleIds));
                } catch (\Exception $e) {
                    Log::error("PermissionController: Error checking Discord membership for user {$user->id}.", ['error' => $e->getMessage()]);
                    if (app()->environment('local')) {
                        $discordRoleIds = ['1071024925437067294', '1335685797080465434']; // Fallback for local
                        Log::warning("PermissionController: Using fallback roles for user {$user->id} due to error: " . implode(', ', $discordRoleIds));
                    } else {
                        return redirect()->route('discord.membership.required')->with('error', 'Fehler bei der Überprüfung der Discord-Mitgliedschaft.');
                    }
                }
            }

            // Use the PermissionSyncService to update permissions
            // $user->id is the Discord ID here
            $syncSuccess = $this->permissionSyncService->syncUserPermissionsFromDiscordRoles($user->id, $discordRoleIds);

            if ($syncSuccess) {
                Log::info("PermissionController: Permissions successfully initialized/updated for user {$user->id} via PermissionSyncService.");
            } else {
                Log::error("PermissionController: PermissionSyncService failed to update permissions for user {$user->id}.");
                // Decide if this should be a hard error for the user or just logged
            }
            
            // The old AdminController->syncRoles call to the Node.js bot is no longer needed here
            // as role changes are now event-driven from the new PHP bot.
            // If there was other logic in AdminController->syncRoles, it needs to be re-evaluated.

            return redirect('/bewerben')->with('success', 'Berechtigungen erfolgreich synchronisiert');

        } catch (\Exception $e) {
            Log::error("PermissionController: Failed to initialize permissions for user {$user->id}: {$e->getMessage()}", [
                'trace' => $e->getTraceAsString()
            ]);
            return redirect('/bewerben')->with('error', 'Fehler bei der Synchronisation der Berechtigungen');
        }
    }

    /**
     * Manuelle Synchronisation der Berechtigungen eines bestimmten Benutzers.
     *
     * @param User $user The Laravel User model instance (where $user->id is Discord ID)
     * @return \Illuminate\Http\RedirectResponse
     */
    public function syncRoles(User $user) // $user->id is the Discord ID
    {
        Gate::authorize('MINEWACHE_TEAM'); // Or appropriate gate

        try {
            Log::info("PermissionController: Manually syncing permissions for user {$user->id} ({$user->name}).");
            $guildId = config('services.discord.guild_id');
            $discordRoleIds = [];

            try {
                $guildMember = $user->getGuildMember($guildId); // Larascord's User model method
                if (!$guildMember) {
                    Log::warning("PermissionController: User {$user->id} is not a member of guild {$guildId} for manual sync.");
                    return back()->with('error', "Benutzer {$user->username} ist kein Mitglied des Discord-Servers.");
                }
                $discordRoleIds = $guildMember->roles ?? [];
                Log::debug("PermissionController: Fetched Discord roles for manual sync of user {$user->id}: " . implode(', ', $discordRoleIds));
            } catch (\Exception $e) {
                Log::error("PermissionController: Error fetching Discord roles for manual sync of user {$user->id}.", ['error' => $e->getMessage()]);
                return back()->with('error', "Fehler bei der Überprüfung der Discord-Mitgliedschaft: {$e->getMessage()}");
            }

            // Use the PermissionSyncService
            $syncSuccess = $this->permissionSyncService->syncUserPermissionsFromDiscordRoles($user->id, $discordRoleIds);

            if ($syncSuccess) {
                Log::info("PermissionController: Permissions successfully updated via manual sync for user {$user->id}.");
                return back()->with('success', "Berechtigungen für {$user->username} aktualisiert.");
            } else {
                Log::error("PermissionController: PermissionSyncService failed to update permissions for manual sync of user {$user->id}.");
                return back()->with('error', "Fehler beim Aktualisieren der Berechtigungen für {$user->username}.");
            }

        } catch (\Exception $e) {
            Log::error("PermissionController: Failed to manually sync permissions for user {$user->id}: {$e->getMessage()}", ['trace' => $e->getTraceAsString()]);
            return back()->with('error', 'Fehler bei der Synchronisation.');
        }
    }

    /**
     * Aktualisiert die Berechtigungen eines Benutzers basierend auf Discord-ID.
     * Legacy endpoint, previously called by the Node.js Discord-Bot.
     * The new PHP bot uses an event-driven approach (UserRolesChanged event).
     * This endpoint can be kept for backward compatibility or specific cases but is not the primary sync mechanism anymore.
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     * @deprecated This method is for the old bot. New bot uses event UserRolesChanged.
     */
    public function updateRolesFromBot(Request $request)
    {
        Log::info("PermissionController: Received role update request from external source (legacy bot API).", [
            'content' => $request->getContent(),
            'ip' => $request->ip(),
        ]);

        $validatedData = $request->validate([
            'user_id' => 'required|string',
            // The old bot might not send roles; this endpoint used to fetch them itself.
            // If roles are sent, they can be validated: 'roles' => 'sometimes|array'
        ]);

        $discordUserId = $validatedData['user_id'];
        $discordRoleIds = [];

        // This endpoint historically fetched roles itself.
        // For simplicity in refactoring, we'll assume it needs to continue doing so if called.
        // However, this is inefficient if the calling bot already has the roles.
        // The new event-driven flow is preferred.
        $laravelUser = User::where('id', $discordUserId)->first(); // Assuming User->id is Discord ID
        if (!$laravelUser) {
            Log::warning("PermissionController (updateRolesFromBot): User not found for Discord ID: {$discordUserId}");
            return response()->json(['success' => false, 'message' => "User mit Discord-ID {$discordUserId} nicht gefunden"], 404);
        }

        try {
            $guildId = config('services.discord.guild_id');
            $guildMember = $laravelUser->getGuildMember($guildId); // Requires Larascord User Trait
            if ($guildMember) {
                $discordRoleIds = $guildMember->roles ?? [];
                Log::debug("PermissionController (updateRolesFromBot): Fetched roles for {$discordUserId}: " . implode(', ', $discordRoleIds));
            } else {
                 Log::warning("PermissionController (updateRolesFromBot): User {$discordUserId} not found in guild {$guildId}. Permissions might be set to none.");
                 // $discordRoleIds remains empty, which will result in minimal/no permissions
            }
        } catch (\Exception $e) {
            Log::error("PermissionController (updateRolesFromBot): Error fetching roles for {$discordUserId}.", ['exception' => $e]);
            // Depending on policy, either fail or proceed with empty roles
            return response()->json(['success' => false, 'message' => "Fehler beim Abrufen der Discord-Rollen: " . $e->getMessage()], 500);
        }
        
        $syncSuccess = $this->permissionSyncService->syncUserPermissionsFromDiscordRoles($discordUserId, $discordRoleIds);

        if ($syncSuccess) {
            return response()->json(['success' => true, 'message' => "Berechtigungen für Benutzer {$discordUserId} aktualisiert."]);
        } else {
            return response()->json(['success' => false, 'message' => "Fehler beim Aktualisieren der Berechtigungen für {$discordUserId}."], 500);
        }
    }

    // getRoleMapping and calculatePermissionsBitmask are now in PermissionSyncService
    // getGuildId can be removed if not used elsewhere, or kept if still useful for this controller specifically.
    // For now, let's assume it's not needed here if all logic goes through the service.

    // debug method can remain as is if it's useful
    public function debug(Request $request)
    {
        // ... (keep existing debug method if needed) ...
        if (!app()->environment('local')) {
            abort(404);
        }
        $user = $request->user();
        $bitmask = $user->permissions; // Assuming $user->permissions is already up-to-date
        $roleMapping = $this->permissionSyncService->getRoleMapping(); // Use service for mapping
        $effectiveRoles = [];
        foreach ($roleMapping as $discordRoleId => $roleDetails) {
            if (($bitmask & $roleDetails['value']) === $roleDetails['value']) {
                $effectiveRoles[$discordRoleId] = $roleDetails['name'];
            }
        }
        return response()->json([
            'user' => $user->only(['id', 'name']), // Assuming 'id' is Discord ID
            'permissions_raw_bitmask' => $bitmask,
            'permissions_binary' => $bitmask ? decbin($bitmask) : '0',
            'effective_laravel_roles' => $effectiveRoles,
            'last_synced_at' => $user->last_synced_at ? $user->last_synced_at->toDateTimeString() : null,
            'role_id_to_laravel_enum_mapping' => $this->permissionSyncService->getRoleMapping()
        ]);
    }
    
    // getUserRolesFromLocalBot and syncUserRolesFromLocalBot are related to the old bot and can be removed.
    // If any part of their logic is still needed (e.g. as an admin tool but for the new system),
    // it would need significant rethinking. For now, assume they are deprecated with the old bot.
}
