<?php

namespace App\Services;

use App\Models\Ticket;
use App\Models\TicketMessage; // If closure reason is stored as a message
use App\Models\User;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Auth; // To get current web user if applicable for reason
use Exception;

class TicketUpdateService
{
    /**
     * Closes a ticket in the database.
     *
     * @param int $ticketDbId The database ID of the ticket.
     * @param string|null $reason The reason for closing the ticket.
     * @param string|null $closedByDiscordUserId The Discord ID of the user who initiated the closure.
     * @return Ticket|null The updated Ticket model or null on failure.
     */
    public function closeTicket(int $ticketDbId, ?string $reason = null, ?string $closedByDiscordUserId = null): ?Ticket
    {
        $ticket = Ticket::find($ticketDbId);
        if (!$ticket) {
            Log::error("TicketUpdateService: Ticket #{$ticketDbId} not found for closure.");
            return null;
        }

        if ($ticket->status === Ticket::STATUS_CLOSED) { // Assuming STATUS_CLOSED constant
            Log::info("TicketUpdateService: Ticket #{$ticketDbId} is already closed.");
            return $ticket; // Or handle as an error/warning if needed
        }

        try {
            $ticket->status = Ticket::STATUS_CLOSED;
            // Optionally store the closure reason and closer info on the ticket model itself if it has fields for them
            // $ticket->closure_reason = $reason;
            // $ticket->closed_by_discord_id = $closedByDiscordUserId;
            $ticket->save();

            // Optionally, add the reason as a system message in the ticket
            if (!empty($reason)) {
                // Determine the user_id for the message (bot, system, or specific user)
                // For now, let's assume null user_id for system messages, or find a dedicated bot/system user.
                $systemUserId = null; // Or User::where('name', 'System')->first()->id;

                TicketMessage::create([
                    'ticket_id' => $ticket->id,
                    'user_id' => $systemUserId,
                    'discord_user_id' => $closedByDiscordUserId, // User who triggered close via Discord
                    'content' => "Ticket closed. Reason: " . $reason,
                    'source' => TicketMessage::SOURCE_SYSTEM, // Assuming SOURCE_SYSTEM constant
                    'is_system_message' => true,
                ]);
            }

            Log::info("TicketUpdateService: Ticket #{$ticketDbId} closed successfully.");
            return $ticket;
        } catch (Exception $e) {
            Log::error("TicketUpdateService: Failed to close ticket #{$ticketDbId}.", ['exception' => $e]);
            return null;
        }
    }

    /**
     * Updates the status of a ticket (e.g., to 'in_progress').
     *
     * @param int $ticketDbId The database ID of the ticket.
     * @param string $newStatus The new status.
     * @param string|null $updatedByDiscordUserId The Discord ID of the user who initiated the status update.
     * @return Ticket|null The updated Ticket model or null on failure.
     */
    public function updateTicketStatus(int $ticketDbId, string $newStatus, ?string $updatedByDiscordUserId = null): ?Ticket
    {
        $ticket = Ticket::find($ticketDbId);
        if (!$ticket) {
            Log::error("TicketUpdateService: Ticket #{$ticketDbId} not found for status update.");
            return null;
        }

        try {
            $oldStatus = $ticket->status;
            $ticket->status = $newStatus;
            $ticket->save();

            // Optionally, add a system message for status change
            TicketMessage::create([
                'ticket_id' => $ticket->id,
                'user_id' => null, // System message
                'discord_user_id' => $updatedByDiscordUserId,
                'content' => "Ticket status changed from {$oldStatus} to {$newStatus}.",
                'source' => TicketMessage::SOURCE_SYSTEM,
                'is_system_message' => true,
            ]);

            Log::info("TicketUpdateService: Ticket #{$ticketDbId} status updated to {$newStatus}.");
            return $ticket;
        } catch (Exception $e) {
            Log::error("TicketUpdateService: Failed to update status for ticket #{$ticketDbId}.", ['exception' => $e]);
            return null;
        }
    }
}
