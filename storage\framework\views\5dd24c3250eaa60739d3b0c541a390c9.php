<?php $attributes ??= new \Illuminate\View\ComponentAttributeBag;

$__newAttributes = [];
$__propNames = \Illuminate\View\ComponentAttributeBag::extractPropNames(([
    'ticket',
    'isSupporter' => false,
    'compact' => false
]));

foreach ($attributes->all() as $__key => $__value) {
    if (in_array($__key, $__propNames)) {
        $$__key = $$__key ?? $__value;
    } else {
        $__newAttributes[$__key] = $__value;
    }
}

$attributes = new \Illuminate\View\ComponentAttributeBag($__newAttributes);

unset($__propNames);
unset($__newAttributes);

foreach (array_filter(([
    'ticket',
    'isSupporter' => false,
    'compact' => false
]), 'is_string', ARRAY_FILTER_USE_KEY) as $__key => $__value) {
    $$__key = $$__key ?? $__value;
}

$__defined_vars = get_defined_vars();

foreach ($attributes->all() as $__key => $__value) {
    if (array_key_exists($__key, $__defined_vars)) unset($$__key);
}

unset($__defined_vars); ?>

<?php
$actions = [];

// View action - always available
$actions[] = [
    'label' => __('tickets.view'),
    'href' => route('tickets.show', $ticket),
    'icon' => '<svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path></svg>',
    'variant' => 'ghost',
    'primary' => true
];

// Supporter actions
if ($isSupporter) {
    // Edit/Assign action
    $actions[] = [
        'label' => __('tickets.manage'),
        'href' => route('tickets.show', $ticket) . '#manage',
        'icon' => '<svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path></svg>',
        'variant' => 'outline'
    ];

    // Status actions based on current status
    if ($ticket->status === 'open') {
        $actions[] = [
            'label' => __('tickets.mark_in_progress'),
            'action' => 'setStatus',
            'params' => ['in_progress'],
            'icon' => '<svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path></svg>',
            'variant' => 'secondary'
        ];
    } elseif ($ticket->status === 'in_progress') {
        $actions[] = [
            'label' => __('tickets.mark_closed'),
            'action' => 'setStatus',
            'params' => ['closed'],
            'icon' => '<svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path></svg>',
            'variant' => 'success'
        ];
    } elseif ($ticket->status === 'closed') {
        $actions[] = [
            'label' => __('tickets.reopen'),
            'action' => 'setStatus',
            'params' => ['open'],
            'icon' => '<svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 11V7a4 4 0 118 0m-4 8v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2z"></path></svg>',
            'variant' => 'primary'
        ];
    }
}

$containerClasses = $compact ? 'flex items-center gap-2' : 'flex flex-col sm:flex-row gap-2';
?>

<div class="<?php echo e($containerClasses); ?>">
    <!--[if BLOCK]><![endif]--><?php $__currentLoopData = $actions; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $action): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
        <!--[if BLOCK]><![endif]--><?php if(isset($action['href'])): ?>
            
            <?php if (isset($component)) { $__componentOriginal19987cd45c699eefead8b7a8ac222798 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal19987cd45c699eefead8b7a8ac222798 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.modern-button','data' => ['variant' => ''.e($action['variant']).'','size' => ''.e($compact ? 'sm' : 'md').'','href' => ''.e($action['href']).'','icon' => ''.$action['icon'].'','class' => ''.e(isset($action['primary']) ? 'order-first' : '').'']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('modern-button'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['variant' => ''.e($action['variant']).'','size' => ''.e($compact ? 'sm' : 'md').'','href' => ''.e($action['href']).'','icon' => ''.$action['icon'].'','class' => ''.e(isset($action['primary']) ? 'order-first' : '').'']); ?>
                <!--[if BLOCK]><![endif]--><?php if(!$compact): ?>
                    <?php echo e($action['label']); ?>

                <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
             <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal19987cd45c699eefead8b7a8ac222798)): ?>
<?php $attributes = $__attributesOriginal19987cd45c699eefead8b7a8ac222798; ?>
<?php unset($__attributesOriginal19987cd45c699eefead8b7a8ac222798); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal19987cd45c699eefead8b7a8ac222798)): ?>
<?php $component = $__componentOriginal19987cd45c699eefead8b7a8ac222798; ?>
<?php unset($__componentOriginal19987cd45c699eefead8b7a8ac222798); ?>
<?php endif; ?>
        <?php elseif(isset($action['action'])): ?>
            
            <?php
                $wireParams = isset($action['params']) ? implode(',', array_map(fn($p) => "'{$p}'", $action['params'])) : '';
                $wireClick = $action['action'] . '(' . $wireParams . ')';
            ?>
            <?php if (isset($component)) { $__componentOriginal19987cd45c699eefead8b7a8ac222798 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal19987cd45c699eefead8b7a8ac222798 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.modern-button','data' => ['variant' => ''.e($action['variant']).'','size' => ''.e($compact ? 'sm' : 'md').'','wire:click' => ''.e($wireClick).'','icon' => ''.$action['icon'].'']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('modern-button'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['variant' => ''.e($action['variant']).'','size' => ''.e($compact ? 'sm' : 'md').'','wire:click' => ''.e($wireClick).'','icon' => ''.$action['icon'].'']); ?>
                <?php if(!$compact): ?>
                    <?php echo e($action['label']); ?>

                <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
             <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal19987cd45c699eefead8b7a8ac222798)): ?>
<?php $attributes = $__attributesOriginal19987cd45c699eefead8b7a8ac222798; ?>
<?php unset($__attributesOriginal19987cd45c699eefead8b7a8ac222798); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal19987cd45c699eefead8b7a8ac222798)): ?>
<?php $component = $__componentOriginal19987cd45c699eefead8b7a8ac222798; ?>
<?php unset($__componentOriginal19987cd45c699eefead8b7a8ac222798); ?>
<?php endif; ?>
        <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>

    
    <!--[if BLOCK]><![endif]--><?php if($ticket->messages->count() > 0): ?>
        <?php if (isset($component)) { $__componentOriginal7c5961ba4242859e8f30202299af2419 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal7c5961ba4242859e8f30202299af2419 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.modern-status-badge','data' => ['status' => 'default','size' => ''.e($compact ? 'xs' : 'sm').'','variant' => 'filled','class' => 'ml-auto']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('modern-status-badge'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['status' => 'default','size' => ''.e($compact ? 'xs' : 'sm').'','variant' => 'filled','class' => 'ml-auto']); ?>
            <?php echo e($ticket->messages->count()); ?>

         <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal7c5961ba4242859e8f30202299af2419)): ?>
<?php $attributes = $__attributesOriginal7c5961ba4242859e8f30202299af2419; ?>
<?php unset($__attributesOriginal7c5961ba4242859e8f30202299af2419); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal7c5961ba4242859e8f30202299af2419)): ?>
<?php $component = $__componentOriginal7c5961ba4242859e8f30202299af2419; ?>
<?php unset($__componentOriginal7c5961ba4242859e8f30202299af2419); ?>
<?php endif; ?>
    <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
</div>

<style>
    /* Responsive adjustments */
    @media (max-width: 640px) {
        .modern-ticket-actions {
            flex-direction: column;
            align-items: stretch;
        }

        .modern-ticket-actions .modern-button {
            justify-content: center;
        }
    }

    /* Theme-specific styling */
    [data-theme="minewache-high-contrast"] .modern-ticket-actions .modern-button {
        border-width: 2px;
        font-weight: bold;
    }
</style>
<?php /**PATH C:\Users\<USER>\Documents\GitHub\minewache-website\resources\views/components/modern-ticket-actions.blade.php ENDPATH**/ ?>