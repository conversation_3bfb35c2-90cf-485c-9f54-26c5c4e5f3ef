/**
 * Message Media Processor
 * Detects and processes URLs in messages to embed media content
 */

class MessageMediaProcessor {
    constructor() {
        // Regular expressions for detecting different types of content
        this.urlRegex = /(https?:\/\/[^\s]+)/gi;
        this.imageRegex = /\.(jpeg|jpg|gif|png|webp)(\?.*)?$/i;
        this.videoRegex = /\.(mp4|webm|ogg|mov)(\?.*)?$/i;
        this.audioRegex = /\.(mp3|wav|ogg|opus|aac|m4a)(\?.*)?$/i;
        this.youtubeRegex = /(?:youtube\.com\/(?:[^\/]+\/.+\/|(?:v|e(?:mbed)?)\/|.*[?&]v=)|youtu\.be\/)([^"&?\/\s]{11})/i;
    }

    /**
     * Process a message to detect and embed media
     * @param {string} message - The message text to process
     * @returns {string} - HTML with embedded media
     */
    processMessage(message) {
        if (!message) return '';

        // First, escape HTML to prevent XSS
        let processedMessage = this.escapeHtml(message);

        // Replace URLs with clickable links and embedded media
        processedMessage = processedMessage.replace(this.urlRegex, (url) => {
            // Check if it's a media URL
            if (this.isImageUrl(url)) {
                return this.createImageEmbed(url);
            } else if (this.isVideoUrl(url)) {
                return this.createVideoEmbed(url);
            } else if (this.isAudioUrl(url)) {
                return this.createAudioEmbed(url);
            } else if (this.isYoutubeUrl(url)) {
                return this.createYoutubeEmbed(url);
            } else {
                // Regular link
                return `<a href="${url}" target="_blank" rel="noopener noreferrer" class="text-blue-600 dark:text-blue-400 hover:underline break-all">${url}</a>`;
            }
        });

        // Convert line breaks to <br> tags
        processedMessage = processedMessage.replace(/\n/g, '<br>');

        return processedMessage;
    }

    /**
     * Escape HTML special characters
     * @param {string} html - The HTML string to escape
     * @returns {string} - Escaped HTML
     */
    escapeHtml(html) {
        const div = document.createElement('div');
        div.textContent = html;
        return div.innerHTML;
    }

    /**
     * Check if a URL is an image
     * @param {string} url - The URL to check
     * @returns {boolean} - True if the URL is an image
     */
    isImageUrl(url) {
        return this.imageRegex.test(url);
    }

    /**
     * Check if a URL is a video
     * @param {string} url - The URL to check
     * @returns {boolean} - True if the URL is a video
     */
    isVideoUrl(url) {
        return this.videoRegex.test(url);
    }

    /**
     * Check if a URL is an audio file
     * @param {string} url - The URL to check
     * @returns {boolean} - True if the URL is an audio file
     */
    isAudioUrl(url) {
        return this.audioRegex.test(url);
    }

    /**
     * Check if a URL is a YouTube video
     * @param {string} url - The URL to check
     * @returns {boolean} - True if the URL is a YouTube video
     */
    isYoutubeUrl(url) {
        return this.youtubeRegex.test(url);
    }

    /**
     * Create an image embed
     * @param {string} url - The image URL
     * @returns {string} - HTML for the image embed
     */
    createImageEmbed(url) {
        return `
            <div class="mt-2 mb-2">
                <a href="${url}" target="_blank" rel="noopener noreferrer" class="block">
                    <img src="${url}" alt="Embedded image" class="max-w-full max-h-[300px] rounded-lg object-contain hover:opacity-90 transition-opacity cursor-zoom-in" onclick="openImageModal('${url}', 'Embedded image')">
                </a>
                <div class="text-xs text-gray-500 dark:text-gray-400 mt-1">
                    <a href="${url}" target="_blank" rel="noopener noreferrer" class="text-blue-600 dark:text-blue-400 hover:underline break-all">${url}</a>
                </div>
            </div>
        `;
    }

    /**
     * Create a video embed
     * @param {string} url - The video URL
     * @returns {string} - HTML for the video embed
     */
    createVideoEmbed(url) {
        return `
            <div class="mt-2 mb-2">
                <div class="relative group w-full max-w-[300px]">
                    <video controls class="w-full rounded-lg">
                        <source src="${url}" type="video/mp4">
                        Video not supported
                    </video>
                    <div class="flex justify-between items-center mt-1 text-xs">
                        <button onclick="openVideoModal('${url}', 'Embedded video')" class="text-blue-600 dark:text-blue-400 hover:underline">
                            Fullscreen
                        </button>
                        <a href="${url}" target="_blank" rel="noopener noreferrer" class="text-blue-600 dark:text-blue-400 hover:underline">
                            Download
                        </a>
                    </div>
                </div>
                <div class="text-xs text-gray-500 dark:text-gray-400 mt-1">
                    <a href="${url}" target="_blank" rel="noopener noreferrer" class="text-blue-600 dark:text-blue-400 hover:underline break-all">${url}</a>
                </div>
            </div>
        `;
    }

    /**
     * Create an audio embed
     * @param {string} url - The audio URL
     * @returns {string} - HTML for the audio embed
     */
    createAudioEmbed(url) {
        return `
            <div class="mt-2 mb-2">
                <div class="w-full max-w-[300px]">
                    <audio controls class="w-full">
                        <source src="${url}" type="audio/mpeg">
                        Audio not supported
                    </audio>
                    <div class="flex justify-between items-center mt-1 text-xs">
                        <span class="truncate max-w-[180px]">Audio file</span>
                        <a href="${url}" target="_blank" rel="noopener noreferrer" class="text-blue-600 dark:text-blue-400 hover:underline">
                            Download
                        </a>
                    </div>
                </div>
                <div class="text-xs text-gray-500 dark:text-gray-400 mt-1">
                    <a href="${url}" target="_blank" rel="noopener noreferrer" class="text-blue-600 dark:text-blue-400 hover:underline break-all">${url}</a>
                </div>
            </div>
        `;
    }

    /**
     * Create a YouTube embed
     * @param {string} url - The YouTube URL
     * @returns {string} - HTML for the YouTube embed
     */
    createYoutubeEmbed(url) {
        const match = url.match(this.youtubeRegex);
        if (!match) return this.createLink(url);

        const videoId = match[1];
        const embedUrl = `https://www.youtube.com/embed/${videoId}`;

        return `
            <div class="mt-2 mb-2">
                <div class="relative w-full max-w-[300px] aspect-video">
                    <iframe
                        src="${embedUrl}"
                        class="absolute top-0 left-0 w-full h-full rounded-lg"
                        frameborder="0"
                        allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
                        allowfullscreen>
                    </iframe>
                </div>
                <div class="text-xs text-gray-500 dark:text-gray-400 mt-1">
                    <a href="${url}" target="_blank" rel="noopener noreferrer" class="text-blue-600 dark:text-blue-400 hover:underline break-all">${url}</a>
                </div>
            </div>
        `;
    }

    /**
     * Create a regular link
     * @param {string} url - The URL
     * @returns {string} - HTML for the link
     */
    createLink(url) {
        return `<a href="${url}" target="_blank" rel="noopener noreferrer" class="text-blue-600 dark:text-blue-400 hover:underline break-all">${url}</a>`;
    }
}

// Initialize the processor
const messageMediaProcessor = new MessageMediaProcessor();

// Process all messages when the DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    processAllMessages();

    // Set up a MutationObserver to process new messages
    const messagesContainer = document.getElementById('messages-container');
    if (messagesContainer) {
        const observer = new MutationObserver(function(mutations) {
            mutations.forEach(function(mutation) {
                if (mutation.type === 'childList' && mutation.addedNodes.length > 0) {
                    processAllMessages();
                }
            });
        });

        observer.observe(messagesContainer, { childList: true, subtree: true });
    }
});

// Process all messages in the container
function processAllMessages() {
    const messageElements = document.querySelectorAll('.message-content');
    messageElements.forEach(function(element) {
        if (!element.dataset.processed) {
            const originalText = element.textContent;
            // Store the original text for copying
            element.setAttribute('data-original-text', originalText);
            element.innerHTML = messageMediaProcessor.processMessage(originalText);
            element.dataset.processed = 'true';
        }
    });
}

// Process a single message
function processMessage(text) {
    return messageMediaProcessor.processMessage(text);
}

// Add support for Livewire updates
if (window.Livewire) {
    window.Livewire.hook('message.processed', () => {
        processAllMessages();
    });
}
