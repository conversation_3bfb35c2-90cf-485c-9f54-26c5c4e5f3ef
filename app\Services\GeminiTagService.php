<?php

namespace App\Services;

use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Config;
use Exception;

class GeminiTagService
{
    protected $apiKey;
    protected $modelName;

    public const MANDATORY_HASHTAG = '#minewache'; // Brand hashtag

    public function __construct()
    {
        $this->apiKey = Config::get('services.gemini.api_key');
        $this->modelName = Config::get('services.gemini.model_name', 'gemini-1.5-flash-001');

        // For testing purposes, don't throw an exception if API key is missing
        // The actual API call will handle the missing key appropriately
        if (!$this->apiKey && !app()->environment('testing')) {
            throw new Exception('Gemini API key is not configured in services.gemini.api_key');
        }
    }

    protected function getSystemPrompt(): string
    {
        // This prompt is adapted from the TypeScript version.
        // Ensure MANDATORY_HASHTAG is correctly interpolated if it's dynamic.
        $m_hashtag = self::MANDATORY_HASHTAG;
        return <<<PROMPT
Du bist ein KI-Assistent, der auf die Analyse von deutschen Minecraft-Story-Videos spezialisiert ist, um SEO-optimierte Tags und Zusammenfassungen für Social Media (TikTok, Instagram) zu generieren.
Analysiere den Videoclip, der in dieser Anfrage mitgesendet wird.
Gib deine Antwort NUR im JSON-Format zurück. Das JSON-Objekt muss exakt die folgende Struktur haben:
{
  "videoSummary": {
    "overallGerman": "Eine prägnante, fesselnde Zusammenfassung des Videoinhalts in deutscher Sprache (max. 4-7 Sätze).",
    "keyVisualElements": ["Liste der auffälligsten visuellen Elemente oder Szenen (max. 5 Elemente)."],
    "detectedObjects": ["Liste der erkannten Objekte, Gegenstände oder Fahrzeuge im Video (max. 5 Elemente)."],
    "detectedActions": ["Liste der wichtigsten Aktionen, Events oder Handlungen im Video (max. 5 Elemente)."],
    "spokenNamesOrRoles": ["Liste der im Video gesprochenen Namen von Charakteren oder deren Rollen (z.B. 'Dorfbewohner', 'König') (max. 5 Elemente)."],
    "dialogueThemes": ["Liste der Hauptthemen oder Kernaussagen aus den Dialogen im Video (max. 5 Elemente)."]
  },
  "tiktokHashtags": ["#beispielTikTok1", "#beispielTikTok2", "#{$m_hashtag}", "... (weitere 12-17 Hashtags)"],
  "instagramHashtags": ["#beispielInstagram1", "#beispielInstagram2", "#{$m_hashtag}", "... (weitere 12-17 Hashtags)"],
  "categorizedTags": [
    { "category": "Hauptthema des Videos", "tags": ["Keyword1", "Keyword2", "Keyword3"] },
    { "category": "Stimmung/Atmosphäre", "tags": ["Keyword1", "Keyword2", "Keyword3"] },
    { "category": "Charaktere/Rollen", "tags": ["Keyword1", "Keyword2", "Keyword3"] },
    { "category": "Orte/Settings im Video", "tags": ["Keyword1", "Keyword2", "Keyword3"] },
    { "category": "Minecraft-spezifische Elemente", "tags": ["Redstone", "PvP", "Roleplay", "Survival", "Building"] }
  ]
}

Wichtig:
- Alle Texte müssen auf Deutsch sein.
- Die Hashtags müssen für ein deutsches Publikum optimiert sein und mit '#' beginnen. Für tiktokHashtags und instagramHashtags, liefere jeweils 15-20 relevante Hashtags.
- Analysiere sowohl visuelle als auch auditive Aspekte des Videos.
- Konzentriere dich auf Aspekte, die für Minecraft-Story-Videos relevant sind.
- Für `categorizedTags`, liefere 2-5 Keywords pro Kategorie.
- Stelle sicher, dass der Hashtag '{$m_hashtag}' in tiktokHashtags und instagramHashtags enthalten ist. Wenn der Nutzer spezifische Gameplay-Elemente wie 'Redstone-Falle', 'automatisierte Farm' oder 'komplexes Bauwerk' zeigt, erwähne diese explizit in den relevanten Kategorien.
- Versuche, aus Dialogen oder gezeigten Schildern konkrete Namen oder Projekttitel zu extrahieren, falls vorhanden, und nutze sie in den Tags/Keywords.
PROMPT;
    }

    public function generateTagsForVideo(string $videoContentsBase64, string $mimeType): array
    {
        // Option 1: Using Google AI Generative Language SDK (google-ai-generativelanguage/google-ai-php)
        // This is a more direct equivalent to the JS SDK used in the taggenerator.
        // Ensure 'google-ai-generativelanguage/google-ai-php' is in composer.json and installed.
        try {
            // Note: The google-ai-php SDK might have a slightly different API structure.
            // This is a conceptual example based on common patterns.
            // Refer to the actual google-ai-php SDK documentation for correct usage.
            // The JS SDK uses `new GoogleGenerativeAI(apiKey).getGenerativeModel({ model: MODEL_NAME })`
            // Then `model.generateContent([prompt, videoPart])`

            // The PHP SDK might be structured like this:
            // $client = new \Google\AI\GenerativeLanguage\V1beta\GenerativeServiceClient([
            //     'credentials' => new \Google\ApiCore\InsecureCredentialsWrapper(), // Adjust for API key auth
            // ]);
            // For API Key auth with google-ai-php, it's often part of the endpoint or a specific client option.
            // The Vertex AI SDK is more common for server-side PHP with Google Cloud.
            // If using the general Gemini API (not Vertex specific), the endpoint is different.

            // Let's assume a direct HTTP call if the PHP SDK for general Gemini API isn't straightforward to use with just an API key.
            // The Vertex AI SDK (`google/cloud-aiplatform`) is more robust for server-side but needs project_id and location.
            // If `google-ai-php` is installed and configured for API key:
            // $genAI = new \Google\GenerativeAI\GoogleGenerativeAI($this->apiKey);
            // $model = $genAI->getGenerativeModel($this->modelName);
            // $videoPart = ['inline_data' => ['mime_type' => $mimeType, 'data' => $videoContentsBase64]];
            // $response = $model->generateContent([$this->getSystemPrompt(), $videoPart]);
            // $jsonStr = $response->text();


            // Fallback/Alternative: Direct HTTP request to Gemini API
            // This is often simpler if you're not deep into the Google Cloud SDK ecosystem for PHP.
            // Endpoint for gemini-pro-vision (if that's the model):
            // https://generativelanguage.googleapis.com/v1beta/models/gemini-pro-vision:generateContent?key=YOUR_API_KEY
            // The model in the TSX version was not specified, but vision is needed for video.
            // Let's use a generic model endpoint that supports vision, like 'gemini-1.5-flash-001' or 'gemini-pro-vision'

            $url = "https://generativelanguage.googleapis.com/v1beta/models/{$this->modelName}:generateContent?key={$this->apiKey}";

            $payload = [
                'contents' => [
                    [
                        'parts' => [
                            ['text' => $this->getSystemPrompt()]
                        ]
                    ],
                    [
                        'parts' => [
                            [
                                'inline_data' => [
                                    'mime_type' => $mimeType,
                                    'data' => $videoContentsBase64
                                ]
                            ]
                        ]
                    ]
                ],
                // Optional: Add generationConfig if needed
                // 'generationConfig' => [
                //   'temperature' => 0.4,
                //   'topK' => 32,
                //   'topP' => 1,
                //   'maxOutputTokens' => 4096,
                //   'stopSequences' => [],
                // ],
            ];

            Log::debug("Sending request to Gemini API", ['url' => $url, 'model' => $this->modelName]);

            $response = Http::timeout(120)->post($url, $payload); // 120 seconds timeout

            if (!$response->successful()) {
                Log::error('Gemini API request failed', [
                    'status' => $response->status(),
                    'body' => $response->body(),
                    'model' => $this->modelName,
                ]);
                throw new Exception('Gemini API request failed: ' . $response->body());
            }

            $jsonStr = $response->json()['candidates'][0]['content']['parts'][0]['text'] ?? null;

            if (empty($jsonStr)) {
                 Log::error('Gemini API response is empty or has an unexpected format', [
                    'response_body' => $response->body(),
                    'model' => $this->modelName
                ]);
                throw new Exception('Failed to get valid text response from AI. The response might be empty or malformed.');
            }


        } catch (Exception $e) {
            Log::error('Error in GeminiTagService: ' . $e->getMessage(), [
                'trace' => $e->getTraceAsString(),
                'model' => $this->modelName
            ]);
            // Re-throw to be caught by the Livewire component
            throw new Exception("Error processing video with AI: " . $e->getMessage());
        }

        // Clean the response string (remove markdown code block fences)
        $fenceRegex = '/^```(\w*)?\s*
?(.*?)
?\s*```$/s';
        if (preg_match($fenceRegex, $jsonStr, $matches)) {
            $jsonStr = $matches[2] ?? $jsonStr;
        }
        $jsonStr = trim($jsonStr);

        try {
            $parsedData = json_decode($jsonStr, true, 512, JSON_THROW_ON_ERROR);
        } catch (Exception $e) {
            Log::error("Failed to parse JSON response from Gemini:", ['raw_response' => $jsonStr, 'error' => $e->getMessage()]);
            throw new Exception("Failed to parse AI response. The response was not valid JSON. Raw response snippet: " . substr($jsonStr, 0, 200));
        }

        return $this->formatResult($parsedData);
    }

    protected function formatResult(array $parsedData): array
    {
        $m_hashtag = self::MANDATORY_HASHTAG;

        $ensureMandatoryHashtag = function (array $hashtags = []) use ($m_hashtag): array {
            $cleanedHashtags = array_map(function ($h) {
                return str_starts_with($h, '#') ? $h : '#' . trim($h);
            }, $hashtags);
            $cleanedHashtags = array_filter($cleanedHashtags, fn($h) => strlen($h) > 1);
            $uniqueHashtags = array_values(array_unique($cleanedHashtags));

            if (!in_array($m_hashtag, $uniqueHashtags)) {
                array_unshift($uniqueHashtags, $m_hashtag);
            }
            return $uniqueHashtags;
        };

        $tiktokHashtags = $ensureMandatoryHashtag($parsedData['tiktokHashtags'] ?? []);
        $instagramHashtags = $ensureMandatoryHashtag($parsedData['instagramHashtags'] ?? []);

        $categorizedTags = array_map(function ($ct) {
            $tags = array_map(fn($tagStr) => trim($tagStr), $ct['tags'] ?? []);
            $tags = array_filter($tags, fn($t) => !empty($t));
            return [
                'category' => $ct['category'] ?? 'Uncategorized',
                'tags' => $tags,
            ];
        }, $parsedData['categorizedTags'] ?? []);
        $categorizedTags = array_filter($categorizedTags, fn($ct) => !empty($ct['tags']) && !empty(trim($ct['category'])));


        $defaultVideoSummary = [
            'overallGerman' => 'Keine Zusammenfassung verfügbar.',
            'keyVisualElements' => [],
            'detectedObjects' => [],
            'detectedActions' => [],
            'spokenNamesOrRoles' => [],
            'dialogueThemes' => [],
        ];

        $videoSummary = array_merge($defaultVideoSummary, $parsedData['videoSummary'] ?? []);
        $videoSummary['overallGerman'] = $parsedData['videoSummary']['overallGerman'] ?? $defaultVideoSummary['overallGerman'];


        return [
            'videoSummary' => $videoSummary,
            'tiktokHashtags' => $tiktokHashtags,
            'instagramHashtags' => $instagramHashtags,
            'categorizedTags' => array_values($categorizedTags), // Re-index array
        ];
    }
}
