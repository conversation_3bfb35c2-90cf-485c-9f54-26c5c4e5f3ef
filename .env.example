# IMPORTANT: Copy this file to .env before running `npm run dev`
# The custom dev script needs to read Reverb environment variables from your .env file
APP_NAME=Minewache
APP_ENV=production
APP_KEY=
APP_DEBUG=false
APP_TIMEZONE=Europe/Berlin
APP_URL=https://dev.minewache.de

APP_LOCALE=en
APP_FALLBACK_LOCALE=en
APP_FAKER_LOCALE=en_US

APP_MAINTENANCE_DRIVER=file
APP_MAINTENANCE_STORE=database

BCRYPT_ROUNDS=12

LOG_CHANNEL=stack
LOG_STACK=single
LOG_DEPRECATIONS_CHANNEL=null
LOG_LEVEL=error

DB_CONNECTION=mysql
DB_HOST=127.0.0.1
DB_PORT=3306
DB_DATABASE=minewache
DB_USERNAME=minewache_user
DB_PASSWORD=strong_password_here

SESSION_DRIVER=redis
SESSION_LIFETIME=120
SESSION_ENCRYPT=true
SESSION_PATH=/
SESSION_DOMAIN=.minewache.de
SESSION_SECURE_COOKIE=true

# Broadcasting Configuration
# Options: pusher, ably, redis, log, null, reverb
BROADCAST_CONNECTION=reverb
BROADCAST_DRIVER=pusher

# Reverb Configuration
REVERB_APP_ID="minewache_app"
REVERB_APP_KEY="your_secure_reverb_key_here"
REVERB_APP_SECRET="your_secure_reverb_secret_here"
REVERB_HOST="dev.minewache.de" # Host for the Reverb server (used by client and broadcasting driver)
REVERB_PORT=6001 # Port that PHP's broadcasting driver connects to (should match REVERB_SERVER_PORT)
REVERB_SCHEME="https" # Scheme for the Reverb server (http or https)

# Reverb Server Configuration
REVERB_SERVER_HOST="127.0.0.1" # Host the Reverb WebSocket server binds to
REVERB_SERVER_PORT=6001 # Port the Reverb WebSocket server listens on

# Filesystem Configuration
FILESYSTEM_DISK=local
FILESYSTEM_PUBLIC_DISK=public

# Queue Configuration
QUEUE_CONNECTION=redis
QUEUE_FAILED_DRIVER=database

# Cache Configuration
CACHE_STORE=redis
CACHE_PREFIX=minewache_cache_

# Redis Configuration
REDIS_CLIENT=phpredis
REDIS_HOST=127.0.0.1
REDIS_PASSWORD=null
REDIS_PORT=6379

MAIL_MAILER=smtp
MAIL_HOST=smtp.example.com
MAIL_PORT=587
MAIL_USERNAME=<EMAIL>
MAIL_PASSWORD=your_mail_password_here
MAIL_ENCRYPTION=tls
MAIL_FROM_ADDRESS="<EMAIL>"
MAIL_FROM_NAME="${APP_NAME}"

AWS_ACCESS_KEY_ID=
AWS_SECRET_ACCESS_KEY=
AWS_DEFAULT_REGION=us-east-1
AWS_BUCKET=
AWS_USE_PATH_STYLE_ENDPOINT=false

VITE_APP_NAME="${APP_NAME}"

# Vite Reverb Konfiguration
VITE_REVERB_APP_KEY="${REVERB_APP_KEY}"
VITE_REVERB_HOST="${REVERB_HOST}" # Host the client-side JavaScript (Echo) connects to
VITE_REVERB_PORT=6001 # Port the client-side JavaScript (Echo) connects to in local development (should match REVERB_SERVER_PORT)
VITE_REVERB_SCHEME="${REVERB_SCHEME}" # Scheme the client-side JavaScript (Echo) uses

# Vite Pusher Konfiguration (auskommentiert, da wir jetzt Reverb verwenden)
# VITE_PUSHER_APP_KEY="${PUSHER_APP_KEY}"
# VITE_PUSHER_APP_CLUSTER="${PUSHER_APP_CLUSTER}"
# VITE_PUSHER_HOST="${PUSHER_HOST}"
# VITE_PUSHER_PORT="${PUSHER_PORT}"
# VITE_PUSHER_SCHEME="${PUSHER_SCHEME}"


# Discord Authentication (Larascord)
LARASCORD_CLIENT_ID=your_discord_client_id_here
LARASCORD_CLIENT_SECRET=your_discord_client_secret_here
LARASCORD_GRANT_TYPE=authorization_code
LARASCORD_PREFIX=larascord
LARASCORD_SCOPE=identify&guilds&guilds.members.read
LARASCORD_GUILD_ID=your_discord_guild_id_here

# Discord Bot Configuration
# Important: Replace with a valid Discord Bot Token
# See https://discord.com/developers/applications for creating a bot and getting a token
DISCORD_BOT_TOKEN=your_discord_bot_token_here
DISCORD_TICKET_CATEGORY_ID=your_discord_ticket_category_id_here
DISCORD_BOT_ACTIVITY_NAME="Minewache"
DISCORD_BOT_ACTIVITY_TYPE="PLAYING"
DISCORD_SUPPORT_ROLE_IDS="your_support_role_id1,your_support_role_id2"

# Discord Bot Integration
DISCORD_ENABLED=true

# YouTube API Integration
YOUTUBE_API_KEY=
YOUTUBE_CHANNEL_ID=Die-Minewache

# Gemini AI Integration
GEMINI_API_KEY=
GEMINI_ENABLED=true
GEMINI_SYSTEM_USER_ID=

# Gemini Rate Limiting
GEMINI_RATE_LIMIT_PER_MINUTE=10
GEMINI_RATE_LIMIT_PER_HOUR=100
GEMINI_RATE_LIMIT_PER_DAY=1000
GEMINI_RATE_LIMIT_PER_TICKET=5

# PHP Version Compatibility
# This application is configured to run with PHP 8.4 as specified in the NGINX configuration

# FFmpeg Configuration
FFMPEG_BINARIES=/usr/bin/ffmpeg
FFPROBE_BINARIES=/usr/bin/ffprobe
FFMPEG_TEMPORARY_FILES_ROOT=/var/www/minewache-website/storage/app/ffmpeg-temp
