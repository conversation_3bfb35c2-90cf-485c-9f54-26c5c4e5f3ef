<x-app-layout>
    <x-slot name="title">Bewerbung bearbeiten - MineWache</x-slot>

    <div class="container mx-auto px-4 py-8">
        <div class="max-w-4xl mx-auto">
            <h1 class="text-2xl font-bold mb-4">Bewerbung #{{ $application->id }} bearbeiten</h1>

            <div class="alert alert-info shadow-lg mb-6">
                <div>
                    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" class="stroke-current flex-shrink-0 w-6 h-6"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path></svg>
                    <div>
                        <h3 class="font-bold">Hin<PERSON><PERSON> zur Bearbeitung</h3>
                        <p>Du kannst alle Angaben deiner Bewerbung bearbeiten, <strong>außer die Tätigkeitsbereiche</strong>. Diese können nicht nachträglich geändert werden.</p>
                        <p class="mt-2">Wenn du dich für weitere Bereiche bewerben möchtest, <a href="{{ route('bewerben') }}" class="link link-primary">reiche bitte eine neue Bewerbung ein</a>.</p>
                    </div>
                </div>
            </div>

            @if($application->status !== 'pending')
                <div class="alert alert-info mb-6">
                    <div>
                        <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" class="stroke-current flex-shrink-0 w-6 h-6"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path></svg>
                        <span>Deine Bewerbung wurde für eine Bearbeitung freigegeben. Nach dem Absenden wird sie erneut vom Team geprüft.</span>
                    </div>
                </div>
            @endif

            <form action="{{ route('my.applications.update', $application->id) }}" method="POST" class="space-y-6">
                @csrf

                <!-- Persönliche Informationen -->
                <div class="card bg-base-200 shadow">
                    <div class="card-body">
                        <h2 class="card-title mb-4">Persönliche Informationen</h2>

                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div class="form-control">
                                <label class="label">
                                    <span class="label-text">Name</span>
                                </label>
                                <x-modern-input type="text" name="name" variant="outlined" value="{{ old('name', $application- />name) }}" />
                                @error('name')
                                    <span class="text-error text-sm mt-1">{{ $message }}</span>
                                @enderror
                            </div>

                            <div class="form-control">
                                <label class="label">
                                    <span class="label-text">Alter</span>
                                </label>
                                <x-modern-input type="number" name="age" variant="outlined" value="{{ old('age', $application- />age) }}" min="0" max="120" />
                                @error('age')
                                    <span class="text-error text-sm mt-1">{{ $message }}</span>
                                @enderror
                            </div>

                            <div class="form-control">
                                <label class="label">
                                    <span class="label-text">Geschlecht</span>
                                </label>
                                <select name="gender" class="select select-bordered">
                                    <option value="" {{ old('gender', $application->gender) ? '' : 'selected' }}>Bitte wählen</option>
                                    <option value="male" {{ old('gender', $application->gender) == 'male' ? 'selected' : '' }}>Männlich</option>
                                    <option value="female" {{ old('gender', $application->gender) == 'female' ? 'selected' : '' }}>Weiblich</option>
                                    <option value="diverse" {{ old('gender', $application->gender) == 'diverse' ? 'selected' : '' }}>Divers</option>
                                    <option value="no_answer" {{ old('gender', $application->gender) == 'no_answer' ? 'selected' : '' }}>Keine Angabe</option>
                                </select>
                                @error('gender')
                                    <span class="text-error text-sm mt-1">{{ $message }}</span>
                                @enderror
                            </div>

                            <div class="form-control">
                                <label class="label">
                                    <span class="label-text">Pronomen</span>
                                </label>
                                <x-modern-input type="text" name="pronouns" variant="outlined" value="{{ old('pronouns', $application- />pronouns) }}" />
                                @error('pronouns')
                                    <span class="text-error text-sm mt-1">{{ $message }}</span>
                                @enderror
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Tätigkeitsbereiche -->
                <div class="card bg-base-200 shadow">
                    <div class="card-body">
                        <h2 class="card-title mb-4">Tätigkeitsbereiche</h2>

                        <div class="form-control">
                            <label class="label">
                                <span class="label-text font-medium">Deine ausgewählten Bereiche:</span>
                            </label>
                            <div class="flex flex-wrap gap-2 p-4 bg-base-300 rounded-box">
                                @php
                                    $professions = $application->professions ?? [];
                                    if (!is_array($professions)) $professions = [];
                                @endphp

                                @foreach($professions as $profession)
                                    @switch($profession)
                                        @case('actor')
                                            <span class="badge badge-lg badge-primary">Schauspieler</span>
                                            @break
                                        @case('voice_actor')
                                            <span class="badge badge-lg badge-primary">Synchronsprecher</span>
                                            @break
                                        @case('builder')
                                            <span class="badge badge-lg badge-secondary">Builder</span>
                                            @break
                                        @case('designer')
                                            <span class="badge badge-lg badge-accent">Designer</span>
                                            @break
                                        @case('cutter')
                                            <span class="badge badge-lg badge-info">Cutter</span>
                                            @break
                                        @case('cameraman')
                                            <span class="badge badge-lg badge-info">Kameramann</span>
                                            @break
                                        @case('developer')
                                            <span class="badge badge-lg badge-info">Entwickler</span>
                                            @break
                                        @case('modeler')
                                            <span class="badge badge-lg badge-accent">3D-Modellierer</span>
                                            @break
                                        @case('music_producer')
                                            <span class="badge badge-lg badge-primary">Musikproduzent</span>
                                            @break
                                        @case('other')
                                            <span class="badge badge-lg">Andere: {{ $application->other_profession }}</span>
                                            @break
                                        @default
                                            <span class="badge badge-lg">{{ $profession }}</span>
                                    @endswitch
                                @endforeach
                            </div>

                            <div class="mt-4 text-sm text-base-content/70">
                                <p>Diese Bereiche können nicht geändert werden. Für weitere Bereiche <a href="{{ route('bewerben') }}" class="link link-primary">reiche eine neue Bewerbung ein</a>.</p>
                            </div>
                        </div>

                        <!-- Versteckte Felder, um die ursprünglichen Werte beizubehalten -->
                        @foreach($professions as $profession)
                            <input type="hidden" name="professions[]" value="{{ $profession }}" />
                        @endforeach
                        @if(in_array('other', $professions))
                            <input type="hidden" name="otherProfession" value="{{ $application->other_profession }}" />
                        @endif
                    </div>
                </div>

                <!-- Über dich -->
                <div class="card bg-base-200 shadow">
                    <div class="card-body">
                        <h2 class="card-title mb-4">Über dich</h2>

                        <div class="form-control mb-4">
                            <label class="label">
                                <span class="label-text">Erzähle uns mehr über dich</span>
                            </label>
                            <textarea name="about_you" class="textarea textarea-bordered h-32">{{ old('about_you', $application->about_you) }}</textarea>
                            @error('about_you')
                                <span class="text-error text-sm mt-1">{{ $message }}</span>
                            @enderror
                        </div>

                        <div class="form-control mb-4">
                            <label class="label">
                                <span class="label-text">Was sind deine Stärken und Schwächen?</span>
                            </label>
                            <textarea name="strengths_weaknesses" class="textarea textarea-bordered h-32">{{ old('strengths_weaknesses', $application->strengths_weaknesses) }}</textarea>
                            @error('strengths_weaknesses')
                                <span class="text-error text-sm mt-1">{{ $message }}</span>
                            @enderror
                        </div>

                        <div class="form-control">
                            <label class="label">
                                <span class="label-text">Abschließende Worte</span>
                            </label>
                            <textarea name="final_words" class="textarea textarea-bordered h-24">{{ old('final_words', $application->final_words) }}</textarea>
                            @error('final_words')
                                <span class="text-error text-sm mt-1">{{ $message }}</span>
                            @enderror
                        </div>
                    </div>
                </div>

                <!-- Synchronsprecher-Informationen (bedingt) -->
                <div id="voice-actor-section" class="card bg-base-200 shadow {{ !in_array('voice_actor', $professions) ? 'hidden' : '' }}">
                    <div class="card-body">
                        <h2 class="card-title mb-4">Synchronsprecher-Informationen</h2>

                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div class="form-control">
                                <label class="label">
                                    <span class="label-text">Stimmtyp</span>
                                </label>
                                <x-modern-input type="text" name="voice_type" variant="outlined" value="{{ old('voice_type', $application- />voice_type) }}" />
                                @error('voice_type')
                                    <span class="text-error text-sm mt-1">{{ $message }}</span>
                                @enderror
                            </div>

                            <div class="form-control">
                                <label class="label">
                                    <span class="label-text">Mikrofon</span>
                                </label>
                                <x-modern-input type="text" name="microphone" variant="outlined" value="{{ old('microphone', $application- />microphone) }}" />
                                @error('microphone')
                                    <span class="text-error text-sm mt-1">{{ $message }}</span>
                                @enderror
                            </div>

                            <div class="form-control">
                                <label class="label">
                                    <span class="label-text">DAW (Digital Audio Workstation)</span>
                                </label>
                                <x-modern-input type="text" name="daw" variant="outlined" value="{{ old('daw', $application- />daw) }}" />
                                @error('daw')
                                    <span class="text-error text-sm mt-1">{{ $message }}</span>
                                @enderror
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Entwickler-Informationen (bedingt) -->
                <div id="developer-section" class="card bg-base-200 shadow {{ !in_array('developer', $professions) ? 'hidden' : '' }}">
                    <div class="card-body">
                        <h2 class="card-title mb-4">Entwickler-Informationen</h2>

                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div class="form-control">
                                <label class="label">
                                    <span class="label-text">Programmiersprachen</span>
                                </label>
                                <x-modern-input type="text" name="languages" variant="outlined" value="{{ old('languages', $application- />languages) }}" />
                                @error('languages')
                                    <span class="text-error text-sm mt-1">{{ $message }}</span>
                                @enderror
                            </div>

                            <div class="form-control">
                                <label class="label">
                                    <span class="label-text">Bevorzugte IDE</span>
                                </label>
                                <x-modern-input type="text" name="ide" variant="outlined" value="{{ old('ide', $application- />ide) }}" />
                                @error('ide')
                                    <span class="text-error text-sm mt-1">{{ $message }}</span>
                                @enderror
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Designer-Informationen (bedingt) -->
                <div id="designer-section" class="card bg-base-200 shadow {{ !in_array('designer', $professions) ? 'hidden' : '' }}">
                    <div class="card-body">
                        <h2 class="card-title mb-4">Designer-Informationen</h2>

                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div class="form-control">
                                <label class="label">
                                    <span class="label-text">Programme</span>
                                </label>
                                <x-modern-input type="text" name="program" variant="outlined" value="{{ old('program', $application- />program) }}" />
                                @error('program')
                                    <span class="text-error text-sm mt-1">{{ $message }}</span>
                                @enderror
                            </div>

                            <div class="form-control">
                                <label class="label">
                                    <span class="label-text">Design-Stil</span>
                                </label>
                                <x-modern-input type="text" name="design_style" variant="outlined" value="{{ old('design_style', $application- />design_style) }}" />
                                @error('design_style')
                                    <span class="text-error text-sm mt-1">{{ $message }}</span>
                                @enderror
                            </div>

                            <div class="form-control col-span-1 md:col-span-2">
                                <label class="label">
                                    <span class="label-text">Dein Lieblingsdesign</span>
                                </label>
                                <x-modern-input type="text" name="favorite_design" variant="outlined" value="{{ old('favorite_design', $application- />favorite_design) }}" />
                                @error('favorite_design')
                                    <span class="text-error text-sm mt-1">{{ $message }}</span>
                                @enderror
                            </div>

                            <div class="form-control col-span-1 md:col-span-2">
                                <label class="label">
                                    <span class="label-text">Portfolio (Links oder Beschreibung)</span>
                                </label>
                                <textarea name="portfolio" class="textarea textarea-bordered">{{ old('portfolio', $application->portfolio) }}</textarea>
                                @error('portfolio')
                                    <span class="text-error text-sm mt-1">{{ $message }}</span>
                                @enderror
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Buttons -->
                <div class="flex justify-between mt-8">
                    <x-modern-button variant="ghost" href="{{ route('my.applications') }}" >Abbrechen</x-modern-button>
                    <x-modern-button variant="primary" size="md" type="submit">Bewerbung aktualisieren</x-modern-button>
                </div>
            </form>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Selektoren für die Checkboxen und Sektionen
            const checkboxes = document.querySelectorAll('input[name="professions[]"]');
            const voiceActorSection = document.getElementById('voice-actor-section');
            const developerSection = document.getElementById('developer-section');
            const designerSection = document.getElementById('designer-section');

            // Event-Handler für Checkbox-Änderungen
            function updateVisibility() {
                const selectedProfessions = Array.from(checkboxes)
                    .filter(checkbox => checkbox.checked)
                    .map(checkbox => checkbox.value);

                voiceActorSection.classList.toggle('hidden', !selectedProfessions.includes('voice_actor'));
                developerSection.classList.toggle('hidden', !selectedProfessions.includes('developer'));
                designerSection.classList.toggle('hidden', !selectedProfessions.includes('designer'));
            }

            // Überwachen von Änderungen an den Checkboxen
            checkboxes.forEach(checkbox => {
                checkbox.addEventListener('change', updateVisibility);
            });

            // Initial ausführen, um den Anfangszustand korrekt zu setzen
            updateVisibility();
        });
    </script>
</x-app-layout>