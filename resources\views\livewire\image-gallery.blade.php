<div>
    <!-- Image Modal -->
    <div id="imageModal" class="fixed inset-0 bg-black bg-opacity-80 flex items-center justify-center z-50 @if(!$isOpen) hidden @endif">
        <div class="relative max-w-4xl max-h-[90vh] p-4">
            <button wire:click="closeGallery" class="absolute -top-2 -right-2 text-white bg-gray-700 hover:bg-gray-600 rounded-full p-2 z-10">
                <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd"></path>
                </svg>
            </button>
            <img id="imageModalContent" src="" alt="" class="max-w-full max-h-[85vh] object-contain rounded-lg shadow-lg">
            <p id="imageModalTitle" class="text-center text-white text-sm mt-2">{{ $title }}</p>

            <!-- Gallery Navigation -->
            <div id="imageModalNavigation" class="absolute inset-y-0 left-0 right-0 flex items-center justify-between px-4">
                <button wire:click="navigate(-1)" class="text-white bg-gray-700/50 hover:bg-gray-600/70 rounded-full p-3">
                    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
                    </svg>
                </button>
                <span id="imageCounter" class="text-white bg-black/50 px-2 py-1 rounded-md text-xs"></span>
                <button wire:click="navigate(1)" class="text-white bg-gray-700/50 hover:bg-gray-600/70 rounded-full p-3">
                    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                    </svg>
                </button>
            </div>
        </div>
    </div>

    <script>
        document.addEventListener('livewire:initialized', function() {
            // Listen for build-gallery-data event
            Livewire.on('build-gallery-data', function(data) {
                buildGalleryData(data.messageId);
            });
        });

        function buildGalleryData(messageId) {
            window.galleryImages = {};
            document.querySelectorAll('[data-gallery-id="' + messageId + '"]').forEach(function(img, index) {
                if (!window.galleryImages[messageId]) {
                    window.galleryImages[messageId] = [];
                }

                window.galleryImages[messageId].push({
                    src: img.src,
                    alt: img.alt,
                    element: img
                });
            });

            updateCurrentImage(messageId);
        }

        function updateCurrentImage(messageId) {
            if (!messageId || !window.galleryImages[messageId]) return;

            const gallery = window.galleryImages[messageId];
            const currentIndex = @js($currentIndex);

            if (!gallery[currentIndex]) return;

            const image = gallery[currentIndex];
            document.getElementById('imageModalContent').src = image.src;

            // Update counter
            const totalImages = gallery.length;
            document.getElementById('imageCounter').textContent = (currentIndex + 1) + ' / ' + totalImages;

            // Show/hide navigation based on number of images
            const navControls = document.getElementById('imageModalNavigation');
            if (totalImages > 1) {
                navControls.classList.remove('hidden');
            } else {
                navControls.classList.add('hidden');
            }
        }
    </script>
</div>
