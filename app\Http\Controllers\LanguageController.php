<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\App;
use Illuminate\Support\Facades\Session;

class LanguageController extends Controller
{
    /**
     * Change the application language.
     *
     * @param string $locale The locale to switch to
     * @return \Illuminate\Http\RedirectResponse
     */
    public function change($locale)
    {
        // Check if the requested locale is supported
        if (in_array($locale, ['en', 'de'])) {
            // Store the locale in the session
            Session::put('locale', $locale);
            
            // Set the application locale
            App::setLocale($locale);
        }
        
        // Redirect back to the previous page
        return redirect()->back();
    }
}
