<?php

namespace App\Console\Commands;

use App\Models\RegisterYoutubeLink;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\File;

class ExportYoutubeData extends Command
{
    protected $signature = 'youtube:export {--output=database/seeders/youtube_data.php}';
    protected $description = 'Export YouTube data to a PHP file for seeding';

    public function handle()
    {
        $videos = RegisterYoutubeLink::all();
        
        if ($videos->isEmpty()) {
            $this->error('No YouTube videos found in the database.');
            return 1;
        }
        
        $this->info('Found ' . $videos->count() . ' videos.');
        
        // Format the data for the seeder
        $data = [];
        foreach ($videos as $video) {
            $data[] = [
                'id' => $video->id,
                'season' => $video->season,
                'episode' => $video->episode,
                'link' => $video->link,
                'title' => $video->title,
                'description' => $video->description,
                'thumbnail_url' => $video->thumbnail_url,
                'published_at' => $video->published_at ? $video->published_at->format('Y-m-d H:i:s') : null,
                'duration_seconds' => $video->duration_seconds,
                'view_count' => $video->view_count,
                'is_featured' => $video->is_featured,
                'status' => $video->status,
                'created_at' => $video->created_at->format('Y-m-d H:i:s'),
                'updated_at' => $video->updated_at->format('Y-m-d H:i:s'),
            ];
        }
        
        // Create the PHP file content
        $output = $this->option('output');
        $content = "<?php\n\nreturn " . var_export($data, true) . ";\n";
        
        // Save the file
        File::put($output, $content);
        
        $this->info('YouTube data exported to ' . $output);
        return 0;
    }
}
