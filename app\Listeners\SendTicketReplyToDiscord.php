<?php

namespace App\Listeners;

use App\Events\TicketRepliedByUser;
use App\Services\DiscordMessagingService;
use App\Models\Ticket; // For type hinting status constants
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage; // Added for file storage access

class SendTicketReplyToDiscord implements ShouldQueue
{
    use InteractsWithQueue;

    protected DiscordMessagingService $discordMessagingService;

    /**
     * Create the event listener.
     *
     * @param DiscordMessagingService $discordMessagingService
     * @return void
     */
    public function __construct(DiscordMessagingService $discordMessagingService)
    {
        $this->discordMessagingService = $discordMessagingService;
    }

    /**
     * Handle the event.
     *
     * @param  \App\Events\TicketRepliedByUser  $event
     * @return void
     */
    public function handle(TicketRepliedByUser $event)
    {
        $ticket = $event->ticket;
        $ticketMessage = $event->ticketMessage;
        $replyingUser = $event->replyingUser;

        if (empty($ticket->discord_channel_id)) {
            Log::warning("SendTicketReplyToDiscord: Ticket #{$ticket->id} has no Discord channel ID. Cannot send reply to Discord.");
            return;
        }

        // Optional: Don't send if ticket is closed on Discord side already (though UI should prevent this)
        // This would require fetching channel status or relying on local ticket status if it's synced.
        // For now, we assume if a reply is made, it should be sent if channel_id exists.

        Log::info("SendTicketReplyToDiscord: Sending reply for ticket #{$ticket->id} to Discord channel {$ticket->discord_channel_id}.");

        // Prepare the message content and embed
        // Determine author name (could be Laravel username or associated Discord tag if known)
        $authorName = $replyingUser->name; // Default to Laravel username
        if (!empty($replyingUser->global_name) && !empty($replyingUser->discriminator) && $replyingUser->discriminator !== '0') {
            // If we have discord global_name and discriminator from User model (synced perhaps by Larascord)
            $authorName = $replyingUser->global_name . '#' . $replyingUser->discriminator;
        } elseif (!empty($replyingUser->global_name)) {
            $authorName = $replyingUser->global_name;
        }


        // Basic embed format for staff reply
        $embedData = [
            'author' => [
                'name' => "Reply from {$authorName}",
                // 'icon_url' => $replyingUser->avatar_url ?? null, // If available on user model
                // 'url' => route('users.profile', $replyingUser->id) // Link to user profile in web app?
            ],
            'description' => $ticketMessage->content,
            'color' => 0x28a745, // Green for staff/official reply
            'timestamp' => $ticketMessage->created_at->toIso8601String(),
            'footer' => ['text' => 'Minewache Support System | Staff Reply']
        ];

        // Handle attachments
        $attachmentsForDiscord = [];
        if ($ticketMessage->relationLoaded('attachments') || $ticketMessage->load('attachments')) {
            Log::info("SendTicketReplyToDiscord: Processing " . $ticketMessage->attachments->count() . " attachments for TicketMessage ID: {$ticketMessage->id}.");
            foreach ($ticketMessage->attachments as $attachmentModel) {
                // Assuming $attachmentModel->filepath is relative to a configured storage disk
                // and $attachmentModel->original_filename stores the original name.
                if (Storage::exists($attachmentModel->filepath)) {
                    $attachmentsForDiscord[] = [
                        'path' => Storage::path($attachmentModel->filepath), // Absolute path for DiscordPHP
                        'filename' => $attachmentModel->original_filename,
                    ];
                    Log::debug("SendTicketReplyToDiscord: Added attachment '{$attachmentModel->original_filename}' (Path: {$attachmentModel->filepath}) for TicketMessage ID: {$ticketMessage->id}.");
                } else {
                    Log::warning("SendTicketReplyToDiscord: Attachment file not found at path: {$attachmentModel->filepath} for TicketMessage ID: {$ticketMessage->id}. Skipping this attachment.");
                }
            }
        } else {
            Log::info("SendTicketReplyToDiscord: No attachments found or relation not loaded for TicketMessage ID: {$ticketMessage->id}.");
        }


        $embed = $this->discordMessagingService->createEmbed($embedData);

        $sentMessage = null;
        $messageContent = ''; // Content can be empty if only sending embed + attachments

        if ($embed) {
            $sentMessage = $this->discordMessagingService->sendMessageToChannel(
                $ticket->discord_channel_id,
                $messageContent,
                [$embed],
                null, // No components for now
                $attachmentsForDiscord // Pass attachments here
            );
        } else {
            // Fallback to text if embed creation fails, still include attachments
            $fallbackContent = "**Reply from {$authorName}:**\n{$ticketMessage->content}";
            $sentMessage = $this->discordMessagingService->sendMessageToChannel(
                $ticket->discord_channel_id,
                $fallbackContent,
                [], // No embed
                null, // No components
                $attachmentsForDiscord // Pass attachments here
            );
            Log::warning("SendTicketReplyToDiscord: Failed to create embed for ticket #{$ticket->id} reply. Sent as plain text with attachments (if any).");
        }

        if ($sentMessage && $sentMessage->id) {
            // Update the ticket_message with the discord_message_id
            $ticketMessage->discord_message_id = $sentMessage->id;
            $ticketMessage->save();
            Log::info("SendTicketReplyToDiscord: Reply for ticket #{$ticket->id} successfully sent to Discord channel {$ticket->discord_channel_id}. Discord Message ID: {$sentMessage->id}");
        } else {
            Log::error("SendTicketReplyToDiscord: Failed to send reply for ticket #{$ticket->id} to Discord channel {$ticket->discord_channel_id}.");
        }
    }
}
