{"private": true, "type": "module", "scripts": {"predev": "node scripts/dev-setup.js", "dev:server": "php artisan serve", "dev:queue": "php artisan queue:work-reverb --queue=default,media --timeout=0", "dev:vite": "vite", "dev:discord": "php artisan minewache:run-discord-bot", "dev:reverb": "php artisan reverb:start --debug", "dev": "node scripts/dev-manager.js standard", "dev:no-reverb": "node scripts/dev-manager.js no-reverb", "dev:no-discord": "node scripts/dev-manager.js no-discord", "dev:minimal": "node scripts/dev-manager.js minimal", "postdev": "node scripts/dev-cleanup.js", "reverb": "node scripts/start-reverb.js", "reverb:win": "./start-reverb.bat", "reverb:stop": "node scripts/stop-reverb.js", "reverb:stop:win": "./stop-reverb.bat", "build": "vite build"}, "devDependencies": {"@tailwindcss/forms": "^0.5.2", "alpinejs": "^3.4.2", "autoprefixer": "^10.4.2", "axios": "^1.6.4", "concurrently": "^9.1.2", "daisyui": "^5.0.9", "dotenv": "^16.5.0", "laravel-echo": "^2.0.2", "laravel-vite-plugin": "^1.0", "postcss": "^8.4.31", "pusher-js": "^8.4.0", "tailwindcss": "^3.1.0", "vite": "^5.0"}, "dependencies": {"@fontsource/geist-sans": "^5.2.5"}}