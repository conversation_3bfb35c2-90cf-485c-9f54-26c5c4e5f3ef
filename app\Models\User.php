<?php

namespace App\Models;

use App\Enums\Role;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use <PERSON>akyer<PERSON>\Larascord\Traits\InteractsWithDiscord;
use App\Models\DataDeletionRequest;
use App\Models\UserAiConsent;

class User extends Authenticatable
{
    use HasFactory, Notifiable, InteractsWithDiscord;

    /**
     * The attributes that are mass assignable.
     *
     * @var string[]
     */
    protected $fillable = [
        'id',
        'username',
        'global_name',
        'discriminator',
        'avatar',
        'verified',
        'banner',
        'permissions',
        'last_synced_at',
        'banner_color',
        'accent_color',
        'locale',
        'mfa_enabled',
        'premium_type',
        'public_flags',
    ];

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var array
     */
    protected $hidden = [
        'remember_token',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array
     */
    protected $casts = [
        'id' => 'string', // Changed from 'integer' to 'string' to preserve Discord ID precision
        'username' => 'string',
        'global_name' => 'string',
        'discriminator' => 'string',
        'avatar' => 'string',
        'verified' => 'boolean',
        'banner' => 'string',
        'status' => 'integer',
        'banner_color' => 'string',
        'accent_color' => 'string',
        'locale' => 'string',
        'mfa_enabled' => 'boolean',
        'premium_type' => 'integer',
        'public_flags' => 'integer',
        'permissions' => 'string', // Cast to string to handle large integers consistently
        'last_synced_at' => 'datetime',
    ];

    /**
     * Check if the user has a specific role.
     *
     * @param Role $role
     * @return bool
     */
    public function hasRole(Role $role): bool
    {
        $userPermissions = (string) $this->permissions;
        $targetRoleValue = $role->value; // This is an int
        $adminRoleValue = Role::MINEWACHE_TEAM->value; // This is an int

        // If the role being checked IS MINEWACHE_TEAM
        if ($targetRoleValue === $adminRoleValue) {
            // The most reliable check is direct string comparison of the user's permission
            // with the string representation of the MINEWACHE_TEAM enum value.
            return $userPermissions === (string)$adminRoleValue;
        }

        // If the user IS MINEWACHE_TEAM (has all permissions), they should have any role.
        // This also handles cases where MINEWACHE_TEAM might be stored as an integer in $this->permissions,
        // though current casting is to string.
        if ($userPermissions === (string)$adminRoleValue || (is_numeric($this->permissions) && (int)$this->permissions === $adminRoleValue)) {
             return true;
        }

        // For other roles, perform the bitwise check.
        // Ensure $this->permissions is treated as an integer for bitwise operations.
        // This might involve `intval()`, but be mindful of PHP_INT_MAX on 32-bit systems
        // if the permission value can exceed it. Given the existing string cast,
        // this path is for non-admin roles.
        if (is_numeric($this->permissions)) {
             return ((int)$this->permissions & $targetRoleValue) === $targetRoleValue;
        }
        // If $this->permissions is not numeric (e.g. an unexpected format), it cannot match.
        return false;
    }

    /**
     * Check if the user has any of the given roles.
     *
     * @param array $roles
     * @return bool
     */
    public function hasAnyRole(array $roles): bool
    {
        // Special case for admin check with direct string comparison
        $permissions = (string)$this->permissions;
        $adminValue = (string)Role::MINEWACHE_TEAM->value;

        if ($permissions === $adminValue) {
            return true;
        }

        foreach ($roles as $role) {
            if ($this->hasRole($role)) {
                return true;
            }
        }

        return false;
    }

    /**
     * Check if the user has all of the given roles.
     *
     * @param array $roles
     * @return bool
     */
    public function hasAllRoles(array $roles): bool
    {
        // Special case for admin check with direct string comparison
        $permissions = (string)$this->permissions;
        $adminValue = (string)Role::MINEWACHE_TEAM->value;

        if ($permissions === $adminValue) {
            return true;
        }

        foreach ($roles as $role) {
            if (!$this->hasRole($role)) {
                return false;
            }
        }

        return true;
    }

    /**
     * Get all applications submitted by this user
     *
     * @return \Illuminate\Database\Eloquent\Relations\HasMany
     */
    public function applications()
    {
        return $this->hasMany(Application::class, 'user_id');
    }

    /**
     * Get all data deletion requests submitted by this user
     *
     * @return \Illuminate\Database\Eloquent\Relations\HasMany
     */
    public function dataDeletionRequests()
    {
        return $this->hasMany(DataDeletionRequest::class, 'user_id');
    }

    /**
     * Get all tickets created by this user
     *
     * @return \Illuminate\Database\Eloquent\Relations\HasMany
     */
    public function tickets()
    {
        return $this->hasMany(Ticket::class, 'user_id');
    }

    /**
     * Get all tickets assigned to this user
     *
     * @return \Illuminate\Database\Eloquent\Relations\HasMany
     */
    public function assignedTickets()
    {
        return $this->hasMany(Ticket::class, 'assigned_to');
    }

    /**
     * Check if the user is a supporter (has MINEWACHE_TEAM role)
     *
     * @return bool
     */
    public function isSupporter(): bool
    {
        // Special case for admin check with direct string comparison
        $permissions = (string)$this->permissions;
        $adminValue = (string)Role::MINEWACHE_TEAM->value;

        if ($permissions === $adminValue) {
            return true;
        }

        return $this->hasRole(Role::MINEWACHE_TEAM);
    }

    // Die ursprünglichen Methoden aus dem InteractsWithDiscord-Trait verwenden
    // Wir überschreiben nur die getAvatar-Methode für den AI-User

    /**
     * Überschreibt die getAvatar-Methode aus dem InteractsWithDiscord-Trait.
     * Diese Methode wird von den Ansichten verwendet, um das Avatar-Bild eines Benutzers anzuzeigen.
     *
     * @param array $options
     * @return string
     */
    public function getAvatar(array $options = []): string
    {
        // Wenn es sich um den AI-User handelt, verwende das benutzerdefinierte Avatar-Bild
        if ($this->isAIUser()) {
            return $this->getAIAvatar($options);
        }

        // Ansonsten verwende die ursprüngliche Methode aus dem InteractsWithDiscord-Trait
        // Wir rufen die ursprüngliche Methode nicht direkt auf, um Fehler zu vermeiden

        // Standardwerte für die Optionen
        $size = $options['size'] ?? 128;
        $extension = $options['extension'] ?? 'png';

        // Wenn der Benutzer ein Avatar-Bild hat, verwende es
        if ($this->avatar) {
            return "https://cdn.discordapp.com/avatars/{$this->id}/{$this->avatar}.{$extension}?size={$size}";
        }

        // Ansonsten verwende ein Standard-Avatar-Bild
        $discriminator = $this->discriminator ? intval($this->discriminator) % 5 : 0;
        return "https://cdn.discordapp.com/embed/avatars/{$discriminator}.png?size={$size}";
    }

    /**
     * Get the AI consent record for this user
     *
     * @return \Illuminate\Database\Eloquent\Relations\HasOne
     */
    public function aiConsent()
    {
        return $this->hasOne(UserAiConsent::class, 'user_id');
    }

    /**
     * Get the data consent record for this user
     *
     * @return \Illuminate\Database\Eloquent\Relations\HasOne
     */
    public function dataConsent()
    {
        return $this->hasOne(UserDataConsent::class, 'user_id');
    }

    /**
     * Check if the user has consented to AI responses
     *
     * @return bool
     */
    public function hasConsentedToAi(): bool
    {
        return $this->aiConsent?->has_consented ?? false;
    }

    /**
     * Check if the user is an AI user
     *
     * @return bool
     */
    public function isAIUser(): bool
    {
        return $this->username === 'AI Support';
    }

    /**
     * Get the avatar URL for the AI user
     *
     * @param array $options
     * @return string
     */
    public function getAIAvatar(array $options = []): string
    {
        $size = $options['size'] ?? 128;

        // Verwende das neue SVG-Icon, wenn verfügbar, sonst das Fallback-Bild
        if (file_exists(public_path('images/ai-assistant-icon.svg'))) {
            return asset('images/ai-assistant-icon.svg') . '?size=' . $size;
        }

        return asset('images/ai-assistant-avatar.png') . '?size=' . $size;
    }

    /**
     * Check if the user has consented to sharing specific data
     *
     * @param string $dataType
     * @return bool
     */
    public function hasConsentedToShareData(string $dataType): bool
    {
        if (!$this->dataConsent) {
            // Erstelle einen Standard-Datensatz, wenn keiner existiert
            $this->dataConsent()->create([
                'share_username' => true,
                'share_applications' => false,
                'share_tickets' => false,
                'share_discord_info' => false,
            ]);
            $this->refresh();
        }

        return $this->dataConsent->{"share_{$dataType}"} ?? false;
    }

    /**
     * Set consent for sharing specific data
     *
     * @param string $dataType
     * @param bool $consent
     * @return void
     */
    public function setDataSharingConsent(string $dataType, bool $consent): void
    {
        if (!$this->dataConsent) {
            // Erstelle einen Standard-Datensatz, wenn keiner existiert
            $this->dataConsent()->create([
                'share_username' => true,
                'share_applications' => false,
                'share_tickets' => false,
                'share_discord_info' => false,
            ]);
            $this->refresh();
        }

        $this->dataConsent->update([
            "share_{$dataType}" => $consent
        ]);
    }
}
