<?php

namespace App\Livewire;

use App\Models\User;
use Illuminate\Support\Facades\Auth;
use Livewire\Component;

class UserDataConsentModal extends Component
{
    public bool $show = false;
    public string $requestedDataType = '';
    public string $requestedDataName = '';
    public string $ticketId = '';
    
    protected $listeners = [
        'requestDataConsent' => 'showConsentModal',
    ];
    
    /**
     * Show the consent modal for a specific data type
     *
     * @param string $dataType
     * @param string $ticketId
     * @return void
     */
    public function showConsentModal(string $dataType, string $ticketId): void
    {
        $this->requestedDataType = $dataType;
        $this->ticketId = $ticketId;
        
        // Übersetze den Datentyp in einen benutzerfreundlichen Namen
        $this->requestedDataName = match($dataType) {
            'username' => __('tickets.data_type_username'),
            'applications' => __('tickets.data_type_applications'),
            'tickets' => __('tickets.data_type_tickets'),
            'discord_info' => __('tickets.data_type_discord_info'),
            default => $dataType,
        };
        
        $this->show = true;
    }
    
    /**
     * Grant consent for the requested data type
     *
     * @return void
     */
    public function grantConsent(): void
    {
        /** @var User $user */
        $user = Auth::user();
        
        if ($user) {
            $user->setDataSharingConsent($this->requestedDataType, true);
            
            // Benachrichtige die TicketView-Komponente, dass die Zustimmung erteilt wurde
            $this->dispatch('dataSharingConsentGranted', [
                'dataType' => $this->requestedDataType,
                'ticketId' => $this->ticketId,
            ]);
        }
        
        $this->show = false;
    }
    
    /**
     * Deny consent for the requested data type
     *
     * @return void
     */
    public function denyConsent(): void
    {
        /** @var User $user */
        $user = Auth::user();
        
        if ($user) {
            $user->setDataSharingConsent($this->requestedDataType, false);
            
            // Benachrichtige die TicketView-Komponente, dass die Zustimmung verweigert wurde
            $this->dispatch('dataSharingConsentDenied', [
                'dataType' => $this->requestedDataType,
                'ticketId' => $this->ticketId,
            ]);
        }
        
        $this->show = false;
    }
    
    /**
     * Render the component
     *
     * @return \Illuminate\View\View
     */
    public function render()
    {
        return view('livewire.user-data-consent-modal');
    }
}
