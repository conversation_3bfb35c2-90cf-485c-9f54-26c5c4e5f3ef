<?php

namespace App\Providers;

use App\Enums\Role;
use App\Models\User;
use App\Services\ResponseGeneratorService;
use Illuminate\Auth\Access\Response;
use Illuminate\Support\Facades\Blade;
use Illuminate\Support\Facades\Gate;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Response as ResponseFactory;
use Illuminate\Support\ServiceProvider;
use Illuminate\Broadcasting\BroadcastManager;
use Illuminate\Broadcasting\Broadcasters\ReverbBroadcaster;

class AppServiceProvider extends ServiceProvider
{
    /**
     * Register any application services.
     */
    public function register(): void
    {
        // Service für die Antwortgenerierung registrieren
        $this->app->singleton(ResponseGeneratorService::class, function ($app) {
            return new ResponseGeneratorService();
        });

        // Override the default broadcaster to ensure Reverb is used
        $this->app->extend('Illuminate\Broadcasting\BroadcastManager', function ($manager, $app) {
            Log::info('Extending BroadcastManager to use Reverb by default');

            // Force the manager to use Reverb driver
            $manager->setDefaultDriver('reverb');

            return $manager;
        });
    }

    /**
     * Bootstrap any application services.
     */
    public function boot(): void
    {
        // Register gates for each role
        $this->registerRoleGates(); // Re-enabled for admin access

        // Register generic role gates
        $this->registerGenericRoleGates();

        // Register response macros for API
        $this->registerResponseMacros();
    }

    /*
     * Register individual gates for each role
     */
   // Commented out as per refactoring
    private function registerRoleGates(): void
    {
        // Define a gate for each role in the enum
        Gate::define('MINEWACHE_TEAM', function (User $user) {
            return $user->hasRole(Role::MINEWACHE_TEAM)
                ? Response::allow()
                : Response::denyAsNotFound('Du benötigst die Minewache-Team Rolle.');
        });

        Gate::define('SCHAUSPIELER', function (User $user) {
            return $user->hasRole(Role::SCHAUSPIELER)
                ? Response::allow()
                : Response::denyAsNotFound('Du benötigst die Schauspieler Rolle.');
        });

        Gate::define('BUILDER', function (User $user) {
            return $user->hasRole(Role::BUILDER)
                ? Response::allow()
                : Response::denyAsNotFound('Du benötigst die Builder Rolle.');
        });

        Gate::define('DESIGNER', function (User $user) {
            return $user->hasRole(Role::DESIGNER)
                ? Response::allow()
                : Response::denyAsNotFound('Du benötigst die Designer Rolle.');
        });

        Gate::define('SYNCHRONSPRECHER', function (User $user) {
            return $user->hasRole(Role::SYNCHRONSPRECHER)
                ? Response::allow()
                : Response::denyAsNotFound('Du benötigst die Synchronsprecher Rolle.');
        });

        Gate::define('MODELLIERER', function (User $user) {
            return $user->hasRole(Role::MODELLIERER)
                ? Response::allow()
                : Response::denyAsNotFound('Du benötigst die Modellierer Rolle.');
        });

        Gate::define('DEVELOPER', function (User $user) {
            return $user->hasRole(Role::DEVELOPER)
                ? Response::allow()
                : Response::denyAsNotFound('Du benötigst die Developer Rolle.');
        });

        Gate::define('KAMERAMANN', function (User $user) {
            return $user->hasRole(Role::KAMERAMANN)
                ? Response::allow()
                : Response::denyAsNotFound('Du benötigst die Kameramann Rolle.');
        });

        Gate::define('CUTTER', function (User $user) {
            return $user->hasRole(Role::CUTTER)
                ? Response::allow()
                : Response::denyAsNotFound('Du benötigst die Cutter Rolle.');
        });

        Gate::define('ANGENOMMEN', function (User $user) {
            return $user->hasRole(Role::ANGENOMMEN)
                ? Response::allow()
                : Response::denyAsNotFound('Du benötigst die Angenommen Rolle.');
        });
    }


    /**
     * Register generic role gates for more flexible role checking
     */
    private function registerGenericRoleGates(): void
    {
        // Gate for checking a specific role with enum as parameter
        Gate::define('role', function (User $user, $role) {
            // Convert string representation to Role enum if needed
            if (is_string($role) && str_contains($role, 'App\Enums\Role::')) {
                $roleName = substr($role, strrpos($role, ':') + 1);
                $role = constant("App\Enums\Role::$roleName");
            }

            // Special case for MINEWACHE_TEAM role - direct string comparison
            if ($role === Role::MINEWACHE_TEAM || (is_object($role) && $role == Role::MINEWACHE_TEAM)) {
                $userPerms = (string)$user->permissions;
                $adminValue = (string)Role::MINEWACHE_TEAM->value;

                if ($userPerms === $adminValue) {
                    return Response::allow();
                }
            }

            return $user->hasRole($role)
                ? Response::allow()
                : Response::deny("Du benötigst die {$role->label()} Rolle.");
        });

        // Gate for checking if a user has any role from a list
        Gate::define('any-role', function (User $user, array $roles) {
            // Special case for admin check with direct string comparison
            $userPerms = (string)$user->permissions;
            $adminValue = (string)Role::MINEWACHE_TEAM->value;

            if ($userPerms === $adminValue) {
                return Response::allow();
            }

            return $user->hasAnyRole($roles)
                ? Response::allow()
                : Response::deny('Du benötigst mindestens eine der erforderlichen Rollen.');
        });

        // Gate for checking if a user has all roles from a list
        Gate::define('all-roles', function (User $user, array $roles) {
            // Special case for admin check with direct string comparison
            $userPerms = (string)$user->permissions;
            $adminValue = (string)Role::MINEWACHE_TEAM->value;

            if ($userPerms === $adminValue) {
                return Response::allow();
            }

            return $user->hasAllRoles($roles)
                ? Response::allow()
                : Response::deny('Du benötigst alle erforderlichen Rollen.');
        });

        // Bitmask gate for direct permission checking
        Gate::define('permission', function (User $user, int $permissionBitmask) {
            // If the bitmask being checked is the MINEWACHE_TEAM's full value,
            // rely on the robust hasRole check for MINEWACHE_TEAM.
            // Make sure to import `App\Enums\Role` if not already imported in this file.
            if ($permissionBitmask === \App\Enums\Role::MINEWACHE_TEAM->value) {
                return $user->hasRole(\App\Enums\Role::MINEWACHE_TEAM)
                    ? \Illuminate\Auth\Access\Response::allow()
                    : \Illuminate\Auth\Access\Response::deny("Fehlende Berechtigung: MINEWACHE_TEAM (" . \App\Enums\Role::MINEWACHE_TEAM->name . ")");
            }

            // For any other bitmask, perform standard bitwise check.
            // This assumes $user->permissions can be safely cast to int for checking individual, smaller permission bits.
            // This is generally true if $user->permissions string represents a number.
            // If $user->permissions is not numeric, (int) will make it 0.
            $userPermissionsIntValue = (int)$user->permissions;

            return ($userPermissionsIntValue & $permissionBitmask) === $permissionBitmask
                ? \Illuminate\Auth\Access\Response::allow()
                : \Illuminate\Auth\Access\Response::deny("Fehlende Berechtigung für Bitmask: " . decbin($permissionBitmask));
        });
    }

    /**
     * Register response macros for standardized API responses
     */
    private function registerResponseMacros(): void
    {
        ResponseFactory::macro('success', function ($data = null, $message = 'Success', $status = 200) {
            return ResponseFactory::json([
                'success' => true,
                'message' => $message,
                'data' => $data,
                'timestamp' => now()->toIso8601String(),
            ], $status);
        });

        ResponseFactory::macro('error', function ($message = 'Error', $status = 400, $errors = null) {
            return ResponseFactory::json([
                'success' => false,
                'message' => $message,
                'errors' => $errors,
                'timestamp' => now()->toIso8601String(),
            ], $status);
        });
    }
}
