# Minewache Website

![Minewache Logo](public/favicon.svg)

[![<PERSON><PERSON>](https://img.shields.io/badge/Laravel-11.x-FF2D20.svg?style=flat&logo=laravel&logoColor=white)](https://laravel.com)
[![PHP](https://img.shields.io/badge/PHP-8.2+-777BB4.svg?style=flat&logo=php&logoColor=white)](https://php.net)
[![License](https://img.shields.io/badge/License-MIT-blue.svg)](LICENSE)

## Über das Projekt

Die Minewache-Website ist eine Laravel-basierte Web-Applikation zum Verwalten von Bewerbungen für das Minewache-Minecraft-Projekt. Die Plattform ermöglicht es Interessenten, sich für verschiedene Rollen zu bewerben (z.B<PERSON> Schaus<PERSON>ler, Builder, Designer, etc.) und bietet Team-Administratoren Tools zur Bewertung und Verwaltung dieser Bewerbungen.

Die Anwendung verfügt über eine Discord-Integration für Authentifizierung und Rollenverwaltung, die eine nahtlose Synchronisation zwischen Discord-Server-Rollen und Anwendungsberechtigungen ermöglicht. Ein in PHP implementierter Discord-Bot, der direkt in die Laravel-Anwendung integriert ist, sorgt für zuverlässige Rollenabfragen und -aktualisierungen ohne Rate-Limiting-Probleme.

## Hauptfunktionen

### Bewerbungssystem

#### Für Bewerber
- Ausfüllen eines Bewerbungsformulars mit rollenspezifischen Fragen
- Angabe persönlicher Daten und Fähigkeiten
- Einsicht in den Bewerbungsstatus
- Bearbeitung von Bewerbungen vor der Überprüfung
- Discord-Authentifizierung

#### Für Administratoren
- Umfassendes Dashboard mit Bewerbungsstatistiken
- Übersichtliche Darstellung aller Bewerbungen mit Filteroptionen
- Detaillierte Ansicht einzelner Bewerbungen
- Funktionen zum Annehmen oder Ablehnen von Bewerbungen
- Automatische Generierung von Antwortschreiben mit intelligenten Vorschlägen
- Interne Notizen zu Bewerbern
- Statistiken zu Bewerbungseingängen und -status

### Ticket-System
- Echtzeit-Support-Ticket-System mit WebSocket-Integration (Laravel Reverb)
- Dateianhänge mit automatischer Medienverarbeitung (Bilder, Videos, Audio, PDFs)
- Zweistufiges Broadcasting für optimale Benutzererfahrung
- Synchronisation mit Discord-Kanälen/Threads
- Zuweisungssystem für Support-Mitarbeiter
- Echtzeit-Benachrichtigungen und Tippindikatoren
- Umfangreiche Medienvorschau und -wiedergabe

### Discord-Integration
- Benutzerverwaltung mit Echtzeit-Discord-Rollensynchronisation
- In PHP implementierter Discord-Bot für zuverlässige Rollenaktualisierungen und nahtlose Laravel-Integration
- Nahtlose Integration zwischen Website und Discord-Server
- Automatische Kanalverwaltung für Support-Tickets

## Technische Details

### Anforderungen
- PHP 8.2 oder höher
- Laravel 11.x
- MySQL/MariaDB oder SQLite
- Node.js und NPM für Frontend-Assets
- Composer für PHP-Abhängigkeiten
- Discord-Anwendung (für Authentifizierung)
- FFmpeg (für Medienverarbeitung)

### Verwendete Technologien und Frameworks
- **Backend Framework**: Laravel 11.x
- **Frontend**:
  - Livewire 3.x (Interaktive Komponenten)
  - Tailwind CSS (Styling)
  - DaisyUI (UI-Komponenten für Tailwind)
  - Alpine.js (JavaScript-Framework)
- **Authentifizierung**: Larascord (Discord OAuth2)
- **Echtzeit-Kommunikation**: Laravel Reverb (WebSockets)
- **Hintergrundverarbeitung**: Laravel Queue mit Database-Treiber
- **Icons**: Blade Heroicons
- **Medienverarbeitung**: FFmpeg (via Laravel FFmpeg)
- **Entwicklungstools**:
  - Laravel Telescope (Debugging)
  - Laravel Pulse (Monitoring)
  - Laravel Pint (Code-Formatierung)

## Installation

### Voraussetzungen
- PHP 8.2+
- Composer
- Node.js & NPM
- MySQL/MariaDB oder SQLite
- Discord-Anwendung mit OAuth2-Anmeldedaten

### Schritte

1. **Repository klonen**
   ```bash
   git clone https://github.com/yourusername/minewache-website.git
   cd minewache-website
   ```

2. **Abhängigkeiten installieren**
   ```bash
   composer install
   npm install
   ```

3. **Umgebungsvariablen einrichten**
   ```bash
   cp .env.example .env
   php artisan key:generate
   ```

4. **Discord-Integration konfigurieren**

   Bearbeite die `.env`-Datei und füge deine Discord-Anwendungsdaten hinzu:
   ```
   LARASCORD_CLIENT_ID=your_discord_client_id
   LARASCORD_CLIENT_SECRET=your_discord_client_secret
   LARASCORD_GRANT_TYPE=authorization_code
   LARASCORD_SCOPE=identify&guilds&guilds.members.read
   LARASCORD_GUILD_ID=your_discord_guild_id
   LARASCORD_ACCESS_TOKEN=your_bot_token

   # Discord Bot Configuration
   DISCORD_BOT_TOKEN=your_discord_bot_token_here
   DISCORD_TICKET_CATEGORY_ID=your_discord_ticket_category_id_here
   DISCORD_BOT_ACTIVITY_NAME="Minewache"
   DISCORD_BOT_ACTIVITY_TYPE="PLAYING"
   DISCORD_SUPPORT_ROLE_IDS="your_support_role_id1,your_support_role_id2"
   DISCORD_ENABLED=true
   ```

5. **Datenbank einrichten**
   Passe die Datenbankeinstellungen in der `.env`-Datei an und führe dann aus:
   ```bash
   php artisan migrate
   ```

6. **Discord-Bot konfigurieren**
   ```bash
   # Anleitung zur Einrichtung des PHP Discord-Bots lesen
   cat docs/php_discord_bot_setup.md
   ```

7. **Frontend-Assets kompilieren und Server starten**
   ```bash
   # Startet Laravel, Vite, Queue-Worker, Discord-Bot und Reverb-Server
   npm run dev
   ```

   Alternativ können Sie die Dienste in separaten Fenstern starten:

   **Verschiedene Entwicklungsumgebungen:**
   ```bash
   # Startet alles außer Reverb
   npm run dev:no-reverb

   # Startet alles außer Discord-Bot
   npm run dev:no-discord

   # Startet nur Laravel, Vite und Queue-Worker (minimal)
   npm run dev:minimal
   ```

   **Reverb in einem separaten Fenster starten:**
   ```bash
   # Startet Reverb in einem separaten Fenster (Windows)
   npm run reverb:win

   # Startet Reverb in einem separaten Fenster (Unix/Mac)
   npm run reverb
   ```

   **PHP Discord-Bot in einem separaten Fenster starten:**
   ```bash
   # Startet PHP Discord-Bot in einem separaten Fenster (Windows)
   start-discord.bat

   # Startet PHP Discord-Bot in einem separaten Fenster (Unix/Mac)
   php artisan minewache:run-discord-bot
   ```

   **Dienste stoppen:**
   ```bash
   # Stoppt Reverb (Windows)
   npm run reverb:stop:win

   # Stoppt Reverb (Unix/Mac)
   npm run reverb:stop

   # Stoppt PHP Discord-Bot (Windows)
   stop-discord.bat

   # Stoppt PHP Discord-Bot (Unix/Mac)
   # Verwende Ctrl+C im Terminal, in dem der Bot läuft, oder:
   pkill -f "php artisan minewache:run-discord-bot"
   ```

   **Verwendung der tmux-Skripte:**

   Für Entwicklungsumgebungen (startet alle Dienste in einer tmux-Sitzung):
   ```bash
   # Führt das tmux-Skript für die Entwicklungsumgebung aus
   ./scripts/tmux-dev.sh
   ```

   Für Produktionsumgebungen (startet die notwendigen Dienste ohne Vite):
   ```bash
   # Führt das tmux-Skript für die Produktionsumgebung aus
   ./scripts/tmux-production.sh
   ```

   Für Monitoring der Produktionsdienste (überwacht laufende systemd-Dienste):
   ```bash
   # Startet ein Monitoring-Dashboard für die Produktionsdienste
   ./scripts/monitor-production.sh
   ```

   Innerhalb der tmux-Sitzung können Sie mit folgenden Tastenkombinationen navigieren:
   - `Ctrl+B` gefolgt von einer Zahl (0-6): Wechselt zum entsprechenden Fenster
   - `Ctrl+B` gefolgt von `d`: Trennt die Sitzung (lässt sie im Hintergrund laufen)
   - Zum Wiederverbinden: `tmux attach -t minewache-dev` oder `tmux attach -t minewache-prod`
   - Zum Beenden der Sitzung: `tmux kill-session -t minewache-dev` oder `tmux kill-session -t minewache-prod`

   Für die Produktionsumgebung:
   ```bash
   npm run build
   ```

   Die Anwendung ist nun unter `http://localhost:8000` erreichbar.

## Projektstruktur

### Wichtige Verzeichnisse

- **app/Enums/** - PHP 8.1 Enums für typsichere Enumerationen
  - `Role.php` - Definiert Benutzerrollen mit Bitmasking für Berechtigungen

- **app/Http/Controllers/** - Controller für die Verarbeitung von HTTP-Anfragen
  - `ApplicationController.php` - Verarbeitet Bewerbungseinreichungen und -ansichten
  - `AdminController.php` - Admin-Dashboard und Verwaltungsfunktionen
  - `PermissionController.php` - Verwaltet die Discord-Rollensynchronisation
  - `TicketController.php` - Verwaltet das Ticket-System
  - `TicketApiController.php` - API-Endpunkte für Discord-Integration

- **app/Http/Middleware/** - Middleware für Anfrageverarbeitung
  - `CheckDiscordMembership.php` - Prüft Discord-Servermitgliedschaft
  - `VerifyApiToken.php` - Authentifiziert API-Anfragen
  - `SetLocale.php` - Setzt die Anwendungssprache

- **app/Jobs/** - Hintergrundaufgaben
  - `ProcessTicketMediaJob.php` - Verarbeitet Medien-Uploads (Bilder, Videos, Audio)

- **app/Events/** - Event-Klassen für das Broadcasting-System
  - `TicketMessageCreated.php` - Event für neue Ticket-Nachrichten
  - `TicketMessageAttachmentsReady.php` - Event für verarbeitete Anhänge
  - `TicketTypingEvent.php` - Event für Tippindikatoren

- **app/Livewire/** - Livewire-Komponenten für interaktive UI-Elemente
  - `ApplicationForm.php` - Komponente für das Bewerbungsformular
  - `ApplicationManager.php` - Komponente für die Bewerbungsverwaltung
  - `ApplicationWizard.php` - Mehrstufiger Bewerbungsassistent
  - `TicketList.php` - Listet und filtert Tickets
  - `TicketView.php` - Zeigt einzelne Tickets mit Nachrichten an
  - `TicketCreate.php` - Formular zum Erstellen neuer Tickets

- **app/Services/** - Service-Klassen
  - `ResponseGeneratorService.php` - Service zur Generierung von Antwortschreiben
  - `DiscordService.php` - Service für Discord-Integration

- **app/Models/** - Datenmodelle
  - `Application.php` - Modell für Bewerbungsdaten
  - `User.php` - Benutzermodell mit Discord-Integration
  - `RegisterYoutubeLink.php` - Modell für YouTube-Link-Registrierungen
  - `Ticket.php` - Modell für Support-Tickets
  - `TicketMessage.php` - Modell für Ticket-Nachrichten
  - `TicketAttachment.php` - Modell für Ticket-Anhänge mit Medienverarbeitung

- **app/Console/Commands/RunMinewacheDiscordBot.php** - PHP-Discord-Bot-Implementierung
  - **app/Services/DiscordRoleManagementService.php** - Service für Discord-Rollenverwaltung
  - **app/Services/DiscordTicketChannelService.php** - Service für Discord-Ticket-Kanäle
  - **app/Services/DiscordMessagingService.php** - Service für Discord-Nachrichten
  - **app/Services/PermissionSyncService.php** - Service für Berechtigungssynchronisation

- **resources/views/livewire/** - Blade-Templates für Livewire-Komponenten
  - `application-form.blade.php` - Bewerbungsformular
  - `application-manager.blade.php` - Bewerbungsübersicht und -verwaltung
  - `application-wizard.blade.php` - Mehrstufiger Bewerbungsassistent
  - `ticket-list.blade.php` - Ticket-Übersicht mit Filtern
  - `ticket-view.blade.php` - Detailansicht eines Tickets mit Nachrichten
  - `ticket-create.blade.php` - Formular zum Erstellen neuer Tickets

- **resources/js/** - JavaScript-Dateien
  - `bootstrap.js` - WebSocket-Konfiguration und Initialisierung
  - `ticket-view-functions.js` - Funktionen für die Ticket-Ansicht
  - `app.js` - Hauptanwendungsdatei

## Nutzung

### Bewerbungsformular
Das Bewerbungsformular ist für Interessenten zugänglich und passt sich dynamisch an die ausgewählte Rolle an. Je nach gewählter Tätigkeit werden spezifische Felder angezeigt.

### Bewerbungsverwaltung
Administratoren können über das Dashboard alle eingegangenen Bewerbungen einsehen, filtern und verwalten. Die Bewerbungen können nach verschiedenen Kriterien sortiert und gefiltert werden:

- Nach Status (ausstehend, angenommen, abgelehnt)
- Nach Tätigkeitsbereich (Schauspieler, Builder, etc.)
- Nach Name oder Beschreibung

### Antwortgenerierung
Der integrierte Antwortgenerator erstellt automatisch formatierte Antworten basierend auf dem Bewerbungsstatus und ausgewählten Gründen. Die Antworten können sowohl als formatierter Text als auch als Klartext kopiert werden.

## Rollen-System

Das Projekt verwendet PHP 8.1 Enums für die Implementierung von Benutzerrollen mit Bitmasking, was eine flexible und typsichere Verwaltung von Berechtigungen ermöglicht.

### Verfügbare Rollen
- **MINEWACHE_TEAM**: Vollständige Administratorrechte
- **SCHAUSPIELER**: Mitglieder des Schauspielteams
- **BUILDER**: Minecraft-Builder
- **DESIGNER**: Grafik- und UI-Designer
- **SYNCHRONSPRECHER**: Sprecher für Charakterstimmen
- **MODELLIERER**: 3D-Modellierer
- **DEVELOPER**: Entwickler
- **KAMERAMANN**: Kameraleute für Aufnahmen
- **CUTTER**: Video-Editoren
- **ANGENOMMEN**: Standard-Akzeptanzstatus
- **ANGENOMMENPLUS**: Erweiterter Akzeptanzstatus mit zusätzlichen Rechten

### Verwendung von Rollen
```php
use App\Enums\Role;

// Prüfen einer Rolle
if ($user->hasRole(Role::MINEWACHE_TEAM)) {
    // Administrative Aktionen erlauben
}

// Rollenname oder Label abrufen
$roleName = Role::BUILDER->name; // "BUILDER"
$roleLabel = Role::BUILDER->label(); // "Builder"
```

## API-Schnittstellen

Die Anwendung bietet verschiedene API-Endpunkte für die Integration mit externen Systemen, insbesondere mit Discord.

### Verfügbare Endpunkte

- **GET /api/ping** - Healthcheck-Endpunkt zur Statusprüfung
- **POST /api/discord/role-update** - Webhook für Discord-Bot zur Rollenaktualisierung
- **GET /api/auth-test** - Endpoint zum Testen der API-Authentifizierung
- **GET /api/debug/role-mapping** - Debugging-Informationen zu Rollenzuordnungen

### API-Authentifizierung
Die API verwendet Token-basierte Authentifizierung. Der `api.token`-Middleware schützt entsprechende Endpunkte. API-Token können in der `.env`-Datei konfiguriert werden:

```env
API_TOKEN=your_secure_api_token
```

### Discord-Integration
Die Integration mit Discord wird über die Larascord-Bibliothek realisiert. Konfigurationsoptionen sind in `config/larascord.php` zu finden. Für die Integration müssen folgende Umgebungsvariablen gesetzt werden:

```env
LARASCORD_CLIENT_ID=your_discord_client_id
LARASCORD_CLIENT_SECRET=your_discord_client_secret
LARASCORD_GUILD_ID=your_discord_guild_id
```

## Design-System

Die Minewache-Website verwendet ein konsistentes Design-System, das auf Tailwind CSS und DaisyUI basiert. Das Design folgt einer klaren, modernen und benutzerfreundlichen Philosophie mit Fokus auf Klarheit, Funktionalität, Konsistenz und Zugänglichkeit.

### Farbpalette

Die Website verwendet ein angepasstes DaisyUI-Theme namens "minewache" mit folgenden Hauptfarben:

- **Primary**: `oklch(58% 0.169 237.323)` - Hauptfarbe für wichtige Elemente und Aktionen
- **Secondary**: `oklch(64% 0.246 16.439)` - Sekundärfarbe für Akzente und Hervorhebungen
- **Base-100**: `oklch(37% 0.146 265.522)` - Hintergrundfarbe für Hauptbereiche
- **Base-200**: `oklch(20% 0.042 265.755)` - Hintergrundfarbe für Sekundärbereiche
- **Base-300**: `oklch(58% 0.158 241.966)` - Hintergrundfarbe für Tertiärbereiche
- **Info/Success/Warning/Error**: Statusfarben für Benachrichtigungen und Feedback

### Typografie

Das Projekt verwendet eine Kombination aus modernen Schriftarten:

- **Primär**: Geist Sans (für UI-Elemente und allgemeinen Text)
- **Sekundär**: Sora (für geometrische Elemente)
- **Serif**: Literata (für Kontrast und Lesbarkeit bei längeren Texten)

### Komponenten

Das Design-System definiert konsistente Komponenten wie:

- **Cards**: Container für zusammengehörige Informationen
- **Buttons**: Primär, Sekundär, Ghost und Link-Varianten
- **Forms**: Einheitlich gestaltete Eingabefelder, Selects und Checkboxen
- **Alerts**: Informations-, Erfolgs-, Warn- und Fehlermeldungen
- **Badges**: Kleine Kennzeichnungen für Status oder Kategorien

## Animationen

Die Anwendung nutzt ein umfangreiches Animationssystem für verbesserte Benutzerinteraktion und visuelle Rückmeldung.

### Grundlegende Animationen

- **Fade-In**: `animate-fade-in` - Sanftes Einblenden von Elementen
- **Slide-Up/Down/Left/Right**: `animate-slide-up`, `animate-slide-down`, etc. - Bewegungsanimationen
- **Pulse**: `animate-pulse` - Pulsierende Aufmerksamkeitserregung
- **Bounce**: `animate-bounce` - Leichte Hüpfbewegung für interaktive Elemente

### Verzögerte Animationen

- **Delayed-Fade**: `animate-delayed-fade` - Verzögertes Einblenden
- **Delayed-Slide**: `animate-delayed-slide` - Verzögerte Bewegungsanimation

### Füllstand-Animationen

Spezielle Animationen für Fortschrittsbalken:

- **Fill-Animation**: `fill-animation` - Füllt auf 33%
- **Fill-Animation2-3**: `fill-animation2-3` - Füllt auf 66%
- **Fill-Animation3-3**: `fill-animation3-3` - Füllt auf 100%

### Verwendung in Komponenten

```html
<!-- Card mit Fade-In Animation -->
<div class="card-themed animate-fade-in">
  <!-- Card Content -->
</div>

<!-- Alert mit Slide-Up Animation -->
<div class="alert-themed success animate-slide-up">
  Deine Änderungen wurden erfolgreich gespeichert!
</div>

<!-- Fortschrittsbalken mit Füllanimation -->
<div class="bg-base-300 rounded-full h-2.5">
  <div class="bg-primary h-2.5 rounded-full fill-animation3-3"></div>
</div>
```

Weitere Dokumentation zu Animationen ist in der Datei `docs/animations.md` zu finden.

## Wartung und Weiterentwicklung

### Neue Rollen hinzufügen
Um neue Rollen hinzuzufügen:

1. Erweitere das `Role`-Enum in `app/Enums/Role.php` mit der neuen Rolle
2. Füge eine passende `label()`-Methode für die neue Rolle hinzu
3. Erweitere das `$roleTranslations`-Array in `ResponseGeneratorService.php`
4. Füge entsprechende Einträge in `$roleInstructions` hinzu
5. Aktualisiere die verfügbaren Professionen in `ApplicationManager.php` und `ApplicationForm.php`

### Ablehnungsgründe anpassen
Die Ablehnungsgründe können im `$rejectionReasons`-Array in `ResponseGeneratorService.php` angepasst werden.

### API-Endpunkte erweitern
Neue API-Endpunkte können in `routes/api.php` definiert werden. Verwende die `api.token`-Middleware für geschützte Endpunkte:

```php
Route::get('/api/new-endpoint', function () {
    // Implementation
})->middleware('api.token');
```

## Dokumentation

Weitere technische Dokumentationen sind im `docs/`-Verzeichnis zu finden:
- `animations.md` - Dokumentation zu verwendeten Animationen
- `ffmpeg-integration.md` - Dokumentation zur FFmpeg-Integration für Medienverarbeitung
- `deployment_guide.md` - Anleitung zur Bereitstellung der Anwendung
- `php_discord_bot_setup.md` - Anleitung zur Einrichtung und Bereitstellung des PHP-Discord-Bots
- `ticket-system-overview.md` - Übersicht über das Ticket-System mit WebSocket-Integration
- `websocket-configuration.md` - Detaillierte Konfiguration und Fehlerbehebung für WebSockets
- `file-upload-system.md` - Dokumentation zum Datei-Upload-System
- `file-upload-troubleshooting.md` - Fehlerbehebung für Datei-Uploads
- `layout-guide.md` - Anleitung zum Layout-System
- `application-guide.md` - Anleitung zum Bewerbungssystem

### tmux-Skripte

Das Projekt enthält mehrere tmux-Skripte zur Vereinfachung der Entwicklung und des Betriebs:

- **scripts/tmux-dev.sh**: Startet alle Dienste für die Entwicklungsumgebung in einer tmux-Sitzung
- **scripts/tmux-complete.sh**: Vollständiges Entwicklungsskript mit Port-Prüfung und Prozessmanagement
- **scripts/tmux-production.sh**: Startet die notwendigen Dienste für die Produktionsumgebung ohne Vite
- **scripts/monitor-production.sh**: Überwacht die laufenden systemd-Dienste in der Produktionsumgebung

Diese Skripte bieten eine bequeme Möglichkeit, alle erforderlichen Dienste in einer organisierten tmux-Sitzung zu starten und zu verwalten. Für die Produktionsumgebung wird empfohlen, die systemd-Dienste mit `scripts/setup-production.sh` einzurichten und dann `monitor-production.sh` für die Überwachung zu verwenden.

## Lizenz

Dieses Projekt steht unter der [MIT Lizenz](https://opensource.org/licenses/MIT).
