# File Upload Troubleshooting Guide

This guide provides solutions for common issues with the file upload functionality in the Minewache ticket system.

## Common Issues and Solutions

### 1. Files Upload But Don't Appear in the Message

**Symptoms:**
- File upload progress reaches 100%
- No error messages are displayed
- Files don't appear in the message or show as "Processing..."

**Possible Causes:**
- Queue worker is not running
- Media processing job failed
- WebSocket events are not being received

**Solutions:**
1. **Check Queue Worker:**
   ```bash
   # Check if queue worker is running
   ps aux | grep "queue:work"
   
   # Start queue worker if not running
   php artisan queue:work --queue=default,media --tries=3 --timeout=600
   ```

2. **Check Failed Jobs:**
   ```bash
   # Check for failed jobs
   php artisan queue:failed
   
   # Retry failed jobs
   php artisan queue:retry all
   ```

3. **Check WebSocket Server:**
   ```bash
   # Check if Reverb server is running
   ps aux | grep "reverb:start"
   
   # Start Reverb server if not running
   php artisan reverb:start
   ```

4. **Check Logs:**
   ```bash
   # Check Laravel logs for errors
   tail -f storage/logs/laravel.log
   ```

### 2. File Upload Fails with Error

**Symptoms:**
- Upload progress stops before reaching 100%
- Error message is displayed
- Files don't appear in the message

**Possible Causes:**
- File size exceeds server limits
- File type is not allowed
- Server disk space is full
- Temporary directory permissions issue

**Solutions:**
1. **Check File Size Limits:**
   - Verify that the file size is within the limits set in `php.ini`
   - Check the `upload_max_filesize` and `post_max_size` settings

2. **Check File Type Restrictions:**
   - Verify that the file type is allowed in the `TicketReplyForm` validation rules
   - Check the MIME type of the file

3. **Check Disk Space:**
   ```bash
   # Check disk space
   df -h
   ```

4. **Check Temporary Directory Permissions:**
   ```bash
   # Check permissions on the temporary directory
   ls -la /tmp
   ```

### 3. Media Files Don't Process Correctly

**Symptoms:**
- Files upload successfully
- Media files (images, videos, audio) don't display correctly
- Processing status shows as "failed"

**Possible Causes:**
- Missing dependencies (FFmpeg, Ghostscript)
- Incorrect file format
- Processing timeout

**Solutions:**
1. **Check Dependencies:**
   ```bash
   # Check if FFmpeg is installed
   which ffmpeg
   
   # Check if Ghostscript is installed
   which gs
   ```

2. **Install Missing Dependencies:**
   ```bash
   # Install FFmpeg
   sudo apt-get install ffmpeg
   
   # Install Ghostscript
   sudo apt-get install ghostscript
   ```

3. **Check Processing Logs:**
   ```bash
   # Check Laravel logs for processing errors
   grep "Media processing failed" storage/logs/laravel.log
   ```

4. **Increase Processing Timeout:**
   - Update the `$timeout` property in `ProcessTicketMediaJob` to a higher value

### 4. WebSocket Updates Not Working

**Symptoms:**
- Files upload and process successfully
- UI doesn't update automatically
- Manual refresh is required to see new attachments

**Possible Causes:**
- WebSocket connection failed
- Authentication issues
- Event broadcasting configuration issues

**Solutions:**
1. **Check WebSocket Connection:**
   - Open browser developer tools
   - Check for WebSocket connection errors in the Console tab

2. **Check Reverb Server:**
   ```bash
   # Check if Reverb server is running
   ps aux | grep "reverb:start"
   
   # Start Reverb server if not running
   php artisan reverb:start
   ```

3. **Check Broadcasting Configuration:**
   - Verify that the `BROADCAST_CONNECTION` is set to `reverb` in `.env`
   - Check the `broadcasting.php` configuration

4. **Check Event Broadcasting:**
   - Verify that the `TicketMessageAttachmentsReady` event implements `ShouldBroadcast`
   - Check that the event is being dispatched correctly

### 5. Queue Worker Keeps Failing

**Symptoms:**
- Queue worker stops unexpectedly
- Jobs remain in the queue
- Processing never completes

**Possible Causes:**
- Memory limits exceeded
- Timeouts
- Unhandled exceptions

**Solutions:**
1. **Increase Memory Limit:**
   ```bash
   # Start queue worker with increased memory limit
   php -d memory_limit=512M artisan queue:work
   ```

2. **Increase Timeout:**
   ```bash
   # Start queue worker with increased timeout
   php artisan queue:work --timeout=600
   ```

3. **Use Supervisor:**
   - Set up Supervisor to automatically restart the queue worker if it fails
   - Create a configuration file in `/etc/supervisor/conf.d/minewache-worker.conf`

4. **Check for Unhandled Exceptions:**
   ```bash
   # Check Laravel logs for exceptions
   grep "Exception" storage/logs/laravel.log
   ```

## Preventive Measures

1. **Regular Maintenance:**
   - Regularly check for failed jobs
   - Monitor disk space usage
   - Check logs for errors

2. **Automated Monitoring:**
   - Set up monitoring for queue workers
   - Set up alerts for failed jobs
   - Monitor WebSocket server status

3. **Regular Testing:**
   - Regularly test file uploads with different file types
   - Test with large files
   - Test with multiple simultaneous uploads

4. **Documentation:**
   - Keep documentation up-to-date
   - Document common issues and solutions
   - Document server requirements and dependencies

## Conclusion

By following this troubleshooting guide, you should be able to identify and resolve common issues with the file upload functionality in the Minewache ticket system. If you encounter issues not covered in this guide, please check the Laravel logs for more detailed error messages and consult the Laravel documentation for additional troubleshooting steps.
