<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}" class="scroll-smooth">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="csrf-token" content="{{ csrf_token() }}">
    <meta name="description" content="Die offizielle Webseite der Minewache - Die beliebte Minecraft-Polizeiserie mit spannenden Verfolgungsjagden, Kriminalfällen und Rollenspiel im Minecraft-Universum">
    <meta name="theme-color" content="#265FC2">
    <meta name="keywords" content="Die Minewache, Minecraft Polizei Serie, Minecraft Rollenspiel, Minecraft Krimi, Minecraft Verfolgungsjagd, Minecraft Polizeiserie, Polizei Rollenspiel Minecraft, Minecraft Story Serie, Minecraft Großeinsatz">

    <!-- Canonical URL -->
    <x-canonical-url />

    <title>Die Minewache - {{ $heading ?? 'Minecraft Polizeiserie' }}</title>

    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Literata:ital,wght@0,400;0,700;1,400&family=Sora:wght@400;500;600;700&display=swap" rel="stylesheet">

    <!-- Inline theme styles to prevent FOUC -->
    <style>
        /* Dark theme (minewache) */
        [data-theme="minewache"] {
            --color-base-100: oklch(37% 0.146 265.522);
            --color-base-100-rgb: 37, 37, 70;
            --color-base-200: oklch(20% 0.042 265.755);
            --color-base-200-rgb: 20, 20, 40;
            --color-base-300: oklch(58% 0.158 241.966);
            --color-base-300-rgb: 58, 58, 100;
            --color-base-content: oklch(98% 0.002 247.839);
            --color-base-content-rgb: 255, 255, 255;
        }

        /* Light theme (minewache-light) */
        [data-theme="minewache-light"] {
            --color-base-100: oklch(98% 0.005 247.839);
            --color-base-100-rgb: 248, 250, 252;
            --color-base-200: oklch(95% 0.01 265.755);
            --color-base-200-rgb: 241, 245, 249;
            --color-base-300: oklch(90% 0.015 241.966);
            --color-base-300-rgb: 226, 232, 240;
            --color-base-content: oklch(20% 0.042 265.755);
            --color-base-content-rgb: 20, 20, 40;
        }
    </style>

    <!-- Theme initialization script (before any other scripts) -->
    <script src="{{ asset('js/theme-init.js') }}"></script>

    <!-- Scripts -->
    @vite(['resources/css/app.css', 'resources/js/app.js'])
</head>
<body class="font-sans antialiased bg-base-100 text-base-content">
    <!-- Theme Switcher -->
    <x-theme-switcher position="bottom-right" />
    <!-- Skip to content link for accessibility -->
    <a href="#main-content" class="sr-only focus:not-sr-only focus:absolute focus:p-4 focus:bg-primary focus:text-primary-content">
        {{ __('messages.skip_to_content') }}
    </a>
    @if (session('status'))
        <div class="alert alert-success shadow-lg max-w-md mx-auto mt-4 z-50">
            <div>
                <svg xmlns="http://www.w3.org/2000/svg" class="stroke-current flex-shrink-0 h-6 w-6" fill="none" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" /></svg>
                <span>{{ session('status') }}</span>
            </div>
        </div>
    @endif
    <div id="main-content" class="min-h-screen flex flex-col justify-center items-center px-4 py-12 sm:px-6 lg:px-8">
        <div class="w-full max-w-md">
            {{ $slot }}
        </div>
    </div>

    <!-- Theme is now managed by the theme-switcher component -->

    <!-- Cookie Consent Banner -->
    <x-cookie-consent position="bottom" />

    <!-- GDPR Consent Modal -->
    <x-gdpr-consent-modal :show="session()->has('show_gdpr_consent')" />

    @stack('scripts')
</body>
</html>
